/*
 * @Date: 2023-02-01 18:17:56
 * @LastEditors: l<PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2023-05-05 16:04:58
 * @FilePath: /newicp/test/cron.js
 */
const uuid = require('uuid/v4')
const fs = require('fs')
// 获取配置信息
const ENV = 'gray'
// const ENV = 'test'
global.CONFIG = require('../configs/' + ENV + '/config')
const Cron = require('../libs/cron')
// 获取Cron名称
let cronName = process.argv[2]

// 参数没传
if (!cronName) return console.log('请输入Cron名称')

// 验证Cron文件是否存在
let cronFile = '../crons/' + cronName + '.js'
if (!fs.existsSync(cronFile)) return console.log('找不到对应Cron')

// 引入Cron文件
require(cronFile)
/**
 * **************注意*****************
 * 如果该cron本身是重复性的定时任务的话 需要在
 * redis中的   bull:BullCron:repeat  中删除该repeat任务
 */
Cron.setCron(cronName, {
    data: '',
    delay: 1000, //单位毫秒
})
