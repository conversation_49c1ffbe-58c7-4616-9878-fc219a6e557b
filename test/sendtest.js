const nodemailer = require('nodemailer')

let config = {
    port: 994,
    host: 'hwsmtp.qiye.163.com',
    secure: true,
    auth: {
        user: '<EMAIL>',
        pass: 'TuPUcMVCU6eDHG9a',
    },
}
config.pool = true

let smtpTransport = nodemailer.createTransport(config)

let b = `<html>
<head>
    <meta http-equiv='Content-Type' content='text/html; charset=UTF-8'>
</head>
<body>
<style>
    font{
        line-height: 1.6;
    }
    ul,ol{
        padding-left: 20px;
        list-style-position: inside;
    }
</style>
<div style = 'font-family:微软雅黑,Verdana,&quot;Microsoft Yahei&quot;,SimSun,sans-serif;font-size:14px; line-height:1.6;'>
    <div ></div><div>
    <div>您好
            该IP: *********** 非我司所有，请再次核实。</div>
    <div>
        <span>
            <br>
        </span>
    </div>
    </div>
</div><div class="J-reply" style="background-color:#f2f2f2;color:black;padding-top:6px;padding-bottom:6px;border-radius:3px;-moz-border-radius:3px;-webkit-border-radius:3px;margin-top:45px;margin-bottom:20px;font-family:'微软雅黑';">
    <div style="font-size:12px;line-height:1.5;word-break:break-all;margin-left:10px;margin-right:10px">On <span class="mail-date">Mon Jan 19 1970 17:22:35 GMT+0800 (CST)</span>，<a class="mail-to" style="text-decoration:none;color:#2a83f2;" href="mailto:<EMAIL>">BitNinja&lt;<EMAIL>&gt;</a> wrote： </div>
</div>

<blockquote id="ntes-pcmail-quote" style="margin: 0; padding: 0; font-size: 14px; font-family: '微软雅黑';">
<html>
<head>
<meta http-equiv='Content-Type' content='text/html; charset=UTF-8'>
</head>
<body>
<style>
font{
    line-height: 1.6;
}
ul,ol{
    padding-left: 20px;
    list-style-position: inside;
}
</style>
<div style = 'font-family:微软雅黑,Verdana,&quot;Microsoft Yahei&quot;,SimSun,sans-serif;font-size:14px; line-height:1.6;'>
<div ></div>
<style>
font{
    line-height: 1.6;
}
ul,ol{
    padding-left: 20px;
    list-style-position: inside;
}
</style>
<div style="font-family:微软雅黑,Verdana,&quot;Microsoft Yahei&quot;,SimSun,sans-serif;font-size:14px; line-height:1.6;">
<div></div><div>
<div><span style="color: rgb(49, 53, 59); font-family: Verdana, 'Microsoft Yahei', SimSun, sans-serif; line-height: 22.4px;">Dear Sir/Madam,</span><br style="color: rgb(49, 53, 59); font-family: Verdana, 'Microsoft Yahei', SimSun, sans-serif; line-height: 22.4px;"><br style="color: rgb(49, 53, 59); font-family: Verdana, 'Microsoft Yahei', SimSun, sans-serif; line-height: 22.4px;"><span style="color: rgb(49, 53, 59); font-family: Verdana, 'Microsoft Yahei', SimSun, sans-serif; line-height: 22.4px;">We have detected abuse from the IP address ( *********** ), which according to a whois lookup is on your network. We would appreciate if you would investigate and take action as appropriate. Any feedback is welcome but not mandatory.</span><br style="color: rgb(49, 53, 59); font-family: Verdana, 'Microsoft Yahei', SimSun, sans-serif; line-height: 22.4px;"><br style="color: rgb(49, 53, 59); font-family: Verdana, 'Microsoft Yahei', SimSun, sans-serif; line-height: 22.4px;"><span style="color: rgb(49, 53, 59); font-family: Verdana, 'Microsoft Yahei', SimSun, sans-serif; line-height: 22.4px;">Log lines are given below, but please ask if you require any further information.</span><br style="color: rgb(49, 53, 59); font-family: Verdana, 'Microsoft Yahei', SimSun, sans-serif; line-height: 22.4px;"><br style="color: rgb(49, 53, 59); font-family: Verdana, 'Microsoft Yahei', SimSun, sans-serif; line-height: 22.4px;"><span style="color: rgb(49, 53, 59); font-family: Verdana, 'Microsoft Yahei', SimSun, sans-serif; line-height: 22.4px;">(If you are not the correct person to contact about this please accept our apologies - your e-mail address was extracted from the whois record by an automated process. This mail was generated by Fail2Ban.)</span><br style="color: rgb(49, 53, 59); font-family: Verdana, 'Microsoft Yahei', SimSun, sans-serif; line-height: 22.4px;"><br style="color: rgb(49, 53, 59); font-family: Verdana, 'Microsoft Yahei', SimSun, sans-serif; line-height: 22.4px;"><span style="color: rgb(49, 53, 59); font-family: Verdana, 'Microsoft Yahei', SimSun, sans-serif; line-height: 22.4px;">IP of the attacker: &nbsp;***********&nbsp;</span><br style="color: rgb(49, 53, 59); font-family: Verdana, 'Microsoft Yahei', SimSun, sans-serif; line-height: 22.4px;"><br style="color: rgb(49, 53, 59); font-family: Verdana, 'Microsoft Yahei', SimSun, sans-serif; line-height: 22.4px;"><span style="color: rgb(49, 53, 59); font-family: Verdana, 'Microsoft Yahei', SimSun, sans-serif; line-height: 22.4px;">You can contact us by using: <EMAIL></span><br style="color: rgb(49, 53, 59); font-family: Verdana, 'Microsoft Yahei', SimSun, sans-serif; line-height: 22.4px;"><br style="color: rgb(49, 53, 59); font-family: Verdana, 'Microsoft Yahei', SimSun, sans-serif; line-height: 22.4px;"><span style="color: rgb(49, 53, 59); font-family: Verdana, 'Microsoft Yahei', SimSun, sans-serif; line-height: 22.4px;">Addresses to send to:</span><br style="color: rgb(49, 53, 59); font-family: Verdana, 'Microsoft Yahei', SimSun, sans-serif; line-height: 22.4px;"><span style="color: rgb(49, 53, 59); font-family: Verdana, 'Microsoft Yahei', SimSun, sans-serif; line-height: 22.4px;"><EMAIL>,<EMAIL>,<EMAIL>,<EMAIL></span><br style="color: rgb(49, 53, 59); font-family: Verdana, 'Microsoft Yahei', SimSun, sans-serif; line-height: 22.4px;"><br style="color: rgb(49, 53, 59); font-family: Verdana, 'Microsoft Yahei', SimSun, sans-serif; line-height: 22.4px;"><span style="color: rgb(49, 53, 59); font-family: Verdana, 'Microsoft Yahei', SimSun, sans-serif; line-height: 22.4px;">==================== Excerpt from log for *********** ====================</span><br style="color: rgb(49, 53, 59); font-family: Verdana, 'Microsoft Yahei', SimSun, sans-serif; line-height: 22.4px;"><span style="color: rgb(49, 53, 59); font-family: Verdana, 'Microsoft Yahei', SimSun, sans-serif; line-height: 22.4px;">Note: Local timezone is +0200 (CEST)</span><br style="color: rgb(49, 53, 59); font-family: Verdana, 'Microsoft Yahei', SimSun, sans-serif; line-height: 22.4px;"><span style="color: rgb(49, 53, 59); font-family: Verdana, 'Microsoft Yahei', SimSun, sans-serif; line-height: 22.4px;">May &nbsp;6 04:53:11 shared07 sshd[28438]: Invalid user win from *********** port 34970</span><br style="color: rgb(49, 53, 59); font-family: Verdana, 'Microsoft Yahei', SimSun, sans-serif; line-height: 22.4px;"><span style="color: rgb(49, 53, 59); font-family: Verdana, 'Microsoft Yahei', SimSun, sans-serif; line-height: 22.4px;">May &nbsp;6 04:53:11 shared07 sshd[28438]: pam_unix(sshd:auth): authentication failure; logname= uid=0 euid=0 tty=ssh ruser= rhost=***********</span><br style="color: rgb(49, 53, 59); font-family: Verdana, 'Microsoft Yahei', SimSun, sans-serif; line-height: 22.4px;"><span style="color: rgb(49, 53, 59); font-family: Verdana, 'Microsoft Yahei', SimSun, sans-serif; line-height: 22.4px;">May &nbsp;6 04:53:13 shared07 sshd[28438]: Failed password for invalid user win from *********** port 34970 ssh2</span><br style="color: rgb(49, 53, 59); font-family: Verdana, 'Microsoft Yahei', SimSun, sans-serif; line-height: 22.4px;"><span style="color: rgb(49, 53, 59); font-family: Verdana, 'Microsoft Yahei', SimSun, sans-serif; line-height: 22.4px;">May &nbsp;6 04:53:13 shared07 sshd[28438]: Received disconnect from *********** port 34970:11: Bye Bye [preauth]</span><br style="color: rgb(49, 53, 59); font-family: Verdana, 'Microsoft Yahei', SimSun, sans-serif; line-height: 22.4px;"><span style="color: rgb(49, 53, 59); font-family: Verdana, 'Microsoft Yahei', SimSun, sans-serif; line-height: 22.4px;">May &nbsp;6 04:53:13 shared07 sshd[28438]: Disconnected from invalid user win *********** port 34970 [preauth]</span><br style="color: rgb(49, 53, 59); font-family: Verdana, 'Microsoft Yahei', SimSun, sans-serif; line-height: 22.4px;"><span style="color: rgb(49, 53, 59); font-family: Verdana, 'Microsoft Yahei', SimSun, sans-serif; line-height: 22.4px;">May &nbsp;6 05:09:12 shared07 sshd[2989]: Invalid user sandy from *********** port 50210</span><br style="color: rgb(49, 53, 59); font-family: Verdana, 'Microsoft Yahei', SimSun, sans-serif; line-height: 22.4px;"><span style="color: rgb(49, 53, 59); font-family: Verdana, 'Microsoft Yahei', SimSun, sans-serif; line-height: 22.4px;">May &nbsp;6 05:09:12 shared07 sshd[2989]: pam_unix(sshd:auth): authentication failure; logname= uid=0 euid=0 tty=ssh ruser= rhost=***********</span><br style="color: rgb(49, 53, 59); font-family: Verdana, 'Microsoft Yahei', SimSun, sans-serif; line-height: 22.4px;"><span style="color: rgb(49, 53, 59); font-family: Verdana, 'Microsoft Yahei', SimSun, sans-serif; line-height: 22.4px;">May &nbsp;6 05:09:13 shared07 sshd[2989]: Failed password for invalid user sandy from *********** port 50210 ssh2</span><br style="color: rgb(49, 53, 59); font-family: Verdana, 'Microsoft Yahei', SimSun, sans-serif; line-height: 22.4px;"><span style="color: rgb(49, 53, 59); font-family: Verdana, 'Microsoft Yahei', SimSun, sans-serif; line-height: 22.4px;">May &nbsp;6 05:09:13 shared07 sshd[2989]: Received disconnect from *********** port 50210:11: Bye Bye [preauth]</span><br style="color: rgb(49, 53, 59); font-family: Verdana, 'Microsoft Yahei', SimSun, sans-serif; line-height: 22.4px;"><span style="color: rgb(49, 53, 59); font-family: Verdana, 'Microsoft Yahei', SimSun, sans-serif; line-height: 22.4px;">May &nbsp;6 05:09:13 shared07 sshd[2989]: Disconnected from invalid user sandy *********** port 50210 [preauth]</span><br style="color: rgb(49, 53, 59); font-family: Verdana, 'Microsoft Yahei', SimSun, sans-serif; line-height: 22.4px;"></div><div id="ntes-pcmac-signature" style="font-family:'微软雅黑'">
 </div>
</div><!--?-->
</div><!--?-->
</div>
</body>
</html>
 </blockquote><!--?-->
</div>
</body>
</html>`

let message = {
    from: '<EMAIL>', //发件人邮箱
    to: '<EMAIL>', //收件人邮箱，多个邮箱地址间用','隔开
    cc: '<EMAIL>',
    html: b,
}
smtpTransport.sendMail(message, function (err, res) {
    console.log(message, res)
})
