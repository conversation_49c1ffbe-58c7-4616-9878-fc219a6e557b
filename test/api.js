const fs = require('fs')
const _ = require('lodash')
const assert = require('assert')
const request = require('request')
const apiUrl = require('../configs/development/config').localUrl

// 必须要找{MethodName}.json的格式
let testCaseNames = fs
    .readdirSync(__dirname + '/apis')
    .map((name) => name.split('.')[0])
let testCases = {}

testCaseNames.forEach((name) => {
    testCases[name] = require(__dirname + '/apis/' + name)
})

_.each(testCases, (cases, methodName) => {
    _.each(cases, (c) => {
        describe(methodName, function () {
            describe(JSON.stringify(c.input), function () {
                it('should without error', function (done) {
                    request(
                        {
                            method: 'POST',
                            url: apiUrl + methodName,
                            body: c.input,
                            json: true,
                            timeout: 10000,
                        },
                        (err, resp, body) => {
                            if (err) return done(err)

                            let checkRes = checkResponse(c.output, body)

                            if (!checkRes.hasError) return done()

                            done(checkRes.error)
                        }
                    )
                })
            })
        })
    })
})

// rules
// name:
// type: int string object array
// required: true or false
// value: if undefined check type if not check value equal
function checkResponse(rules, output) {
    let typeArr = ['int', 'string', 'object', 'array']

    // 验证rule中每一条规则的情况
    for (var i in rules) {
        let type = rules[i].type.toLowerCase()

        // 验证类型不支持
        if (typeArr.indexOf(rules[i].type) === -1) {
            return { hasError: true, error: type + ' is not supported' }
        }

        // 必填字段缺失
        if (output[rules[i].name] === undefined && rules[i].required) {
            return { hasError: true, error: rules[i].name + ' is required' }
        }

        // 验证每个状态下返回参数是不是正确
        switch (type) {
            case 'int':
                if (rules[i].value !== undefined) {
                    if (output[rules[i].name] !== rules[i].value) {
                        return {
                            hasError: true,
                            error: rules[i].name + ' is not match',
                        }
                    }
                } else {
                    if (!_.isInteger(output[rules[i].name])) {
                        return {
                            hasError: true,
                            error: rules[i].name + ' is not Integer',
                        }
                    }
                }
                break
            case 'string':
                if (rules[i].value !== undefined) {
                    if (output[rules[i].name] !== rules[i].value) {
                        return {
                            hasError: true,
                            error: rules[i].name + ' is not match',
                        }
                    }
                } else {
                    if (!_.isString(output[rules[i].name])) {
                        return {
                            hasError: true,
                            error: rules[i].name + ' is not String',
                        }
                    }
                }
                break
            case 'object':
                if (rules[i].value !== undefined) {
                    let checkRes = checkResponse(
                        rules[i].value,
                        output[rules[i].name]
                    )
                    if (checkRes.hasError) return checkRes
                } else {
                    if (
                        !_.isObject(output[rules[i].name]) ||
                        _.isArray(output[rules[i].name])
                    ) {
                        return {
                            hasError: true,
                            error: rules[i].name + ' is not Object',
                        }
                    }
                }

                break
            case 'array':
                if (rules[i].value !== undefined) {
                    for (var j in output[rules[i].name]) {
                        let d = {}
                        d[rules[i].name] = output[rules[i].name][j]

                        let checkRes = checkResponse(
                            [
                                {
                                    name: rules[i].name,
                                    type: rules[i].arrType,
                                    value: rules[i].value,
                                },
                            ],
                            d
                        )

                        if (checkRes.hasError) return checkRes
                    }
                } else {
                    if (!_.isArray(rules[i].value)) {
                        return {
                            hasError: true,
                            error: rules[i].name + ' is not Array',
                        }
                    }
                }
                break
        }
    }

    return { hasError: false }
}
