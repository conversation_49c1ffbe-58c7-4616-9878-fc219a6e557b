# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
 
# Runtime data
pids
*.pid
*.seed
*.pid.lock
 
# Directory for instrumented libs generated by jscoverage/JSCover
lib-cov
 
# Coverage directory used by tools like istanbul
coverage
 
# nyc test coverage
.nyc_output
 
# Grunt intermediate storage (http://gruntjs.com/creating-plugins#storing-task-files)
.grunt
 
# Bower dependency directory (https://bower.io/)
bower_components
 
# node-waf configuration
.lock-wscript
 
# Compiled binary addons (https://nodejs.org/api/addons.html)
build/Release
 
# Dependency directories
node_modules/
jspm_packages/

# Optional npm cache directory
.npm
 
# Optional eslint cache
.eslintcache
 
# Optional REPL history
.node_repl_history
 
# Yarn Integrity file
.yarn-integrity
 
# dotenv environment variables file
.env

# temp
.tmp/

# vscode
.vscode/

# dist
dist/

# install packages
build/
target/

# intellj
.idea/
.classpath
.project

# Created by https://www.gitignore.io/api/macos
# Edit at https://www.gitignore.io/?templates=macos

### macOS ###
# General
.DS_Store
.AppleDouble
.LSOverride

# Icon must end with two \r
Icon

# Thumbnails
._*

# Files that might appear in the root of a volume
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent

# Directories potentially created on remote AFP share
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# End of https://www.gitignore.io/api/macos


# Created by https://www.gitignore.io/api/linux
# Edit at https://www.gitignore.io/?templates=linux

### Linux ###
*~

# temporary files which can be created if a process still has a handle open of a deleted file
.fuse_hidden*

# KDE directory preferences
.directory

# Linux trash folder which might appear on any partition or disk
.Trash-*

# .nfs files are created when an open file is removed but is still being accessed
.nfs*

# End of https://www.gitignore.io/api/linux



# Created by https://www.gitignore.io/api/windows
# Edit at https://www.gitignore.io/?templates=windows

### Windows ###
# Windows thumbnail cache files
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db

# Dump file
*.stackdump

# Folder config file
[Dd]esktop.ini

# Recycle Bin used on file shares
$RECYCLE.BIN/

# Windows Installer files
*.cab
*.msi
*.msix
*.msm
*.msp

# Windows shortcuts
*.lnk

# End of https://www.gitignore.io/api/windows

# install lock
package-lock.json
yarn.lock

# userConfig
docs/数据字典

# releasePkg
app.tar
app.tar.gz

# file
/data/*
/cache/*


# git registry
.git
.gitignore

# pretty code
.eslintrc
.prettierrc.js

# test
src/test/*
jest.config.js

#java
jre.tar.gz

#build
app.tar

