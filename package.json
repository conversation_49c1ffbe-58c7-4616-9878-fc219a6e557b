{"name": "weibeian-backend", "version": "0.0.0", "private": true, "scripts": {"watch": "forever -w --spinSleepTime 30000 -m 2 app.js", "test": "./node_modules/mocha/bin/mocha --no-timeouts"}, "dependencies": {"@opentelemetry/api": "^1.4.1", "@opentelemetry/context-async-hooks": "^1.14.0", "@opentelemetry/exporter-zipkin": "^1.11.0", "@opentelemetry/instrumentation": "^0.37.0", "@opentelemetry/instrumentation-amqplib": "^0.32.2", "@opentelemetry/instrumentation-express": "^0.32.1", "@opentelemetry/instrumentation-http": "^0.37.0", "@opentelemetry/resources": "^1.11.0", "@opentelemetry/sdk-trace-node": "^1.11.0", "@opentelemetry/semantic-conventions": "^1.11.0", "@opentelemetry/tracing": "^0.24.0", "ajv": "^6.5.1", "amqplib": "^0.5.2", "assert": "^1.4.1", "axios": "^1.4.0", "bankcardinfo": "^2.0.5", "base-x": "^3.0.8", "base64-url": "^2.3.3", "body-parser": "~1.18.2", "bull": "^4.7.0", "cookie-parser": "~1.4.3", "cors": "^2.8.4", "cron": "^1.3.0", "crypto": "^1.0.1", "crypto-js": "^4.0.0", "csv-parse": "^2.0.4", "csvjson": "^5.1.0", "csvtojson": "^2.0.10", "debug": "^3.1.0", "exec-sh": "^0.4.0", "express": "^4.16.4", "express-rate-limit": "^6.4.0", "fetch": "^1.1.0", "fluent-ffmpeg": "^2.1.2", "general-verification": "^1.2.29", "get-pixels": "^3.3.2", "gm": "^1.23.1", "idcard": "^4.2.0", "ioredis": "^4.17.1", "ip": "^1.1.8", "ipip-ipdb": "^0.6.0", "json2csv": "^6.0.0-alpha.2", "lodash": "^4.17.4", "log4js": "^6.9.1", "mail": "^0.2.3", "md5": "^2.2.1", "mime-types": "^2.1.27", "moment": "^2.20.1", "mongodb": "^4.1.1", "mongojs": "^3.1.0", "mongoose": "^6.6.5", "mysql": "^2.18.1", "mysql-activerecord": "^0.8.6", "mysql2": "^2.2.5", "netmask": "^1.0.6", "node-snowflake": "^0.0.1", "nodemailer": "^6.4.6", "openai": "^4.55.1", "psl": "^1.4.0", "punycode": "^2.1.1", "qs": "^6.11.2", "rate-limit-redis": "^3.0.1", "redlock": "^4.2.0", "request": "^2.88.0", "request-promise": "^4.2.6", "require-all": "~2.2.0", "sendcloud": "^1.6.3", "sequelize": "^6.31.1", "soap": "^1.0.0", "string-random": "^0.1.3", "swagger-and-schema": "^2.1.5", "underscore": "^1.13.6", "uppercamelcase": "^3.0.0", "uri-parse-lib": "^2.3.0", "url-parse": "^1.4.7", "urlencode": "^1.1.0", "util": "^0.11.1", "uuid": "^3.1.0", "validator": "^13.7.0", "xml2js": "^0.6.2", "xmlbuilder2": "^3.1.1"}, "devDependencies": {"eslint": "^7.32.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^3.4.1", "forever": "^0.15.3", "jshint": "^2.9.5", "mocha": "^5.0.0", "pm2": "^2.7.2", "prettier": "^2.3.2"}, "description": "标签（空格分隔）： 检验 配置 在线 API", "main": ".prettierrc.js", "directories": {"doc": "docs", "test": "test"}, "repository": {"type": "git", "url": "***********************:crd/newicp.git"}, "keywords": [], "author": "", "license": "ISC"}