# dependency 
# FROM hub.ucloudadmin.com/tpl_crd_icp/node-buster:14.18-buster
FROM hub.ucloudadmin.com/tpl_crd_icp/node-add-chromium:20.17.0-bullseye
# MAINTAINER yuewen.li "<EMAIL>"
COPY package.json /var/node/newicp/package.json

COPY *.js /var/node/newicp/
COPY crons /var/node/newicp/crons
COPY configs /var/node/newicp/configs
COPY fns /var/node/newicp/fns
COPY libs /var/node/newicp/libs
COPY methods /var/node/newicp/methods
COPY models /var/node/newicp/models
COPY mongoModels /var/node/newicp/mongoModels
COPY mq /var/node/newicp/mq
COPY test /var/node/newicp/test

# 设置时间区
ENV TZ=Asia/Shanghai \
    SERVER_ENV=gray \
    APPPath=/var/node/newicp

RUN ln -fs /usr/share/zoneinfo/${TZ} /etc/localtime  && \
    echo ${TZ} > /etc/timezone

WORKDIR /var/node/newicp

RUN npm install -g cnpm --registry=https://registry.npmmirror.com/
RUN cnpm i
# RUN npm install

RUN mkdir -p /data/logs/new/tmpFile
RUN mkdir -p /data/logs/new/shotPic
# console
EXPOSE  6060

CMD ["node", "--harmony", "/var/node/newicp/console.js"]

# admin
# EXPOSE 6161

# CMD ["node", "/var/node/newicp/admin.js"]
