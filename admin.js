/*
 * @Date: 2021-12-16 11:52:07
 * @LastEditors: li<PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2023-07-26 15:31:02
 * @FilePath: /newicp/admin.js
 */
let ENV = require('./configs/env')
if (process.env.SERVER_ENV) {
    ENV = process.env.SERVER_ENV
}
global.CONFIG = require('./configs/' + ENV + '/config')
if (process.env.APPPath) {
    global.CONFIG.appPath = process.env.APPPath
}
// 增加追踪
if (global.CONFIG.env === 'production') {
    // require('./libs/tracer')('admin')
}
const Logger = require('./libs/logger')

const express = require('express')
const http = require('http')
const cookieParser = require('cookie-parser')
const bodyParser = require('body-parser')
const cors = require('cors')
const os = require('os')
const initChannelInfo = require('./libs/initChannelInfo')

let app = express()
process.env.UV_THREADPOOL_SIZE = 128

process.env.NODE_TLS_REJECT_UNAUTHORIZED = '0'
app.use(cors())
app.use(bodyParser.json({ limit: '50mb' }))
app.use(
    bodyParser.urlencoded({
        limit: '50mb',
        extended: false,
    })
)
app.use(cookieParser())
app.use('/', require('./adminRouter'))

initChannelInfo()

const getLocalIP = function () {
    let IPv4 = '0.0.0.0'
    const interfaces = os.networkInterfaces() || {}
    if (interfaces.eth0) {
        IPv4 = interfaces.eth0[0].address
    }
    return IPv4
}
let localIP = getLocalIP()

http.createServer(app).listen(global.CONFIG.adminPort, localIP, ENV, () => {
    console.log(
        `Listen to ${
            localIP + ':' + global.CONFIG.adminPort
        } at ${new Date()} Env ${ENV}`
    )
})
app.on('error', function (err) {
    Logger.getLogger('error').error(
        `[${new Date()}], system: server error occurred: ${err.message}`
    )
})

process.on('uncaughtException', (err) =>
    Logger.getLogger('error').error(
        `[${new Date()}] system: uncaughtException`,
        err.message
    )
)
