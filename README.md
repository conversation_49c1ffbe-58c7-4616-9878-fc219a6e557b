<!--
 * @Date: 2021-12-16 11:52:07
 * @LastEditors: l<PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2023-06-13 18:29:32
 * @FilePath: /newicp/README.md
-->
# 新版备案后端，结合审核侧与用户侧

标签（空格分隔）： 检验 配置 在线 API

---

### 环境依赖

推荐Centos7以上操作系统，因存在对图片的大量处理工具，因此需要安装GraphicsMagick与ffmpeg工具.
安装方式如下:


```bash
<!-- GraphicsMagick安装 -->
# 安装基础工具  centos
yum install -y gcc libpng libjpeg libpng-devel libjpeg-devel ghostscript libtiff libtiff-devel freetype freetype-devel gd-devel libgomp

# 安装基础工具 ubuntu
apt-get -y install gcc


# 下载

这个也可以，但可能会出错yum install GraphicsMagick

wget http://ftp.icm.edu.pl/pub/unix/graphics/GraphicsMagick/1.3/GraphicsMagick-1.3.21.tar.gz
# 解压
tar zxvf GraphicsMagick-1.3.21.tar.gz
cd GraphicsMagick-1.3.21
# 编译与安装
./configure
make -j8
make install

<!-- ImageMagick安装 -->
# convert命令使用
yum install ImageMagick-*******-15.el7_2


<!-- ffmpeg安装 -->
# 加载源
rpm -Uvh http://li.nux.ro/download/nux/dextop/el7/x86_64/nux-dextop-release-0-5.el7.nux.noarch.rpm
# 安装
yum install -y ffmpeg
# 确定版本
ffmpeg -version

<!-- chromium安装 -->
# 因爬虫功能需要模拟浏览器渲染后的效果，需要安装浏览器
yum clean all
yum install -y epel-release
yum -y update
yum install -y chromium
# 基础镜像构建
apt update
apt-get install chromium
```

---

### 业务部署。
#### console
灰度  k8s 部署 
namespace prj-icp
svc  newicp-console-bak

线上  172.18.180.51   /data/newicp
      172.18.176.47  /data/newicp

#### admin 
全部k8s部署
namespace prj-icp
线上 svc newicp-admin-bak-online
灰度 svc newicp-admin-bak

#### mq
附属于console 服务上监听

#### cron
附属于admin 服务上监听处理


### 发布流程
#### console 灰度发布
灰度测试  将测试代码一次性merge到dev分支  
git checkout dev
git merge 需要测试的代码的分支（将测试代码合并的admin-dev）
git push
触发自动cicd

#### console 线上发布  
1.login 跳板机
2.git clone xxx.git 服务
3.tar cvf newicp.tar newicp  打包
4.执行发布脚本 pub_newicp_console.sh 发布

#### admin 灰度发布
灰度测试 将测试代码一次性merge到admin-dev分支  
git checkout admin-dev
git merge 需要测试的代码的分支（将测试代码合并的admin-dev）
git push
触发自动cicd

#### admin线上发布  
git tag v1.xx.xx-admin git push origin v1.xx.xx-admin 触发自动cicd

注意：我司.gitlad-ci.yaml 使用的是busybox shell命令只包含最基础的（应是基础镜像不包含） 模糊判断无法使用

### console部署管理机原因
爬虫相关的k8s不太行，k8s若访问v4地址 需要将v4 映射到v6地址，。。。且puppeteer库测试下来 有问题
故先放管理机，后续service 拆分后 或者其他调整后 若无影响了 再迁移

### console迁移注意
RUN mkdir -p /data/logs/new/tmpFile
RUN mkdir -p /data/logs/new/shotPic
手动创建者两个临时文件路径

