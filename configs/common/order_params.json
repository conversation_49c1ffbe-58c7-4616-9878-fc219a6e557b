{"main": {"1": {"common": ["AreaId", "OrganizerName", "OrganizerType", "OrganizerLicenseType", "OrganizerLicenseId", "OrganizerLicenseArea", "Organizer<PERSON>ddress", "OrganizerLicensePicture", "OrganizerResidencePermitPicture", "PICMainName", "PICMainLicenseType", "PICMainLicenseId", "PICMainLicenseDate", "PICMainMobile", "PICMainEmail", "PICMainQQ", "PICMainLicensePicture", "PromiseLetterPicture", "Remark", "EmergencyPhone", "IsExtractType", "OrganizerUUID", "EditStatus", "FrontError", "FrontPictureError", "Website"], "afterVerify": ["OtherPicture", "SubmitReexamineType"]}, "8": {"common": ["AreaId", "OrganizerName", "OrganizerType", "OrganizerLicenseType", "OrganizerLicenseId", "OrganizerLicenseArea", "Organizer<PERSON>ddress", "OrganizerLicensePicture", "OrganizerResidencePermitPicture", "PromiseLetterPicture", "PICMainName", "PICMainLicenseType", "PICMainLicenseId", "PICMainLicenseDate", "PICMainMobile", "PICMainEmail", "Update<PERSON><PERSON>nt", "PICMainQQ", "PICMainLicensePicture", "Remark", "EmergencyPhone", "IsExtractType", "OrganizerUUID"], "afterVerify": ["OtherPicture", "SubmitReexamineType"]}, "7": {"common": ["AreaId", "OrganizerName", "OrganizerType", "ICPMainNo", "OrganizerLicenseType", "OrganizerLicenseId", "OrganizerLicenseArea", "Organizer<PERSON>ddress", "OrganizerLicensePicture", "PromiseLetterPicture", "OrganizerResidencePermitPicture", "PICMainName", "PICMainLicenseType", "PICMainLicenseId", "PICMainLicenseDate", "PICMainMobile", "PICMainEmail", "PICMainQQ", "PICMainLicensePicture", "Remark", "EmergencyPhone", "IsExtractType", "Update<PERSON><PERSON>nt", "Website"], "afterVerify": ["OtherPicture", "SubmitReexamineType"]}, "2": {"common": ["AreaId", "OrganizerName", "OrganizerType", "ICPMainNo", "OrganizerLicenseType", "OrganizerLicenseId", "OrganizerLicenseArea", "Organizer<PERSON>ddress", "OrganizerLicensePicture", "PromiseLetterPicture", "OrganizerResidencePermitPicture", "PICMainName", "PICMainLicenseType", "PICMainLicenseId", "PICMainLicenseDate", "PICMainMobile", "PICMainEmail", "PICMainQQ", "PICMainLicensePicture", "Remark", "EmergencyPhone", "IsExtractType", "Update<PERSON><PERSON>nt", "AppCode", "Website"], "afterVerify": ["OtherPicture", "SubmitReexamineType"]}, "9": {"common": ["ICPMainNo", "status", "Update<PERSON><PERSON>nt", "ICPMainNo", "PromiseLetterPicture", "Website"], "afterVerify": ["OtherPicture", "SubmitReexamineType"]}, "3": {"common": ["AreaId", "OrganizerName", "OrganizerType", "ICPMainNo", "OrganizerLicenseType", "OrganizerLicenseId", "OrganizerLicenseArea", "Organizer<PERSON>ddress", "OrganizerLicensePicture", "PromiseLetterPicture", "OrganizerResidencePermitPicture", "PICMainName", "PICMainLicenseType", "PICMainLicenseId", "PICMainLicenseDate", "PICMainMobile", "PICMainEmail", "PICMainQQ", "PICMainLicensePicture", "Remark", "EmergencyPhone", "IsExtractType", "Update<PERSON><PERSON>nt", "AppCode", "Website"], "afterVerify": ["OtherPicture", "SubmitReexamineType"]}, "10": {"common": ["ICPMainNo", "Update<PERSON><PERSON>nt", "Website"], "afterVerify": ["OtherPicture", "SubmitReexamineType"]}, "4": {"common": ["ICPMainNo", "ICPMainPassword", "CMainId", "ReasonId"]}, "5": {"common": ["ICPWebNo", "CWebsiteId", "ReasonId", "Details"]}, "6": {"common": ["ICPWebNo", "CWebsiteId", "ReasonId", "Details"]}}, "website": {"1": {"common": ["Name", "Domain", "Url", "ServiceType", "Language", "IP", "PreAppoval", "PICName", "LicenseType", "LicensePicture", "LicenseId", "LicenseDate", "PhonePre", "Phone", "Mobile", "Email", "QQ", "FrontError", "EmergencyPhone", "Remark", "AuthenticationCode", "CurtainPicture", "AppCode", "AuthVerificationPicture", "CurtainPicture", "OtherPicture", "FetchInfo"], "afterVerify": ["CurtainPicture", "AppCode", "AuthVerificationPicture", "OtherPicture"]}, "8": {"common": [], "afterVerify": []}, "2": {"common": ["Name", "Domain", "Url", "ServiceType", "Language", "IP", "PreAppoval", "PICName", "LicenseType", "LicensePicture", "LicenseId", "PhonePre", "Phone", "Mobile", "Email", "QQ", "EmergencyPhone", "Remark", "AuthenticationCode", "CurtainPicture", "AppCode", "CurtainPicture", "AuthVerificationPicture", "OtherPicture", "FetchInfo"], "afterVerify": ["CurtainPicture", "AppCode", "AuthVerificationPicture", "OtherPicture"]}, "9": {"common": ["Name", "Domain", "Url", "ServiceType", "ServiceContent", "Language", "ICPWebNo", "PreAppoval", "Domain", "PICName", "LicenseType", "LicensePicture", "LicenseId", "LicenseDate", "PhonePre", "Phone", "Mobile", "Email", "QQ", "EmergencyPhone", "Remark", "CurtainPicture", "CurtainPicture", "AppCode", "AuthVerificationPicture", "OtherPicture"], "afterVerify": ["CurtainPicture", "AppCode", "AuthVerificationPicture", "OtherPicture"]}, "3": {"common": ["IP", "Domain", "LicensePicture", "Mobile", "Email", "ICPWebNo", "CurtainPicture", "AppCode", "AuthVerificationPicture", "OtherPicture"], "afterVerify": ["CurtainPicture", "AppCode", "AuthVerificationPicture", "OtherPicture"]}, "10": {"common": ["IP", "BaseManageprovince", "ConnectType", "ICPWebNo", "AuthenticationCode", "CurtainPicture", "AppCode", "AuthVerificationPicture", "OtherPicture"], "afterVerify": ["CurtainPicture", "AppCode", "AuthVerificationPicture", "OtherPicture"]}, "4": {"common": []}, "5": {"common": []}, "6": {"common": []}, "7": {"common": ["Name", "Domain", "Url", "ICPWebNo", "ServiceType", "Language", "IP", "PreAppoval", "PICName", "LicenseType", "LicensePicture", "LicenseId", "LicenseDate", "PhonePre", "Phone", "Mobile", "Email", "QQ", "EmergencyPhone", "Remark", "ConnenctType", "BaseManageprovince", "AuthenticationCode", "CurtainPicture", "AppCode", "CurtainPicture", "AuthVerificationPicture", "OtherPicture"], "afterVerify": ["CurtainPicture", "AppCode", "AuthVerificationPicture", "OtherPicture"]}}}