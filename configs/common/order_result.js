'use strict'

//根据订单类型 返回订单中指定的字段
module.exports = {
    1: [
        'OrganizerType',
        'OrganizerAddress',
        'OrganizerLicenseArea',
        'OrganizerLicensePicture',
        'OrganizerResidencePermitPicture',
        'PICMainLicensePicture',
        'PromiseLetterPicture',
        'EmergencyPhone',
        'PICMainMobile',
        'PICMainEmail',
        'PICMainQQ',
        'Website',
        'IsExtractType',
        'AppCode',
        'UnitSuperior',
        'UUID',
        'Step',
        'OrganizerUUID',
        'Recipient',
    ],
    // 变更主体
    8: [
        'OrganizerType',
        'OrganizerAddress',
        'OrganizerLicenseArea',
        'OrganizerLicensePicture',
        'OrganizerResidencePermitPicture',
        'PICMainLicensePicture',
        'PromiseLetterPicture',
        'EmergencyPhone',
        'PICMainMobile',
        'PICMainEmail',
        'PICMainQQ',
        'Website',
        'AppCode',
        'UnitSuperior',
        'UUID',
        'OrganizerUUID',
        'Recipient',
    ],
    2: [
        'OrganizerType',
        'OrganizerAddress',
        'OrganizerLicenseArea',
        'OrganizerLicensePicture',
        'OrganizerResidencePermitPicture',
        'PICMainLicensePicture',
        'PromiseLetterPicture',
        'EmergencyPhone',
        'PICMainMobile',
        'PICMainEmail',
        'PICMainQQ',
        'Website',
        'IsExtractType',
        'AppCode',
        'UnitSuperior',
        'UUID',
        'Step',
        'OrganizerUUID',
        'Recipient',
    ],
    //变更网站
    9: [
        'OrganizerType',
        'PICMainLicenseType',
        'PromiseLetterPicture',
        'EmergencyPhone',
        'PICMainMobile',
        'Website',
        'Recipient',
        'AppCode',
        'OrganizerLicensePicture',
        'OrganizerResidencePermitPicture',
        'PICMainLicensePicture',
    ],
    3: [
        'OrganizerType',
        'OrganizerAddress',
        'OrganizerLicenseArea',
        'OrganizerLicensePicture',
        'OrganizerResidencePermitPicture',
        'PICMainLicensePicture',
        'PromiseLetterPicture',
        'EmergencyPhone',
        'PICMainMobile',
        'PICMainEmail',
        'PICMainQQ',
        'Website',
        'IsExtractType',
        'AppCode',
        'UnitSuperior',
        'UUID',
        'Step',
        'OrganizerUUID',
        'Recipient',
        'AreaId',
        'ICPMainPassword',
        'OrganizerLicenseType',
        'PICMainName',
        'PICMainLicenseType',
        'PICMainLicenseId',
    ],
    //变更接入
    10: [
        'ICPMainPassword',
        'OrganizerType',
        'Recipient',
        'Website',
        'PICMainLicensePicture',
        'OrganizerLicensePicture',
        'OrganizerResidencePermitPicture',
        'PromiseLetterPicture',
    ],
    4: [
        'ICPMainPassword',
        'LogoutReason',
        'Website',
        'ReasonId',
        'RejectReason',
        'OrganizerName',
    ],
    5: ['ICPWebNo', 'LogoutReason', 'Details', 'ReasonId', 'RejectReason'],
    6: ['ICPWebNo', 'LogoutReason', 'ReasonId', 'RejectReason', 'Details'],
    //变更备案
    7: [
        'OrganizerName',
        'OrganizerType',
        'OrganizerAddress',
        'OrganizerLicenseArea',
        'OrganizerLicensePicture',
        'OrganizerResidencePermitPicture',
        'PICMainLicensePicture',
        'PromiseLetterPicture',
        'EmergencyPhone',
        'PICMainMobile',
        'PICMainEmail',
        'ReasonId',
        'RejectReason',
        'PICMainQQ',
        'Website',
        'IsExtractType',
        'AppCode',
        'UnitSuperior',
        'Recipient',
    ],
    // 主办单位 主体负责人 姓名 状态 类型 相关
    common: [
        'Id',
        'AreaId',
        'ICPMainNo',
        'ICPWebNo',
        'OrderNo',
        'OrganizerName',
        'OrganizerLicenseType',
        'PICMainName',
        'PICMainLicenseType',
        'PICMainLicenseId',
        'PICMainLicenseDate',
        'UnitCertificateExpDate',
        'Status',
        'CWebsiteId',
        'Type',
        'CreateTime',
        'UpdateTime',
        'OrganizerLicenseId',
        'CMainId',
        'SubmitReexamineType',
        'Error',
        'OtherPicture',
        'IsExtractType',
        'EditStatus',
        'Step',
        'EditStatus',
        'CurtainStatus',
        'FrontError',
        'Stash',
        'FrontPictureError',
        'Version',
        'UpdateContent',
        'Remark',
        'Note',
        'NeedModifyIcp'
    ],
}
