[{"_id": "5ecaa61649188f076776af33", "Config": {"main": {"AreaId": [{"name": "必填", "rule": "required", "value": true, "remark": ""}], "Website": [{"name": "必填", "rule": "required", "value": true, "remark": ""}], "UnitSuperior": [{"name": "必填", "rule": "required", "value": true, "remark": ""}], "OrganizerType": [{"name": "必填", "rule": "required", "value": true, "remark": ""}, {"name": "类型", "rule": "lodash", "value": "isNumber", "remark": ""}], "OrganizerName": [{"name": "必填", "rule": "required", "value": true, "remark": ""}, {"name": "特殊字符", "rule": "fun", "value": "char<PERSON><PERSON><PERSON>"}], "OrganizerAddress": [{"name": "必填", "rule": "required", "value": true, "remark": ""}], "OrganizerLicenseType": [{"name": "必填", "rule": "required", "value": true, "remark": ""}, {"ruleTest": "MUST_USE_LOCAL_ID_CARD", "rule": "equal", "value": 2, "errorTip": "请选择身份证"}], "OrganizerLicenseArea": [{"name": "必填", "rule": "required", "value": true, "remark": ""}, {"name": "不能包含空格", "rule": "fun", "value": "<PERSON><PERSON><PERSON><PERSON>", "remark": ""}], "OrganizerLicenseId": [{"name": "必填", "rule": "required", "value": true, "remark": ""}, {"name": "特殊字符", "rule": "fun", "value": "char<PERSON><PERSON><PERSON>"}, {"name": "身份证号", "bindKey": "OrganizerLicenseType", "bindValue": [1], "rule": "fun", "value": "organizerLicenseIdCheck", "remark": ""}, {"ruleTest": "SPECIAL_FOR_ORGANIZER_LICENSE_TYPE_IS_CREDIT_CODE", "rule": "fun", "value": "creditCodeCheck", "errorTip": "此处是特殊规则的错误文案", "remark": ""}, {"name": "身份证号", "rule": "fun", "bindKey": "OrganizerLicenseType", "bindValue": [2, 41], "value": "id<PERSON><PERSON><PERSON>", "remark": ""}], "PICMainName": [{"name": "必填", "rule": "required", "value": true, "remark": ""}, {"name": "特殊字符", "rule": "fun", "value": "char<PERSON><PERSON><PERSON>"}], "Remark": [{"ruleTest": "REQUIRES_REMARK_PROVINCES", "name": "必填", "rule": "fun", "value": "<PERSON><PERSON><PERSON><PERSON>", "remark": ""}], "PICMainMobile": [{"name": "必填", "rule": "required", "value": true, "remark": ""}, {"name": "特殊字符", "rule": "fun", "value": "mobileCheck", "remark": ""}], "EmergencyPhone": [{"name": "必填", "rule": "required", "value": true, "remark": ""}, {"name": "长度", "rule": "length", "value": 11, "remark": "", "errorTip": "请输入正确的手机号码"}, {"name": "手机号", "rule": "fun", "value": "mobileCheck", "remark": ""}, {"ruleTest": "NO_HEI_LONG_JIANG_SPECIAL", "name": "特殊规则", "rule": "notEqual", "bindKey": "PICMainMobile", "remark": "", "errorTip": "不可与手机号码重复"}, {"ruleCheck": "IF_PERSONAL_IS_NOT_SAME_EMERGENCYPHONE_MUST_BE_SAME", "name": "主体应急电话复杂规则", "remark": ""}], "PICMainEmail": [{"name": "必填", "rule": "required", "value": true, "remark": ""}, {"name": "邮箱", "rule": "fun", "value": "emailCheck", "remark": ""}], "PICMainLicenseType": [{"name": "必填", "rule": "required", "value": true, "remark": ""}, {"ruleTest": "MUST_USE_LOCAL_ID_CARD", "rule": "equal", "value": 2, "errorTip": "请选择身份证"}], "PICMainLicenseId": [{"name": "必填", "rule": "required", "value": true, "remark": ""}, {"name": "特殊字符", "rule": "fun", "value": "char<PERSON><PERSON><PERSON>"}, {"name": "身份证号", "bindKey": "PICMainLicenseType", "bindValue": [2, 41], "rule": "fun", "value": "id<PERSON><PERSON><PERSON>"}]}, "web": {"Name": [{"name": "必填", "rule": "required", "value": true, "remark": ""}, {"name": "特殊字符", "rule": "fun", "value": "char<PERSON><PERSON><PERSON>"}, {"ruleCheck": "WEBSITE_NAME_CAN_NOT_CONTAIN_KEYWORDS", "name": "网站备案范畴", "remark": ""}], "Domain": [{"name": "必填", "rule": "required", "value": true, "remark": ""}, {"name": "域名", "rule": "fun", "value": "domainFormatCheck"}, {"name": "域名异步校验", "rule": "asyncFun", "value": "QueryDomainStatus", "tag": "IsICP", "tagValue": false, "errorTip": "该域名已有工信部备案记录"}], "IP": [{"name": "必填", "rule": "required", "value": true, "remark": ""}, {"name": "ip校验", "rule": "fun", "value": "ipCheck", "remark": ""}, {"ruleCheck": "CAN_ONLY_HAVE_ONE_IP", "name": "一个网站只能对应一个IP", "remark": ""}, {"name": "ip异步校验", "bindKey": "IPType", "bindValue": [1], "rule": "asyncFun", "value": "asyncCheckIp", "remark": ""}], "ConnectType": [{"name": "必填", "rule": "required", "value": true, "remark": ""}], "BaseManageprovince": [{"name": "必填", "rule": "required", "value": true, "remark": ""}], "AuthenticationCode": [{"name": "必填", "bindKey": "IPType", "bindValue": [2], "rule": "required", "value": true, "remark": ""}, {"bindKey": "IPType", "bindValue": [2], "rule": "asyncFun", "value": "CheckAuthCode"}], "Url": [{"name": "必填", "rule": "required", "value": true, "remark": ""}, {"name": "URL格式", "rule": "fun", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"ruleCheck": "WEBSITE_URL_VALIDATE", "name": "网站首页地址复杂校验"}], "Language": [{"name": "必填", "rule": "required", "value": true, "remark": ""}], "ServiceType": [{"name": "必填", "rule": "required", "value": true, "remark": ""}], "ServiceContent": [{"name": "必填", "rule": "required", "value": true, "remark": ""}], "PICName": [{"name": "必填", "rule": "required", "value": true, "remark": ""}, {"name": "特殊字符", "rule": "fun", "value": "char<PERSON><PERSON><PERSON>"}], "Mobile": [{"name": "必填", "rule": "required", "value": true, "remark": ""}, {"name": "手机号", "rule": "fun", "value": "mobileCheck"}, {"ruleCheck": "IF_PERSONAL_IS_NOT_SAME_MOBILE_CAN_NOT_BE_SAME", "name": "网站手机号复杂校验", "remark": ""}], "EmergencyPhone": [{"name": "必填", "rule": "required", "value": true, "remark": ""}, {"name": "手机号", "rule": "fun", "value": "mobileCheck"}, {"ruleTest": "NO_HEI_LONG_JIANG_SPECIAL", "name": "长度", "rule": "notEqual", "bindKey": "Mobile", "remark": "", "errorTip": "不可与手机号码重复"}, {"ruleCheck": "IF_PERSONAL_IS_NOT_SAME_SPECIAL_AREA_EMERGENCYPHONE_CAN_NOT_BE_SAME", "name": "网站应急联系电话复杂校验", "remark": ""}, {"ruleCheck": "IF_PERSONAL_IS_NOT_SAME_EMERGENCYPHONE_MUST_BE_SAME", "name": "网站应急联系电话复杂校验", "remark": ""}], "Email": [{"name": "必填", "rule": "required", "value": true, "remark": ""}, {"name": "邮箱", "rule": "fun", "value": "emailCheck"}, {"ruleCheck": "IF_PERSONAL_IS_NOT_SAME_EMAIL_CAN_NOT_BE_SAME", "name": "主体负责人与网站负责人不同时，不能填写同一邮箱", "remark": ""}], "LicenseType": [{"name": "必填", "rule": "required", "value": true, "remark": ""}], "LicenseId": [{"name": "必填", "rule": "required", "value": true, "remark": ""}, {"name": "特殊字符", "rule": "fun", "value": "char<PERSON><PERSON><PERSON>"}, {"name": "身份证号", "rule": "fun", "bindKey": "LicenseType", "bindValue": [2, 41], "value": "id<PERSON><PERSON><PERSON>", "remark": ""}], "Remark": [{"ruleCheck": "WEBSITE_REMARK_VALIDATE", "name": "网站备注复杂校验"}, {"name": "备注", "rule": "fun", "value": "<PERSON><PERSON><PERSON><PERSON>", "remark": ""}]}}, "Name": "20200525Mmd", "UnixTime": **********}]