{"描述": "订单类型，订单旧状态，订单新状态", "1": {"1": {"1": ["@All"], "2": ["@All"]}, "3": {"6": ["@Website.CurtainPicture", "@Website.AppCode", "@Website.AuthVerificationPicture", "@Website.OtherPicture", "@Website.FrontError", "OtherPicture", "Step", "EditStatus", "FrontError", "UnitCertificateExpDate", "SubmitReexamineType"], "7": ["@Website.CurtainPicture", "@Website.AppCode", "@Website.AuthVerificationPicture", "@Website.OtherPicture", "@Website.FrontError", "OtherPicture", "Step", "EditStatus", "FrontError", "UnitCertificateExpDate", "SubmitReexamineType"]}, "4": {"2": ["@Error", "@AddWeb", "OrganizerUUID", "UUID", "Step", "EditStatus", "FrontError", "OtherPicture", "UnitCertificateExpDate", "@Website.UUID", "@Website.OtherPicture", "@Website.CurtainUUID", "@Website.FrontError", "@Website.CurtainPicture", "@Website.ReuseWebsite", "@Website.AuthoriztionPicture", "@Website.FetchInfo"], "4": ["@Error", "@AddWeb", "OrganizerUUID", "UUID", "Step", "EditStatus", "FrontError", "OtherPicture", "UnitCertificateExpDate", "@Website.UUID", "@Website.OtherPicture", "@Website.CurtainUUID", "@Website.FrontError", "@Website.CurtainPicture", "@Website.ReuseWebsite", "@Website.AuthoriztionPicture", "@Website.FetchInfo"], "5": ["@Error", "@AddWeb"]}, "5": {"2": ["@Error", "@AddWeb"]}, "6": {"6": ["@Website.CurtainPicture", "@Website.AppCode", "@Website.AuthVerificationPicture", "@Website.OtherPicture", "@Website.FrontError", "OtherPicture", "UnitCertificateExpDate", "SubmitReexamineType"], "7": ["@Website.CurtainPicture", "@Website.AppCode", "@Website.AuthVerificationPicture", "@Website.OtherPicture", "@Website.FrontError", "UnitCertificateExpDate", "OtherPicture", "SubmitReexamineType"]}, "9": {"7": ["@Error", "Step", "EditStatus", "FrontError", "@Website.CurtainPicture", "@Website.FrontError", "@Website.AppCode"], "9": ["@Error", "Step", "EditStatus", "FrontError", "@Website.CurtainPicture", "@Website.FrontError", "@Website.AppCode"]}, "13": {"7": ["@Error"], "2": ["@Error", "@Website.FetchInfo"], "13": ["@Error", "@Website.FetchInfo"], "15": ["@Error"]}, "15": {"7": ["@Error"], "15": ["@Error"]}}, "2": {"1": {"1": ["@All"], "2": ["@All"]}, "3": {"6": ["@Website.CurtainPicture", "@Website.AppCode", "@Website.AuthVerificationPicture", "@Website.FrontError", "@Website.OtherPicture", "OtherPicture", "Step", "EditStatus", "FrontError", "SubmitReexamineType"], "7": ["@Website.CurtainPicture", "@Website.AppCode", "@Website.FrontError", "@Website.AuthVerificationPicture", "@Website.OtherPicture", "OtherPicture", "Step", "EditStatus", "FrontError", "SubmitReexamineType"]}, "4": {"2": ["@Error", "@Website.FetchInfo"], "4": ["@Error", "@Website.FetchInfo"], "5": ["@Error"]}, "5": {"2": ["@Error"]}, "6": {"6": ["@Website.CurtainPicture", "@Website.AppCode", "@Website.FrontError", "@Website.AuthVerificationPicture", "@Website.OtherPicture", "OtherPicture", "SubmitReexamineType"], "7": ["@Website.CurtainPicture", "@Website.AppCode", "@Website.FrontError", "@Website.AuthVerificationPicture", "@Website.OtherPicture", "OtherPicture", "SubmitReexamineType"]}, "9": {"7": ["Step", "EditStatus", "FrontError", "@Website.FrontError", "@Error"], "9": ["Step", "EditStatus", "FrontError", "@Website.FrontError", "@Error"], "10": ["Step", "EditStatus", "FrontError", "@Website.FrontError", "@Error"]}, "13": {"7": ["@Error"], "2": ["@Error", "@Website.FetchInfo"], "13": ["@Error", "@Website.FetchInfo"], "15": ["@Error"]}, "15": {"7": ["@Error"], "15": ["@Error"]}}, "3": {"1": {"1": ["OrderNo", "Type", "Status", "CompanyId", "AreaId", "ICPMainNo", "OrganizerType", "OrganizerName", "Organizer<PERSON>ddress", "OrganizerLicenseType", "OrganizerLicenseId", "OrganizerLicenseArea", "PICMainName", "PICMainOfficePhone", "PICMainOfficePhonePre", "PICMainMobile", "PICMainEmail", "PICMainQQ", "PICMainLicenseType", "PICMainLicenseId", "PICMainLicenseDate", "OrganizerLicensePicture", "OrganizerResidencePermitPicture", "PICMainLicensePicture", "PromiseLetterPicture", "OtherPicture", "Remark", "ICPMainPassword", "SubmitReexamineType", "Auditor", "RejectReason", "LogoutReason", "OrganizerLicensePictureForAuth", "PICMainLicensePictureForAuth", "ICPWebNo", "UnitSuperior", "EmergencyPhone", "AppCode", "CMainId", "CWebsiteId", "ICPWebId", "ICPId", "ReasonId", "SelectTimes", "Update<PERSON><PERSON>nt", "InnerDisplay", "UnitCertificateExpDate", "Version", "PersonsAuditStatus", "@Website.OrderNo", "@Website.ICPWebNo", "@Website.Name", "@Website.Domain", "@Website.IP", "@Website.Url", "@Website.Language", "@Website.ServiceType", "@Website.PICName", "@Website.Phone", "@Website.PhonePre", "@Website.Mobile", "@Website.Email", "@Website.QQ", "@Website.LicenseType", "@Website.LicenseId", "@Website.LicenseDate", "@Website.LicensePicture", "@Website.CurtainPicture", "@Website.AuthVerificationPicture", "@Website.OtherPicture", "@Website.Remark", "@Website.LicensePictureForAuth", "@Website.CurtainPictureForAuth", "@Website.AuthVerificationPictureForAuth", "@Website.EmergencyPhone", "@Website.ServiceContent", "@Website.FetchInfo", "@Website.AppCode", "@Website.CWebsiteId", "@Website.ConnectType", "@Website.BaseManageprovince", "@Website.AuthoriztionPicture", "@Website.InternetServiceType", "@Website.OperatingPlatformType", "@Website.ProvideSdkService", "@Website.UseThirdPartySdkServices", "@Website.UseThirdPartySdkServiceDetails", "@Website.AppIcon", "@Website.AppPlatformInformationList"], "2": ["OrderNo", "Status", "CompanyId", "ICPMainNo", "OrganizerType", "OrganizerName", "Organizer<PERSON>ddress", "OrganizerLicenseType", "OrganizerLicenseId", "PICMainLicenseDate", "OrganizerLicenseArea", "PICMainName", "PICMainOfficePhone", "PICMainOfficePhonePre", "PICMainMobile", "PICMainEmail", "PICMainQQ", "PICMainLicenseType", "PICMainLicenseId", "OrganizerLicensePicture", "PromiseLetterPicture", "OrganizerResidencePermitPicture", "PICMainLicensePicture", "OtherPicture", "Remark", "ICPMainPassword", "SubmitReexamineType", "Auditor", "RejectReason", "LogoutReason", "OrganizerLicensePictureForAuth", "PICMainLicensePictureForAuth", "ICPWebNo", "UnitSuperior", "EmergencyPhone", "AppCode", "CMainId", "CWebsiteId", "ICPWebId", "ICPId", "ReasonId", "SelectTimes", "Update<PERSON><PERSON>nt", "InnerDisplay", "UnitCertificateExpDate", "Version", "PersonsAuditStatus", "@Website.OrderNo", "@Website.ICPWebNo", "@Website.Name", "@Website.Domain", "@Website.IP", "@Website.Url", "@Website.Language", "@Website.ServiceType", "@Website.PICName", "@Website.Phone", "@Website.PhonePre", "@Website.Mobile", "@Website.Email", "@Website.QQ", "@Website.LicenseType", "@Website.LicenseId", "@Website.LicenseDate", "@Website.LicensePicture", "@Website.CurtainPicture", "@Website.AuthVerificationPicture", "@Website.OtherPicture", "@Website.Remark", "@Website.LicensePictureForAuth", "@Website.CurtainPictureForAuth", "@Website.AuthVerificationPictureForAuth", "@Website.EmergencyPhone", "@Website.ServiceContent", "@Website.FetchInfo", "@Website.AppCode", "@Website.CWebsiteId", "@Website.ConnectType", "@Website.BaseManageprovince", "@Website.AuthoriztionPicture", "@Website.InternetServiceType", "@Website.OperatingPlatformType", "@Website.ProvideSdkService", "@Website.UseThirdPartySdkServices", "@Website.UseThirdPartySdkServiceDetails", "@Website.AppIcon", "@Website.AppPlatformInformationList"]}, "3": {"6": ["@Website.CurtainPicture", "@Website.AppCode", "@Website.AuthVerificationPicture", "@Website.OtherPicture", "@Website.FrontError", "OtherPicture", "SubmitReexamineType"], "7": ["@Website.CurtainPicture", "@Website.AppCode", "@Website.AuthVerificationPicture", "@Website.OtherPicture", "@Website.FrontError", "OtherPicture", "Step", "EditStatus", "FrontError", "SubmitReexamineType"]}, "4": {"2": ["@Error", "Step", "@Website.FrontError", "@Website.FetchInfo", "EditStatus", "FrontError"], "4": ["@Error", "Step", "@Website.FrontError", "@Website.FetchInfo", "EditStatus", "FrontError"], "5": ["@Error"]}, "5": {"2": ["@Error"]}, "6": {"6": ["CurtainPicture", "AppCode", "AuthVerificationPicture", "@Website.OtherPicture", "@Website.FrontError", "OtherPicture", "SubmitReexamineType"], "7": ["CurtainPicture", "AppCode", "AuthVerificationPicture", "@Website.FrontError", "@Website.OtherPicture", "OtherPicture", "SubmitReexamineType"]}, "9": {"7": ["Step", "EditStatus", "FrontError", "@Website.FrontError", "@Error"], "9": ["Step", "EditStatus", "FrontError", "@Website.FrontError", "@Error"]}, "13": {"7": ["@Error"], "2": ["@Error", "@Website.FetchInfo"], "13": ["@Error", "@Website.FetchInfo"], "15": ["@Error"]}, "15": {"7": ["@Error"], "15": ["@Error"]}}, "4": {}, "5": {}, "6": {}, "7": {"1": {"1": ["@All"], "2": ["@All"]}, "3": {"6": ["@Website.CurtainPicture", "@Website.AppCode", "@Website.AuthVerificationPicture", "@Website.FrontError", "@Website.OtherPicture", "OtherPicture", "SubmitReexamineType"], "7": ["@Website.CurtainPicture", "@Website.AppCode", "@Website.AuthVerificationPicture", "@Website.OtherPicture", "OtherPicture", "Step", "EditStatus", "FrontError", "@Website.FrontError", "Update<PERSON><PERSON>nt", "SubmitReexamineType"]}, "4": {"2": ["@Error", "Step", "EditStatus", "FrontError", "@Website.FrontError", "@Website.FetchInfo", "@AddWeb"], "4": ["@Error", "Step", "EditStatus", "FrontError", "@Website.FrontError", "@Website.FetchInfo", "@AddWeb"], "5": ["@Error", "@AddWeb"]}, "5": {"2": ["@Error", "@AddWeb"]}, "6": {"6": ["@Website.CurtainPicture", "@Website.AppCode", "@Website.AuthVerificationPicture", "@Website.OtherPicture", "@Website.FrontError", "OtherPicture", "SubmitReexamineType"], "7": ["@Website.CurtainPicture", "@Website.AppCode", "@Website.AuthVerificationPicture", "@Website.OtherPicture", "OtherPicture", "@Website.FrontError", "Update<PERSON><PERSON>nt", "SubmitReexamineType"]}, "9": {"7": ["@Error", "@Website.CurtainPicture", "@Website.AppCode", "@Website.FrontError", "@Website.AuthVerificationPicture", "@Website.OtherPicture", "Step", "EditStatus", "FrontError", "OtherPicture", "Update<PERSON><PERSON>nt", "SubmitReexamineType"], "9": ["@Error", "@Website.CurtainPicture", "@Website.AppCode", "@Website.FrontError", "@Website.AuthVerificationPicture", "@Website.OtherPicture", "Step", "EditStatus", "FrontError", "OtherPicture", "Update<PERSON><PERSON>nt", "SubmitReexamineType"], "10": ["@Error"]}, "13": {"2": ["@Error", "@Website.FetchInfo"], "7": ["@Error", "@Website.CurtainPicture", "@Website.AppCode", "@Website.AuthVerificationPicture", "@Website.OtherPicture", "@Website.FrontError", "OtherPicture", "Step", "EditStatus", "FrontError", "Update<PERSON><PERSON>nt", "SubmitReexamineType"], "13": ["@Error", "@Website.CurtainPicture", "@Website.AppCode", "@Website.AuthVerificationPicture", "@Website.OtherPicture", "@Website.FrontError", "@Website.FetchInfo", "OtherPicture", "Step", "EditStatus", "FrontError", "Update<PERSON><PERSON>nt", "SubmitReexamineType"], "15": ["@Error"]}, "15": {"7": ["@Error"], "15": ["@Error"]}}, "8": {"1": {"1": ["@All"], "2": ["@All"]}, "3": {"6": ["@Website.CurtainPicture", "@Website.AppCode", "@Website.AuthVerificationPicture", "@Website.OtherPicture", "@Website.FrontError", "UnitCertificateExpDate", "OtherPicture", "UnitCertificateExpDate", "SubmitReexamineType"], "7": ["@Website.CurtainPicture", "@Website.AppCode", "@Website.AuthVerificationPicture", "@Website.OtherPicture", "@Website.FrontError", "UnitCertificateExpDate", "OtherPicture", "SubmitReexamineType"]}, "4": {"2": ["@Error", "@AddWeb", "OrganizerUUID", "UUID", "OtherPicture", "UnitCertificateExpDate", "@Website.UUID", "@Website.OtherPicture", "@Website.FrontError", "@Website.CurtainUUID", "@Website.CurtainPicture", "@Website.ReuseWebsite", "@Website.AuthoriztionPicture"], "4": ["@Error", "@AddWeb", "OrganizerUUID", "UUID", "OtherPicture", "UnitCertificateExpDate", "@Website.UUID", "@Website.OtherPicture", "@Website.FrontError", "@Website.CurtainUUID", "@Website.CurtainPicture", "@Website.ReuseWebsite", "@Website.AuthoriztionPicture"], "5": ["@Error", "@AddWeb"]}, "5": {"2": ["@Error", "@AddWeb"]}, "6": {"6": ["@Website.CurtainPicture", "@Website.AppCode", "@Website.AuthVerificationPicture", "@Website.OtherPicture", "@Website.FrontError", "OtherPicture", "UnitCertificateExpDate", "SubmitReexamineType"], "7": ["@Website.CurtainPicture", "@Website.AppCode", "@Website.AuthVerificationPicture", "@Website.FrontError", "@Website.OtherPicture", "OtherPicture", "UnitCertificateExpDate", "SubmitReexamineType"]}, "9": {"7": ["@Error", "@Website.CurtainPicture", "@Website.FrontError", "@Website.AppCode"], "9": ["@Error", "@Website.CurtainPicture", "@Website.FrontError", "@Website.AppCode"], "10": ["@Error"]}, "13": {"7": ["@Error"], "2": ["@Error"], "13": ["@Error"], "15": ["@Error"]}, "15": {"7": ["@Error"], "15": ["@Error"]}}, "9": {"1": {"1": ["@All"], "2": ["@All"]}, "3": {"6": ["@Website.CurtainPicture", "@Website.AppCode", "@Website.AuthVerificationPicture", "@Website.FrontError", "@Website.OtherPicture", "OtherPicture", "SubmitReexamineType"], "7": ["@Website.CurtainPicture", "@Website.AppCode", "@Website.AuthVerificationPicture", "@Website.FrontError", "@Website.OtherPicture", "OtherPicture", "SubmitReexamineType"]}, "4": {"2": ["@Error", "@Website.FetchInfo"], "4": ["@Error", "@Website.FetchInfo"], "5": ["@Error"]}, "5": {"2": ["@Error"]}, "6": {"6": ["@Website.CurtainPicture", "@Website.AppCode", "@Website.AuthVerificationPicture", "@Website.OtherPicture", "@Website.FrontError", "OtherPicture", "SubmitReexamineType"], "7": ["@Website.CurtainPicture", "@Website.AppCode", "@Website.AuthVerificationPicture", "@Website.FrontError", "@Website.OtherPicture", "OtherPicture", "SubmitReexamineType"]}, "9": {"7": ["@Error"], "2": ["@Error"], "9": ["@Error"], "10": ["@Error"]}, "13": {"7": ["@Error"], "2": ["@Error", "@Website.FetchInfo"], "13": ["@Error", "@Website.FetchInfo"], "15": ["@Error"]}, "15": {"7": ["@Error"], "15": ["@Error"]}}, "10": {"1": {"1": ["@All"], "2": ["@All"]}, "3": {"6": ["@Website.CurtainPicture", "@Website.AppCode", "@Website.AuthVerificationPicture", "@Website.OtherPicture", "@Website.FrontError", "OtherPicture", "Step", "EditStatus", "FrontError", "SubmitReexamineType"], "7": ["@Website.CurtainPicture", "@Website.AppCode", "@Website.AuthVerificationPicture", "@Website.OtherPicture", "@Website.FrontError", "OtherPicture", "Step", "EditStatus", "FrontError", "SubmitReexamineType"]}, "4": {"2": ["Step", "EditStatus", "@Website.FrontError", "@Website.FetchInfo", "FrontError", "@Error"], "4": ["Step", "EditStatus", "@Website.FrontError", "@Website.FetchInfo", "FrontError", "@Error"], "5": ["@Error"]}, "5": {"2": ["@Error"]}, "6": {"6": ["CurtainPicture", "AppCode", "AuthVerificationPicture", "@Website.OtherPicture", "OtherPicture", "@Website.FrontError", "SubmitReexamineType"], "7": ["CurtainPicture", "AppCode", "AuthVerificationPicture", "@Website.FrontError", "@Website.OtherPicture", "OtherPicture", "SubmitReexamineType"]}, "9": {"7": ["Step", "EditStatus", "FrontError", "@Website.FrontError", "@Error"], "9": ["Step", "EditStatus", "FrontError", "@Website.FrontError", "@Error"], "10": ["@Error"]}, "13": {"7": ["@Error"], "13": ["@Error"], "15": ["@Error"]}, "15": {"7": ["@Error"], "15": ["@Error"]}}}