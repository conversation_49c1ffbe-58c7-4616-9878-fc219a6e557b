{"host": "0.0.0.0", "port": "4040", "mq": {"serverUrl": "amqp://edm-service:password4edm-service@*************:5672", "processorUrl": "amqp://edm-processor:password4edm-processor@*************:5672", "baseQueuesURL": "http://*************:15672/api/queues/", "Authorization": "Basic YWRtaW46d3d3MXVjbG91ZDFjbg==", "tasks": [{"type": "illegal", "topics": ["getCompanyInfo"]}, {"type": "illegal", "topics": ["getNotifyInfo"]}, {"type": "illegal", "topics": ["sendSms"]}, {"type": "illegal", "topics": ["sendEmail"]}, {"type": "illegal", "topics": ["checkSmsEmailStatus"]}, {"type": "illegal", "topics": ["checkBuEmailStatus"]}, {"type": "icp", "topics": ["CompareHenganOnlineData"]}, {"type": "icp", "topics": ["CompareHenganRedisIcpWebNo"]}]}, "db": {"hegui": {"server": "127.0.0.1", "username": "root", "password": "", "database": "<PERSON><PERSON><PERSON>", "reconnectTimeout": 2000}, "shimingzhi": {"server": "127.0.0.1", "username": "root", "password": "ucloud.cn", "database": "shiming<PERSON>", "reconnectTimeout": 2000}, "weibeian": {"server": "127.0.0.1", "username": "root", "password": "", "database": "weibeian", "reconnectTimeout": 2000}, "usec": {"server": "***************", "username": "ucloud", "password": "ucloud.cn", "database": "usec", "reconnectTimeout": 2000}, "ubeian": {"server": "***************", "username": "ucloud", "password": "ucloud.cn", "database": "ubeian", "reconnectTimeout": 2000}, "huangfan": {"server": "***************", "username": "root", "password": "", "database": "<PERSON>uang<PERSON>", "reconnectTimeout": 2000}}, "etcd": {"host": "127.0.0.1:2379"}, "faceplusplus": {"apiKey": "********************************", "apiSecret": "K8YYzt3UQzuapbJIfiJS-_fIffgcgU1N"}, "faceplusplusaction": {"detect": "https://api-cn.faceplusplus.com/facepp/v3/detect", "ocridcard": "https://api-cn.faceplusplus.com/cardpp/v1/ocridcard", "compare": "https://api-cn.faceplusplus.com/facepp/v3/compare"}, "sendmail": {"api_user": "<EMAIL>", "api_key": "kqrb65XkjdCBZqHb", "from": "<EMAIL>", "fromname": "UCloud云计算团队", "to": "<EMAIL>;<EMAIL>"}, "internalApiKeys": {"publicKey": "ucloudwilliam.qian@ucloud.cn1476072002381557", "secretKey": "81520503ae6186215158b5ec13355fb82b0eaad8"}, "sendColud": {"apiUser": "icpbei", "apiKey": "yJjAiM8LJpULcdFr", "from": "<EMAIL>", "respEmailId": true, "url": "https://api.sendcloud.net/apiv2/mail/send"}, "logPath": "/data/william/logs/weibeian", "icpUrl": "http://internal.api.ucloud.cn/?Backend=ICP&AuthKey=ae632513974f4376&Action=QueryDomainICPStatus&Domain=", "companiesUrl": "http://uauto_crm.ucloudadmin.com/api/companies/?q", "ipdbUrl": "http://************:8002/public/ipseg", "dataDDApi": "http://data_api.ucloudadmin.com", "DataDDToken": "1788e9a6-a82e-4755-b5c2-e6171aaf4159", "ucloudInternalApi": "http://internal.api.pre.ucloudadmin.com/", "ucloudExternalApi": "http://api.ucloud.cn/", "now.cn": {"DescribeICPDataAllURL": "https://api.now.cn/api/other/DescribeICPDataAll?", "AccessKeyID": "814786", "secret": "www2ucloud2cn"}, "verifyAPI": {"url": "http://internal.api.pre.ucloudadmin.com/", "epicEntPersonRelationUrl": "http://*************:6969"}, "ucloudApi": "http://api.ucloudadmin.com/"}