{"host": "0.0.0.0", "consolePort": "6060", "adminPort": "6161", "ICPId": 90000000361, "versionTimeForUserMork": 1591860536, "env": "gray", "mq": {"serverUrl": "***************************************************", "processorUrl": "***************************************************", "baseQueuesURL": "http://[2002:a40:23d:1::fd13]:15672/api/queues/", "Authorization": "Basic YWRtaW46d3d3MXVjbG91ZDFjbg==", "tasks": [{"type": "icp-delay", "topics": ["SetNoAccessDNSRedis"], "exchange_type": "x-delayed-message"}, {"type": "icp-delay", "topics": ["NoAccessNotifyAutoCreate"], "exchange_type": "x-delayed-message"}, {"type": "icp-delay", "topics": ["GetNoAccessDNS"], "exchange_type": "x-delayed-message"}, {"type": "icp", "topics": ["WebCheckForICPInfo"]}, {"type": "icp", "topics": ["FinishWebCheckForICPInfo"]}, {"type": "icp", "topics": ["AddDomainToWhiteList"]}, {"type": "icp", "topics": ["CheckBlockBatchFinish"]}, {"type": "icp", "topics": ["AuditPassAddDomainToWhiteList"]}, {"type": "icp", "topics": ["DelDomainFromWhiteList"]}, {"type": "icp-delay", "topics": ["CheckUMSSendStatus"], "exchange_type": "x-delayed-message"}, {"type": "icp-delay", "topics": ["CheckBatchSendStatus"], "exchange_type": "x-delayed-message"}, {"type": "icp", "topics": ["GetCompanyInfo"]}, {"type": "icp", "topics": ["NotifyEmailOrSms"]}, {"type": "icp", "topics": ["SyncProxyCompanyWhiteToOtherRedis"]}, {"type": "icp", "topics": ["SavePicture"]}, {"type": "icp", "topics": ["CheckOtherAPIStatus"]}, {"type": "icp", "topics": ["CancelToken"]}, {"type": "icp", "topics": ["PushWebSocketForFinshH5"]}, {"type": "icp", "topics": ["ResolveHook"]}, {"type": "icp", "topics": ["AddDomainForResolveOrder"]}, {"type": "icp", "topics": ["DelDomainForResolveOrder"]}, {"type": "icp", "topics": ["UCloudDomainReCheckFromRedis"]}, {"type": "icp", "topics": ["uploadFileToUS3"]}, {"type": "icp", "topics": ["GetResourceCompanyInfo"]}, {"type": "icp", "topics": ["GetResourceFromCompanyId"]}, {"type": "icp", "topics": ["GetResourceICPInfo"]}, {"type": "icp", "topics": ["BlockDoaminForNoAccess"]}, {"type": "icp", "topics": ["CompareHenganOnlineData"]}, {"type": "icp", "topics": ["CompareHenganRedisIcpWebNo"]}, {"type": "icp", "topics": ["CreateBillNotifyBatch"]}, {"type": "icp-delay", "topics": ["CheckBatchSendAndPushBill"], "exchange_type": "x-delayed-message"}, {"type": "icp-delay", "topics": ["NotifyUsedBill"], "exchange_type": "x-delayed-message"}, {"type": "icp-delay", "topics": ["CheckBillPayStatus"], "exchange_type": "x-delayed-message"}, {"type": "icp", "topics": ["CreateBillNotifyBatch"]}, {"type": "icp-delay", "topics": ["CheckBatchSendAndPushBill"], "exchange_type": "x-delayed-message"}, {"type": "icp-delay", "topics": ["NotifyUsedBill"], "exchange_type": "x-delayed-message"}, {"type": "icp-delay", "topics": ["CheckBillPayStatus"], "exchange_type": "x-delayed-message"}, {"type": "icp", "topics": ["AddDomainToCache"]}, {"type": "icp", "topics": ["ChangeDomainToCache"]}, {"type": "icp", "topics": ["DelD<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"]}, {"type": "icp-delay", "topics": ["SyncLocalICPDBFromHengan"], "exchange_type": "x-delayed-message"}]}, "db": {"icp": {"server": "*************", "username": "ucloud", "password": "aN^Q7r8A", "database": "icp", "reconnectTimeout": 2000}}, "sendMail": {"hegui_op": {"port": 994, "host": "hwsmtp.qiye.163.com", "secure": true, "auth": {"user": "<EMAIL>", "pass": "TuPUcMVCU6eDHG9a"}}}, "emailAndSms": {"email": "<EMAIL>", "mobile": "17613758757"}, "compass": {"target": "http://compass-backend.prj-tpl-production.svc.c1.uae/api/compass"}, "IAssumeRoleToken": {"PublicKey": "P9M0XUk78jfNFdw210BktTYxEoJ_RKV0z-sXMbZn", "PrivateKey": "X0fjB2s6LoXro81t1GawPEbnc6BQbDvucNYkqg1cuNXAfQDBGOO4RA0rbUByHgU4", "CharacterID": "ICPCharacter"}, "mongo": {"icp-config": "mongodb://admin:aDog0^9t@************:27017/icp-config?authSource=admin&readPreference=primary&directConnection=true&ssl=false", "icp": "mongodb://admin:aDog0^9t@************:27017/icp?authSource=admin&readPreference=primary&directConnection=true&ssl=false"}, "aodun": {"url": "https://*************:5151", "getToken": "/oauth/token", "getTrustDomain": "/v1.0/idcadm_api/rule/get_trust_domain", "addTrustDomain": "/v1.0/idcadm_api/rule/add_trust_domain", "delTrustDomain": "/v1.0/idcadm_api/rule/del_trust_domain", "unSealDomain": "/v1.0/idcadm_api/rule/del_filter_domain", "sealDomain": "/v1.0/idcadm_api/rule/add_filter_domain", "get_filter_domain": "/v1.0/idcadm_api/rule/get_filter_domain", "add_filter_domain": "/v1.0/idcadm_api/rule/add_filter_domain", "del_filter_domain": "/v1.0/idcadm_api/rule/del_filter_domain", "get_unaccess_data": "/v1.0/idcadm_api/domain/get_unaccess_data", "username": "apitestuser", "password": "BpdGV=zd3HVz", "client_id": "c06fc25a-c891-11ed-abe4-b0518e07ff13"}, "ufile": {"target": "http://icp.cn-bj.ufileos.com", "publicKey": "4E9UU0VhDy6mp5pBg6YhGi5V+6Ag2NjoOnUSLDUl6INJ/MdW3ZAPWQ==", "privateKey": "694581ea1d27471f0d18d970206c9a40333a0d45"}, "tmpUS3PictureDir": "tmp_picture", "redis": {"port": 6379, "host": "*************", "password": "Hegui2023.Redis", "_remark": "防止弱密码，用发现的违规域名当密码"}, "——redis": [{"port": 6379, "host": "*************", "password": "ucloud.cn"}, {"port": 6379, "host": "*************", "password": "ucloud.cn"}], "zipkinURL": "http://[2002:a40:23d:1::8dfa]:9411/api/v2/spans", "aoDunICPQuery": "http://*************:5000/query_domain_record_info?domain=", "SessionKey": "MIGfMA0GCSqGSIb3DQEBAQUAA", "internalApiKeys": {"publicKey": "ucloudwilliam.qian@ucloud.cn1476072002381557", "secretKey": "81520503ae6186215158b5ec13355fb82b0eaad8"}, "now.cn": {"DescribeICPDataAllURL": "https://api.now.cn/api/other/DescribeICPDataAll?", "AccessKeyID": "814786", "secret": "www2ucloud2cn"}, "operatorList": ["yan.meng", "di.kang", "jeff.li"], "xinnet": {"getDomainICPInfoURL": "http://beian.xinnet.com/api/provider/domain?", "getDomainICPInfoURLForCeBoos": "http://icp.ceboss.cn/api/provider/domain?", "key": "b684de6f-3b0d-11ec-b30a-e685e5eceee3", "sign": "06f6a86c-3b0e-11ec-b30a-e685e5eceee3"}, "sendColud": {"default": "<EMAIL>", "apiUser": "icpbei", "apiKey": "yJjAiM8LJpULcdFr", "from": "<EMAIL>", "respEmailId": true, "url": "https://api.sendcloud.net/apiv2/mail/send"}, "uxiaoELK": "http://uxiaogw.service.ucloud.cn/esnew/", "ucloudInternalApi": "http://internal.api.ucloud.cn", "verifyAPI": {"url": "http://internal.api.ucloud.cn/", "epicEntPersonRelationUrl": "http://*************:6969", "epicEntPersonRelationUrl1": "http://*************:6060"}, "ucloudIPSegmentApi": "http://api-gw.ucloudadmin.com/ipdb/public/ipseg", "ucloudIPSegmentTokenApi": "https://cmdb-web.ucloudadmin.com/apiKEY", "dataDDApi": "http://data_api.ucloudadmin.com", "ucloudApi": "http://api.ucloudadmin.com/", "DataDDToken": "1788e9a6-a82e-4755-b5c2-e6171aaf4159", "newicpadmin": "http://*************:6161/", "IPWhiteList": ["::1/32", "127.0.0.1/32", "************/24", "**************/32", "**************/32", "**************/32", "**************/32", "*************/32", "*************/32", "*************/32", "**************/32", "*************/32"], "RepliceTwoIP": "*************", "LongYiAPI": {"url": "http://*************:12312/Admin/Bacx", "user": "admin", "password": "ucloud@1234", "remark": "kun上业务，需要做ipv4至ipv6的转换[2002:a40:23e::**************] "}, "chat_ai_url": "http://*************:8000", "openAIConfig": {"apiKey": "********************************", "apiVersion": "2024-05-01-preview", "endpoint": "https://ucloud-openai-japan.openai.azure.com/", "deployment": "gpt-4-32k"}, "openAIGPT4oConfig": {"apiKey": "F5WK72g3IKdHxS9jRQiheCFZFzo84Hz3qul3AXz3o4tuvU4L0K2JJQQJ99AKACi0881XJ3w3AAABACOGnHfv", "apiVersion": "2024-05-01-preview", "endpoint": "https://dmlandcurosrjapaneast.openai.azure.com/", "deployment": "gpt-4o"}, "admins": ["System.Auto", "tian.li", "william.qian", "yuewen.li", "qiming.xie"], "crosssendemail": "<EMAIL>", "checkAuditFunction": {"CheckEpicEntPersonRelation": "CheckEpicEntPersonRelation", "CheckBusinessLicenseData": "CheckBusinessLicenseData", "CheckBusinessInfoWithTYC": "CheckBusinessInfoWithTYC", "GetIdenityOCRInfoFront": "GetIdenityOCRInfoFront", "GetIdenityOCRInfoBack": "GetIdenityOCRInfoBack", "CheckPersonalInfo": "CheckPersonalInfo", "CheckPersonalInfoCurtainPicture": "CheckPersonalInfoCurtainPicture", "AnalyzePhotoRequirements": "AnalyzePhotoRequirements", "MobileOrganizerNameCheck": "MobileOrganizerNameCheck", "CompareTYCWithOCR": "CompareTYCWithOCR", "CheckTycRegStatus": "CheckTycRegStatus", "CheckPersonAgeByLicenseId": "CheckPersonAgeByLicenseId", "CheckLicenseDateVaild": "CheckLicenseDateVaild"}, "logPath": "/data/logs/new", "appPath": "/data/newicp", "getDomainInfoUrl": "http://10.66.172.168:31004/", "nativeOrderMessageFiled": ["picAndWebPhoneCheck"], "cnnicConfig": {"key": "A30E86C49F71CD18", "iv": "A543FA1432CFDFA4", "url": "http://*************:8000", "ispId": 1000, "username": "ucloud"}, "getThisDayAuditCountKey": "getThisDayAuditCountId"}