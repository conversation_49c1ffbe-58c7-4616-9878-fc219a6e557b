/*
 * 公司检查批次状态
 * 对应的是t_company_info_check_batch的Status
 */
const BatchStatusList = [
    {
        VALUE: 0,
        STATUS: 'INIT',
        COMMENT: '初始化',
    },
    {
        VALUE: 1,
        STATUS: 'DOING',
        COMMENT: '查询中',
    },
    {
        VALUE: 2,
        STATUS: 'DONE',
        COMMENT: '完成',
    },
]

/*
 * 已接入同步日志状态
 * 对应的是Mongo 中的connect_sync_log的Status
 */
const ConnectSyncLogStatusList = [
    {
        VALUE: 0,
        STATUS: 'INIT',
        COMMENT: '初始化',
    },
    {
        VALUE: 1,
        STATUS: 'DOING',
        COMMENT: '执行中',
    },
    {
        VALUE: 2,
        STATUS: 'DONE',
        COMMENT: '完成',
    },
    {
        VALUE: 3,
        STATUS: 'ERROR',
        COMMENT: '异常',
    },
]

/*
 * 公司检查批次状态
 * 对应的是t_company_info_check_batch的Status
 */
const SourceTypeList = [
    {
        VALUE: 1,
        STATUS: 'ALLICPCOMPANY',
        COMMENT: '全部已备案的企业',
    },
    {
        VALUE: 3,
        STATUS: 'FILE',
        COMMENT: '上传文件',
    },
    {
        VALUE: 2,
        STATUS: 'SQL',
        COMMENT: 'SQL查询',
    },
]

/*
 * 公司检查批次状态
 * 对应的是t_company_info_check_batch的Status
 */
const CheckResultList = [
    {
        VALUE: 0,
        STATUS: 'DOING',
        COMMENT: '未检查',
    },
    {
        VALUE: 1,
        STATUS: 'SUCESS',
        COMMENT: '检查通过',
    },
    {
        VALUE: -1,
        STATUS: 'FAILD',
        COMMENT: '检查未通过',
    },
    {
        VALUE: 2,
        STATUS: 'ERROR',
        COMMENT: '检查过程出错',
    },
]

function getDict(list, keyname, valuename) {
    return list.reduce((prev, item) => {
        prev[item[keyname]] = item[valuename]
        return prev
    }, {})
}

/*
 * 目前的设计逻辑是
 * 每一个List里面有三个值
 * 1. VALUE: 一般是数值，代表其在数据库中显示的值
 * 2. STATUS: 数值对应的英文状态描述，推荐用大写
 * 3. COMMENT: 数值对应的中文描述
 * 然后根据list生成相应的字典
 *
 */
module.exports = {
    BATCHSTATUSLISTCOMMENT: getDict(BatchStatusList, 'VALUE', 'COMMENT'),
    BATCHSTATUSLISTVALUE: getDict(BatchStatusList, 'STATUS', 'VALUE'),
    CONNECTSYNCCOMMENT: getDict(ConnectSyncLogStatusList, 'VALUE', 'COMMENT'), // 接入同步状态
    CONNECTSYNCVALUE: getDict(ConnectSyncLogStatusList, 'STATUS', 'VALUE'),
    SOURCETYPELISTCOMMENT: getDict(SourceTypeList, 'VALUE', 'COMMENT'),
    SOURCETYPELISTVALUE: getDict(SourceTypeList, 'VALUE', 'STATUS'),
    CHECKRESULTLISTCOMMENT: getDict(CheckResultList, 'VALUE', 'COMMENT'),
    CHECKRESULTLISTVALUE: getDict(CheckResultList, 'STATUS', 'VALUE'),
}
