{"tags": ["编辑", "网站"], "summary": "更新网站", "description": "传入Id与网站Object,做更新操作", "body": {"required": ["CompanyId", "Website", "OrderNo"], "properties": {"Id": {"type": "integer", "description": "网站记录Id"}, "CompanyId": {"type": "integer", "description": "公司Id"}, "OrderNo": {"type": "string", "description": "订单号"}, "NeedModifyIcp": {"type": "boolean", "description": "是否变更主体"}, "Website": {"type": "object", "required": [], "properties": {"Id": {"type": "integer", "description": "网站id"}, "LicenseId": {"type": "string", "description": "网站负责人证件"}, "NullPreAppoval": {"type": "string", "description": "前置审批置空"}, "OperatingPlatformType": {"type": "array", "description": "运行平台类型  0：安卓; 1：IOS; 2: 黑莓; 3：鸿蒙; 4. Linux; 5:  其他"}, "ProvideSdkService": {"type": "integer", "description": "是否提供SDK服务(0: 不提供, 1:提供)"}, "UseThirdPartySdkServices": {"type": "integer", "description": "是否使用第三方SDK服务(0: 不使用, 1:使用)"}, "UseThirdPartySdkServiceDetails": {"type": "array", "description": "使用第三方SDK服务", "items": {"type": "object", "required": ["ThirdPartySdkVendorId", "ThirdPartySdkVendorServiceTypeId"], "properties": {"ThirdPartySdkVendorId": {"type": "integer", "description": "第三方SDK厂商ID"}, "ThirdPartySdkVendorServiceTypeId": {"type": "array", "description": "第三方SDK服务类型ID"}}}}, "AppIcon": {"type": "string", "description": "APP图标的base64编码"}, "NeedModifyWeb": {"type": "boolean", "description": "是否变更网站"}, "AppPlatformInformationList": {"type": "array", "description": "App平台信息列表", "items": {"type": "object", "required": [], "properties": {"AppPlatformTypeID": {"type": "integer", "description": "平台类型ID"}, "TraitInfo": {"type": "array", "description": "平台特性信息", "items": {"type": "object", "required": [], "properties": {"AppPackageName": {"type": "string", "description": "平台包名"}, "AppSignedMd5": {"type": "string", "description": "平台签名MD5"}, "AppPlatformPublicKey": {"type": "string", "description": "平台公钥"}}}, "AppPlatformName": {"type": "string", "description": "平台名称"}, "AppPlatformDomains": {"type": "array", "description": "平台域名列表"}}}}}}}}}, "responses": {"200": {"description": "", "schema": {"type": "object", "properties": {"Message": {"description": "报错原因", "type": "string"}, "RetCode": {"type": "integer"}}}}}}