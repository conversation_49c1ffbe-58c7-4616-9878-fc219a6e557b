{"tags": ["订单查询"], "summary": "简化版订单查询", "description": "根据公司Id和订单号查询订单信息", "body": {"required": ["CompanyId"], "properties": {"CompanyId": {"type": "integer", "description": "公司Id"}, "OrderNo": {"type": "string", "description": "订单号"}}}, "responses": {"200": {"description": "", "schema": {"type": "object", "properties": {"Message": {"description": "报错原因", "type": "string"}, "RetCode": {"type": "integer"}, "TotalCount": {"type": "integer", "description": "订单总数"}, "Order": {"type": "array", "description": "订单列表", "items": {"type": "object", "properties": {"Status": {"type": "integer", "description": "订单状态"}, "CurtainStatus": {"type": "integer", "description": "窗帘状态"}, "Error": {"type": "object", "description": "错误信息"}, "Website": {"type": "array", "description": "网站列表", "items": {"type": "object", "properties": {"Domain": {"type": "array", "description": "域名列表", "items": {"type": "object", "properties": {"CerificationPicture": {"type": "array", "description": "认证图片列表", "items": {"type": "string"}}, "Domain": {"type": "string", "description": "域名"}, "IsMain": {"type": "string", "description": "是否为主域名"}}}}, "CurtainPicture": {"type": "array", "description": "窗帘图片列表", "items": {"type": "string"}}, "Id": {"type": "integer", "description": "网站Id"}}}}}}}}}}}}