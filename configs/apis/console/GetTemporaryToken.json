{"tags": ["Token", "STS"], "summary": "获取授权Token", "description": " 生成TokenId,tokenId存数据库，实际对应着账号STS的临时用户密钥", "body": {"required": ["CompanyId", "organization_id"], "properties": {"CompanyId": {"type": "integer", "description": "公司Id"}, "OrderNo": {"type": "string", "description": "订单号"}, "LicenseType": {"type": "integer", "description": "证件类型"}, "LicenseId": {"type": "string", "description": "证件号"}, "organization_id": {"type": "integer", "description": "项目Id"}}}, "responses": {"200": {"description": "", "schema": {"type": "object", "properties": {"Message": {"description": "报错原因", "type": "string"}, "RetCode": {"type": "integer"}}}}}}