{"tags": ["实名", "配额检查"], "summary": "根据实名来确定配额情况", "description": "根据实名来确定配额情况", "body": {"required": ["CompanyId", "OrganizerLicenseId"], "properties": {"CompanyId": {"type": "integer", "description": "公司Id"}, "LicenseType": {"type": "integer", "description": "证件类型"}, "OrganizerLicenseId": {"type": "string", "description": "证件号"}}}, "responses": {"200": {"description": "", "schema": {"type": "object", "properties": {"Message": {"description": "报错原因", "type": "string"}, "RetCode": {"type": "integer"}}}}}}