{"tags": ["创建订单快递记录"], "summary": "创建订单快递记录", "description": "创建订单快递记录", "body": {"required": ["OrderNo", "Recipient", "Recipient<PERSON>ddress", "RecipientMobile"], "properties": {"OrderNo": {"type": "string", "description": "订单号"}, "Recipient": {"type": "integer", "description": "收件人"}, "RecipientAddress": {"type": "integer", "description": "收件地址"}, "RecipientMobile": {"type": "integer", "description": "收件人手机号"}}}, "responses": {"200": {"description": "", "schema": {"type": "object", "properties": {"Message": {"description": "报错原因", "type": "string"}, "RetCode": {"type": "integer"}}}}}}