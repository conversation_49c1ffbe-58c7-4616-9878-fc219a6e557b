{"tags": ["Token", "STS"], "summary": "获取已授权的Token", "description": "通过搜索条件取出已经存在的订单号", "body": {"required": ["CompanyId", "OrderNo", "LicenseType", "LicenseId"], "properties": {"CompanyId": {"type": "integer", "description": "公司Id"}, "OrderNo": {"type": "string", "description": "订单号"}, "LicenseType": {"type": "integer", "description": "证件类型"}, "LicenseId": {"type": "string", "description": "证件号"}}}, "responses": {"200": {"description": "", "schema": {"type": "object", "properties": {"Message": {"description": "报错原因", "type": "string"}, "RetCode": {"type": "integer"}}}}}}