{"tags": ["四要要素查询"], "summary": "编辑规则", "description": "如果传Id,则是编辑器如果不传，则是新建", "body": {"required": ["CompanyId"], "properties": {"Limit": {"type": "integer", "description": "总数限制"}, "Offset": {"type": "integer", "description": " 偏移"}, "Status": {"type": "integer", "description": " 公司Id"}, "EndTime": {"type": "integer", "description": " 公司Id"}, "StartTime": {"type": "integer", "description": " 公司Id"}, "CompanyId": {"type": "integer", "description": " 公司Id"}, "OrderNo": {"type": "string", "description": "订单号"}, "OrganizerName": {"type": "string", "description": "主办单位名"}, "Type": {"type": "integer", "description": "订单类型"}, "Note": {"type": "string", "description": "订单备注，方便自己辨识"}}}, "responses": {"200": {"description": "", "schema": {"type": "object", "properties": {"Message": {"description": "报错原因", "type": "string"}, "RetCode": {"type": "integer"}}}}}}