{"tags": ["日志", "核验"], "summary": "获取核验日志", "description": "获取核验日志", "body": {"required": ["UUID", "CompanyId"], "properties": {"UUID": {"type": "string", "description": "UUID"}, "CompanyId": {"type": "integer", "description": "公司Id"}}}, "responses": {"200": {"description": "", "schema": {"type": "object", "properties": {"Message": {"description": "报错原因", "type": "string"}, "RetCode": {"type": "integer"}}}}}}