{"tags": ["创建订单", "订单"], "summary": "创建备案订单", "description": "创建备案订单", "body": {"required": ["Type", "Status"], "properties": {"Type": {"type": "integer", "description": "备案类型"}, "Status": {"type": "integer", "description": "备案状态"}, "AreaId": {"type": "integer", "description": "地区"}, "OrganizerType": {"type": "integer", "description": "主办方性质"}, "OrganizerName": {"type": "string", "description": "主办单位名称"}, "OrganizerAddress": {"type": "string", "description": "主办单位证件地址"}, "OrganizerLicenseType": {"type": "integer", "description": "主办单位证件类型"}, "OrganizerLicenseId": {"type": "string", "description": "主办单位证件号"}, "OrganizerLicenseArea": {"type": "string", "description": "主办单位证件地域"}, "PICMainName": {"type": "string", "description": "主体负责人姓名"}, "PICMainQQ": {"type": "string", "description": "主体负责人QQ"}, "PICMainLicenseId": {"type": "string", "description": "主体负责人证件号"}, "PICMainLicensePicture": {"type": "array", "description": "主体负责人证件图片"}, "UnitSuperior": {"type": "string", "description": "订单号"}, "EmergencyPhone": {"type": "string", "description": "订单号"}, "PICMainOfficePhonePre": {"type": "string", "description": "订单号"}, "PICMainMobile": {"type": "string", "description": "订单号"}, "PICMainEmail": {"type": "string", "description": "订单号"}, "ICPMainNo": {"type": "string", "description": "订单号"}, "ICPMainPassword": {"type": "string", "description": "订单号"}, "PICWebLicensePicture": {"type": "array", "description": "订单号"}, "OrganizerLicensePicture": {"type": "array", "description": "订单号"}, "OrganizerResidencePermitPicture": {"type": "array", "description": "订单号"}, "LogoutReason": {"type": "string", "description": "订单号"}, "Remark": {"type": "string", "description": "订单号"}, "IP": {"type": "array", "description": "订单号"}, "PICMainLicenseType": {"type": "integer", "description": "订单号"}, "NeedModifyIcp": {"type": "boolean", "description": "是否变更主体"}, "Website": {"type": "array", "description": "网站数据", "items": {"type": "object", "required": [], "properties": {"Name": {"type": "string", "description": "网站id"}, "Id": {"type": "integer", "description": "网站id"}, "Remark": {"type": "string", "description": "网站备案号"}, "Url": {"type": "string", "description": "网站备案号"}, "InternetServiceType": {"type": "integer", "description": "服务类型"}, "ServiceType": {"type": "array", "description": "网站备案号"}, "ServiceContent": {"type": "integer", "description": "网站备案号"}, "ICPWebNo": {"type": "string", "description": "网站备案号"}, "Language": {"type": "array", "description": "网站备案号"}, "PICName": {"type": "string", "description": "网站备案号"}, "PhonePre": {"type": "string", "description": "网站备案号"}, "Phone": {"type": "string", "description": "网站备案号"}, "Mobile": {"type": "string", "description": "网站备案号"}, "Email": {"type": "string", "description": "网站备案号"}, "LicenseType": {"type": "integer", "description": "网站备案号"}, "LicenseId": {"type": "string", "description": "网站备案号"}, "LicensePicture": {"type": "array", "description": "网站备案号", "items": {"type": "string"}}, "OtherPicture": {"type": "array", "description": "网站备案号", "items": {"type": "string"}}, "QQ": {"type": "string", "description": "网站备案号"}, "Domain": {"type": "array", "description": "网站数据", "items": {"type": "object", "properties": {"Domain": {"type": "string", "description": "网站备案号"}, "CerificationPicture": {"type": "array", "description": "网站备案号"}}}}, "OperatingPlatformType": {"type": "array", "description": "运行平台类型  0：安卓; 1：IOS; 2: 黑莓; 3：鸿蒙; 4. Linux; 5:  其他"}, "ProvideSdkService": {"type": "integer", "description": "是否提供SDK服务(0: 不提供, 1:提供)"}, "UseThirdPartySdkServices": {"type": "integer", "description": "是否使用第三方SDK服务(0: 不使用, 1:使用)"}, "UseThirdPartySdkServiceDetails": {"type": "array", "description": "使用第三方SDK服务", "items": {"type": "object", "required": ["ThirdPartySdkVendorId", "ThirdPartySdkVendorServiceTypeId"], "properties": {"ThirdPartySdkVendorId": {"type": "integer", "description": "第三方SDK厂商ID"}, "ThirdPartySdkVendorServiceTypeId": {"type": "array", "description": "第三方SDK服务类型ID"}}}}, "AppIcon": {"type": "string", "description": "APP图标的base64编码"}, "NeedModifyWeb": {"type": "boolean", "description": "是否变更网站"}, "AppPlatformInformationList": {"type": "array", "description": "App平台信息列表", "items": {"type": "object", "required": ["AppPlatformTypeID", "TraitInfo", "AppPlatformName", "AppPlatformDomains"], "properties": {"AppPlatformTypeID": {"type": "integer", "description": "平台类型ID"}, "TraitInfo": {"type": "array", "description": "平台特性信息", "items": {"type": "object", "required": ["AppPackageName", "AppSignedMd5", "AppPlatformPublicKey"], "properties": {"AppPackageName": {"type": "string", "description": "平台包名"}, "AppSignedMd5": {"type": "string", "description": "平台签名MD5"}, "AppPlatformPublicKey": {"type": "string", "description": "平台公钥"}}}, "AppPlatformName": {"type": "string", "description": "平台名称"}, "AppPlatformDomains": {"type": "array", "description": "平台域名列表"}}}}}}}}}}, "responses": {"200": {"description": "", "schema": {"type": "object", "properties": {"Message": {"description": "报错原因", "type": "string"}, "RetCode": {"type": "integer"}}}}}}