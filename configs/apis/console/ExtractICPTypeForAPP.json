{"tags": ["建议", "备案类型建议"], "summary": "推荐APP的ICP类型", "description": "此API用于推荐APP的ICP类型，推荐依据为主办单位证件类型、主办单位代码、APP域名和APP名。", "body": {"required": ["APPName", "OrganizerLicenseType", "OrganizerLicenseId"], "properties": {"OrganizerLicenseType": {"type": "integer", "description": "主办单位证件类型"}, "OrganizerLicenseId": {"type": "string", "description": "主办单位代码"}, "APPName": {"type": "string", "description": "APP名"}}}, "responses": {"200": {"description": "", "schema": {"type": "object", "properties": {"Message": {"description": "报错原因", "type": "string"}, "RetCode": {"type": "integer"}}}}}}