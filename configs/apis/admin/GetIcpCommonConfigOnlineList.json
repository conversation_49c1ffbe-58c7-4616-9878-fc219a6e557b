{"tags": ["规则", "CONFIG"], "summary": "获取通用配置历史规则列表", "description": "获取通用配置历史规则列表", "body": {"required": [], "properties": {"Id": {"type": "string", "description": "记录Id"}, "Name": {"type": "string", "description": "记录Id"}, "Offset": {"type": "integer", "description": "偏移量"}, "Limit": {"type": "integer", "description": "记录长度"}}}, "responses": {"200": {"description": "", "schema": {"type": "object", "properties": {"Message": {"description": "报错原因", "type": "string"}, "RetCode": {"type": "integer"}}}}}}