{"tags": ["查询", "公司信息"], "summary": "根据邮箱获取公司信息", "description": "根据邮箱获取公司信息", "body": {"required": ["ChannelId", "UserEmail"], "properties": {"ChannelId": {"type": "integer", "description": "记录Id"}, "UserEmail": {"type": "string", "description": "用户邮箱"}}}, "responses": {"200": {"description": "", "schema": {"type": "object", "properties": {"Message": {"description": "报错原因", "type": "string"}, "RetCode": {"type": "integer"}}}}}}