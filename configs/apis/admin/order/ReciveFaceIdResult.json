{"tags": ["Token", "STS"], "summary": "接收炫彩活体回调", "description": "接收炫彩活体回调，成功或失败", "body": {"required": ["Token", "ReturnObject"], "properties": {"Token": {"type": "string", "description": "24小时有效的Token"}, "Picture": {"type": "string", "description": "最佳成像照"}, "ReturnObject": {"type": "object", "description": "返回给客户的结果", "required": ["RetCode"], "properties": {"Message": {"type": "string", "description": "报错原因"}, "RetCode": {"type": "integer", "description": "0表示成功，其他表示失败"}}}}}, "responses": {"200": {"description": "", "schema": {"type": "object", "properties": {"Message": {"description": "报错原因", "type": "string"}, "RetCode": {"type": "integer"}}}}}}