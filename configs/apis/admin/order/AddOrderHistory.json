{"tags": ["订单", "历史记录"], "summary": "保存订单的历史记录", "description": "保存订单的历史记录", "body": {"required": ["OrderNo", "Status", "Action"], "properties": {"OrderNo": {"type": "string", "description": "订单号"}, "Status": {"type": "integer", "description": "状态"}, "UserAction": {"type": "string", "description": "行为描述"}}}, "responses": {"200": {"description": "", "schema": {"type": "object", "properties": {"Message": {"description": "报错原因", "type": "string"}, "RetCode": {"type": "integer"}}}}}}