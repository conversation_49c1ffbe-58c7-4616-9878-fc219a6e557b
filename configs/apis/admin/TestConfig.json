{"tags": ["测试规则", "规则", "CONFIG"], "summary": "规则与订单的核验", "description": "传入订单号与规则Id,做检查", "body": {"required": ["Id", "OrderNo"], "properties": {"Id": {"type": "string", "description": "记录Id"}, "OrderNo": {"type": "string", "description": "测试订单"}}}, "responses": {"200": {"description": "", "schema": {"type": "object", "properties": {"Message": {"description": "报错原因", "type": "string"}, "RetCode": {"type": "integer"}}}}}}