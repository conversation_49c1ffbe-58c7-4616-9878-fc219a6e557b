{"tags": ["公司", "核验", "获取"], "summary": "获取公司信息核验的批次详情", "description": "指定查询范围，获取公司查询的批次的详细详情", "body": {"required": ["BatchId"], "properties": {"BatchId": {"type": "integer", "description": "批次Id"}, "Offset": {"type": "integer", "description": "查询偏移值"}, "Limit": {"type": "integer", "description": "查询长度"}, "Status": {"type": "array", "description": "状态"}, "CheckResult": {"type": "array", "description": "检查结果"}}}, "responses": {"200": {"description": "", "schema": {"type": "object", "properties": {"Message": {"description": "报错原因", "type": "string"}, "RetCode": {"type": "integer"}}}}}}