{"tags": ["测试通知模板"], "summary": "测试通知模板", "description": "测试通知模板", "body": {"required": ["Type", "NotifyType", "SendTo"], "properties": {"Type": {"type": "integer", "description": "通知类型"}, "NotifyType": {"type": "integer", "description": "短信/邮件"}, "SendTo": {"type": "string", "description": "收件人"}}}, "responses": {"200": {"description": "", "schema": {"type": "object", "properties": {"Message": {"description": "报错原因", "type": "string"}, "RetCode": {"type": "integer"}}}}}}