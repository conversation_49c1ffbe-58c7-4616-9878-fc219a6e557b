{"tags": ["更新", "状态", "Order"], "summary": "更新状态", "description": "更新订单，主体，已备案完成网站的状态", "body": {"required": ["Type", "Key", "Status"], "properties": {"Type": {"type": "string", "description": "类型，可以为'Order','ICP','Web'"}, "Key": {"type": "string", "description": "测试订单"}, "Status": {"type": "integer", "description": "测试订单"}}}, "responses": {"200": {"description": "", "schema": {"type": "object", "properties": {"Message": {"description": "报错原因", "type": "string"}, "RetCode": {"type": "integer"}}}}}}