{"tags": ["获取通知批次列表"], "summary": "获取通知批次列表", "description": "获取通知批次列表", "body": {"required": [], "properties": {"Id": {"type": "integer", "description": "批次Id"}, "Type": {"type": "integer", "description": "通知类型"}, "BeginTime": {"type": "integer", "description": "按时间查询，开始的时间"}, "EndTime": {"type": "integer", "description": "按时间查询，结束的时间"}, "Offset": {"type": "integer", "description": "偏移量"}, "Limit": {"type": "integer", "description": "限制"}}}, "responses": {"200": {"description": "", "schema": {"type": "object", "properties": {"Message": {"description": "报错原因", "type": "string"}, "RetCode": {"type": "integer"}}}}}}