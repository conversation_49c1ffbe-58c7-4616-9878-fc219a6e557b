{"tags": ["检查", "网站信息检查", "爬虫"], "summary": "获取网站信息检查的批次", "description": "获取网站信息检查的批次", "body": {"required": [], "properties": {"Offset": {"type": "integer", "description": "查询偏移值"}, "Limit": {"type": "integer", "description": "查询长度"}}}, "responses": {"200": {"description": "", "schema": {"type": "object", "properties": {"Message": {"description": "报错原因", "type": "string"}, "RetCode": {"type": "integer"}}}}}}