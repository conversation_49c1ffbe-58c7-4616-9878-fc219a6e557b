{"tags": ["查询", "规则", "CONFIG", "列表"], "summary": "查询规则列表", "description": "查询规则列表，默认倒续", "body": {"required": [], "properties": {"Id": {"type": "string", "description": "记录Id"}, "Offset": {"type": "integer", "description": "偏移量"}, "Limit": {"type": "integer", "description": "记录长度"}}}, "responses": {"200": {"description": "", "schema": {"type": "object", "properties": {"Message": {"description": "报错原因", "type": "string"}, "RetCode": {"type": "integer"}}}}}}