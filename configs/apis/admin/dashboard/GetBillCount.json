{"tags": ["账单", "统计"], "summary": "账单统计", "description": "账单统计", "body": {"required": [], "properties": {"Type": {"type": "string", "description": "账单维度"}, "Step": {"type": "string", "description": "账单分组"}, "BeginTime": {"type": "integer", "description": "账单开始时间"}, "EndTime": {"type": "integer", "description": "账单结束时间"}}}, "responses": {"200": {"description": "", "schema": {"type": "object", "properties": {"Message": {"description": "报错原因", "type": "string"}, "RetCode": {"type": "integer"}}}}}}