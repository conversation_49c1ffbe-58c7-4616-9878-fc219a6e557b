{"tags": ["编辑", "规则", "CONFIG"], "summary": "编辑规则", "description": "如果传Id,则是编辑器如果不传，则是新建", "body": {"required": ["Rule"], "properties": {"Id": {"type": "string", "description": "记录Id"}, "Rule": {"type": "object", "description": "配置"}, "Name": {"type": "string", "description": "配置名"}}}, "responses": {"200": {"description": "", "schema": {"type": "object", "properties": {"Message": {"description": "报错原因", "type": "string"}, "RetCode": {"type": "integer"}}}}}}