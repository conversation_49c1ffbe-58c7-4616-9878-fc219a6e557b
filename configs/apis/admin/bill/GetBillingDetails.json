{"tags": ["计费", "详情"], "summary": "获取单个账单详情", "description": "获取单个账单详情", "body": {"required": ["Id"], "properties": {"Id": {"type": "string", "description": "记录Id"}, "Offset": {"type": "integer", "description": "偏移量"}, "Limit": {"type": "integer", "description": "记录长度"}}}, "responses": {"200": {"description": "", "schema": {"type": "object", "properties": {"Message": {"description": "报错原因", "type": "string"}, "RetCode": {"type": "integer"}}}}}}