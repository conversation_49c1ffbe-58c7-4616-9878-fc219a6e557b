{"tags": ["IP", "白名单"], "summary": "获取IP白名单列表", "description": "获取IP白名单列表", "body": {"required": [], "properties": {"CompanyId": {"type": "integer", "description": "记录Id"}, "Offset": {"type": "integer", "description": "记录Id"}, "Limit": {"type": "integer", "description": "记录Id"}}}, "responses": {"200": {"description": "", "schema": {"type": "object", "properties": {"Message": {"description": "报错原因", "type": "string"}, "RetCode": {"type": "integer"}}}}}}