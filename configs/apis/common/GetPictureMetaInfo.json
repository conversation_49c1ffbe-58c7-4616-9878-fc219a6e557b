{"tags": ["图片"], "summary": "获取图片Meta信息", "description": "获取图片Meta信息，比如长宽，大小", "body": {"required": [], "properties": {"PictureName": {"type": "string", "description": "图片名"}, "PictureURL": {"type": "string", "description": "图片URL"}}}, "responses": {"200": {"description": "", "schema": {"type": "object", "properties": {"Message": {"description": "报错原因", "type": "string"}, "RetCode": {"type": "integer"}}}}}}