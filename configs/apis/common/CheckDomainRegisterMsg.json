{"tags": ["对比", "域名持有者信息"], "summary": "对比域名以及持有者信息", "description": "以域名、域名持有者(姓名/企业名称)、证件类型、证件号码为条件，通过该接口与支撑平台域名注册信息比对，返回域名持有者(姓名/企业名称)、证件类型、证件号码比对结果，若比对不一致同时返回正确信息", "body": {"required": ["DomainInfo", "CompanyId"], "properties": {"CompanyId": {"type": "integer", "description": "公司id"}, "DomainInfo": {"type": "array", "description": "域名以及持有者信息", "items": {"type": "object", "required": ["domain", "registrant", "certType", "certNo"], "properties": {"domain": {"type": "string", "description": "域名"}, "registrant": {"type": "string", "description": "注册者"}, "certType": {"description": "证件类型,可以是integer,string"}, "certNo": {"type": "string", "description": "证件号码"}}}}}}, "responses": {"200": {"description": "", "schema": {"type": "object", "properties": {"Message": {"description": "报错原因", "type": "string"}, "RetCode": {"type": "integer"}}}}}}