{"tags": ["获取", "跨境报备申请列表"], "summary": "获取跨境报备申请列表", "description": "获取跨境报备申请列表", "body": {"required": [], "properties": {"Id": {"type": "integer", "description": "申请id"}, "CompanyId": {"type": "integer", "description": "公司id"}, "CompanyName": {"type": "string", "description": "公司名称"}, "CompanyCode": {"type": "string", "description": "公司统一信用代码"}, "ManagerName": {"type": "string", "description": "经办人姓名"}, "ManagerLicenseId": {"type": "string", "description": "经办人身份证号"}, "Status": {"type": "integer", "description": "状态"}, "Offset": {"type": "integer", "description": "偏移量"}, "Limit": {"type": "integer", "description": "数量限制"}, "Source": {"type": "string", "description": "请求来源"}}}, "responses": {"200": {"description": "", "schema": {"type": "object", "properties": {"Message": {"description": "报错原因", "type": "string"}, "RetCode": {"type": "integer"}}}}}}