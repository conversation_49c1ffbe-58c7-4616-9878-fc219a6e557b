{"tags": ["修改", "跨境报备申请"], "summary": "修改跨境报备申请", "description": "修改跨境报备申请", "body": {"required": ["Id", "CompanyId"], "properties": {"Id": {"type": "integer", "description": "申请id"}, "CompanyId": {"type": "integer", "description": "公司id"}, "CompanyName": {"type": "string", "description": "公司名称"}, "CompanyCode": {"type": "string", "description": "公司统一信用代码"}, "LegalEntityName": {"type": "string", "description": "法人姓名"}, "BusinessPlace": {"type": "string", "description": "公司地址"}, "LicenseIssuingAgency": {"type": "string", "description": "发证机构"}, "PostalCode": {"type": "string", "description": "邮政编码"}, "BusinessLicenseBeginTime": {"type": "integer", "description": "营业执照开始时间"}, "BusinessLicenseEndTime": {"type": "integer", "description": "营业执照结束时间"}, "ManagerName": {"type": "string", "description": "经办人姓名"}, "ManagerLicenseId": {"type": "string", "description": "经办人身份证号"}, "ManagerAddress": {"type": "string", "description": "经办人地址"}, "ManagerPhone": {"type": "string", "description": "经办人联系电话"}, "ManagerEmail": {"type": "string", "description": "经办人邮箱"}, "BusinessLicensePic": {"type": "string", "description": "营业执照图片"}, "ManagerLicensePic": {"type": "string", "description": "经办人身份证图片"}, "PromiseLetterPic": {"type": "string", "description": "承诺书"}, "ProxyLetterPic": {"type": "string", "description": "委托书"}, "ServiceProtocolPic": {"type": "string", "description": "服务协议"}, "UCloudProxyLetterPic": {"type": "string", "description": "授优刻得委托书"}, "Status": {"type": "integer", "description": "状态"}}}, "responses": {"200": {"description": "", "schema": {"type": "object", "properties": {"Message": {"description": "报错原因", "type": "string"}, "RetCode": {"type": "integer"}}}}}}