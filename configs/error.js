/**
 * error定义
 * 注意：通用的code 在通用部分定义，避免重复定义
 * 其他 code定义 按照模块分割
 * code从上 从小到大顺序定义， 不要大小穿插，避免code定义重复
 */

module.exports = {
    //数据库
    11000: '数据库查询失败',
    11001: '数据库插入失败',
    11002: '数据库更新失败',
    11003: '服务器内部错误',
    //健康检查
    30001: '健康检查时Mongo查询失败',
    30002: '健康检查时获取Redis数据失败',
    30003: '健康检查时Redis数据读写异常',
    30004: '健康检查时Redis执行管道存在异常',

    //配置相关
    30101: '获取Redis线上配置时网络异常',
    30102: '获取Redis线上配置时结果解析异常',
    30103: '获取Redis线上配置时其它异常',
    30201: '更新配置时发生其它异常',
    30202: '新建配置请输入配置名!',
    30203: '线上配置不允许更新',
    30204: '更新通用户配置时，保存出错',
    30205: '更新通用户配置时，其它出错',
    30301: '配置生效时遇到异常错误',
    30302: '线上配置格式化失败',
    30303: '写入Mongo失败',
    30304: '写入Mongo检查失败',
    30305: '写入Redis失败',
    30401: '删除配置时异常',
    30402: '删除配置时，Mongo返回结果异常',
    30501: '配置测试时异常',
    30601: '找不到Id对应的数据',
    30602: '应用规则失败',

    // 企业四要素验证
    30701: '验证企业四要素失败',
    30702: '日志更新失败',
    30703: '验证企业四要素查询失败',
    30801: '查询日志，发生异常',
    30901: '设置禁止提交配置失败',
    30902: '查询日志，发生异常',

    //视频验证
    31001: '验证视频换脸与光线过暗时,请求出错',
    31003: '验证视频换脸与光线过暗时,存在多张脸',
    31002: '验证视频换脸与光线过暗时,出错',

    //订单状态更新
    31101: '更新状态时，未知的类型',
    31102: '更新状态时，记录数量不唯一',
    31103: '未知的更新类型',

    // 订单操作记录
    31104: '更新订单记录，其它出错',

    //审核员订单提交
    31201: '存在未通过二要素审核的用户',

    //三要素审核
    31301: '请求个人三要素接口出错',
    31302: '个人三要素接口，日志保存出错',
    31303: '个人三要素接口，其它错误',
    31304: '请求源发生错误',
    31401: '个人三要素接口，请求发生错误',
    31402: '个人三要素接口，日志保存出错',
    31403: '个人三要素接口，其它错误',
    31404: '个人三要素接口，请求源发生错误',

    31410: '自动审核出错',
    // 企业信息查询
    31501: '创建企业信息核查批次，SQL语句异常',
    31502: '创建企业信息核查批次，数据库执行出错',
    31503: '创建企业信息核查批次，文件转化失败',
    31504: '创建企业信息核查批次，文件转化后，内容检查失败',
    31505: '创建企业信息核查批次，其它出错',
    31601: '启动批次时，批次详细查询异常',
    31602: '启动批次时，批次状态异常',
    31603: '启动批次时，查询记录失败',
    31604: '启动批次时，推送查询失败',
    31605: '启动批次时，其它出错',
    31701: '结束批次时，查询是否有未完成的记录',
    31702: '结束批次时，存在未完成的记录',
    31703: '结束批次时，更新批次状态出错',
    31704: '结束批次时，其它出错',
    31801: '批次文件生成时，查询批次内的记录出错',
    31802: '批次文件生成时，此批次不存在任务记录',
    31803: '批次文件生成时，文件上传到UFile时出错',
    31804: '批次文件生成时，文件记录更新至批次详情出错',
    31805: '批次文件生成时，其它出错',
    31901: '强制清空队列时，网络出错',
    31902: '强制清空队列时，其它出错',
    32001: '展示队列详情时，其它出错',
    32002: '展示队列详情时，数据库查询出错',
    32101: '重新推送查询记录时，查询MQ时出错',
    32102: '重新推送查询记录时，队列长度不为空，不能重新推送',
    // 网站相关的备案信息查询
    31506: '生成网站备案信息查询批次，省份异常',
    31507: '生成网站备案信息查询批次，获取待查询数据异常',
    31508: '生成网站备案信息查询批次，写入待查询数据异常',
    31509: '生成网站备案信息查询批次，其它异常',
    31510: '查询批次详情时，查询数据库异常',
    31511: '查询批次详情时，其它异常',
    31512: '查询数据详情时，查询数据库异常',
    31513: '查询数据详情时，其它异常',

    //订单删除
    32111: '删除订单时，查询当前订单请求数据库出错',
    32112: '删除订单时，请求Redis出错',
    32113: '删除订单时，权限不够',
    32114: '删除订单时，此订单不存在',
    32115: '删除订单时，此状态，不允许删除',
    32116: '删除失败',

    //订单编辑
    32151: '该订单不存在',
    32152: '该订单已删除',
    32153: '该订单不支持修改',
    32154: '订单状态异常',
    32155: '订单图片压缩失败',
    32156: '图片上传失败',
    32159: '图片内容获取失败',
    32157: '图片像素不符合要求',
    32158: '图片尺寸不符合要求',

    32181: '没有发现该备案主体',
    32182: '没有发现该网站',
    32183: '该主体不允许操作，存在流程中的订单',
    32184: '该网站不允许操作，存在流程中的订单',

    //文件压缩
    32201: '文件压缩时，执行文件本地操作失败',
    32202: '文件压缩时，压缩过程出错',
    32203: '文件压缩时，上传或者创建新记录出错',
    32204: '文件压缩时，超过197kb请手工压缩',
    32205: '文件压缩时，获取最优图片出错',

    // 线上订单拉取到本地，供测试使用
    32301: '拉取线上订单时，发生异常',
    32302: '拉取线上订单时，没有发现该订单信息',
    32303: '拉取线上订单时，本地已存在该订单',
    32304: '拉取线上订单时操作只能在测试环境中使用',

    //同步域名的接入 到redis中
    32401: '同步目前的接入域名时，其它错误',
    32402: '同步目前的接入域名时，待同步的域名长度异常',
    32403: '同步目前的接入域名时，新增过程出错',
    32404: '同步目前的接入域名时，删除过程出错',

    // 审核通过订单
    32501: '审核订单通过时，执行数据库请求出错',
    32502: '审核订单通过时，此订单不存在',
    32503: '审核订单通过时，下一个状态异常',
    32504: '审核订单通过时，执行更新出错',
    32505: '审核订单通过时，未找到匹配的网站',

    // 查询域名接入情况
    32601: '查询域名是否在其它接入商时，过程出错',
    32602: '查询域名是否在其它接入商时，解析结果出错',
    32603: '查询域名是否在其它接入商时，读取缓存结果失效出错',
    32604: '查询域名是否在其它接入商时，待查询的域名不在允许的数据量范围内',
    32605: '查询域名备案时出错',
    // 同步CID
    32701: '同步CID时，未发现该订单',
    32702: '该订单不支持同步CID',
    32703: '同步CID时，未发现该备案号的备案信息',
    32704: '同步CID时，内部错误',

    //订单查询
    32800: '查找订单信息时出错',
    32801: '找不到对应的订单信息',
    32802: '订单不支持修改',
    32803: '订单已删除',

    //备案分配
    32901: '备案分配出错',
    //备案同步
    32911: '获取备案差异信息时出错',
    
    //订单加白
    33100: '域名已在大客户白名单中加白过',
    33101: '更新域名加白信息失败',
    33102: '域名未因未接入被封禁',
    33103: '域名非首次加白，请联系备案部门处理',
    33201: '删除域名加白信息失败',
    33301: '获取域名白名单列表失败',
    33401: '没有发现该记录',
    33501: '定时更新白名单状态出错',
    34001: '傲顿获取token出错',
    34002: '傲顿封禁域名出错',
    34003: '傲顿解封域名出错',
    34004: '傲顿查看域名封禁情况出错',
    34005: '傲顿增加域名封禁情况出错',
    34006: '傲顿解封域名封禁情况出错',
    34007: '傲顿查看域名封禁情况出错',

    // 幕布照替换
    35001: '查询可替换的幕布照时出错',
    35002: '更新幕布照时，找不到该订单',
    35003: '该订单状态不支持修改幕布照',
    35004: '更新幕布照时出错',

    // 代理商标记与主体&网站配额相关
    35101: '获取代理商列表时出错',
    35102: '更新代理商与配额时，其他错误',
    35103: '更新代理商与配额时，更新操作失败',
    35104: '更新代理商与配额时，不存在的代理信息，更新失败',
    35105: '创建代理商与配额时，其他错误',
    35106: '创建代理商与配额时，创建记录失败，请确定公司是否已存在',
    35107: '删除代理商与配额时，执行失败',
    35108: '删除代理商与配额时，其它出错',

    // 公司屏蔽相关
    35109: '获取公司屏蔽列表失败',
    35110: '屏蔽公司时，此公司已经被屏蔽',
    35111: '屏蔽公司时，执行过程出错',
    35112: '屏蔽公司时，其它出错',
    35113: '黑名单中不存在此记录',

    // IP白名单相关
    35114: '获取IP白名单出错',
    35115: '增加IP白名单时，其它错误',

    // 无资源有备案通知
    35201: '批次不存在',
    35202: '记录不存在',
    35203: '该批次状态不支持发送通知',
    35204: '该通知不支持重新发送',
    35205: '没有上传数据',
    35206: '没有在我司已备案的数据',
    35207: '没有需要重新获取的数据',
    35208: '该批次状态下不支持重新拉取数据',
    35209: '该批次创建未超过2小时，不允许修改状态为完成',

    // 通知类型和模板相关
    35221: '请先设定该类型相关通知模板',
    35222: '该通知类型已存在',
    35223: '创建通知类型及模板出错',
    35224: '无导入数据',
    35225: '该模板不存在',

    // 域名信息检查
    35301: '检查域名信息请求出错',
    35302: '更新检查域名信息日志出错',
    35303: '对比域名信息请求出错',
    35304: '更新对比域名信息日志出错',
    34305: '域名信息提取出错',

    //已备案未接入
    36001: '批次不存在',
    36002: '记录不存在',
    36003: '该批次状态不支持发送通知',
    36004: '该通知不支持重新发送',
    36005: '没有上传数据',
    36006: '没有在我司已备案的数据',
    36007: '没有需要重新获取的数据',
    36008: '该批次状态下不支持重新拉取数据',
    36009: '该批次创建未超过2小时，不允许修改状态为完成',
    36010: '该批次状态通知未完成，不允许修改状态为完成',
    36011: '查看已备案未接入批次通知详情出错',
    36012: '批量发送通知时出错',
    36013: '创建已备案未接入批次时出错',
    36014: '导入已备案未接入数据时出错',
    36015: '获取已备案未接入批次列表出错',
    36016: '数据异常，不支持重发',

    // 账单计费功能
    35305: '推送账单时，其它错误',
    35306: '推送账单时，Id正常推送计费，请刷新',
    35307: '推送账单时，未查到此Id',
    35308: '推送账单时，计费接口提示异常',
    35309: '生成迁移账单时，请求异常账户接口提示异常',
    35310: '生成迁移账单时，账户接口返回信息异常',
    35311: '生成迁移账单时，其它异常',
    35312: '获取账单列表时，其它异常',
    35313: '获取账单详情时，其它异常',

    // 网站编辑
    67060: '编辑网站时，在对应公司下找不到对应的订单或已删除',
    67061: '编辑网站时，发生异常',
    67062: '查询订单信息时，发生异常',
    67066: '编辑网站时，更新数据失败',
    67063: '更新网站时，插入数据失败',
    67064: '图片像素不符合要求',
    67065: '图片大小不符合要求',
    67084: '该订单不支持上传承诺书',

    // 订单清理
    67070: '该批次无法清理 请确认批次状态',

    //备案数检查
    67114: '确定可备案数时，查询出现异常',
    67115: '确定可备案数时，未有发现实名，无法备案',
    67116: '确定可备案数时，出现其它异常',
    68001: '查找订单时，发生异常',

    67043: '获取验证日志出错',
    67042: '调用获取活体用随机数字其它出错',
    67052: '调用获取活体用随机数字接口报错',
    67029: '查询实名信息出错',

    //大客户白名单
    67100: '查询大客户白名单时，接口出错',
    67101: '增加大客户域名白名单时，接口出错',
    67102: '增加大客户域名白名单时，不存在的待删除域名',
    67103: '删除大客户域名白名单时，其他出错',
    67104: '批量增加大客户域名白名单时，文件处理错误',
    67105: '增加大客户域名白名单时，处理错误',

    //大客户白名单
    67110: '非年维度的查询，时间跨度超过366天',
    67111: "步长单位非标准值，取什仅限于'DAY', 'WEEK', 'MONTH', 'YEAR'",
    67112: '开始时间不能大于结束时间',
    67113: '查询UCloud审核率时，其它出错',
    67117: '查询公司数据统计时，其它出错',
    67118: '查询管局审核率时，其它出错',
    67119: '查询API接口成本时，其它出错',

    //跨境报备
    67120: '该用户不支持申请跨境报备，请先进行企业认证',
    67121: '该营业执照已过期，请检查',
    67122: '该申请不存在',
    67123: '该申请不支持修改',
    67124: '该账号不支持新建申请',
    67125: '该申请不能删除',

    67130: '个人二要素校验失败',
    67131: '身份证OCR失败',
    67132: '营业执照OCR失败',
    67133: '获取用户认证信息失败',
    69000: '备案已存在且状态正常',
    69001: '备案迁移同步异常',
    69002: '在恒安未查到此ICP备案信息',
    69003: '信息验证时，缺失负责人证件',
    69004: '信息验证时，缺失负责人证件图片',
    69005: '获取授权码失败',
    69006: '认证过程失败',
    69007: 'token失效',

    69100: '恒安线上数据比对失败',
    69200: '上传文件信息存到redis过程中出错',

    69300: '恒安数据和redis数据比对失败',
    //驳回话术
    65701: '展示话术出错，无效的话术类型',
    65702: '展示话术出错，查询Redis异常',

    // 批量封禁
    36051: '生成批次出错',
    36052: '生成批次出错,读取文件失败',
    36053: '获取批次列表失败',
    36054: '获取批次详情失败',
    36055: '开始封禁出错',
    36056: '开始封禁出错',

    // 生成备案订单
    36057: '生成订单时，指定周期内不存在需要计费的订单',
    36058: '生成订单时，过程出错',

    // UChat相关
    36059: '获取UChat回复时其它出错',

    // 上传数据到工信部
    36060: '没有找到对应的ICP记录',

    // CheckICPCompanyInfo related errors
    67086: '使用公司名查询接口时出错',
    67085: '营业执照号与入参不一致',

    67067: '查询ICP信息出错',

    36061: '解析域名IP出错',

    67090: '查询/访问页面报错',

    67091: '该备案主体在我司系统已存在，请在对应账号已备案完成列表处进行操作',

    67092: '检查是否在我司域名出错',

    67093: '不允许更改网站域名',

    end: 'END',
}
