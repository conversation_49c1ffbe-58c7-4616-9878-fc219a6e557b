/* 新增接入审核通过 更新或者插入白名单记录
 * @Date: 2022-10-31 10:47:50
 * @LastEditors: li<PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2022-11-07 18:25:30
 * @FilePath: /newicp/mq/unConnNotify/AuditPassAddDomainToWhiteList.js
 */
'use strict'
const { parseJSON } = require('../../fns/kits')
const {
    DomainWhiteListModel,
    ProxyCompanyWhiteDomainModel,
    LogModel,
} = require('../../models')
const { getToken, unSealDomain } = require('../../fns/aodun/DomainService')
const logger = require('../../libs/logger').getLogger('mqCustomer')
const Common = require('../Common')
const _ = require('lodash')
const uuid = require('uuid/v4')
const { Op } = require('sequelize')
const moment = require('moment')

class AuditPassAddDomainToWhiteList extends Common {
    constructor(params) {
        super(params)
        this.topics = ['AuditPassAddDomainToWhiteList']
        this.type = 'icp'
        this.init()
        this.name = 'AuditPassAddDomainToWhiteList'
    }
    async handle(msg) {
        // await sleep(1000)
        let { Domains, RetryTime } = this.parseContent(msg)
        let uid = uuid()
        logger.info(`${this.name}[${uid}] - start`, msg.content.toString())
        let err = false

        //查看公司信息
        try {
            let domainList = await DomainWhiteListModel.findAll({
                where: {
                    Domain: {
                        [Op.in]: Domains,
                    },
                },
            })
            domainList = parseJSON(domainList)
            let CreateDomainList = []
            for (let domain of Domains) {
                let domainWhiteInfo = _.filter(
                    domainList,
                    (domainInfo) => domainInfo.Domain === domain
                )
                if (domainWhiteInfo?.length > 0) {
                    domainWhiteInfo = domainWhiteInfo[0]
                    let Remark = '接入成功，更新记录'
                    let Sealed = domainWhiteInfo.Sealed
                    if (domainWhiteInfo.Sealed) {
                        try {
                            let audoToken = await getToken()
                            await unSealDomain(audoToken, [domain])
                            Remark = '接入成功，更新记录，解封域名'
                            Sealed = 0
                        } catch (err) {
                            Remark = `接入成功，解封出错，${err.message}`
                        }
                    }
                    await DomainWhiteListModel.update(
                        {
                            Sealed,
                            Connected: 1,
                            Remark,
                            Operator: 'system.auto',
                        },
                        {
                            where: {
                                Domain: domain,
                            },
                        }
                    )
                } else {
                    let proxyCompanyDomain =
                        await ProxyCompanyWhiteDomainModel.findAll({
                            where: {
                                Domain: domain,
                            },
                        })
                    proxyCompanyDomain = parseJSON(proxyCompanyDomain)
                    if (proxyCompanyDomain.length === 0) {
                        CreateDomainList.push({
                            Domain: domain,
                            Connected: 1,
                            ExpiredTime: moment(moment().format('YYYY-MM-DD'))
                                .subtract(1, 'seconds')
                                .format('X'),
                            Operator: 'system.auto',
                            Remark: '接入成功，新增白名单记录',
                        })
                    }
                }
            }
            if (CreateDomainList.length > 0) {
                await DomainWhiteListModel.bulkCreate(CreateDomainList)
            }
            await LogModel.create(
                // 写入日志
                {
                    Action: 'AddDomainWhiteList',
                    Domain: Domains,
                    Operator: 'system.auto',
                    Remark: '接入成功，更新白名单记录',
                    Result: '成功',
                }
            )
        } catch (error) {
            err = true
            // 如果出错，重新推送，允许重试3次
            if (RetryTime === undefined || RetryTime < 3) {
                RetryTime += 1

                logger.error(
                    `${this.name}[${uid}] - retry error: ${error}`,
                    msg.content.toString()
                )
                await this.producer.send({
                    type: 'icp',
                    topic: 'AuditPassAddDomainToWhiteList',
                    data: {
                        Domains,
                        RetryTime,
                    },
                })
            } else {
                // 多次失败,标记原因，以及 从redis中删除该公司Id
                logger.error(
                    `${this.name}[${uid}] - 修改取消接入状态失败 failed`,
                    {
                        Message: msg.content.toString(),
                        Reason: error.message,
                    }
                )
            }
        }
        logger.info(
            `${this.name}[${uid}] - finished with ${err ? 'error' : 'success'}`
        )
    }
}

const sleep = (millseconds = 3000) => {
    return new Promise((rs) => {
        setTimeout(() => rs(), millseconds)
    })
}

module.exports = new AuditPassAddDomainToWhiteList()
