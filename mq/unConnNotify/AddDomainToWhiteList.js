/* 提交新增接入订单  添加域名到白名单列表
 * @Date: 2022-10-31 10:47:50
 * @LastEditors: li<PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2022-11-07 18:25:10
 * @FilePath: /newicp/mq/unConnNotify/AddDomainToWhiteList.js
 */
'use strict'
const { parseJSON } = require('../../fns/kits')
const {
    DomainWhiteListModel,
    ProxyCompanyWhiteDomainModel,
    LogModel,
} = require('../../models')
const logger = require('../../libs/logger').getLogger('mqCustomer')
const {
    getToken,
    unSealDomain,
    getSealStatus,
} = require('../../fns/aodun/DomainService')
const Common = require('../Common')
const uuid = require('uuid/v4')
const moment = require('moment')
const { Op } = require('sequelize')
const _ = require('lodash')

class AddDomainToWhiteList extends Common {
    constructor(params) {
        super(params)
        this.topics = ['AddDomainToWhiteList']
        this.type = 'icp'
        this.init()
        this.name = 'AddDomainToWhiteList'
    }
    async handle(msg) {
        // await sleep(1000)
        let self = this
        let { Domains, RetryTime } = this.parseContent(msg)
        let date = moment().format('X')
        let uid = uuid()
        logger.info(`${this.name}[${uid}] - start`, msg.content.toString())
        let err = false

        try {
            let ExpiredTime = moment(moment().format('YYYY-MM-DD'))
                .add(30, 'days')
                .subtract(1, 'seconds')
                .format('X')
            let domainList = await DomainWhiteListModel.findAll({
                where: {
                    Domain: {
                        [Op.in]: Domains,
                    },
                },
            })
            let logArray = []
            let CreateDomainList = []
            let UnSealDomainInfo = {}
            let unSealLogInfo = {}
            domainList = parseJSON(domainList)
            for (let domain of Domains) {
                let Remark = ''
                let domainWhiteInfo = _.filter(
                    domainList,
                    (domainInfo) => domainInfo.Domain === domain
                )
                if (domainWhiteInfo?.length > 0) {
                    domainWhiteInfo = domainWhiteInfo[0]
                    // 已经在白名单了
                    if (
                        domainWhiteInfo.Sealed &&
                        domainWhiteInfo.UnsealTimes === 0
                    ) {
                        // 被已备案未接入 封禁的 且未解封过的 自动解封
                        UnSealDomainInfo[domain] = {
                            Sealed: 0,
                            UnsealTimes: domainWhiteInfo.UnsealTimes + 1,
                            Operator: 'system.auto',
                            ExpiredTime,
                            Remark: '提交接入订单，自动解封',
                        }
                        unSealLogInfo[domain] = {
                            Domain: [domain],
                            Operator: 'system.auto',
                            Action: 'AddDomainWhiteList',
                            Remark: '提交接入订单，自动解封',
                            Result: '成功',
                        }
                    } else if (
                        domainWhiteInfo.IsDeleted ||
                        domainWhiteInfo.ExpiredTime < date
                    ) {
                        // 已删除 或者过期的 不处理
                        Remark = '域名已删除或已过期，不处理'
                    } else {
                        Remark =
                            '域名已在白名单，' +
                            (domainWhiteInfo.Sealed
                                ? '已封禁，解封过'
                                : '未过期')
                    }
                } else {
                    let proxyCompanyDomain =
                        await ProxyCompanyWhiteDomainModel.findAll({
                            where: {
                                Domain: domain,
                            },
                        })
                    proxyCompanyDomain = parseJSON(proxyCompanyDomain)
                    // 判断域名在不在大客户白名单
                    if (proxyCompanyDomain.length === 0) {
                        // 写入域名表 并设置为加白
                        CreateDomainList.push({
                            Domain: domain,
                            ExpiredTime,
                            Operator: 'system.auto',
                            Remark: '提交接入订单',
                        })
                        Remark = '新增接入订单，域名加白'
                    } else {
                        Remark = '域名在大客户白名单已加白，无需再次加白'
                    }
                }
                // 记录一些 不需要 解封等操作的 域名日志记录
                // 需要解封操作的  在解封是 根据情况 记录域名日志
                if (Remark !== '') {
                    logArray.push({
                        Domain: [domain],
                        Operator: 'system.auto',
                        Action: 'AddDomainWhiteList',
                        Remark,
                        Result: '成功',
                    })
                }
            }

            let NeedUnSealDomains = Object.keys(UnSealDomainInfo)
            let unSealRecord = []
            if (NeedUnSealDomains.length > 0) {
                // 符合解封条件的------解封
                let token = ''
                try {
                    token = await getToken()
                    await unSealDomain(token, NeedUnSealDomains)
                    // 更新解封信息
                } catch (err) {
                    // 解封出错，再次检查傲顿 接口，查看是否解封成功
                    await Promise.all(
                        _.map(NeedUnSealDomains, (domain) =>
                            getSealStatus(token, { domain })
                        )
                    )
                        .then((res) => {
                            for (let i = 0; i < res.length; i++) {
                                // 域名没被封禁-----认为是解封成功
                                UnSealDomainInfo[NeedUnSealDomains[i]].Remark =
                                    '提交接入订单，自动' + res[i].data === null
                                        ? '解封成功'
                                        : '解封失败'
                                UnSealDomainInfo[NeedUnSealDomains[i]].Sealed =
                                    res[i].data === null ? 0 : 1
                                // 重新查 那么就 记录 具体哪个成功 哪个失败了
                                unSealLogInfo[NeedUnSealDomains[i]].Result =
                                    res[i].data === null ? '成功' : '失败'
                            }
                        })
                        .catch((err) => {
                            // catch到error那就说明 整个接口出问题了，暂时记录整个接口所有域名失败
                            unSealLogInfo = {}
                            logArray.push({
                                Domain: NeedUnSealDomains,
                                Operator: 'system.auto',
                                Action: 'UpdateDomainWhiteList',
                                Remark:
                                    '解封域名失败，再次查询封禁状态，傲顿接口返回错误' +
                                    err.message,
                                Result: '失败',
                            })
                        })
                } finally {
                    for (let domain of NeedUnSealDomains) {
                        unSealRecord.push(
                            DomainWhiteListModel.update(
                                UnSealDomainInfo[domain],
                                {
                                    where: {
                                        Domain: domain,
                                    },
                                }
                            )
                        )
                        if (unSealLogInfo[domain]) {
                            logArray.push(unSealLogInfo[domain])
                        }
                    }
                }
            }
            if (unSealRecord.length > 0) {
                await Promise.all(unSealRecord)
            }
            // 未在白名单 加入域名情况记录
            if (CreateDomainList.length > 0) {
                await DomainWhiteListModel.bulkCreate(CreateDomainList)
            }
            // 日志记录
            await LogModel.bulkCreate(logArray)
        } catch (error) {
            err = true
            // 如果出错，重新推送，允许重试3次
            if (RetryTime === undefined || RetryTime < 3) {
                RetryTime += 1

                logger.error(
                    `${this.name}[${uid}] - retry error: ${error}`,
                    msg.content.toString()
                )
                await this.producer.send({
                    type: 'icp',
                    topic: 'AddDomainToWhiteList',
                    data: {
                        Domains,
                        RetryTime,
                    },
                })
            } else {
                // 多次失败,标记原因，以及 从redis中删除该公司Id
                logger.error(`${this.name}[${uid}] - 加白失败 failed`, {
                    Message: msg.content.toString(),
                    Reason: error.message,
                })
            }
        }
        logger.info(
            `${this.name}[${uid}] - finished with ${err ? 'error' : 'success'}`
        )
    }
}

const sleep = (millseconds = 3000) => {
    return new Promise((rs) => {
        setTimeout(() => rs(), millseconds)
    })
}

module.exports = new AddDomainToWhiteList()
