/* 取消接入 从域名列表删除白名单
 * @Date: 2022-10-31 10:47:50
 * @LastEditors: l<PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2022-11-07 18:25:30
 * @FilePath: /newicp/mq/unConnNotify/DelDomainFromWhiteList.js
 */
'use strict'
const { parseJSON } = require('../../fns/kits')
const { DomainWhiteListModel, LogModel } = require('../../models')
const logger = require('../../libs/logger').getLogger('mqCustomer')
const Common = require('../Common')
const _ = require('lodash')
const uuid = require('uuid/v4')
const { Op } = require('sequelize')

class DelDomainFromWhiteList extends Common {
    constructor(params) {
        super(params)
        this.topics = ['DelDomainFromWhiteList']
        this.type = 'icp'
        this.init()
        this.name = 'DelDomainFromWhiteList'
    }
    async handle(msg) {
        await sleep(1000)
        let { Domains, RetryTime } = this.parseContent(msg)
        let uid = uuid()
        logger.info(`${this.name}[${uid}] - start`, msg.content.toString())
        let err = false

        //查看公司信息
        try {
            let domainList = await DomainWhiteListModel.findAll({
                where: {
                    Domain: {
                        [Op.in]: Domains,
                    },
                },
            })
            domainList = parseJSON(domainList)
            for (let domain of Domains) {
                let domainWhiteInfo = _.filter(
                    domainList,
                    (domainInfo) => domainInfo.Domain === domain
                )
                if (domainWhiteInfo?.length > 0) {
                    domainWhiteInfo = domainWhiteInfo[0]
                    await DomainWhiteListModel.update(
                        {
                            Connected: 0,
                            IsDeleted: 1,
                            Remark: '取消接入自动删除白名单',
                            Operator: 'system.auto',
                        },
                        {
                            where: {
                                Domain: domain,
                            },
                        }
                    )
                }
            }
            await LogModel.create(
                // 写入日志
                {
                    Action: 'DeleteDomainWhiteList',
                    Domain: Domains,
                    Operator: 'system.auto',
                    Remark: '取消接入自动删除白名单，并置为未接入',
                    Result: '成功',
                }
            )
        } catch (error) {
            err = true
            // 如果出错，重新推送，允许重试3次
            if (RetryTime === undefined || RetryTime < 3) {
                RetryTime += 1

                logger.error(
                    `${this.name}[${uid}] - retry error: ${error}`,
                    msg.content.toString()
                )
                await this.producer.send({
                    type: 'icp',
                    topic: 'DelDomainFromWhiteList',
                    data: {
                        Domains,
                        RetryTime,
                    },
                })
            } else {
                // 多次失败,标记原因，以及 从redis中删除该公司Id
                logger.error(
                    `${this.name}[${uid}] - 修改取消接入状态失败 failed`,
                    {
                        Message: msg.content.toString(),
                        Reason: error.message,
                    }
                )
            }
        }
        logger.info(
            `${this.name}[${uid}] - finished with ${err ? 'error' : 'success'}`
        )
    }
}

const sleep = (millseconds = 3000) => {
    return new Promise((rs) => {
        setTimeout(() => rs(), millseconds)
    })
}

module.exports = new DelDomainFromWhiteList()
