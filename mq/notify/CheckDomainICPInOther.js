/*
 * @Date: 2023-08-14 18:08:34
 * @LastEditors: l<PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2023-08-15 10:51:17
 * @FilePath: /newicp/mq/notify/CheckDomainICPInOther.js
 */
'use strict'

/**
 * 通过公司Id查询公司信息以及主账号联系人信息
 */
const Common = require('../Common')
const CheckDomainInOtherISP = require('../../methods/admin/Common/CheckDomainInOtherISP')
const { Op } = require('sequelize')
const { UninsertBatchRecordModel } = require('../../models')
const logger = require('../../libs/logger').getLogger('mqCustomer')
const _ = require('lodash')
const uuid = require('uuid/v4')
/*
 * @class
 * @extent Common
 */
class CheckDomainICPInOther extends Common {
    constructor(params) {
        super(params)
        this.topics = ['CheckDomainICPInOther']
        this.type = 'icp'
        this.init()
        this.name = 'CheckDomainICPInOther'
    }
    async handle(msg) {
        await sleep(1000)
        let self = this
        let redis = this.redis.get()
        let { BatchId, Domain, RetryTime = 3 } = this.parseContent(msg)
        let uid = uuid()
        logger.info(`${this.name}[${uid}] - start`, msg.content.toString())

        //查看公司信息
        try {
            let nextDomain = null
            let domainICP = false
            // 重新推送域名检查
            let params = {
                Domain: [Domain],
            }
            let method = new CheckDomainInOtherISP((RetCode, data) => {
                if (RetCode === 0) {
                    if (data.domainInfo[Domain]?.IsICPInOtherISP) {
                        domainICP = true
                    }
                } else {
                    // 请求出错 重试 多次出错则打印错误日志
                    RetryTime--
                    if (RetryTime > 0) {
                        nextDomain = Domain
                    } else {
                        logger.error(`${self.name}[${uid}] - 请求出错, RetryTime: ${RetryTime}`)
                    }
                }
            })
            await method.exec(params)

            if (domainICP) {
                await redis.sadd(`${BatchId}_await_update_icpinother`, Domain)
            }
            if (nextDomain === null) {
                nextDomain = await redis.spop(
                    `${BatchId}_NoAccess_Need_ReCheck_Domains`
                )
            }
            if (nextDomain) {
                 // 继续推送mq
                let mqOptions = {
                    type: 'icp',
                    topic: 'CheckDomainICPInOther',
                    data: {
                        BatchId,
                        Domain: nextDomain,
                        RetryTime: nextDomain === Domain ? RetryTime : 3
                    },
                }
                await this.producer.send(mqOptions)
            } else {
                await redis.sadd(`${BatchId}_lock_all_search_finished`,'DomainICPSearch')
                let Domains = await redis.
                    smembers(`${BatchId}_await_update_icpinother`)
                if (Domains.length > 0) {
                    await UninsertBatchRecordModel.update(
                        { RegisterStatus: 2 },
                        {
                            where: {
                                BatchId: BatchId,
                                Domain: {
                                    [Op.in]: Domains
                                },
                            },
                        }
                    )
                    await redis.expire(`${BatchId}_await_update_icpinother`, 300)
                }
                // 推送延迟mq
                let mqOptions = {
                    type: 'icp-delay',
                    topic: 'CheckAllParseIsFinished',
                    data: {
                        BatchId,
                    },
                    opts: {
                        headers: {
                            'x-delay': 5 * 60 * 1000,
                        }, // 延迟5分钟检查时间
                    },
                }
                await this.producer.send(mqOptions)
            }
        } catch (err) {
            logger.error(
                `${self.name}[${uid}] - 查询域名备案 error,错误信息：${err}`
            )
        }
    }
}

const sleep = (millseconds = 3000) => {
    return new Promise((rs) => {
        setTimeout(() => rs(), millseconds)
    })
}

module.exports = new CheckDomainICPInOther()
