'use strict'
const Common = require('../Common')
const {
    model: UninsertBatchRecordModel,
} = require('../../models/t_no_access_record')
const {
    model: UninsertBatchModel,
    BatchStatusEnum,
} = require('../../models/t_no_access_batch')
const DomainWriteListService = require('../../fns/domain_white_list/DomainWhiteListService')
const { getTableModel, parseJSON } = require('../../fns/kits')
const axios = require('../../libs/axiosApi')
const logger = require('../../libs/logger').getLogger('mqCustomer')
const uuid = require('uuid/v4')
const { Op, BaseError } = require('sequelize')
const _ = require('lodash')

/**
 * 过滤缓存中的已备案域名
 * 推送 其他未接入域名的备案号查询 以及二次确认是否真的未接入
 * 将当前需查询的所有IP推送到 公司信息查询中
 */
class GetNoAccessDomainStatus extends Common {
    constructor(params) {
        super(params)
        this.topics = ['GetNoAccessDomainStatus']
        this.type = 'icp'
        this.init()
        this.name = 'GetNoAccessDomainStatus'
    }
    async handle(msg) {
        await sleep(1000)
        let self = this
        const { BatchId } = this.parseContent(msg)
        let redis = this.redis.get()
        let producer = this.producer
        let uid = uuid()
        logger.info(`${this.name}[${uid}] - start`, msg.content.toString())
        //查看公司信息
        try {
            let NoAccessDataDomainIP = await redis.get(
                `${BatchId}_NoAccess_DomainIP_in_ucloud_ipSegment`
            )
            NoAccessDataDomainIP = JSON.parse(NoAccessDataDomainIP)
            let needSearchDomainInfos = []
            let needSearchDomains = []
            let needParseIPs = []
            const effectiveDomain =
                await DomainWriteListService.getEffectiveDomain()
            let batchUpdate1 = []
            let batchUpdate2 = []
            for (let i = 0; i < NoAccessDataDomainIP.length; i++) {
                try {
                    const domain = NoAccessDataDomainIP[i].Domain
                    if (!domain) {
                        continue
                    }
                    //判断在我司是否有接入register_status: 2，去redis表中查询
                    let DomainIcpInOtherMap = await redis.hmget(
                        `domain_icp_in_other_map`,
                        domain
                    )
                    let DomainIcpInUcloudMap = await redis.hmget(
                        `domain_icp_in_ucloud_map`,
                        domain
                    )
                    //判断domain是否在白名单,register_status: 1
                    if (effectiveDomain.includes(domain)) {
                        batchUpdate1.push(domain)
                    } else if (
                        DomainIcpInUcloudMap[0] ||
                        DomainIcpInOtherMap[0]
                    ) {
                        batchUpdate2.push(domain)
                    } else {
                        needSearchDomainInfos.push(NoAccessDataDomainIP[i])
                        needSearchDomains.push(domain)
                    }
                } catch (e) {
                    logger.error(`域名过滤失败-${e}`)
                }
            }
            //批量更新 备案在我司
            await UninsertBatchRecordModel.update(
                { RegisterStatus: 1 },
                {
                    where: {
                        BatchId: BatchId,
                        Domain: {
                            [Op.in]: batchUpdate1,
                        },
                    },
                }
            )
            //批量更新 备案在我司接入商
            await UninsertBatchRecordModel.update(
                { RegisterStatus: 2 },
                {
                    where: {
                        BatchId: BatchId,
                        Domain: {
                            [Op.in]: batchUpdate2,
                        },
                    },
                }
            )
            //ip去重去空取唯一值
            needParseIPs = getUniqueIPList(needSearchDomainInfos)
            // 记录需要重新查询的域名 二次请求 接入商 查看域名是否备案， 并将此时需要查询备案号的域名写入redis方便后续查询
            await Promise.all([
                redis.sadd(
                    `${BatchId}_NoAccess_Need_ReCheck_Domains`,
                    needSearchDomains
                ),
                redis.sadd(`${BatchId}_NoAccessDataDomains`, needSearchDomains),
                redis.sadd(`${BatchId}_NoAccess_NeedParseIPs`, needParseIPs),
                redis.expire(`${BatchId}_NoAccess_DomainIP_in_ucloud_ipSegment`, 300)
            ])
            //过滤白名单，保存白名单后的数据总数

            logger.info('需要解析的IP数量：', needParseIPs.length)
            if (needParseIPs && needParseIPs.length > 0) {
                // 取出一部分 开启查询
                let [needReCheckDomains, needSearchWebNos, needFirstParseIPs] =
                    await Promise.all([
                        redis.spop(
                            `${BatchId}_NoAccess_Need_ReCheck_Domains`,
                            7
                        ),
                        redis.spop(`${BatchId}_NoAccessDataDomains`, 7),
                        redis.spop(`${BatchId}_NoAccess_NeedParseIPs`, 7),
                    ])
                //解析ip确定通知人公司id，若redis:unconn_filter_companyId中已有公司id则不通知.
                for (let j = 0; j < needFirstParseIPs.length; j++) {
                    await producer.send({
                        type: 'icp',
                        topic: 'GetNoAccessCompanyInfoByIp',
                        data: {
                            BatchId,
                            IP: needFirstParseIPs[j],
                        },
                    })
                }

                for (let i = 0; i < needSearchWebNos.length; i++) {
                    // 查询网站备案号
                    await producer.send({
                        type: 'icp',
                        topic: 'GetNoAccessDomainWebNo',
                        data: {
                            BatchId,
                            Domain: needSearchWebNos[i],
                        },
                    })
                }
                for (let k = 0; k < needReCheckDomains.length; k++) {
                    // 再次推送检查是否在供应商备案 因为供应商不会实时告诉我们是否备案
                    await producer.send({
                        type: 'icp',
                        topic: 'CheckDomainICPInOther',
                        data: {
                            BatchId,
                            Domain: needReCheckDomains[k],
                        },
                    })
                }
            } else {
                //过滤完后没有解析信息，批次状态改为已完成
                await UninsertBatchModel.update(
                    { Status: BatchStatusEnum.Finish },
                    {
                        where: {
                            Id: BatchId,
                        },
                    }
                )
            }
        } catch (error) {
            logger.error(
                `${self.name}[${uid}]-已备案未接入域名状态过滤时出错：` + error
            )
        }
        logger.info(`${self.name}[${uid}] - finished`)
    }
}
/*
 * @params {Array}
 * @return {Array} 去重后的IP数据，并去空
 */
const getUniqueIPList = (records) => {
    records = records.map((item) => item.IP)
    records = _.uniq(records)
    return records
}
const sleep = (millseconds = 3000) => {
    return new Promise((rs) => {
        setTimeout(() => rs(), millseconds)
    })
}
module.exports = new GetNoAccessDomainStatus()
