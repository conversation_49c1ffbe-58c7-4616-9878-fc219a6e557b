/*
 * @Date: 2023-08-14 18:08:34
 * @LastEditors: l<PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2023-08-15 10:51:17
 * @FilePath: /newicp/mq/notify/CheckDomainICPInOther.js
 */
'use strict'

/**
 * 通过公司Id查询公司信息以及主账号联系人信息
 */
const Common = require('../Common')
const { UninsertBatchModel, UninsertBatchStatusEnum } = require('../../models')
const logger = require('../../libs/logger').getLogger('mqCustomer')
const _ = require('lodash')
const uuid = require('uuid/v4')
/*
 * @class
 * @extent Common
 */
class CheckAllParseIsFinished extends Common {
    constructor(params) {
        super(params)
        this.topics = ['CheckAllParseIsFinished']
        this.type = 'icp-delay'
        this.init()
        this.name = 'CheckAllParseIsFinished'
    }
    async handle(msg) {
        await sleep(1000)
        let self = this
        let redis = this.redis.get()
        let { BatchId } = this.parseContent(msg)
        let producer = this.producer
        let uid = uuid()
        logger.info(`${this.name}[${uid}] - start`, msg.content.toString())

        //查看公司信息
        try {
            let batchInfo = await UninsertBatchModel.findOne({
                where: {
                    Id: BatchId,
                    Status: UninsertBatchStatusEnum.Pull,
                },
            })

            if (batchInfo) {
                let finishedInfo = await redis.smembers(
                    `${BatchId}_lock_all_search_finished`
                )

                if (
                    _.indexOf(finishedInfo, 'WebNoSearch') !== -1 &&
                    _.indexOf(finishedInfo, 'DomainICPSearch') !== -1 &&
                    _.indexOf(finishedInfo, 'IPCompanyInfo') !== -1
                ) {
                    // 所有的查询完成 更新批次状态为已解析
                    await UninsertBatchModel.update(
                        { Status: UninsertBatchStatusEnum.Parsed },
                        {
                            where: {
                                Id: BatchId,
                            },
                        }
                    )
                    await redis.expire(
                        `${BatchId}_lock_all_search_finished`,
                        300
                    )
                } else {
                    // 延迟一会儿再检查 可能有的还没有处理完
                    await producer.send({
                        type: 'icp-delay',
                        topic: 'CheckAllParseIsFinished',
                        data: {
                            BatchId,
                        },
                        opts: {
                            headers: {
                                'x-delay': 5 * 60 * 1000,
                            }, // 延迟5分钟检查时间
                        },
                    })
                }
            }
        } catch (err) {
            logger.error(
                `${self.name}[${uid}] - 查询域名备案 error,错误信息：${err}`
            )
        }
    }
}

const sleep = (millseconds = 3000) => {
    return new Promise((rs) => {
        setTimeout(() => rs(), millseconds)
    })
}

module.exports = new CheckAllParseIsFinished()
