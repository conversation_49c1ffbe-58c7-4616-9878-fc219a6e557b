'use strict'

const Common = require('../Common')
const {
    model: NotifyModel,
    SendStatusEnum,
    NotifyTypeEnum,
} = require('../../models/t_notify')
const { model: RecordModel } = require('../../models/t_notify_companyInfo')
const { Op } = require('sequelize')
const logger = require('../../libs/logger').getLogger('mqCustomer')
const uuid = require('uuid/v4')
const { parseJSON, convertUS3FileToBase64 } = require('../../fns/kits')
const { signature: us3Signature } = require('../../fns/uflieFuns')
const moment = require('moment')
const { sendMail, sendSms } = require('../../fns/umsMsgSend')
const {
    getMailContent,
    getSmsContent,
} = require('../../fns/notify/GetNotifyContent')
/**
 * 通过公司Id查询资源信息，
 * 并将无资源的公司Id插入至该批次的记录中
 */
class NotifyEmailOrSms extends Common {
    constructor(params) {
        super(params)
        this.topics = ['NotifyEmailOrSms']
        this.type = 'icp'
        this.name = 'NotifyEmailOrSms'
        this.init()
    }
    async handle(msg) {
        await sleep(1000)
        // Id: 通知Id, Contact: 联系人， CompanyNotifyId: 公司信息Id，
        // Type: 短信/邮件，RetryTime:重试次数，day:通知用户的截止时间
        // NotifyType,通知类型 用来寻找通知模板 例如 域名过期通知 或者域名信息和备案不一致通知
        // NotifyVersion, 通知模板版本，用来定位当前使用哪一个版本的通知模板
        // AttachFiles 自定义的邮件附件，可兼容模板附件一起发送[{AttachName: 'xxx', AttachContent: 'xxx}]
        let {
            Id,
            Contact,
            CompanyNotifyId,
            Type,
            NotifyType,
            NotifyVersion,
            AttachFiles,
            RetryTime,
        } = this.parseContent(msg)
        let uid = uuid()
        logger.info(`${this.name}[${uid}] - start`, msg.content.toString())
        //查看有效的备案信息
        try {
            let notifyUpdate = await NotifyModel.update(
                { SendStatus: SendStatusEnum.Trytosending },
                {
                    where: {
                        Id,
                        SendStatus: {
                            [Op.in]: [
                                SendStatusEnum.New,
                                SendStatusEnum.Sendfailed,
                                SendStatusEnum.Recivefailed,
                            ],
                        },
                    },
                }
            )

            notifyUpdate = parseJSON(notifyUpdate)

            if (notifyUpdate.changedRows < 1) return

            let icpInfo = await RecordModel.findOne({
                attributes: ['ICPInfo', 'MainMail', 'Manager'],
                where: {
                    Id: CompanyNotifyId,
                },
            })

            icpInfo = parseJSON(icpInfo)
            // 发送消息
            try {
                let taskId
                if (Type === NotifyTypeEnum.Email) {
                    if (AttachFiles && AttachFiles?.length > 0) {
                        for (let file of AttachFiles) {
                            if (!file.AttachContent) {
                                throw new Error('file.AttachContent不能为空')
                            }
                            let tmpUrl =
                                global.CONFIG.ufile.target +
                                '/' +
                                file.AttachContent +
                                us3Signature(
                                    file.AttachContent,
                                    Math.floor(Date.now() / 1000) + 600
                                )
                            file.AttachContent = await convertUS3FileToBase64(
                                tmpUrl
                            )
                        }
                    }
                    let isAdmin = false
                    if (
                        icpInfo.MainMail === Contact ||
                        icpInfo.Manager === Contact
                    ) {
                        isAdmin = true
                    }
                    taskId = await this.sendEmailMsg(
                        icpInfo.ICPInfo,
                        NotifyType,
                        Contact,
                        isAdmin,
                        AttachFiles
                    )
                } else {
                    taskId = await this.SendSmsMsg(
                        icpInfo.MainMail,
                        NotifyType,
                        Contact
                    )
                }
                //更新发送状态为发送中, 更新任务id，以及重试次数
                await NotifyModel.update(
                    {
                        SendStatus: SendStatusEnum.Sending,
                        TaskId: taskId,
                        RetryTime: RetryTime + 1,
                    },
                    {
                        where: {
                            Id,
                            SendStatus: SendStatusEnum.Trytosending,
                        },
                    }
                )
                // 发送状态检查
                await this.producer.send({
                    type: 'icp-delay',
                    topic: 'CheckUMSSendStatus',
                    data: {
                        uidreadom: uuid(),
                        send_time: moment().format('X'),
                        Id,
                        Type,
                        task_id: taskId,
                    },
                    opts: {
                        headers: { 'x-delay': 60 * 1000 }, // 延迟1分钟检查时间
                    },
                })
            } catch (err) {
                // 出现错误 记录日志 更新状态为发送失败
                logger.error(
                    `${this.name}[${uid}] - 发送出现失败，error：${err}`
                )
                await NotifyModel.update(
                    {
                        SendStatus: SendStatusEnum.Sendfailed,
                        RetryTime: RetryTime + 1,
                    },
                    {
                        where: {
                            Id,
                            SendStatus: SendStatusEnum.Trytosending,
                        },
                    }
                )
            }
        } catch (err) {
            logger.error(`${this.name}[${uid}] - error：${err}`)
        }
        logger.info(`${this.name}[${uid}] - finished`)
    }
    /**
     * 根据备案信息 和通知人信息发送通知
     * @param {*} ICPInfo 备案信息
     * @param {number} Type 具体那种类型的通知 例如： 域名过期 或者 备案和实名不一致
     * @param {*} email 收件人
     * @param {Array} Attach[{FileName: xxx, FileContent: xxx}] // FileContent 使用base64编码的文件
     * @param {*} isAdmin 主账号接收账号下所有的通知
     */
    async sendEmailMsg(ICPInfo, Type, email, isAdmin, Attach = []) {
        //获取通知内容
        const { Content, Title, AttachFiles } = await getMailContent(
            ICPInfo,
            Type,
            isAdmin,
            email
        )
        // 如果有携带的自定义附件
        console.log(Content, 'ContentMail')
        console.log(Title, 'Title')
        //发送通知
        const taskId = await sendMail({
            email,
            content: Content,
            title: Title,
            attachFiles: [...AttachFiles, ...Attach],
        })
        return taskId
    }

    /**
     * 发送无资源有备案短信通知
     * @param {*} MainMail 主账号邮箱
     * @param {*} mobile 手机号
     * @param {number} Type 具体那种类型的通知 例如： 域名过期 或者 备案和实名不一致
     */
    async SendSmsMsg(MainMail, Type, mobile) {
        let content = await getSmsContent(Type, MainMail)
        console.log(content, 'ContentSms')
        const taskId = await sendSms({
            mobile,
            content,
        })
        return taskId
    }
}
const sleep = (millseconds = 3000) => {
    return new Promise((rs) => {
        setTimeout(() => rs(), millseconds)
    })
}

module.exports = new NotifyEmailOrSms()
