'use strict'

/**
 * 通过公司Id查询公司信息以及主账号联系人信息
 */
const Common = require('../Common')
const {
    model: NotifyBatchModel,
    BatchStatusEnum: BatchStatusEnum,
    TypeEnum: BatchTypeEnum,
} = require('../../models/t_notify_batch')
const {
    model: NotifyCompanyInfoModel,
    RecordStatusEnum: SendStatusEnum,
} = require('../../models/t_notify_companyInfo')
const { model: NotifyModel, NotifyTypeEnum } = require('../../models/t_notify')
const axios = require('../../libs/axiosApi')
const logger = require('../../libs/logger').getLogger('mqCustomer')
const _ = require('lodash')
const uuid = require('uuid/v4')
/*
 * @class
 * @extent Common
 */
class GetCompanyInfo extends Common {
    constructor(params) {
        super(params)
        this.topics = ['GetCompanyInfo']
        this.type = 'icp'
        this.init()
        this.name = 'GetCompanyInfo'
    }
    async handle(msg) {
        await sleep(1000)
        let self = this
        let redis = this.redis.get()
        let {
            CompanyId,
            CompanyNotifyId,
            BatchId,
            RetryTime,
            NotNotifyMainNo, // 是否通知主账号
        } = this.parseContent(msg)
        let uid = uuid()
        logger.info(`${this.name}[${uid}] - start`, msg.content.toString())
        let result

        //查看公司信息
        try {
            let options = {
                method: 'POST',
                url: global.CONFIG.dataDDApi,
                headers: {
                    'Content-Type': 'application/json',
                },
                data: {
                    Action: 'Account.GetCompanyInfo',
                    Token: global.CONFIG.DataDDToken,
                    CompanyId: parseInt(CompanyId),
                },
                json: true,
            }
            result = await axios(options)
            if (result.status !== 200 || result.data.RetCode !== 0) {
                let err = new Error(
                    `账户查询公司信息失败，公司Id: ${CompanyId},错误信息：${
                        result.status !== 200
                            ? result.status
                            : result.data.Message
                    }`
                )
                throw err
            }
        } catch (error) {
            // 如果出错，重新推送，允许重试3次
            if (RetryTime === undefined || RetryTime < 3) {
                RetryTime += 1

                logger.error(
                    `${self.name}[${uid}] - retry error: ${error}`,
                    msg.content.toString()
                )
                return await self.producer.send({
                    type: 'icp',
                    topic: 'GetCompanyInfo',
                    data: {
                        CompanyId,
                        CompanyNotifyId,
                        BatchId,
                        RetryTime,
                    },
                })
            } else {
                // 多次失败,标记原因，以及 从redis中删除该公司Id
                logger.error(`${self.name}[${uid}] - 查看公司信息 failed`, {
                    CompanyNotifyId,
                    CompanyId,
                    Reason: '获取公司信息失败',
                })
                await NotifyCompanyInfoModel.update(
                    {
                        IsError: true,
                    },
                    {
                        where: {
                            Id: CompanyNotifyId,
                        },
                    }
                )
            }
        }

        //若获取到公司信息，更新公司信息
        result = result.data.Data
        let notifyInfo = _.find(result?.main_contacts, (contact) => {
            return (
                contact.admin === 1 &&
                !/^spt.*@ucloud\.cn$/.test(contact.user_email)
            )
        })
        if (result) {
            try {
                await NotifyCompanyInfoModel.update(
                    {
                        CompanyName: result.company_name,
                        Manager: result.manager,
                        BU: result.bu ? result.bu : '',
                        Level: result.vip_level,
                        MainMail: notifyInfo.user_email,
                    },
                    {
                        where: {
                            Id: CompanyNotifyId,
                        },
                    }
                )
            } catch (err) {
                logger.error(
                    `${self.name}[${uid}] - 更新公司信息 error,错误信息：${err}`
                )
            }

            // 将主联系人加入通知列表中
            try {
                //所有公司id已查询完毕，更新批次状态,到解析中
                if (!NotNotifyMainNo) {
                    await NotifyModel.bulkCreate(
                        [
                            {
                                CompanyNotifyId,
                                Contact: notifyInfo.user_email,
                                Type: NotifyTypeEnum.Email,
                            },
                            {
                                CompanyNotifyId,
                                Contact: notifyInfo.user_phone.replace(
                                    '(86)',
                                    ''
                                ),
                                Type: NotifyTypeEnum.Phone,
                            },
                        ],
                        { ignoreDuplicates: true }
                    )
                }
            } catch (err) {
                logger.error(
                    `${self.name}[${uid}] - 公司主联系人信息加入通知出现 error,错误信息：${err}`
                )
            }
        }

        //处理记录状态
        try {
            // 从记录中删除 已经查询过的公司信息
            await redis.srem(`${BatchId}_notify_companyInfo`, CompanyId)

            let companyCount = await redis.scard(
                `${BatchId}_notify_companyInfo`
            )

            // 判断所有的记录都已经处理完毕，修改批次状态
            if (companyCount === 0) {
                // 使用redis锁 保证只有一个进程 处理批次状态
                let lock = await redis.set(
                    `${BatchId}_lock_search_companyinfo`,
                    1,
                    'EX',
                    5
                )
                if (lock) {
                    await NotifyBatchModel.update(
                        { Status: BatchStatusEnum.Parsed },
                        {
                            where: {
                                Id: BatchId,
                            },
                        }
                    )
                }
            }
        } catch (err) {
            logger.error(
                `${self.name}[${uid}] - 删除已查询记录或者处理批次状态出错 error,错误信息：${err}`
            )
        }
        logger.info(`${self.name}[${uid}] - finished`)
    }
}

const sleep = (millseconds = 3000) => {
    return new Promise((rs) => {
        setTimeout(() => rs(), millseconds)
    })
}

module.exports = new GetCompanyInfo()
