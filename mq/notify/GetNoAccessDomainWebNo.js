/*
 * @Date: 2023-04-25 14:21:07
 * @LastEditors: l<PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2023-08-17 15:40:15
 * @FilePath: /newicp/mq/notify/GetNoAccessDomainWebNo.js
 */
'use strict'

const Common = require('../Common')
const {
    model: UninsertBatchRecordModel,
} = require('../../models/t_no_access_record')
const {
    model: UninsertBatchModel,
    BatchStatusEnum,
} = require('../../models/t_no_access_batch')
const { Op } = require('sequelize')
const logger = require('../../libs/logger').getLogger('mqCustomer')
const uuid = require('uuid/v4')
const GetDomainPublicICP = require('../../methods/common/GetDomainPublicICP')
const { parseJSON } = require('../../fns/kits')
const _ = require('lodash')

/**
 * 通过域名查看备案信息 并存入
 */
class GetNoAccessDomainWebNo extends Common {
    constructor(params) {
        super(params)
        this.topics = ['GetNoAccessDomainWebNo']
        this.type = 'icp'
        this.name = 'GetNoAccessDomainWebNo'
        this.init()
    }
    async handle(msg) {
        await sleep(1000)
        let { BatchId, Domain, RetryTime = 3 } = this.parseContent(msg)
        let redis = this.redis.get()
        let uid = uuid()
        logger.info(`${this.name}[${uid}] - start`, BatchId)
        //查看有效的备案信息
        try {
            let records = await UninsertBatchRecordModel.findAll({
                attributes: ['Id'],
                where: {
                    BatchId,
                    Domain,
                },
            })
            records = parseJSON(records)
            let ids = records.map((record) => record.Id).toString()

            // 更新通知进度
            let icpInfo
            let method = new GetDomainPublicICP((RetCode, data) => {
                icpInfo = {
                    RetCode,
                    ...data,
                }
            })
            await method.exec({ Domain })
            await UninsertBatchRecordModel.update(
                { ICPWebNo: icpInfo.ICPInfos[0]?.webSiteNum || '' },
                {
                    where: {
                        Id: {
                            [Op.in]: ids.split(','),
                        },
                    },
                }
            )

            let domain = await redis.spop(`${BatchId}_NoAccessDataDomains`)

            if (domain) {
                // 继续推送检查
                await this.producer.send({
                    type: 'icp',
                    topic: 'GetNoAccessDomainWebNo',
                    data: {
                        BatchId,
                        Domain: domain,
                    },
                })
            } else {
                await redis.sadd(`${BatchId}_lock_all_search_finished`,'WebNoSearch')

                let mqOptions = {
                    type: 'icp-delay',
                    topic: 'CheckAllParseIsFinished',
                    data: {
                        BatchId,
                    },
                    opts: {
                        headers: {
                            'x-delay': 5 * 60 * 1000,
                        }, // 延迟5分钟检查时间
                    },
                }
                await this.producer.send(mqOptions)
            }
        } catch (err) {
            logger.error(`${this.name}[${uid}] - 查询备案信息error：${err}`)
        }
        logger.info(`${this.name}[${uid}] - finished`)
    }
}
const sleep = (millseconds = 3000) => {
    return new Promise((rs) => {
        setTimeout(() => rs(), millseconds)
    })
}

module.exports = new GetNoAccessDomainWebNo()
