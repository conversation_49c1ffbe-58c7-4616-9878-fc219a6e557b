'use strict'
const { Resolver } = require('dns')
const resolver = new Resolver()
const Common = require('../Common')
const {
    model: UninsertBatchRecordModel,
} = require('../../models/t_no_access_record')
const { Op } = require('sequelize')
const logger = require('../../libs/logger').getLogger('mqCustomer')
const uuid = require('uuid/v4')
const { parseJSON } = require('../../fns/kits')
const moment = require('moment')
const redis = require('../../libs/redis').get()
const _ = require('lodash')
const getIpSegment = require('../../libs/ipSegment')
/**
 * 根据域名解析IP
 */
class SetNoAccessDNSRedis extends Common {
    constructor(params) {
        super(params)
        this.topics = ['SetNoAccessDNSRedis']
        this.type = 'icp-delay'
        this.name = 'SetNoAccessDNSRedis'
        this.init()
    }
    async handle(msg) {
        await sleep(1000)
        let { BatchId } = this.parseContent(msg)
        let producer = this.producer
        let redis = this.redis.get()
        let uid = uuid()
        //查看有效的备案信息
        try {
            //分批处理，一次解析一百条domain数据
            let DomainPart = await redis.spop(`${BatchId}_DomainSet`, 100)
            if (DomainPart !== null && DomainPart.length > 0) {
                const DomainIPMap = await getDomainDns(DomainPart, {})
                redis.hmset(`${BatchId}_DomainIPMap`, DomainIPMap)
                await producer.send({
                    type: 'icp-delay',
                    topic: 'SetNoAccessDNSRedis',
                    data: {
                        BatchId,
                    },
                    opts: {
                        headers: {
                            'x-delay': 1 * 1000,
                        },
                    },
                })
            } else {
                //更新redis中的DomainIpMap
                let DomainIPMapRedis = await redis.hgetall(
                    `${BatchId}_DomainIPMap`
                )
                let NoAccessDataDomainIPList = await redis.get(
                    `${BatchId}_NoAccessDataDomainIP`
                )
                NoAccessDataDomainIPList = JSON.parse(NoAccessDataDomainIPList)
                if (
                    NoAccessDataDomainIPList instanceof Array &&
                    NoAccessDataDomainIPList.length > 0 &&
                    DomainIPMapRedis
                ) {
                    // 此处记录的是 子域名 以及其对应的IP
                    let ipDomainList = []
                    NoAccessDataDomainIPList.forEach((item) => {
                        let ipList = DomainIPMapRedis[item.SubDomain]
                        if (ipList) {
                            ipList = ipList.split(',')
                        }
                        if (ipList instanceof Array) {
                            ipList.forEach((j) => {
                                ipDomainList.push({
                                    ...item,
                                    IP: j,
                                })
                            })
                        } else {
                            ipDomainList.push({
                                ...item,
                                IP: ipList || '',
                            })
                        }
                    })
                    //去除在我司的ip段的ip
                    ipDomainList = await getIpSegment.init(ipDomainList)
                    await redis.set(
                        `${BatchId}_NoAccess_DomainIP_in_ucloud_ipSegment`,
                        JSON.stringify(ipDomainList)
                    )
                    let recordList = await UninsertBatchRecordModel.findAll({
                        attributes: ['BatchId'],
                        where: {
                            BatchId,
                        },
                    })
                    recordList = parseJSON(recordList)
                    if (!recordList || recordList.length < 1) {
                        await UninsertBatchRecordModel.bulkCreate(
                            ipDomainList,
                            {
                                ignoreDuplicates: true,
                            }
                        )
                        await producer.send({
                            type: 'icp',
                            topic: 'GetNoAccessDomainStatus',
                            data: {
                                BatchId,
                            },
                            opts: {
                                headers: {
                                    'x-delay': 1 * 1000,
                                }, // 延迟10s
                            },
                        })
                    }
                    await Promise.all([
                        redis.expire(`${BatchId}_NoAccessDataDomainIP`, 300),
                        redis.expire(`${BatchId}_DomainIPMap`, 300),
                    ])
                }
            }
        } catch (err) {
            logger.error(`${this.name}[${uid}] - error：${err}`)
        }
        logger.info(`${this.name}[${uid}] - finished`)
    }
}
const sleep = (millseconds = 3000) => {
    return new Promise((rs) => {
        setTimeout(() => rs(), millseconds)
    })
}
/**
 * @param {*} DomainList 所有需要解析的domain
 * @param {*} DomianIPMap 所有解析完成的domain-ip
 * @returns DomianIPMap
 */
const getDomainDns = async (DomainList, DomianIPMap) => {
    let promise = []
    //域名数组去重
    DomainList = _.uniq(DomainList)
    for (let i = 0; i < DomainList.length; i++) {
        const domain = DomainList[i]
        promise.push(
            new Promise((resolve) =>
                resolver.resolve4(domain, (err, address) => {
                    console.log('domain, address', domain, address)
                    DomianIPMap[domain] = address || ''
                    resolve(address || '')
                })
            )
        )
    }
    await Promise.all(promise)
    return DomianIPMap
}
module.exports = new SetNoAccessDNSRedis()
