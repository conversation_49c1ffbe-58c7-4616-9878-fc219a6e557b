'use strict'

const Common = require('../Common')
const {
    model: UninsertBatchModel,
    BatchStatusEnum: BatchStatusEnum,
} = require('../../models/t_no_access_batch')
const {
    model: NotifyCompanyInfoModel,
    RecordStatusEnum: RecordStatusEnum,
} = require('../../models/t_no_access_org_info')
const {
    model: NoAccessNotifyModel,
    SendStatusEnum: SendStatusEnum,
    NotifyTypeEnum: NotifyTypeEnum,
} = require('../../models/t_no_access_notify')
const {
    model: UninsertBatchRecordModel,
} = require('../../models/t_no_access_record')
const {
    NotifyTemplateModel,
    NotifyTypeModel,
} = require('../../mongoModels/icp')
const { Op, where } = require('sequelize')
const logger = require('../../libs/logger').getLogger('mqCustomer')
const uuid = require('uuid/v4')
const { parseJSON } = require('../../fns/kits')
const moment = require('moment')
const { sendMail, sendSms } = require('../../fns/umsMsgSend')
const {
    getMailContent,
    getSmsContent,
} = require('../../fns/notify/GetNotifyContent')
const _ = require('lodash')

/**
 * 通过公司Id查询资源信息，
 * 并将无资源的公司Id插入至该批次的记录中
 */
class NoAccessNotify extends Common {
    constructor(params) {
        super(params)
        this.topics = ['NoAccessNotify']
        this.type = 'icp'
        this.name = 'NoAccessNotify'
        this.init()
    }
    async handle(msg) {
        await sleep(1000)
        let { BatchId, Id, BatchOrgId, Mobile, Email, MainMail } =
            this.parseContent(msg)
        let redis = this.redis.get()
        let uid = uuid()
        logger.info(`${this.name}[${uid}] - start`, BatchId, Id, Mobile, Email)
        //查看有效的备案信息
        try {
            //更新通知进度
            await redis.decr(`${BatchId}_NoAccessNotifyListLength`)
            //更新记录到通知表
            await NoAccessNotifyModel.update(
                {
                    SmsStatus: SendStatusEnum.New,
                    EmailStatus: SendStatusEnum.New,
                },
                {
                    where: {
                        Id: Id,
                    },
                }
            )
            //获取已备案未接入通知内容
            let getNotifyType = await NotifyTypeModel.findOne({
                Name: '已备案未接入通知',
            })
            getNotifyType = parseJSON(getNotifyType)
            //获取通知的ip和domain，可去数据库查询[{IP:"",Domain:"",SubDomain:""}],一个主域名对应多个子域名，表记录是以子域名为维度
            let ICPInfo = await UninsertBatchRecordModel.findAll({
                attributes: ['IP', 'Domain', 'SubDomain'],
                where: {
                    BatchOrgId,
                    BatchId,
                    RegisterStatus: {
                        [Op.in]: [0],
                    },
                },
            })
            ICPInfo = parseJSON(ICPInfo)

            // 如果无 需要通知的数据 则直接修改状态为 “禁止发送”
            if (ICPInfo.length === 0) {
                await Promise.all([
                    NoAccessNotifyModel.update(
                        {
                            EmailStatus: SendStatusEnum.Forbidden,
                        },
                        {
                            where: {
                                Id,
                            },
                        }
                    ),
                    NotifyCompanyInfoModel.update(
                        { Status: RecordStatusEnum.Forbidden },
                        {
                            where: {
                                Id: BatchOrgId,
                            },
                        }
                    ),
                ])
            } else {
                //发送邮件通知
                if (Email) {
                    try {
                        const taskIdEmail = await this.sendEmailMsg(
                            ICPInfo,
                            getNotifyType.Type,
                            Email
                        )
                        if (taskIdEmail) {
                            //更新数据库的邮件状态
                            await NoAccessNotifyModel.update(
                                {
                                    EmailStatus: SendStatusEnum.Sending,
                                    EmailTaskId: taskIdEmail,
                                },
                                {
                                    where: {
                                        Id: Id,
                                    },
                                }
                            )
                            // 发送状态检查
                            await this.producer.send({
                                type: 'icp-delay',
                                topic: 'CheckUMSSendStatus',
                                data: {
                                    uidreadom: uuid(),
                                    send_time: moment().format('X'),
                                    Id: Id,
                                    Type: NotifyTypeEnum.Email,
                                    task_id: taskIdEmail,
                                    notifyType: '已备案未接入通知类型',
                                    BatchOrgId,
                                    BatchId,
                                },
                                opts: {
                                    headers: { 'x-delay': 60 * 1000 }, // 延迟1分钟检查时间
                                },
                            })
                        }
                    } catch (err) {
                        logger.error(`${this.name}[${uid}] - 发送邮件出错：${err}`)
                    }
                }
                //发送短信通知
                if (Mobile) {
                    try {
                        const taskIdMobile = await this.SendSmsMsg(
                            MainMail,
                            getNotifyType.Type,
                            Mobile
                        )
                        console.log(taskIdMobile, 'taskIdMobile')
                        if (taskIdMobile) {
                            //更新数据库的短信状态
                            await NoAccessNotifyModel.update(
                                {
                                    SmsStatus: SendStatusEnum.Sending,
                                    SmsTaskId: taskIdMobile,
                                },
                                {
                                    where: {
                                        Id: Id,
                                    },
                                }
                            )
                            // 发送状态检查
                            await this.producer.send({
                                type: 'icp-delay',
                                topic: 'CheckUMSSendStatus',
                                data: {
                                    uidreadom: uuid(),
                                    send_time: moment().format('X'),
                                    Id: Id,
                                    Type: NotifyTypeEnum.Phone,
                                    task_id: taskIdMobile,
                                    notifyType: '已备案未接入通知类型',
                                    BatchOrgId,
                                    BatchId,
                                },
                                opts: {
                                    headers: { 'x-delay': 60 * 1000 }, // 延迟1分钟检查时间
                                },
                            })
                        }
                    } catch (e) {
                        logger.error(`${this.name}[${uid}] - 发送短信出错：${e}`)
                    }
                }
            }
        } catch (err) {
            logger.error(`${this.name}[${uid}] - 通知error：${err}`)
        } finally {
            //判断批次是否完成通知
            const NotifyLength = await redis.get(
                `${BatchId}_NoAccessNotifyListLength`
            )
            if (NotifyLength == 0) {
                //批次状态改为通知完成
                await UninsertBatchModel.update(
                    { Status: BatchStatusEnum.SendFinish },
                    {
                        where: {
                            Id: BatchId,
                        },
                    }
                )
            }
        }
        logger.info(`${this.name}[${uid}] - finished`)
    }
    /**
     * 根据备案信息 和通知人信息发送通知
     * @param {*} ICPInfo 备案信息
     * @param {number} Type 具体那种类型的通知 例如： 域名过期 或者 备案和实名不一致
     * @param {*} email 收件人
     */
    async sendEmailMsg(ICPInfo, Type, email) {
        //获取通知内容
        console.log(ICPInfo, Type, email, 'ICPInfo, Type, email')
        const { Content, Title, AttachFiles } = await getMailContent(
            ICPInfo,
            Type
        )
        console.log(Content, 'ContentMail')
        console.log(Title, 'Title')
        //发送通知
        const taskId = await sendMail({
            email,
            content: Content,
            title: Title,
            attachFiles: AttachFiles,
        })
        return taskId
    }

    /**
     * 发送无资源有备案短信通知
     * @param {*} MainMail 主账号邮箱
     * @param {*} mobile 手机号
     * @param {number} Type 具体那种类型的通知 例如： 域名过期 或者 备案和实名不一致
     */
    async SendSmsMsg(MainMail, Type, mobile) {
        let content = await getSmsContent(Type, MainMail)
        const taskId = await sendSms({
            mobile,
            content,
        })
        return taskId
    }
}
const sleep = (millseconds = 3000) => {
    return new Promise((rs) => {
        setTimeout(() => rs(), millseconds)
    })
}

module.exports = new NoAccessNotify()
