/*
 * @file 订阅type为icp topics为 GetCompanyInfoByIp 的事件队列，根据队列获取的record，获取对应的公司信息和告警组联系人信息
 * 设计思路：mq的每一条接受消息都需要保证消息的幕等性
 * 本队列的幕等性，是依赖于t_batch_org表插入的唯一索引来保证
 */

const Common = require('../Common')
const getAllIpInfo = require('../../fns/getIPInfo')
const { parseJSON, sleep } = require('../../fns/kits')
const moment = require('moment')
const {
    model: UninsertBatchModel,
    BatchStatusEnum: BatchStatusEnum,
    TypeEnum: BatchTypeEnum,
} = require('../../models/t_no_access_batch')
const {
    model: CompanyNotifyModel,
    RecordStatusEnum: RecordStatusEnum,
} = require('../../models/t_no_access_org_info')
const {
    model: NoAccessNotifyModel,
    SendStatusEnum: SendStatusEnum,
    NotifyTypeEnum: NotifyTypeEnum,
} = require('../../models/t_no_access_notify')
const {
    model: UninsertBatchRecordModel,
} = require('../../models/t_no_access_record')
const axios = require('../../libs/axiosApi')
const logger = require('../../libs/logger').getLogger('mqCustomer')
const uuid = require('uuid/v4')
const { getContactsByOrgId } = require('../../fns/notify/getContactsByOrgId')
const { Op, BaseError } = require('sequelize')
const _ = require('lodash')
/*
 * @class
 * @extent Common
 */
const CHANNELID = {
    LANGCHAO: 101,
    UCLOUD: 1,
}
const CHANNEL_DICT = {
    [CHANNELID.LANGCHAO]: '浪潮渠道',
    [CHANNELID.UCLOUD]: '直客',
}
class GetNoAccessCompanyInfoByIp extends Common {
    constructor(params) {
        super(params)
        this.topics = ['GetNoAccessCompanyInfoByIp']
        this.type = 'icp'
        this.init()
        this.name = 'GetNoAccessCompanyInfoByIp'
    }
    async handle(msg) {
        let self = this
        let redis = this.redis.get()
        let producer = this.producer
        let { BatchId, IP, RetryTime = 3 } = this.parseContent(msg)
        let uid = uuid()
        logger.info(
            `${this.name}[${uid}]-${BatchId}-${IP} - start`,
            msg.content.toString()
        )
        try {
            //根据ip获取公司信息
            let info = await getAllIpInfo(
                {
                    Action: 'Compass.IGetCompanyByEip',
                    Token: '361d3765-836a-400a-8ecb-025216762c7c',
                },
                [IP]
            )
            logger.info('info', info)
            //未找到公司信息,不做任何解析和通知,直接返回
            if (
                info[0].Data &&
                info[0]?.Data?.TopOrganizationId &&
                info[0]?.Data?.OrganizationId
            ) {
                //公司id
                const CompanyId = info[0]?.Data?.TopOrganizationId
                //项目id
                const OrganizationId = info[0]?.Data?.OrganizationId

                //若redis:unconn_filter_companyId中已有公司id则不通知.//状态设置为禁止发送
                let filterCompanyId = await redis.smembers(
                    `unconn_filter_companyId`
                )
                //组织id是否存在
                let OrgInfo = await CompanyNotifyModel.findOne({
                    where: {
                        BatchId: BatchId,
                        CompanyId,
                        OrganizationId: OrganizationId,
                    },
                })
                OrgInfo = parseJSON(OrgInfo)
                if (!OrgInfo) {
                    OrgInfo = await CompanyNotifyModel.create({
                        BatchId: BatchId,
                        CompanyId: CompanyId,
                        OrganizationId: OrganizationId,
                        Status: filterCompanyId.includes(CompanyId.toString())
                            ? RecordStatusEnum.Forbidden
                            : RecordStatusEnum.New,
                    })
                }
                //更新t_no_access_record表中的batch_org_id
                await UninsertBatchRecordModel.update(
                    { BatchOrgId: OrgInfo.Id },
                    {
                        where: {
                            BatchId: BatchId,
                            IP: IP,
                        },
                    }
                )
                //通过CompanyId找到通知人的联系方式
                let result
                try {
                    let options = {
                        method: 'POST',
                        url: global.CONFIG.dataDDApi,
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        data: {
                            Action: 'Account.GetCompanyInfo',
                            Token: global.CONFIG.DataDDToken,
                            CompanyId: parseInt(CompanyId),
                        },
                        json: true,
                    }
                    result = await axios(options)
                    if (result.status !== 200 || result.data.RetCode !== 0) {
                        let err = new Error(
                            `账户查询公司信息失败，公司Id: ${CompanyId},错误信息：${
                                result.status !== 200
                                    ? result.status
                                    : result.data.Message
                            }`
                        )
                        await CompanyNotifyModel.update(
                            {
                                Reason: err,
                            },
                            {
                                where: {
                                    BatchId: BatchId,
                                    CompanyId: CompanyId,
                                },
                            }
                        )
                        throw err
                    }
                } catch (error) {
                    await CompanyNotifyModel.update(
                        {
                            Reason: '查询公司信息失败',
                        },
                        {
                            where: {
                                BatchId: BatchId,
                                CompanyId,
                            },
                        }
                    )
                    throw Error('查询公司信息失败')
                }

                //若获取到公司信息，更新公司信息
                result = result.data?.Data
                let notifyInfo = _.find(result?.main_contacts, (contact) => {
                    return (
                        contact.admin === 1 &&
                        !/^spt.*@ucloud\.cn$/.test(contact.user_email)
                    )
                })
                if (result) {
                    let params = {
                        CompanyName: result.company_name,
                        Manager: result.manager,
                        BU: result.bu,
                        Level: result.vip_level,
                        ChannelId: result.channel_id,
                        MainMail: notifyInfo.main_email,
                    }
                    //浪潮禁止发送
                    if (CHANNELID.LANGCHAO === result.channel_id) {
                        params['Status'] = RecordStatusEnum.Forbidden
                    }
                    try {
                        await CompanyNotifyModel.update(
                            {
                                ...params,
                            },
                            {
                                where: {
                                    BatchId: BatchId,
                                    CompanyId: CompanyId,
                                },
                            }
                        )
                    } catch (err) {
                        logger.error(
                            `${self.name}[${uid}] - 更新公司信息 error,错误信息：${err}`
                        )
                        await CompanyNotifyModel.update(
                            {
                                Reason: '获取公司信息并更新信息失败',
                            },
                            {
                                where: {
                                    BatchId: BatchId,
                                    CompanyId: CompanyId,
                                },
                            }
                        )
                    }
                }
                //通过组织id获取联系人
                let OrganizationIdContacts = await getContactsByOrgId(
                    OrganizationId,
                    100024
                )
                OrganizationIdContacts = OrganizationIdContacts
                    ? OrganizationIdContacts[0]
                    : []
                OrganizationIdContacts = OrganizationIdContacts.filter(
                    (item) => item.switch_flag === 1
                )
                if (
                    OrganizationIdContacts.length > 0 &&
                    !filterCompanyId.includes(CompanyId)
                ) {
                    for (var i = 0; i < OrganizationIdContacts.length; i++) {
                        const contact = OrganizationIdContacts[i]
                        await NoAccessNotifyModel.findOrCreate({
                            where: {
                                BatchOrgId: OrgInfo.Id,
                                Mobile: contact.mobile || '',
                                Email: contact.email || '',
                                SmsStatus: SendStatusEnum.New,
                                EmailStatus: SendStatusEnum.New,
                            },
                        })
                    }
                }
            }
            // 获取下一个IP 推送查看
            let nextIP = await redis.spop(`${BatchId}_NoAccess_NeedParseIPs`)
            if (nextIP) {
                await producer.send({
                    type: 'icp',
                    topic: 'GetNoAccessCompanyInfoByIp',
                    data: {
                        BatchId,
                        IP: nextIP,
                    },
                })
            } else {
                // 所有的IP都查询完毕， 将此任务写入finished
                await redis.sadd(
                    `${BatchId}_lock_all_search_finished`,
                    'IPCompanyInfo'
                )
                // 推送 finished 延时检查
                let mqOptions = {
                    type: 'icp-delay',
                    topic: 'CheckAllParseIsFinished',
                    data: {
                        BatchId,
                    },
                    opts: {
                        headers: {
                            'x-delay': 5 * 60 * 1000,
                        }, // 延迟5分钟检查时间
                    },
                }
                await producer.send(mqOptions)
            }
            logger.info(`${self.name}[${uid}] - finished`)
        } catch (e) {
            logger.error(
                `${self.name}[${uid}-${IP}] - 解析ip过程出错 error,错误信息：${e.message},5s后开始重新推送一次mq`
            )
            //5秒后重新推送一次解析
            await sleep(5000)
            RetryTime--
            if (RetryTime > 0) {
                await producer.send({
                    type: 'icp',
                    topic: 'GetNoAccessCompanyInfoByIp',
                    data: {
                        BatchId,
                        IP,
                        RetryTime,
                    },
                })
                logger.error(`${self.name}[${uid}-${IP}] - Mq重新推送成功`)
            } else {
                logger.error(`${self.name}[${uid}-${IP}] - 重试多次失败`)
            }
        }
    }
}
module.exports = new GetNoAccessCompanyInfoByIp()
