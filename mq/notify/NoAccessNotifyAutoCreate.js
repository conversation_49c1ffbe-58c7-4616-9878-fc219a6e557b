'use strict'
const Common = require('../Common')
const logger = require('../../libs/logger').getLogger('mqCustomer')
const uuid = require('uuid/v4')
const { parseJSON } = require('../../fns/kits')
const moment = require('moment')
const {
    getAodunNoAccessDomainIPList,
    getToken,
} = require('../../fns/aodun/DomainService')
const {
    model: UninsertBatchModel,
    BatchStatusEnum,
} = require('../../models/t_no_access_batch')
const punycode = require('punycode')
const IPList = [
    '************',
    '************',
    '***********',
    '************',
    '************',
]
/**
 * 自动生成未接入批次 数据从傲顿拉取
 *  -> SetNoAccessDNSRedis 推送 域名解析，获取到这些IP为 查看IP资源所属做准备
 */
class NoAccessNotifyAutoCreate extends Common {
    constructor(params) {
        super(params)
        this.topics = ['NoAccessNotifyAutoCreate']
        this.type = 'icp-delay'
        this.name = 'NoAccessNotifyAutoCreate'
        this.init()
    }
    async handle(msg) {
        await sleep(1000)
        let { time = 0, record } = this.parseContent(msg)
        let producer = this.producer
        let redis = this.redis.get()
        let uid = uuid()
        let now = moment().format('YYYY-MM-DD HH:mm:ss')
        logger.info(`${this.name} - [${uid}] - start`, time)
        //查看有效的备案信息
        try {
            const domain_ip_range = 1 //1.解析已在机房 2.解析不在机房 3.全部
            const auth_status = 2 //备案状态1:未备案 2已备案
            const filter_status = 0 //阻断状态0未阻断，1已阻断
            //整理所有的 要删除的域名
            // 获取token
            const token = await getToken()
            // 获取傲盾不良信息
            let result = await getAodunNoAccessDomainIPList(token, {
                domain_ip_range,
                filter_status,
                auth_status,
            })
            //如果获取全量数据，如果有大量数据则可能会返回一个taskId然后再次查询，数据少则直接返回数据
            //增加延迟到15秒
            await sleep(15000)
            if (result.task_id) {
                result = await getAodunNoAccessDomainIPList(token, {
                    task_id: result.task_id,
                })
            }
            let Data = result.data
            let DomainIPList = []
            if (Data instanceof Array) {
                //自动创建批次
                let BatchId,
                    SubDomains = new Set()
                //创建批次，存入已备案未接入批次表中
                let batchInfo = await UninsertBatchModel.create({
                    Name: '已备案未接入-' + now,
                    Status: BatchStatusEnum.Pull,
                    Operater: 'auto',
                })
                batchInfo = parseJSON(batchInfo)
                BatchId = batchInfo.Id
                try {
                    //根据IP把代理商IP过滤掉处理ip，domain
                    Data.forEach((item) => {
                        if (!IPList.includes(item.ip)) {
                            const SubDomain = filterDomain(item.host)
                            const Domain = item.domain.toLowerCase()
                            SubDomain.forEach((j) => {
                                DomainIPList.push({
                                    BatchId,
                                    Domain,
                                    SubDomain: j,
                                })
                                SubDomains.add(j)
                            })
                        }
                    })

                    await Promise.all([
                        redis.set(
                            `${BatchId}_NoAccessDataDomainIP`,
                            JSON.stringify(DomainIPList)
                        ),
                        redis.sadd(
                            `${BatchId}_DomainSet`,
                            Array.from(SubDomains)
                        ),
                    ])
                    for (let i = 0; i < 8; i++) {
                        //根据domain解析出来ip,然后更新到redis中
                        await producer.send({
                            type: 'icp-delay',
                            topic: 'SetNoAccessDNSRedis',
                            data: {
                                BatchId,
                            },
                            opts: {
                                headers: {
                                    'x-delay': 1 * 60 * 1000,
                                }, // 延迟1分钟检查时间
                            },
                        })
                    }
                } catch (e) {
                    logger.error(
                        `自动创建已备案未接入批次BatchId:${BatchId}[${uid}] - 解析出错：${e}`
                    )
                }
                logger.info(
                    now + '自动创建已备案未接入批次成功！BatchId:' + BatchId
                )
            } else {
                //未返回全量数据，重试
                if (time) {
                    time--
                    await this.producer.send({
                        type: 'icp-delay',
                        topic: 'NoAccessNotifyAutoCreate',
                        data: {
                            time,
                        },
                        opts: {
                            headers: {
                                'x-delay': 1 * 60 * 1000,
                            }, // 延迟1分钟检查时间
                        },
                    })
                }
            }
        } catch (err) {
            logger.error(
                `${this.name}[${uid}] - 未接入通知批次生成error：${err}`
            )
            //重新推送
            if (time) {
                time--
                await this.producer.send({
                    type: 'icp-delay',
                    topic: 'NoAccessNotifyAutoCreate',
                    data: {
                        time,
                    },
                    opts: {
                        headers: {
                            'x-delay': 1 * 60 * 1000,
                        }, // 延迟1分钟检查时间
                    },
                })
            }
        }
        logger.info(`${this.name}[${uid}] - finished`)
    }
}
const sleep = (millseconds = 3000) => {
    return new Promise((rs) => {
        setTimeout(() => rs(), millseconds)
    })
}
//域名转换
const filterDomain = (list) => {
    list = list.map((item) => {
        if (item.indexOf('xn--') > -1) {
            return punycode.toUnicode(item) || item
        }
        return item.toLowerCase()
    })
    return list
}
module.exports = new NoAccessNotifyAutoCreate()
