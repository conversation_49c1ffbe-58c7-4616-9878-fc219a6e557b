'use strict'

const Method = require('../../../libs/method')
const { parseJSON } = require('../../../fns/kits')
const {
    model: UninsertBatchModel,
    BatchStatusEnum,
} = require('../../../models/t_no_access_batch')
const {
    model: UninsertBatchRecordModel,
} = require('../../../models/t_no_access_record')
const { NotifyTypeModel } = require('../../../mongoModels/icp')
const {
    model: CompanyNotifyModel,
    RecordStatusEnum: RecordStatusEnum,
} = require('../../../models/t_no_access_org_info')
const {
    model: NoAccessBUNotifyModel,
    SendStatusEnum,
    NotifyTypeEnum,
} = require('../../../models/t_no_access_bu_notify')
const _ = require('lodash')
const { Op, BaseError } = require('sequelize')
const uuid = require('uuid/v4')
const moment = require('moment')
const { sendMail, sendSms } = require('../../../fns/umsMsgSend')
const {
    getMailContent,
    getSmsContent,
} = require('../../../fns/notify/GetNotifyContent')
/**
 * 查看已备案未接入BU运营通知情况
 */
module.exports = class GetNoAccessBUNotifyData extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({
        BatchId, //批次Id
        Notify,
        EmailTaskId,
    }) {
        let producer = this.producer
        let redis = this.redis.get()
        let uid = uuid()
        let self = this
        try {
            let batchInfo = await UninsertBatchModel.findOne({
                where: {
                    Id: BatchId,
                },
            })
            if (!batchInfo) {
                //批次不存在
                let err = new Error('批次不存在')
                err.code = 36001
                throw err
            }
            batchInfo = parseJSON(batchInfo)
            if (
                [
                    BatchStatusEnum.Finish,
                    BatchStatusEnum.Parsing,
                    BatchStatusEnum.Pull,
                ].includes(batchInfo.Status)
            ) {
                let err = new Error('该批次状态不允许获取BU')
                err.code = 36003
                throw err
            }
            let recordWhere = {
                BatchId,
            }
            //重发bu消息
            if (EmailTaskId) {
                let getNotifyInfo = await NoAccessBUNotifyModel.findOne({
                    where: {
                        EmailTaskId: EmailTaskId,
                        BatchId: BatchId,
                    },
                })
                getNotifyInfo = parseJSON(getNotifyInfo)
                console.log(getNotifyInfo, 'getNotifyInfo')
                if (
                    getNotifyInfo &&
                    getNotifyInfo.Email &&
                    getNotifyInfo.Id &&
                    getNotifyInfo.NotifyInfo
                ) {
                    await this.BatchNotifyBU(
                        getNotifyInfo.Email,
                        JSON.parse(getNotifyInfo.NotifyInfo),
                        getNotifyInfo.Id
                    )
                    return this.cb(0)
                } else {
                    let err = new Error('数据异常，不支持重发')
                    err.code = 36016
                    throw err
                }
            }
            //浪潮渠道的禁止通知
            let { rows: records, count } = parseJSON(
                await CompanyNotifyModel.findAndCountAll({
                    where: {
                        Status: {
                            [Op.notIn]: [RecordStatusEnum.Forbidden],
                        },
                        ...recordWhere,
                    },
                })
            )
            if (count === 0) {
                return this.cb(0, {
                    RecordList: [],
                    TotalCount: 0,
                })
            }
            if (Notify) {
                //BU为SML的记录
                const SMLRecord = records.filter((item) => item.BU === 'SML')
                //获取SML指定联系人
                let SMLManagerList =
                    (await redis.get(`bu_notify_setting`)) || []
                SMLManagerList = JSON.parse(SMLManagerList).SML
                SMLManagerList = (SMLManagerList['CC'] || []).concat(
                    SMLManagerList.To || []
                )
                let SMLICPInfo = []
                //获取通知的ip和domain，可去数据库查询[{ip:"",domain:""}]
                SMLICPInfo = await UninsertBatchRecordModel.findAll({
                    attributes: ['IP', 'Domain'],
                    where: {
                        BatchOrgId: {
                            [Op.in]: SMLRecord.map((item) => item.Id),
                        },
                        BatchId: BatchId,
                        RegisterStatus: { [Op.in]: [0] },
                    },
                })
                SMLICPInfo = parseJSON(SMLICPInfo)
                console.log(SMLICPInfo, 'SMLICPInfo')
                for (var j = 0; j < SMLManagerList.length; j++) {
                    //生成记录到通知表
                    let NotifyInfoSML = await NoAccessBUNotifyModel.create({
                        BatchId: BatchId,
                        Email: SMLManagerList[j],
                        EmailStatus: SendStatusEnum.New,
                        BU: 'SML',
                        NotifyInfo: JSON.stringify(SMLICPInfo),
                    })
                    NotifyInfoSML = parseJSON(NotifyInfoSML)
                    await this.BatchNotifyBU(
                        SMLManagerList[j],
                        SMLICPInfo,
                        NotifyInfoSML.Id
                    )
                }
                //BU为非SML的记录
                const NotSMLRecord = records.filter((item) => item.BU !== 'SML')
                //汇总
                console.log(NotSMLRecord, 'NotSMLRecord')
                for (var k = 0; k < NotSMLRecord.length; k++) {
                    let ICPInfo = await UninsertBatchRecordModel.findAll({
                        attributes: ['IP', 'Domain'],
                        where: {
                            BatchOrgId: {
                                [Op.in]: NotSMLRecord[k].Id,
                            },
                            BatchId: BatchId,
                            RegisterStatus: {
                                [Op.in]: [0],
                            },
                        },
                    })
                    ICPInfo = parseJSON(ICPInfo)
                    //生成记录到通知表
                    let NotifyInfo = await NoAccessBUNotifyModel.create({
                        BatchId: BatchId,
                        Email: NotSMLRecord[k].Manager,
                        EmailStatus: SendStatusEnum.New,
                        BU: NotSMLRecord[k].BU,
                        NotifyInfo: JSON.stringify(ICPInfo),
                    })
                    await this.BatchNotifyBU(
                        NotSMLRecord[k].Manager,
                        ICPInfo,
                        NotifyInfo.Id
                    )
                }
                return this.cb(0)
            }
            let { rows: notifies, number } = parseJSON(
                await NoAccessBUNotifyModel.findAndCountAll({
                    where: recordWhere,
                })
            )
            console.log(notifies, 'records1')
            return this.cb(0, {
                RecordList: notifies,
                TotalCount: number,
            })
        } catch (e) {
            console.log(e)
            self.err(e)
        }
    }
    //通知bu
    async BatchNotifyBU(Email, ICPInfo, Id) {
        //获取已备案未接入通知内容
        let getNotifyType = await NotifyTypeModel.findOne({
            Name: '已备案未接入通知',
        })
        getNotifyType = parseJSON(getNotifyType)
        console.log(ICPInfo, 'ICPInfo')
        //发送邮件通知
        if (Email) {
            try {
                const taskIdEmail = await this.sendEmailMsg(
                    ICPInfo,
                    getNotifyType.Type,
                    Email
                )
                if (taskIdEmail) {
                    //更新数据库的邮件状态
                    await NoAccessBUNotifyModel.update(
                        {
                            EmailStatus: SendStatusEnum.Sending,
                            EmailTaskId: taskIdEmail,
                            EmailRetryTime: 0,
                        },
                        {
                            where: {
                                Id: Id,
                            },
                        }
                    )
                    // 发送状态检查
                    await this.producer.send({
                        type: 'icp-delay',
                        topic: 'CheckUMSSendStatus',
                        data: {
                            uidreadom: uuid(),
                            send_time: moment().format('X'),
                            Id: Id,
                            Type: NotifyTypeEnum.Email,
                            task_id: taskIdEmail,
                            notifyType: '已备案未接入通知类型',
                        },
                        opts: {
                            headers: { 'x-delay': 60 * 1000 }, // 延迟1分钟检查时间
                        },
                    })
                }
            } catch (err) {
                console.log(err)
            }
        }
    }
    /**
     * 根据备案信息 和通知人信息发送通知
     * @param {*} ICPInfo 备案信息
     * @param {number} Type 具体那种类型的通知 例如： 域名过期 或者 备案和实名不一致
     * @param {*} email 收件人
     */
    async sendEmailMsg(ICPInfo, Type, email) {
        //获取通知内容
        console.log(ICPInfo, Type, email, 'ICPInfo, Type, email')
        const { Content, Title, AttachFiles } = await getMailContent(
            ICPInfo,
            Type
        )
        console.log(Content, 'ContentMail')
        console.log(Title, 'Title')
        //发送通知
        const taskId = await sendMail({
            email,
            content: Content,
            title: Title,
            attachFiles: AttachFiles,
        })
        return taskId
    }
}
