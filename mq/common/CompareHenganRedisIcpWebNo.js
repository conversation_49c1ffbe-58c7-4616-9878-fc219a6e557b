const Common = require('../Common')
const { getTableModel, parseJSON } = require('../../fns/kits')
const moment = require('moment')
const henganApi = require('../../libs/henganApiPromise')
const { parse } = require('request/lib/cookies')
const _ = require('lodash')
const { CONNECTSYNCVALUE } = require('../../configs/')
const AddDomainForResolveOrder = require('../../mq/icpHook/AddDomainForResolveOrder')
const DelDomainForResolveOrder = require('../../mq/icpHook/DelDomainForResolveOrder')
const producer = require('../../libs/producer')
const logger = require('../../libs/logger').getLogger('mqCustomer')
const uuid = require('uuid/v4')

//数据A	 入参 域名，网站备案号
//数据B	 Redis hgetall {domain:icpwebno}
class CompareHenganRedisIcpWebNo extends Common {
    constructor(params) {
        super(params)
        this.topics = ['CompareHenganRedisIcpWebNo']
        this.type = 'icp'
        this.name = 'CompareHenganRedisIcpWebNo'
        this.init()
    }
    async handle(msg) {
        let self = this
        let uid = uuid()
        logger.info(`${this.name}[${uid}] - start`, msg.content.toString())
        let { BatchName } = self.parseContent(msg)
        let mongo = self.mongo.get('icp')
        let redis = self.redis.get()
        let now = parseInt(moment().format('X')),
            AllDomain,
            checkBatchName = moment().format('YYYY-MM-DD') + 'DomainCheck'
        try {
            let InputData = await redis.hgetall('CompareHenganRedisIcpWebNo')
            InputData = parseJSON(InputData)
            //获取Redis中的记录{domain:icpwebno}
            AllDomain = await redis.hgetall('domain_icp_in_ucloud_map')
            AllDomain = parseJSON(AllDomain)

            //得到A有B没有的域名
            let InputHasAllNo = _.difference(
                Object.keys(InputData),
                Object.keys(AllDomain)
            )
            if (InputHasAllNo.length > 0) {
                InputHasAllNo.map(async (item) => {
                    if (item) {
                        // 执行状态更新
                        let data = {
                            Domain: item,
                        }
                        await producer.send({
                            type: 'icp',
                            topic: 'AddDomainForResolveOrder',
                            data,
                        })
                    }
                })
            }
            //与B有A没有的域名
            let AllHasInputNo = _.difference(
                Object.keys(AllDomain),
                Object.keys(InputData)
            )
            if (AllHasInputNo.length > 0) {
                AllHasInputNo.map(async (item) => {
                    if (item) {
                        let data = {
                            Domain: item,
                            ICPWebNo: AllDomain[item],
                        }
                        await producer.send({
                            type: 'icp',
                            topic: 'DelDomainForResolveOrder',
                            data,
                        })
                    }
                })
            }
            //把比对结果存到mongo中
            const CompareLogCollection = mongo.collection(
                'compare_hengan_ucloud_data'
            )
            CompareLogCollection.insertOne({
                BatchName: BatchName,
                CreateTime: moment().format('YYYY-MM-DD'),
                Remark: 'ResultInAMove代表恒安有redis中没有，ResultInBUpdate代表redis有恒安没有',
                ResultInAMove: InputHasAllNo,
                ResultInBUpdate: AllHasInputNo,
                Type: 'CompareHenganRedisIcpWebNo',
            })
            logger.info(`${this.name}[${uid}] - finish with success`)
        } catch (e) {
            logger.error(
                `${this.name}[${uid}] - finish with error: CompareHenganRedisIcpWebNo出错: ${e}`,
                msg.content.toString()
            )
        }
    }
}
module.exports = new CompareHenganRedisIcpWebNo()
