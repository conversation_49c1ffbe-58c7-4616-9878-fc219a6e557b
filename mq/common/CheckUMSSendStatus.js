'use strict'

const Common = require('../Common')
const {
    model: NotifyModel,
    SendStatusEnum,
    NotifyTypeEnum,
} = require('../../models/t_notify')
const {
    model: NoAccessNotifyModel,
} = require('../../models/t_no_access_notify')
const logger = require('../../libs/logger').getLogger('mqCustomer')
const uuid = require('uuid/v4')
const moment = require('moment')
const { checkSendingResult } = require('../../fns/umsMsgSend')
const {
    model: NotifyCompanyInfoModel,
    RecordStatusEnum: RecordStatusEnum,
} = require('../../models/t_no_access_org_info')
/**
 * 通过通知的uuid 查询发送情况，
 * 若未返回发送结果，则直接推入下次查询，否则 则更新通知状态
 */
class CheckUMSSendStatus extends Common {
    constructor(params) {
        super(params)
        this.topics = ['CheckUMSSendStatus']
        this.type = 'icp-delay'
        this.name = 'CheckUMSSendStatus'
        this.init()
    }
    async handle(msg) {
        // Id：该条通知的记录Id, task_id：发送任务Id, Type:邮件/短信， send_time: 通知发送时间
        let { Id, task_id, Type, send_time, notifyType, BatchId, BatchOrgId } =
            this.parseContent(msg)
        let uid = uuid()
        logger.info(
            `${this.name}[${uid}] - start`,
            notifyType,
            msg.content.toString()
        )
        //查看有效的备案信息
        try {
            let result
            if (
                parseInt(moment().format('X')) - parseInt(send_time) >
                12 * 60 * 60
            ) {
                result = 'timeout'
            } else if (task_id) {
                if (Type === NotifyTypeEnum.Email) {
                    result = await checkSendingResult(task_id, 'EMAIL')
                } else {
                    result = await checkSendingResult(task_id, 'SMS')
                }
            } else {
                throw Error('CheckSmsEmailStatus need a id')
            }
            const isNoAccessType =
                notifyType &&
                notifyType === '已备案未接入通知类型' &&
                BatchOrgId &&
                BatchId
            if (result.toLowerCase() === 'success') {
                await this.changeTable(
                    Id,
                    SendStatusEnum.Sendfinish,
                    notifyType,
                    Type
                )
                if (isNoAccessType) {
                    await this.UpdateBatchOrgIdStatus(
                        BatchOrgId,
                        BatchId,
                        SendStatusEnum.Sendfinish
                    )
                }
            } else if (result.toLowerCase() === 'timeout') {
                // 超过12小时设置为超时未响应
                await this.changeTable(
                    Id,
                    SendStatusEnum.Timeout,
                    notifyType,
                    Type
                )
                if (isNoAccessType) {
                    await this.UpdateBatchOrgIdStatus(
                        BatchOrgId,
                        BatchId,
                        SendStatusEnum.Timeout
                    )
                }
            } else if (result.toLowerCase() === 'failed') {
                await this.changeTable(
                    Id,
                    SendStatusEnum.Recivefailed,
                    notifyType,
                    Type
                )
                if (isNoAccessType) {
                    await this.UpdateBatchOrgIdStatus(
                        BatchOrgId,
                        BatchId,
                        SendStatusEnum.Recivefailed
                    )
                }
            } else {
                // 继续查询
                await this.producer.send({
                    type: 'icp-delay',
                    topic: 'CheckUMSSendStatus',
                    data: {
                        send_time,
                        uidreadom: uuid(),
                        Id,
                        Type,
                        task_id,
                        notifyType,
                    },
                    opts: {
                        headers: { 'x-delay': 60 * 1000 }, // 延迟1分钟检查时间
                    },
                })
            }
        } catch (err) {
            logger.error(`${this.name}[${uid}] - error：${err}`)
        }
        logger.info(`${this.name}[${uid}] - finished`)
    }
    changeTable(Id, SendStatus, notifyType, Type) {
        if (notifyType && notifyType === '已备案未接入通知类型') {
            //更新已备案未接入的通知表t_no_access_notify
            let obj = {}
            if (Type === NotifyTypeEnum.Email) {
                obj['EmailStatus'] = SendStatus
            } else {
                obj['SmsStatus'] = SendStatus
            }
            return NoAccessNotifyModel.update(
                { ...obj },
                {
                    where: {
                        Id: Id,
                    },
                }
            )
        }
        return NotifyModel.update(
            { SendStatus },
            {
                where: {
                    Id,
                },
            }
        )
    }
    /**
     * 根据发送的BatchOrgId更新组织id的发送状态
     * @param {*} BatchOrgId 组织id
     * @param {*} BatchId 批次id
     */
    UpdateBatchOrgIdStatus(BatchOrgId, BatchId, Status) {
        //判断这个组织id下面的联系人是否全部通知完
        let newStatus = RecordStatusEnum.Sending
        try {
            //有发送成功
            if ([SendStatusEnum.Sendfinish].includes(Status)) {
                newStatus = RecordStatusEnum.Sendfinish
            }
            //有禁止发送
            if ([SendStatusEnum.Forbidden].includes(Status)) {
                newStatus = RecordStatusEnum.Forbidden
            }
            //有发送失败
            if (
                [
                    SendStatusEnum.Sendfailed,
                    SendStatusEnum.Recivefailed,
                ].includes(Status)
            ) {
                newStatus = RecordStatusEnum.Sendfailed
            }
            //有超时
            if (
                [SendStatusEnum.Timeout, SendStatusEnum.Trytosending].includes(
                    Status
                )
            ) {
                newStatus = RecordStatusEnum.Timeout
            }
            logger.info(
                'BatchOrgId---BatchId----newStatus----------',
                BatchOrgId,
                BatchId,
                newStatus
            )
            //更新组织id下面的通知状态,t_no_access_org_info表
            return NotifyCompanyInfoModel.update(
                { Status: newStatus },
                { where: { Id: BatchOrgId, BatchId: BatchId } }
            )
        } catch (e) {
            logger.error(
                `${this.name}[BatchId:${BatchId}] - UpdateBatchOrgIdStatus-error：${e}`
            )
        }
    }
}
const sleep = (millseconds = 3000) => {
    return new Promise((rs) => {
        setTimeout(() => rs(), millseconds)
    })
}

module.exports = new CheckUMSSendStatus()
