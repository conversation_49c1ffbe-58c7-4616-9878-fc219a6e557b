/*
 * @Author: william.qian <EMAIL>
 * @Date: 2023-08-21 15:18:32
 * @LastEditors: william.qian <EMAIL>
 * @LastEditTime: 2023-08-28 17:34:32
 * @FilePath: /newicp/mq/common/GetICPInfoAndSendToMIIT.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
const Common = require('../Common')
const GetICPInfoAndSendToMIIT = require('../../methods/admin/ICPInfo/GetICPInfoAndSendToMIIT')
const uuid = require('uuid/v4')
const logger = require('../../libs/logger').getLogger('mqCustomer')

class GetICPInfoAndSendToMIITConsumer extends Common {
    constructor(params) {
        super(params)
        this.topics = ['GetICPInfoAndSendToMIIT']
        this.type = 'icp'
        this.name = 'GetICPInfoAndSendToMIIT'
        this.init()
    }

    async handle(msg) {
        let uid = uuid()
        let self = this
        let producer = self.producer
        let redis = this.redis.get()

        let ICPMainNo

        logger.info(`${this.name}${uid}] - start`, msg.content.toString())
        let { BatchName } = this.parseContent(msg)

        try {
            ICPMainNo = await redis.spop(BatchName, 1)
            if (ICPMainNo === null || ICPMainNo.length === 0) {
                return
            } else {
                await producer.send({
                    type: 'icp',
                    topic: 'GetICPInfoAndSendToMIIT',
                    data: { BatchName },
                })
                ICPMainNo = ICPMainNo[0]
            }
        } catch (error) {
            logger.info(`${self.name}[${uid}] -  Error:` + error.toString)
            return
        }

        try {
            let getICPInfoAndSendToMIIT = new GetICPInfoAndSendToMIIT((RetCode, data) => {
                logger.info(`${this.name}[${uid}] - GetICPInfoAndSendToMIIT finish, the retcode is ${RetCode}, the data is ${JSON.stringify(data)}`)
            })

            await getICPInfoAndSendToMIIT.exec({ ICPMainNo, Source: 'MQ' })
            logger.info(`${this.name}[${uid}] - finish with success)`)
        } catch (error) {
            logger.error(
                `${this.name}[${uid}] - finish with error: ${error}`,
                msg.content.toString()
            )
        }
    }
}

module.exports = new GetICPInfoAndSendToMIITConsumer()
