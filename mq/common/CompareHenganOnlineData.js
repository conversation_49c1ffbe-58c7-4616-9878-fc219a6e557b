/*
 * @Date: 2022-11-03 10:23:18
 * @LastEditors: l<PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2022-12-07 15:21:27
 * @FilePath: /newicp/mq/common/CompareHenganOnlineData.js
 */
const Common = require('../Common')
const { getTableModel, parseJSON, csvParse } = require('../../fns/kits')
const henganApi = require('../../libs/henganApiPromise')
const { parse } = require('request/lib/cookies')
const _ = require('lodash')
const { Op } = require('sequelize')
const redis = require('../../libs/redis').get()
const moment = require('moment')
const logger = require('../../libs/logger').getLogger('mqCustomer')
const uuid = require('uuid/v4')
class CompareHenganOnlineData extends Common {
    constructor(params) {
        super(params)
        this.topics = ['CompareHenganOnlineData']
        this.type = 'icp'
        this.name = 'CompareHenganOnlineData'
        this.init()
    }
    async handle(msg) {
        let { BatchName } = this.parseContent(msg)
        let self = this
        let uid = uuid()
        logger.info(`${this.name}[${uid}] - start`, msg.content.toString())
        try {
            const icpDatabase = self.db.get('icp')
            const ICPModel = getTableModel('t_icp', icpDatabase)
            const ICPWeb = getTableModel('t_web', icpDatabase)
            let mongo = self.mongo.get('icp')
            //于巍提供恒安侧全部有效的网站备案号的CSV
            let InputInfo = await redis.hgetall('CompareHenganRedisIcpWebNo')
            InputInfo = parseJSON(InputInfo)
            //取出网站备案号
            const DataSourceA = Object.values(InputInfo)
            let ICPWebList = await ICPWeb.findAll({
                attributes: ['ICPWebNo', 'MainId'],
                where: {
                    Status: { [Op.ne]: 1 },
                },
            })
            ICPWebList = parseJSON(ICPWebList)
            let ICPIdList = await ICPModel.findAll({
                attributes: ['Id'],
                where: {
                    Status: { [Op.ne]: 1 },
                },
            })
            ICPIdList = parseJSON(ICPIdList)
            let DataSourceB = []
            ICPWebList.map((data) => {
                ICPIdList.map((item) => {
                    if (item.Id === data.MainId) {
                        DataSourceB.push(data.ICPWebNo)
                    }
                })
            })
            //1:A有B没有,给出数据，由于巍使用分配-》迁移同步功能,2:B有A没有，使用“同步”功能
            //A有B没
            const ResultInAMove = _.difference(DataSourceA, DataSourceB)
            //B有A没
            const ResultInBUpdate = _.difference(DataSourceB, DataSourceA)
            //把比对结果存到mongo中
            const logCollection = mongo.collection('compare_hengan_ucloud_data')
            logCollection.insertOne({
                BatchName: BatchName,
                CreateTime: moment().format('YYYY-MM-DD'),
                ResultInAMove: ResultInAMove,
                ResultInBUpdate: ResultInBUpdate,
                Remark: 'ResultInAMove代表恒安有线上没有，ResultInBUpdate代表线上有恒安没有',
                Type: 'CompareHenganOnlineData',
            })
            logger.info(`${this.name}[${uid}] - finish with success`)
        } catch (e) {
            logger.error(
                `${this.name}[${uid}] - finish with error: CompareHenganOnlineData出错: ${e}`,
                msg.content.toString()
            )
        }
    }
}
module.exports = new CompareHenganOnlineData()
