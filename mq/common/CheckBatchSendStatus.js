'use strict'

const Common = require('../Common')
const {
    model: NotifyModel,
    SendStatusEnum,
    NotifyTypeEnum,
} = require('../../models/t_notify')
const {
    model: RecordModel,
    RecordStatusEnum,
} = require('../../models/t_notify_companyInfo')
const {
    model: BatchModel,
    BatchStatusEnum,
} = require('../../models/t_notify_batch')
const { Op } = require('sequelize')
const _ = require('lodash')
const { parseJSON } = require('../../fns/kits')
const logger = require('../../libs/logger').getLogger('mqCustomer')
const uuid = require('uuid/v4')
const moment = require('moment')
/**
 * 通过通知的uuid 查询发送情况，
 * 若未返回发送结果，则直接推入下次查询，否则 则更新通知状态
 */
class CheckBatchSendStatus extends Common {
    constructor(params) {
        super(params)
        this.topics = ['CheckBatchSendStatus']
        this.type = 'icp-delay'
        this.name = 'CheckBatchSendStatus'
        this.init()
    }
    async handle(msg) {
        // Id：该条通知的记录Id, task_id：发送任务Id, Type:邮件/短信， send_time: 通知发送时间
        let { BatchId } = this.parseContent(msg)
        let uid = uuid()
        logger.info(`${this.name}[${uid}] - start`, msg.content.toString())
        //查看有效的备案信息
        try {
            let [batchInfo, records] = await Promise.all([
                BatchModel.findOne({
                    attributes: ['Id', 'Status'],
                    where: {
                        Id: BatchId,
                        Status: BatchStatusEnum.Sending,
                    },
                }),
                RecordModel.findAll({
                    attributes: ['Id', 'Status'],
                    where: {
                        BatchId,
                        IsError: false,
                        Status: RecordStatusEnum.Sending,
                    },
                }),
            ])
            batchInfo = parseJSON(batchInfo)
            records = parseJSON(records)

            if (!batchInfo || records.length === 0) {
                //没有发现该批次，或者批次中无记录
                logger.info(
                    `Do not meet the query conditions, BatchId: ${BatchId}`
                )
                return
            }
            // 获取批次下所有的通知记录
            let notifys = await NotifyModel.findAll({
                attributes: ['Id', 'CompanyNotifyId', 'SendStatus'],
                where: {
                    CompanyNotifyId: {
                        [Op.in]: _.map(records, 'Id'),
                    },
                },
            })
            notifys = parseJSON(notifys)
            // 过滤出还在发送中的
            let unsendRecords = _.filter(notifys, (notify) => {
                return [
                    SendStatusEnum.New,
                    SendStatusEnum.Sending,
                    SendStatusEnum.Trytosending,
                ].includes(notify.SendStatus)
            })
            let promiseArray = []
            if (unsendRecords.length === 0) {
                //没有还在发送中的，说明 所有都发送完成， 更新批次状态为发送完成
                promiseArray.push(
                    BatchModel.update(
                        { Status: BatchStatusEnum.SendFinish },
                        {
                            where: {
                                Id: BatchId,
                                Status: BatchStatusEnum.Sending,
                            },
                        }
                    )
                )
                // 批量处理记录的发送结果
                let sendFaildArray = []
                let timeoutArray = []
                let sendSuccessArray = []
                records.forEach((record) => {
                    const tmpNotify = _.filter(
                        notifys,
                        (notify) => notify.CompanyNotifyId === record.Id
                    )
                    const noSuccess = _.filter(
                        tmpNotify,
                        (notify) =>
                            SendStatusEnum.Sendfinish !== notify.SendStatus
                    )

                    const sendFailed = _.findIndex(tmpNotify, (notify) =>
                        [
                            SendStatusEnum.Sendfailed,
                            SendStatusEnum.Recivefailed,
                        ].includes(notify.SendStatus)
                    )

                    const timeout = _.findIndex(
                        tmpNotify,
                        (notify) => SendStatusEnum.Timeout === notify.SendStatus
                    )
                    if (noSuccess.length === 0) {
                        // 没有不成功的 就是发送全部成功
                        sendSuccessArray.push(record.Id)
                    } else if (sendFailed !== -1) {
                        sendFaildArray.push(record.Id)
                    } else if (timeout.length !== -1) {
                        timeoutArray.push(record.Id)
                    }
                })
                if (sendSuccessArray.length > 0) {
                    promiseArray.push(
                        RecordModel.update(
                            {
                                Status: RecordStatusEnum.Sendfinish,
                            },
                            {
                                where: {
                                    Id: {
                                        [Op.in]: sendSuccessArray,
                                    },
                                    Status: RecordStatusEnum.Sending,
                                },
                            }
                        )
                    )
                }
                if (timeoutArray.length > 0) {
                    promiseArray.push(
                        RecordModel.update(
                            { Status: RecordStatusEnum.Timeout },
                            {
                                where: {
                                    Id: {
                                        [Op.in]: timeoutArray,
                                    },
                                    Status: RecordStatusEnum.Sending,
                                },
                            }
                        )
                    )
                }
                if (sendFaildArray.length > 0) {
                    promiseArray.push(
                        RecordModel.update(
                            { Status: RecordStatusEnum.Sendfailed },
                            {
                                where: {
                                    Id: {
                                        [Op.in]: sendFaildArray,
                                    },
                                    Status: RecordStatusEnum.Sending,
                                },
                            }
                        )
                    )
                }
                // 批量处理状态
                if (promiseArray.length > 0) {
                    await Promise.all(promiseArray)
                    logger.info(`finished, BatchId: ${BatchId}`)
                    return
                }
            } else {
                // 判断是否已经通知超过12小时 若是 则重新推送mq查询状态，弥补mq丢失
                //晚5分钟后再查
                logger.info(
                    `Unfinished， 5 minutes retry check, BatchId: ${BatchId}`
                )
                await this.producer.send({
                    type: 'icp-delay',
                    topic: 'CheckBatchSendStatus',
                    data: {
                        BatchId,
                    },
                    opts: {
                        headers: { 'x-delay': 5 * 60 * 1000 }, // 延迟5分钟检查时间
                    },
                })
            }
        } catch (err) {
            logger.error(`${this.name}[${uid}] - error：${err}`)
        }
        logger.info(`${this.name}[${uid}] - finished`)
    }
}
const sleep = (millseconds = 3000) => {
    return new Promise((rs) => {
        setTimeout(() => rs(), millseconds)
    })
}

module.exports = new CheckBatchSendStatus()
