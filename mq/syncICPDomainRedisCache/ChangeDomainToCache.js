/*
 * @Date: 2023-02-15 15:25:17
 * @LastEditors: l<PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2023-03-23 17:56:01
 * @FilePath: /newicp/mq/syncICPDomainRedisCache/ChangeDomainToCache.js
 */
/**
 * 将本地 redis和hengan 域名备案情况对比后
 * 发现本地的域名和备案号 和恒安不一致
 * 需要调整 缓存的备案信息 的处理
 * 傲顿接口不支持全量的数据查询，相关定时任务下架，此mq暂时无用武之地
 */

'use strict'
const { parseJSON } = require('../../fns/kits')
const { Op } = require('sequelize')
const axiosAPI = require('../../libs/axiosApi')
const ICPSyncFromAodun = require('../../methods/common/ICPSyncFromAodun')
const henganApi = require('../../libs/henganApiPromise')
const logger = require('../../libs/logger').getLogger('mqCustomer')
const Common = require('../Common')
const uuid = require('uuid/v4')
const _ = require('lodash')

class ChangeDomainToCache extends Common {
    constructor(params) {
        super(params)
        this.topics = ['ChangeDomainToCache']
        this.type = 'icp'
        this.init()
        this.name = 'ChangeDomainToCache'
    }
    async handle(msg) {
        let {
            Domain,
            NewICPWebNo,
            OldICPWebNo,
            retry_times = 0,
        } = this.parseContent(msg)
        let uid = uuid()
        const producer = this.producer
        const redisClient = this.redis.get()
        logger.info(`${this.name}[${uid}] - start`, msg.content.toString())
        try {
            let NewICPMainNo = NewICPWebNo?.split('-')[0]
            let OldICPMainNo = OldICPWebNo?.split('-')[0]
            // step 1：记录老的备案号 执行数据库的同步更新
            // step 2：再做mysql db的同步 新的备案号 做同步
            let method = new ICPSyncFromAodun((RetCode, data) => {
                if (RetCode !== 0) logger.error('ChangeDomainToCache 同步傲盾失败 ' + data?.Message || '')
            })
            await method.exec({
                ICPMainNo: NewICPMainNo,
                __ssoUser: 'ChangeDomainToCache Operator',
            })
            // await Promise.all([
            //     henganApi('ICPSyncFromHengAn', {
            //         ICPMainNo: NewICPMainNo,
            //         timeout: 60 * 1000,
            //     }),
            //     henganApi('ICPPictureSyncFromHengAn', {
            //         ICPMainNo: NewICPMainNo,
            //         timeout: 60 * 1000,
            //     }),
            // ])
            // step 3: 同步原备案号
            await method.exec({
                ICPMainNo: OldICPMainNo,
                __ssoUser: 'ChangeDomainToCache Operator',
            })
            // await Promise.all([
            //     henganApi('ICPSyncFromHengAn', {
            //         ICPMainNo: OldICPMainNo,
            //         timeout: 60 * 1000,
            //     }),
            //     henganApi('ICPPictureSyncFromHengAn', {
            //         ICPMainNo: OldICPMainNo,
            //         timeout: 60 * 1000,
            //     }),
            // ])
            // step 4：存在该备案，更新到缓存中   ---最后同步缓存， 防止前面出错后，下次的查询查不出来，不同步
            await redisClient.hmset('domain_icp_in_ucloud_map', {
                [Domain]: NewICPWebNo,
            })
        } catch (err) {
            if (retry_times < 3) {
                logger.error(`${this.name}[${uid}] - retry`, err.message)
                await producer.send({
                    type: 'icp',
                    topic: 'ChangeDomainToCache',
                    data: {
                        Domain,
                        NewICPWebNo,
                        OldICPWebNo,
                        retry_times: retry_times + 1,
                    },
                })
            } else {
                logger.error(`${this.name}[${uid}] - failed`, err.message)
            }
        }
        logger.info(`${this.name}[${uid}] - finished`)
    }
}

module.exports = new ChangeDomainToCache()
