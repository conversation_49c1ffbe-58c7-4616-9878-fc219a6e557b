/**
 * 将本地 redis和hengan 域名备案情况对比后
 * 发现本地的备案的域名 不在恒安已备案数据里
 * 需要 从本地缓存 删除的备案信息 的处理
 * 如果 从恒安 再查一遍 恒安有备案 则对比一下 备案号
 */
/*
 * @Date: 2023-02-15 15:25:17
 * @LastEditors: li<PERSON>ew<PERSON> <EMAIL>
 * @LastEditTime: 2023-03-29 10:29:01
 * @FilePath: /newicp/mq/syncICPDomainRedisCache/DelDomainFromCache.js
 */

'use strict'
const { parseJSON, sleep } = require('../../fns/kits')
const { Op } = require('sequelize')
const axiosAPI = require('../../libs/axiosApi')
const henganApi = require('../../libs/henganApiPromise')
const logger = require('../../libs/logger').getLogger('mqCustomer')
const Common = require('../Common')
const uuid = require('uuid/v4')
const _ = require('lodash')

class DelDomainFromCache extends Common {
    constructor(params) {
        super(params)
        this.topics = ['DelDomainFromCache']
        this.type = 'icp'
        this.init()
        this.name = 'DelDomainFromCache'
    }
    async handle(msg) {
        await sleep(1000)
        let { Domain, retry_times = 0 } = this.parseContent(msg)
        let uid = uuid()
        const producer = this.producer
        const redisClient = this.redis.get()
        logger.info(`${this.name}[${uid}] - start`, msg.content.toString())
        try {
            let allHenganRes = await henganApi('SelectICPInfoAction', {
                KeyWordType: 4, //  按照域名搜索
                Keyword: Domain,
                QueryType: 2,
                timeout: 60 * 1000,
            })
            if (allHenganRes?.ICPInfos?.length === 0) {
                // 不存在该备案，从缓存中删除
                await redisClient.hdel('domain_icp_in_ucloud_map', Domain)
            }
        } catch (err) {
            if (retry_times < 3) {
                logger.error(`${this.name}[${uid}] - retry`, err.message)
                await producer.send({
                    type: 'icp',
                    topic: 'DelDomainFromCache',
                    data: {
                        Domain,
                        retry_times: retry_times + 1,
                    },
                })
            } else {
                logger.error(`${this.name}[${uid}] - failed`, err.message)
            }
        }
        logger.info(`${this.name}[${uid}] - finished`)
    }
}

module.exports = new DelDomainFromCache()
