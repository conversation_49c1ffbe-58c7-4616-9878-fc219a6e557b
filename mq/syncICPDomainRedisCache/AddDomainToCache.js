/*
 * @Date: 2023-02-15 15:25:17
 * @LastEditors: l<PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2023-03-23 17:55:53
 * @FilePath: /newicp/mq/syncICPDomainRedisCache/AddDomainToCache.js
 */
/**
 * 将本地 redis和hengan 域名备案情况对比后
 * 查看恒安 该域名的信息 然后
 * 需要在 本地缓存 新增信息的处理
 * 傲顿接口不支持全量的数据查询，相关定时任务下架，此mq暂时无用武之地
 */

'use strict'
const logger = require('../../libs/logger').getLogger('mqCustomer')
const ICPSyncFromAodun = require('../../methods/common/ICPSyncFromAodun')
const henganApi = require('../../libs/henganApiPromise')
const Common = require('../Common')
const uuid = require('uuid/v4')
const _ = require('lodash')
class AddDomainToCache extends Common {
    constructor(params) {
        super(params)
        this.topics = ['AddDomainToCache']
        this.type = 'icp'
        this.init()
        this.name = 'AddDomainToCache'
    }
    async handle(msg) {
        let { Domain, ICPWebNo, retry_times = 0 } = this.parseContent(msg)
        let uid = uuid()
        const producer = this.producer
        const redisClient = this.redis.get()
        logger.info(`${this.name}[${uid}] - start`, msg.content.toString())
        try {
            let ICPMainNo = ICPWebNo?.split('-')[0]
            // step 2:再做mysql db的同步 将该备案同步到db
            let method = new ICPSyncFromAodun((RetCode, data) => {
                if (RetCode !== 0) logger.error('AddDomainToCache 同步傲盾失败 ' + data?.Message || '')
            })
            await method.exec({
                ICPMainNo,
                __ssoUser: 'AddDomainToCache Operator',
            })
            // await Promise.all([
            //     henganApi('ICPSyncFromHengAn', {
            //         ICPMainNo,
            //         timeout: 60 * 1000,
            //     }),
            //     henganApi('ICPPictureSyncFromHengAn', {
            //         ICPMainNo,
            //         timeout: 60 * 1000,
            //     }),
            // ])
            await redisClient.hmset('domain_icp_in_ucloud_map', {
                [Domain]: ICPWebNo,
            })
        } catch (err) {
            if (retry_times < 3) {
                logger.error(`${this.name}[${uid}] - retry`, err.message)
                await producer.send({
                    type: 'icp',
                    topic: 'AddDomainToCache',
                    data: {
                        Domain,
                        ICPWebNo,
                        retry_times: retry_times + 1,
                    },
                })
            } else {
                logger.error(`${this.name}[${uid}] - failed`, err.message)
            }
        }
        logger.info(`${this.name}[${uid}] - finished`)
    }
}

module.exports = new AddDomainToCache()
