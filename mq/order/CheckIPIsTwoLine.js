/* 检查网站订单是否是三线IP 若是三线IP 则记录该IP 并将网站中IP替换为指定IP
 * 订单展示时，若网站订单 有我司的固定IP 则将其替换为 该网站记录的三线IP
 * 在订单处 写日志 记录有IP检查的这个过程
 * @Date: 2022-10-31 10:47:50
 * @LastEditors: liyuewen <EMAIL>
 * @LastEditTime: 2022-11-07 18:25:10
 * @FilePath: /newicp/mq/unConnNotify/AddDomainToWhiteList.js
 */
'use strict'
const { checkIPInSeg, parseJSON } = require('../../fns/kits')
const { OrderWebTwoLineModel, OrderWebModel } = require('../../models')
const logger = require('../../libs/logger').getLogger('mqCustomer')
const axiosApi = require('../../libs/axiosApi')
const Common = require('../Common')
const uuid = require('uuid/v4')
const _ = require('lodash')
const qs = require('qs')
class CheckIPIsTwoLine extends Common {
    constructor(params) {
        super(params)
        this.topics = ['CheckIPIsTwoLine']
        this.type = 'icp'
        this.init()
        this.name = 'CheckIPIsTwoLine'
    }
    async handle(msg) {
        let { OrderNo, retry_times = 0 } = this.parseContent(msg)
        let uid = uuid()
        const producer = this.producer
        logger.info(`${this.name}[${uid}] - start`, msg.content.toString())
        try {
            // 获取 嘉兴 和 东莞的三线IP段
            // 城市=上海 + （供应商=杭州优云科技有限公司） + IP段层级=2级段
            // 城市=广州 + （供应商=唯一网络） + IP段层级=2级段
            // 来分别过滤东莞和嘉兴的IP
            let jiaXingConfig = {
                url: `http://api-gw.ucloudadmin.com/ipdb/public/ipseg?kind=0&perPage=10000&level=[1]&status=[0,1,2]&supplier=[${encodeURI(
                    '"杭州优云科技有限公司"'
                )}]&city=[${encodeURI('"上海"')}]`,
                method: 'GET',
                headers: {
                    'content-type': 'application/json',
                    'api-key': '7e4baae4c1df463ca3cff4ed5b3685b0',
                },
            }
            let dongGuanConfig = {
                url: `http://api-gw.ucloudadmin.com/ipdb/public/ipseg?kind=0&perPage=10000&level=[1]&status=[0,1,2]&supplier=[${encodeURI(
                    '"唯一网络"'
                )}]&city=[${encodeURI('"广州"')}]`,
                method: 'GET',
                headers: {
                    'content-type': 'application/json',
                    'api-key': '7e4baae4c1df463ca3cff4ed5b3685b0',
                },
            }

            let [dongGuanRes, jiaXingRes] = await Promise.all([
                axiosApi(dongGuanConfig),
                axiosApi(jiaXingConfig),
            ])
            let IPSegs = []
            dongGuanRes?.data?.data?.forEach((info) => {
                if (info.ipseg) {
                    IPSegs.push(info.ipseg)
                }
            })
            jiaXingRes?.data?.data?.forEach((info) => {
                if (info.ipseg) {
                    IPSegs.push(info.ipseg)
                }
            })
            // 有可备案的二线IP网段
            let twoLineIps = []
            if (IPSegs.length > 0) {
                // 查看订单中所有的网站信息
                let orderWebs = await OrderWebModel.findAll({
                    where: { OrderNo },
                    attributes: ['Id', 'IP'],
                    include: [
                        {
                            model: OrderWebTwoLineModel,
                            as: 'TwoLineIPs',
                            attributes: ['Id', 'IP'],
                        },
                    ],
                })
                orderWebs = parseJSON(orderWebs)
                for (let orderWeb of orderWebs) {
                    let IPs = orderWeb.IP
                    let OrderWebId = orderWeb.Id
                    for (let ip of IPs) {
                        let isTwoLineIp = checkIPInSeg(IPSegs, ip)
                        if (isTwoLineIp) {
                            twoLineIps.push(ip)
                        }
                    }
                    // 如果 网站中包含二线IP
                    // 1.更新网站的二线IP为我司的固定IP
                    // 2.将二线IP 写入 该网站对应的表里
                    if (twoLineIps.length > 0) {
                        let oprIP = _.difference(IPs, twoLineIps).concat([
                            global.CONFIG.RepliceTwoIP,
                        ])
                        let awaitInsert = []
                        if (orderWeb?.TwoLineIPs?.IP?.length > 0) {
                            // 原来有 更新
                            awaitInsert.push(
                                OrderWebTwoLineModel.update(
                                    { IP: twoLineIps },
                                    {
                                        where: {
                                            Id: orderWeb?.TwoLineIPs?.Id,
                                        },
                                    }
                                )
                            )
                        } else {
                            // 原来无 插入
                            awaitInsert.push(
                                OrderWebTwoLineModel.create({
                                    OrderWebId,
                                    IP: twoLineIps,
                                })
                            )
                        }
                        // 更新OrderWeb表 方便 最后真的提交到审核时使用
                        await Promise.all([
                            OrderWebModel.update(
                                { IP: oprIP },
                                {
                                    where: {
                                        Id: OrderWebId,
                                    },
                                }
                            ),
                            awaitInsert,
                        ])
                    } else {
                        // 若没有三线IP 则防止之前变更时提交过 做一遍清除
                        await OrderWebTwoLineModel.destroy({
                            where: {
                                OrderWebId,
                            },
                        })
                    }
                }
            }
        } catch (err) {
            if (retry_times < 3) {
                logger.info(`${this.name}[${uid}] - retry`, err.message)
                return producer.send({
                    type: 'icp',
                    topic: 'CheckIPIsTwoLine',
                    data: {
                        OrderNo,
                        retry_times: retry_times + 1,
                    },
                })
            } else {
                logger.error(`${this.name}[${uid}] - failed`, err.message)
            }
        }
        logger.info(`${this.name}[${uid}] - finished`)
    }
}

module.exports = new CheckIPIsTwoLine()
