/* 订单审核通过后
 * 看订单的网站中是否包含，二线IP， 若包含，则将该IP记录到该 备案网站所对应的表中
 * 若该备案网站原来 对应的表中有二线IP ，则更新该记录（可能是之前备案的IP）
 * 若无（之前未使用二线IP，或者首次备案）， 则插入对应的数据
 * 若原来使用了 二线IP，此次未使用二线IP ，则删除对应的数据（网站关联的二线IP表的数据）
 * @Date: 2023-03-20 11:41:30
 * @LastEditors: li<PERSON>ewen <EMAIL>
 * @LastEditTime: 2023-03-20 13:44:44
 * @FilePath: /newicp/mq/order/UpdateTwoLineIPsToICP.js
 */

'use strict'
const { parseJSON } = require('../../fns/kits')
const {
    OrderWebTwoLineModel,
    OrderTypeEnum,
    OrderWebModel,
    ICPWebModel,
    ICPWebTwoLineModel,
} = require('../../models')
const { Op } = require('sequelize')
const logger = require('../../libs/logger').getLogger('mqCustomer')
const Common = require('../Common')
const uuid = require('uuid/v4')
const _ = require('lodash')
class UpdateTwoLineIPsToICP extends Common {
    constructor(params) {
        super(params)
        this.topics = ['UpdateTwoLineIPsToICP']
        this.type = 'icp'
        this.init()
        this.name = 'UpdateTwoLineIPsToICP'
    }
    async handle(msg) {
        let { OrderNo, OrderType, retry_times = 0 } = this.parseContent(msg)
        let uid = uuid()
        const producer = this.producer
        logger.info(`${this.name}[${uid}] - start`, msg.content.toString())
        try {
            // 对于注销类的不处理
            if (
                ![
                    OrderTypeEnum.CancelConn,
                    OrderTypeEnum.CancelMain,
                    OrderTypeEnum.CancelWeb,
                ].includes(parseInt(OrderType))
            ) {
                let orderWebs = await OrderWebModel.findAll({
                    where: {
                        OrderNo,
                        IsDeleted: 0,
                    },
                    attributes: ['Id', 'ICPWebNo'],
                    include: [
                        {
                            model: OrderWebTwoLineModel,
                            as: 'TwoLineIPs',
                            attributes: ['IP'],
                        },
                    ],
                })
                orderWebs = parseJSON(orderWebs)
                // 记录网站备案号 和二线IP的映射，根据备案号，找到有效的网站，就知道网站ID了，即可实现网站和二线IP的映射
                let ICPWebNos = {}
                // 记录此次没有使用二线IP备案，则执行一次删除，防止 之前用二线IP备案过，影响数据展示
                let awaitDeleteIPSMapIds = []
                orderWebs.forEach((info) => {
                    if (info.TwoLineIPs?.IP.length > 0) {
                        // 网站中包含二线IP 将二线IP 写入网站的备案信息中
                        if (ICPWebNos[info.ICPWebNo] === undefined) {
                            ICPWebNos[info.ICPWebNo] = info.TwoLineIPs?.IP
                        }
                    } else {
                        awaitDeleteIPSMapIds.push(info.Id)
                    }
                })
                if (Object.keys(ICPWebNos).length > 0) {
                    // 存在二线IP 查找对应的网站id 更新或插入 二线IP的映射关系
                    let icpwebs = await ICPWebModel.findAll({
                        where: {
                            Status: 0,
                            ICPWebNo: {
                                [Op.in]: Object.keys(ICPWebNos),
                            },
                        },
                        attributes: ['Id', 'ICPWebNo'],
                        include: [
                            {
                                model: ICPWebTwoLineModel,
                                as: 'TwoLineIPs',
                                attributes: ['Id', 'IP'],
                            },
                        ],
                    })
                    icpwebs = parseJSON(icpwebs)
                    let awaitExecute = []
                    if (icpwebs.length > 0) {
                        icpwebs.forEach((info) => {
                            if (info.TwoLineIPs === null) {
                                // 插入
                                awaitExecute.push(
                                    ICPWebTwoLineModel.create({
                                        ICPWebId: info.Id,
                                        IP: ICPWebNos[info.ICPWebNo],
                                    })
                                )
                            } else {
                                // 更新
                                awaitExecute.push(
                                    ICPWebTwoLineModel.update(
                                        { IP: ICPWebNos[info.ICPWebNo] },
                                        {
                                            where: {
                                                Id: info.TwoLineIPs.Id,
                                            },
                                        }
                                    )
                                )
                            }
                        })
                        await Promise.all(awaitExecute)
                    }
                } else {
                    // 不存在 二线IP的使用，执行一次相关的删除
                    let icpwebs = await ICPWebModel.findAll({
                        where: {
                            Status: 0,
                            ICPWebNo: {
                                [Op.in]: Object.keys(ICPWebNos),
                            },
                        },
                        attributes: ['Id'],
                    })
                    icpwebs = parseJSON(icpwebs)

                    if (icpwebs.length > 0) {
                        await ICPWebTwoLineModel.destroy({
                            where: {
                                ICPWebId: {
                                    [Op.in]: icpwebs.map((web) => web.Id),
                                },
                            },
                        })
                    }
                }
            }
        } catch (err) {
            if (retry_times < 3) {
                logger.error(`${this.name}[${uid}] - retry`, err.message)
                await producer.send({
                    type: 'icp',
                    topic: 'UpdateTwoLineIPsToICP',
                    data: {
                        OrderNo,
                        OrderType,
                        retry_times: retry_times + 1,
                    },
                })
            } else {
                logger.error(`${this.name}[${uid}] - failed`, err.message)
            }
        }
        logger.info(`${this.name}[${uid}] - finished`)
    }
}

module.exports = new UpdateTwoLineIPsToICP()
