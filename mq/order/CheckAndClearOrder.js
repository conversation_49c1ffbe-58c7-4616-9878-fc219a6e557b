/*
 * @Date: 2023-05-17 15:39:10
 * @LastEditors: li<PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2023-05-18 16:56:16
 * @FilePath: /newicp/mq/order/CheckAndClearOrder.js
 */
/* 检查网站订单是否是三线IP 若是三线IP 则记录该IP 并将网站中IP替换为指定IP
 * 订单展示时，若网站订单 有我司的固定IP 则将其替换为 该网站记录的三线IP
 * 在订单处 写日志 记录有IP检查的这个过程
 * @Date: 2022-10-31 10:47:50
 * @LastEditors: liyuewen <EMAIL>
 * @LastEditTime: 2022-11-07 18:25:10
 * @FilePath: /newicp/mq/unConnNotify/AddDomainToWhiteList.js
 */
'use strict'
const { checkIPInSeg, parseJSON } = require('../../fns/kits')
const {
    OrderModel,
    ClearOrderBatchModel,
    ClearBatchStatusEnum,
    ClearOrderRecordModel,
    ClearRecordStatusEnum,
} = require('../../models')
const moment = require('moment')
const logger = require('../../libs/logger').getLogger('mqCustomer')
const DeleteICPOrder = require('../../methods/admin/OrderList/DeleteOrder')
const axiosApi = require('../../libs/axiosApi')
const Common = require('../Common')
const uuid = require('uuid/v4')
const _ = require('lodash')
class CheckAndClearOrder extends Common {
    constructor(params) {
        super(params)
        this.topics = ['CheckAndClearOrder']
        this.type = 'icp'
        this.init()
        this.name = 'CheckAndClearOrder'
    }
    async handle(msg) {
        let { BatchId } = this.parseContent(msg)
        let uid = uuid()
        const producer = this.producer
        const redis = this.redis.get()
        logger.info(`${this.name}[${uid}] - start`, msg.content.toString())
        let record
        try {
            let limitDate = moment().subtract('6', 'months').format('X')
            record = await redis.spop(`ClearOrder_${BatchId}`)
            if (record) {
                record = JSON.parse(record)
                let order = await OrderModel.findOne({
                    where: {
                        OrderNo: record.OrderNo,
                    },
                    attributes: ['UpdateTime', 'IsDeleted'],
                })
                order = parseJSON(order)
                let status
                if (order.IsDeleted) {
                    // 已删除
                    status = ClearRecordStatusEnum.deleted
                } else if (order.UpdateTime > limitDate) {
                    // 已更新
                    status = ClearRecordStatusEnum.updated
                } else {
                    // 未更新 系统执行删除
                    status = ClearRecordStatusEnum.systemDeleted

                    // 调用删除接口
                    let res
                    let method = new DeleteICPOrder((retCode, data) => {
                        if (retCode === 0) {
                            res = 0
                        } else {
                            res = 1
                        }
                    })
                    await method.exec({
                        OrderNo: record.OrderNo,
                        staff_name_en: 'System.Auto',
                    })
                    if (res !== 0) {
                        throw new Error('订单删除出错')
                    }
                }
                await ClearOrderRecordModel.update(
                    {
                        Status: status,
                    },
                    {
                        where: {
                            Id: record.Id,
                            Status: ClearRecordStatusEnum.awaitClear,
                        },
                    }
                )
                producer.send({
                    type: 'icp',
                    topic: 'CheckAndClearOrder',
                    data: {
                        BatchId,
                    },
                })
            } else {
                await ClearOrderBatchModel.update(
                    {
                        Status: ClearBatchStatusEnum.cleared,
                    },
                    {
                        where: {
                            Id: BatchId,
                        },
                    }
                )
            }
        } catch (err) {
            await redis.sadd(
                `ClearOrder_${BatchId}_Error`,
                JSON.stringify(record)
            )
            logger.error(`${this.name}[${uid}] - failed`, err.message)
        }
        logger.info(`${this.name}[${uid}] - finished`)
    }
}

module.exports = new CheckAndClearOrder()
