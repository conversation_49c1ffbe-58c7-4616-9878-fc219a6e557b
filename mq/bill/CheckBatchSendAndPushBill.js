/*
 * @Date: 2023-02-22 15:39:06
 * @LastEditors: l<PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2023-04-03 14:08:04
 * @FilePath: /newicp/mq/bill/CheckBatchSendAndPushBill.js
 */
'use strict'

/**检查批次发送状态，并在发送完成后 推送扣费订单
 */
const Common = require('../Common')
const {
    model: RecordModel,
    RecordStatusEnum: SendStatusEnum,
} = require('../../models/t_notify_companyInfo')
const {
    model: NotifyBatchModel,
    BatchStatusEnum,
    TypeEnum: BatchTypeEnum,
} = require('../../models/t_notify_batch')
const PostBill = require('../../methods/admin/Bill/PostBill')
const { parseJSON } = require('../../fns/kits')
const logger = require('../../libs/logger').getLogger('mqCustomer')
const uuid = require('uuid/v4')
/* 检查批次是否发送完毕
 * 发送完毕 就推送账单扣费
 * @class
 * @extent Common
 */
class CheckBatchSendAndPushBill extends Common {
    constructor(params) {
        super(params)
        this.topics = ['CheckBatchSendAndPushBill']
        this.type = 'icp-delay'
        this.init()
        this.name = 'CheckBatchSendAndPushBill'
    }
    async handle(msg) {
        await sleep(1000)
        let self = this
        /**
         * BatchId 通知成功后 可以推送扣费
         * BillId 账单Id
         */
        let { BatchId, BillInfos } = this.parseContent(msg)
        let uid = uuid()
        const producer = this.producer
        logger.info(`${this.name}[${uid}] - start`, msg.content.toString())

        // 创建收费通知批次
        try {
            // 创建批次
            let batchInfo = await NotifyBatchModel.findOne({
                where: {
                    Id: BatchId,
                    Status: BatchStatusEnum.SendFinish,
                },
            })
            batchInfo = parseJSON(batchInfo)
            if (batchInfo === null) {
                // 延迟再检查
                this.producer.send({
                    type: 'icp-delay',
                    topic: 'CheckBatchSendAndPushBill',
                    data: {
                        BatchId,
                        BillInfos,
                    },
                    opts: {
                        headers: { 'x-delay': 60 * 1000 }, // 延迟1分钟检查时间
                    },
                })
            } else {
                let poseBillArr = []
                for (let info of BillInfos) {
                    let record = await RecordModel.findOne({
                        where: {
                            BatchId,
                            CompanyId: info.CompanyId,
                        },
                    })
                    if (record !== null) {
                        record = parseJSON(record)
                        if (record.Status === SendStatusEnum.Sendfinish) {
                            let method = new PostBill((retCode, data) => {
                                if (retCode !== 0) {
                                    // 推送失败 请手动推送
                                }
                            })
                            poseBillArr.push(
                                method.exec({
                                    Id: info.BillId,
                                    __ssoUser: 'system.auto',
                                })
                            )
                        }
                    }
                }
                await Promise.all(poseBillArr)
            }
        } catch (error) {
            // 批次生成失败,标记原因，以及 从redis中删除该公司Id
            logger.error(
                `${self.name}[${uid}] - 推送账单 failed`,
                {
                    BillInfos,
                    Reason: '推送账单',
                },
                error.message
            )
        }
        logger.info(`${self.name}[${uid}] - finished`)
    }
}

const sleep = (millseconds = 3000) => {
    return new Promise((rs) => {
        setTimeout(() => rs(), millseconds)
    })
}

module.exports = new CheckBatchSendAndPushBill()
