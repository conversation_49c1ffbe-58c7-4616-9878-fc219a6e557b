'use strict'

/**每月自动生成计费订单后，
 * 生成账单通知批次，并自定义附件内容
 */
const Common = require('../Common')
const { model: NotifyModel, NotifyTypeEnum } = require('../../models/t_notify')
const {
    model: RecordModel,
    StatusEnum: SendStatusEnum,
} = require('../../models/t_notify_companyInfo')
const {
    model: NotifyBatchModel,
    BatchStatusEnum,
    TypeEnum: BatchTypeEnum,
} = require('../../models/t_notify_batch')
const axios = require('../../libs/axiosApi')
const { parseJSON } = require('../../fns/kits')
const { uploadFile } = require('../../fns/uflieFuns')
const json2csv = require('json2csv').parse
const _ = require('lodash')
const logger = require('../../libs/logger').getLogger('mqCustomer')
const { ObjectId } = require('mongoose').Types
const uuid = require('uuid/v4')
const moment = require('moment')
/* 创建收费通知批次
 * 根据类型区分批次信息，若有多个公司需要通知，则在cron时一起推送过来
 * 会再record中生成对于公司的通知记录
 * 根据计费账单设计，一个公司一个账单
 * @class
 * @extent Common
 */
class CreateBillNotifyBatch extends Common {
    constructor(params) {
        super(params)
        this.topics = ['CreateBillNotifyBatch']
        this.type = 'icp'
        this.init()
        this.name = 'CreateBillNotifyBatch'
    }
    async handle(msg) {
        await sleep(1000)
        let self = this
        let redis = this.redis.get()
        let mongo = this.mongo.get('icp')
        const billListCollection = mongo.collection('t_bill_list')
        const billDetailsCollection = mongo.collection('t_bill_details')
        /**
         * 一个类型中 前置推送过来保证公司ID唯一
         * BillInfos [{CompanyId: 121, Notify: {EMails: [xxx], Phones: [xxx]}， BillId: xxx}]
         * BillType 计费类型 域名查询 或者备案订单 等
         * Type 通知类型
         */
        let { BillInfos, Type, BillType } = this.parseContent(msg)
        let uid = uuid()
        logger.info(`${this.name}[${uid}] - start`, msg.content.toString())

        // 创建收费通知批次
        try {
            // 创建批次
            let batchInfo = await NotifyBatchModel.create({
                Remark: BillType + moment().format('YYYY-MM-DD'),
                Type,
                Status: BatchStatusEnum.Parsing,
            })
            batchInfo = parseJSON(batchInfo)
            // 含有附件，将附件信息 整理上传至US3
            let attachMaps = await GetAttachment(
                billListCollection,
                billDetailsCollection,
                BillInfos
            )
            // 整理批次记录信息
            let recordInfos = _.map(BillInfos, (info) => {
                return {
                    CompanyId: info.CompanyId,
                    BatchId: batchInfo.Id,
                    AttachFiles: attachMaps[info.CompanyId],
                }
            })

            // 创建批次记录
            let companyInfos = await RecordModel.bulkCreate(recordInfos, {
                ignoreDuplicates: true,
            })
            companyInfos = parseJSON(companyInfos)
            // 更新收费订单 所对应的通知记录id
            let updateBillArr = []
            for (let i = 0; i < companyInfos.length; i++) {
                updateBillArr.push(
                    billListCollection.findOneAndUpdate(
                        { _id: ObjectId(BillInfos[i].BillId) },
                        {
                            $set: {
                                NotifyRecordId: companyInfos[i].Id,
                            },
                        }
                    )
                )
            }
            await Promise.all(updateBillArr)
            // 整理通知人信息
            let notifyInfos = []
            for (let i = 0; i < companyInfos.length; i++) {
                let CompanyNotifyId = companyInfos[i].Id
                // 获取该公司 指定的通知人信息
                let notifys = _.find(BillInfos, [
                    'CompanyId',
                    recordInfos[i].CompanyId,
                ])
                if (notifys) {
                    if (notifys.Notify?.Emails?.length > 0) {
                        notifys.Notify?.Emails.forEach((info) => {
                            notifyInfos.push({
                                Contact: info.trim(),
                                Type: NotifyTypeEnum.Email,
                                CompanyNotifyId,
                            })
                        })
                    }
                    if (notifys.Notify?.Phones?.length > 0) {
                        notifys.Notify?.Phones.forEach((info) => {
                            notifyInfos.push({
                                Contact: info.trim(),
                                Type: NotifyTypeEnum.Phone,
                                CompanyNotifyId,
                            })
                        })
                    }
                }
            }
            // 插入通知人信息
            if (notifyInfos.length > 0) {
                await NotifyModel.bulkCreate(notifyInfos, {
                    ignoreDuplicates: true,
                })
            }
            // 将需要查询的所有公司信息 写入redis
            let CompanyIds = _.map(recordInfos, 'CompanyId')
            await redis.sadd(`${batchInfo.Id}_notify_companyInfo`, CompanyIds)
            // 推送mq查询公司信息
            for (let i = 0; i < CompanyIds.length; i++) {
                await this.producer.send({
                    type: 'icp',
                    topic: 'GetCompanyInfo',
                    data: {
                        BatchId: batchInfo.Id,
                        CompanyId: CompanyIds[i],
                        CompanyNotifyId: companyInfos[i].Id,
                        NotNotifyMainNo: true, // 设置不通知主账号邮箱
                    },
                })
            }
            await Promise.all([
                // 推送检查批次解析状态mq，在检查推送完成后进行自动通知
                this.producer.send({
                    type: 'icp-delay',
                    topic: 'NotifyUsedBill',
                    data: {
                        BatchId: batchInfo.Id,
                    },
                    opts: {
                        headers: { 'x-delay': 60 * 1000 }, // 延迟1分钟检查时间
                    },
                }),
                // 推送批次发送状态检查，发送完成后执行扣费
                this.producer.send({
                    type: 'icp-delay',
                    topic: 'CheckBatchSendAndPushBill',
                    data: {
                        BatchId: batchInfo.Id,
                        BillInfos,
                    },
                    opts: {
                        headers: { 'x-delay': 60 * 1000 }, // 延迟1分钟检查时间
                    },
                }),
            ])
        } catch (error) {
            // 批次生成失败,标记原因，以及 从redis中删除该公司Id
            logger.error(
                `${self.name}[${uid}] - 自动生成账单通知批次 failed`,
                {
                    BillInfos,
                    Type,
                    Reason: '自动生成账单通知批次失败',
                },
                error.message
            )
        }
        logger.info(`${self.name}[${uid}] - finished`)
    }
}

const sleep = (millseconds = 3000) => {
    return new Promise((rs) => {
        setTimeout(() => rs(), millseconds)
    })
}
/**
 * 将附件内容整理 并上传至US3生成文件名
 */
async function GetAttachment(
    billListCollection,
    billDetailsCollection,
    BillInfos
) {
    let attachMaps = {}
    for (let info of BillInfos) {
        let [billBatchInfo, billDetailInfo] = await Promise.all([
            billListCollection
                .find({
                    _id: ObjectId(info.BillId),
                })
                .toArray(),
            billDetailsCollection
                .find(
                    {
                        BillId: ObjectId(info.BillId),
                    },
                    {
                        projection: {
                            Count: 1,
                            BillName: 1,
                            TriggerTime: 1,
                            UId: '_id',
                        },
                    }
                )
                .toArray(),
        ])
        billBatchInfo = billBatchInfo[0]
        let attachFile1Content = [
            {
                总费用: billBatchInfo.TotalAmount + '元',
                计费总次数: billBatchInfo.Count + '次',
                账单开始时间: moment(billBatchInfo.StartTime * 1000).format(
                    'YYYY-MM-DD HH:mm:ss'
                ),
                账单结束时间: moment(billBatchInfo.EndTime * 1000).format(
                    'YYYY-MM-DD HH:mm:ss'
                ),
            },
        ]

        let attachFile2Content = []
        billDetailInfo.forEach(function (info) {
            attachFile2Content.push({
                计费单位: info.UId,
                计费次数: info.Count + '次',
            })
        })
        // 将object转为csv
        attachFile1Content = json2csv(attachFile1Content)
        attachFile2Content = json2csv(attachFile2Content)
        const base64File1 =
            'data:text/csv;base64,' +
            Buffer.from(attachFile1Content).toString('base64')
        const base64File2 =
            'data:text/csv;base64,' +
            Buffer.from(attachFile2Content).toString('base64')
        // 文件上传US3
        let fileNames = await Promise.all([
            uploadFile(base64File1),
            uploadFile(base64File2),
        ])
        attachMaps[info.CompanyId] = [
            {
                AttachName: '对账单_合计.csv',
                AttachContent: fileNames[0],
            },
            {
                AttachName: '对账单_明细.csv',
                AttachContent: fileNames[1],
            },
        ]
    }
    return attachMaps
}

module.exports = new CreateBillNotifyBatch()
