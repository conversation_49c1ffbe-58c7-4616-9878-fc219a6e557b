/*
 * @Date: 2023-02-21 11:10:04
 * @LastEditors: l<PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2023-03-06 15:58:56
 * @FilePath: /newicp/mq/bill/NotifyUsedBill.js
 */
'use strict'

/**检查批次 所在的数据解析情况，解析成功后
 * 为用户发送账单详情的通知
 */
const Common = require('../Common')
const {
    model: NotifyBatchModel,
    BatchStatusEnum: BatchStatusEnum,
} = require('../../models/t_notify_batch')

const NotifyBatch = require('../../methods/admin/Notify/NotifyBatch')
const logger = require('../../libs/logger').getLogger('mqCustomer')
const uuid = require('uuid/v4')
/*
 * @class
 * @extent Common
 */
class NotifyUsedBill extends Common {
    constructor(params) {
        super(params)
        this.topics = ['NotifyUsedBill']
        this.type = 'icp-delay'
        this.init()
        this.name = 'NotifyUsedBill'
    }
    async handle(msg) {
        await sleep(1000)
        let self = this
        let { BatchId } = this.parseContent(msg)
        let uid = uuid()
        let mongo = this.mongo.get('icp')
        logger.info(`${this.name}[${uid}] - start`, msg.content.toString())

        //查看公司信息
        try {
            let batchInfo = await NotifyBatchModel.findOne({
                where: {
                    Id: BatchId,
                    Status: BatchStatusEnum.Parsed,
                },
            })
            if (batchInfo === null) {
                // 再推一个 延迟 队列
                return this.producer.send({
                    type: 'icp-delay',
                    topic: 'NotifyUsedBill',
                    data: {
                        BatchId: batchInfo.Id,
                    },
                    opts: {
                        headers: { 'x-delay': 60 * 1000 }, // 延迟1分钟检查时间
                    },
                })
            } else {
                // 解析完成， 复用公用的通知批次
                let method1 = new NotifyBatch((retCode, data) => {
                    if (retCode === 0) {
                        // 通知成功 推送 检查批次发送状态 和扣费
                        logger.info(`${self.name}[${uid}] - notify send`)
                    } else {
                        // 通知成功
                        logger.info(
                            `${self.name}[${uid}] - 自动通知收费批次 failed`,
                            {
                                retCode,
                                Message: data['Message'],
                            }
                        )
                    }
                })
                await method1.exec({
                    Id: BatchId,
                })
            }
        } catch (error) {
            // 批次生成失败,标记原因，以及 从redis中删除该公司Id
            logger.error(`${self.name}[${uid}] - 自动通知收费批次 failed`, {
                BatchId,
            })
        }
        logger.info(`${self.name}[${uid}] - finished`)
    }
}

const sleep = (millseconds = 3000) => {
    return new Promise((rs) => {
        setTimeout(() => rs(), millseconds)
    })
}

module.exports = new NotifyUsedBill()
