/*
 * @Date: 2023-03-06 11:49:38
 * @LastEditors: l<PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2023-04-03 11:47:40
 * @FilePath: /newicp/mq/bill/CheckBillPayStatus.js
 */
'use strict'

/**检查后付费账单的扣费情况
 */
const Common = require('../Common')
const logger = require('../../libs/logger').getLogger('mqCustomer')
const { BillListModel, BillOrderStatusEnum } = require('../../mongoModels/icp')
const uuid = require('uuid/v4')
const { ObjectId } = require('mongoose').Types
const { requestAsync } = require('../../fns/ucloudInternalApi')
const moment = require('moment')
/*
 *获取扣费账单的支付情况
 */
class CheckBillPayStatus extends Common {
    constructor(params) {
        super(params)
        this.topics = ['CheckBillPayStatus']
        this.type = 'icp-delay'
        this.init()
        this.name = 'CheckBillPayStatus'
    }
    async handle(msg) {
        await sleep(1000)
        let self = this
        const producer = this.producers
        /**
         * 查看订单的实际扣费情况 TopOrganizationId 是companyID
         */
        let { Id } = this.parseContent(msg)
        let uid = uuid()
        logger.info(`${this.name}[${uid}] - start`, msg.content.toString())
        const date = moment().format('X')
        try {
            // 获取账单的信息
            let list = await BillListModel.findById(ObjectId(Id))
            if (!list) {
                throw new Error('Not Found this Bill')
            }
            list = JSON.parse(JSON.stringify(list))
            // 接口标准请参考https://uaccount-wiki.ucloudadmin.com/ubill/product-access-docs/GetUnpaidOrderDetailInfo.md
            let reqData = {
                Action: 'GetUnpaidOrderDetailInfo',
                Backend: 'UBill',
                TopOrganizationId:
                    global.CONFIG.env === 'production'
                        ? list.CompanyId
                        : ********,
                OrderNos: [list.OrderNo],
            }
            console.log(JSON.stringify(reqData))
            let res = await requestAsync(
                {
                    category: 'UBill',
                    method: 'POST',
                    action: 'UBill.GetUnpaidOrderDetailInfo',
                },
                reqData
            )
            if (res?.OrderInfos?.length > 0) {
                let resInfo = res?.OrderInfos[0]
                await BillListModel.findOneAndUpdate(
                    {
                        _id: ObjectId(Id),
                    },
                    {
                        $set: {
                            OrderStatus: resInfo.OrderState,
                            UpdateTime: date,
                        },
                    }
                )
                if (
                    [
                        BillOrderStatusEnum.UNPAID_FINISHED,
                        BillOrderStatusEnum.UNPAID_NEW,
                    ].includes(parseInt(res.OrderState))
                ) {
                    // 如果是新建 或者待支付的状态 先更新支付状态 然后再推送延时任务检查支付状态
                    await producer.send({
                        type: 'icp-delay',
                        topic: 'CheckBillPayStatus',
                        data: {
                            Id,
                        },
                        opts: {
                            headers: { 'x-delay': 5 * 60 * 1000 }, // 延迟5分钟检查时间
                        },
                    })
                }
            }
        } catch (error) {
            // 批次生成失败,标记原因，以及 从redis中删除该公司Id
            logger.error(
                `${self.name}[${uid}] - 检查订单扣费状态 failed`,
                error.message
            )
        }
        logger.info(`${self.name}[${uid}] - finished`)
    }
}

const sleep = (millseconds = 3000) => {
    return new Promise((rs) => {
        setTimeout(() => rs(), millseconds)
    })
}
module.exports = new CheckBillPayStatus()
