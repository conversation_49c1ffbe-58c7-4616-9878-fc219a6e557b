/*
 * @Author: william.qian <EMAIL>
 * @Date: 2022-11-14 16:43:57
 * @LastEditors: william.qian <EMAIL>
 * @LastEditTime: 2023-03-15 16:05:59
 * @FilePath: /newicp/mq/noAccessBlock/BlockDoaminForNoAccess.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
'use strict'

/**
 * 检查域名基础情况，如确实是已备案未接入，执行封禁
 */
const Common = require('../Common')

const { sleep } = require('../../fns/kits')
const {
    sealDomain,
    getToken,
    getSealStatus,
} = require('../../fns/aodun/DomainService.js')
const CheckDomainInOtherISP = require('../../methods/admin/Common/CheckDomainInOtherISP')
const logger = require('../../libs/logger').getLogger('mqCustomer')
const uuid = require('uuid/v4')
const {
    BulkBlockRecordModel,
    DomainWhiteListModel,
    BlockRecordStatusEnum,
} = require('../../models/')
const moment = require('moment')

/*
 * @class
 * @extent Common
 */
class BlockDoaminForNoAccess extends Common {
    constructor(params) {
        super(params)
        this.topics = ['BlockDoaminForNoAccess']
        this.type = 'icp'
        this.init()
        this.name = 'BlockDoaminForNoAccess'
    }
    async handle(msg) {
        await sleep(500)
        let self = this
        let producer = self.producer

        let redis = this.redis.get()
        let { BatchName } = this.parseContent(msg)
        console.log({ BatchName })
        let uid = uuid()
        logger.info(`${this.name}[${uid}] - start`, msg.content.toString())
        let result, domain, id

        //开始执行,从Redis中取结果
        try {
            result = await redis.spop(BatchName)
            if (result === null) {
                // 开始推送检查MQ
                await producer.send({
                    type: 'icp',
                    topic: 'CheckBlockBatchFinish',
                    data: { BatchName },
                })
                return
            }
            result = JSON.parse(result)
            domain = result.Domain
            id = result.Id
        } catch (error) {
            logger.info(`${self.name}[${uid}] -  Error:` + error.toString)
        }

        try {
            await producer.send({
                type: 'icp',
                topic: 'BlockDoaminForNoAccess',
                data: { BatchName },
            })
            // 再次检查是否接入
            let domainICP = false
            let params = {
                Domain: [domain],
            }
            let method = new CheckDomainInOtherISP((RetCode, data) => {
                if (RetCode === 0) {
                    if (data.domainInfo[domain]?.IsICPInOtherISP) {
                        domainICP = true
                    }
                }
            })
            await method.exec(params)

            if (domainICP) {
                await BulkBlockRecordModel.update(
                    {
                        Status: BlockRecordStatusEnum['Accessed'],
                        Remark: '已接入',
                    },
                    {
                        where: {
                            Id: result.Id,
                        },
                    }
                )
            } else {
                //确定是否封禁，如果是结束
                let token = await getToken()
    
                id = result.Id
    
                let domainInBlock = await getSealStatus(token, {
                    domain,
                })
                if (domainInBlock === null) {
                    // 未查到封顶情况，执行封禁
                    token = await getToken()
                    await sealDomain(token, [domain])
    
                    // 封禁后，更新封禁情况
                    await BulkBlockRecordModel.update(
                        {
                            Status: BlockRecordStatusEnum['Success'],
                            Remark: '封禁成功',
                        },
                        {
                            where: {
                                Id: id,
                            },
                        }
                    )
    
                    await CheckWhiteAndUpdateStatus(domain)
                } else {
                    // 查到了，记录结果
                    await BulkBlockRecordModel.update(
                        {
                            Status: BlockRecordStatusEnum['Failed'],
                            Remark: `${domainInBlock[0].domain}域名之前已被${domainInBlock[0].c_name}封禁`,
                        },
                        {
                            where: {
                                Id: id,
                            },
                        }
                    )
                    // 如果是已封禁成功了，确定是不是已备案未接入封禁的
                    if (
                        domainInBlock &&
                        domainInBlock[0] &&
                        domainInBlock[0].reason &&
                        domainInBlock[0].reason === '已备案未接入'
                    ) {
                        await CheckWhiteAndUpdateStatus(domain)
                    }
                }
            }
        } catch (error) {
            await BulkBlockRecordModel.update(
                {
                    Status: BlockRecordStatusEnum['Failed'],
                    Remark: `${domain}封禁失败` + error.toString(),
                },
                {
                    where: {
                        Id: id,
                    },
                }
            )
            // 如果出错，标记，同时记Remark
            logger.info(`${self.name}[${uid}] - Error:` + error.toString())
        }

        logger.info(`${self.name}[${uid}] - finished`)
    }
}

// 执行此函数的前置条件是，确定此域名已执行封禁成功或者已因已经
async function CheckWhiteAndUpdateStatus(domain) {
    let domainExist = await DomainWhiteListModel.count({
        where: {
            Domain: domain,
        },
    })

    if (domainExist === 0) {
        await DomainWhiteListModel.create({
            Domain: domain,
            ExpiredTime: moment(moment().format('YYYY-MM-DD'))
                .subtract(1, 'seconds')
                .format('X'),
            Sealed: 1,
            UnsealTimes: 0,
        })
    } else {
        await DomainWhiteListModel.update(
            { Sealed: 1 },
            {
                where: {
                    Domain: domain,
                },
            }
        )
    }
}

module.exports = new BlockDoaminForNoAccess()
