/*
 * @Author: william.qian <EMAIL>
 * @Date: 2022-11-14 16:43:57
 * @LastEditors: william.qian <EMAIL>
 * @LastEditTime: 2022-12-28 14:28:02
 * @FilePath: /newicp/mq/noAccessBlock/CheckBlockBatchFinish.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
'use strict'

/**
 * 确定域名是否需要
 */
const Common = require('../Common')
const { Op } = require('sequelize')
const { sleep } = require('../../fns/kits')
const logger = require('../../libs/logger').getLogger('mqCustomer')
const uuid = require('uuid/v4')
const {
    BulkBlockRecordModel,
    BlockRecordStatusEnum,
    BulkBlockBatchModel,
    BatchStatusEnum,
} = require('../../models/')
const moment = require('moment')

/*
 * @class
 * @extent Common
 */
class CheckBlockBatchFinish extends Common {
    constructor(params) {
        super(params)
        this.topics = ['CheckBlockBatchFinish']
        this.type = 'icp'
        this.init()
        this.name = 'CheckBlockBatchFinish'
    }
    async handle(msg) {
        await sleep(500)
        let self = this
        let { BatchName } = this.parseContent(msg)
        let BatchId = BatchName.split('|')[0]
        let uid = uuid()
        logger.info(`${this.name}[${uid}] - start`, msg.content.toString())

        try {
            // 检查当前批次的封禁情况，如果全部都封禁成功，则正常本批次的状态情况
            BatchId = parseInt(BatchId)
            let count = await BulkBlockRecordModel.count({
                where: {
                    BatchId,
                    Status: {
                        [Op.ne]: BlockRecordStatusEnum['Success'],
                    },
                },
            })
            if (count === 0) {
                // 说明全是成功，更新批次表
                await BulkBlockBatchModel.update(
                    { Status: BatchStatusEnum['Finish'] },
                    {
                        where: {
                            Id: BatchId,
                        },
                    }
                )
            } else {
                return
            }
        } catch (error) {
            logger.info(`${self.name}[${uid}] -  Error:` + error.toString)
        }

        logger.info(`${self.name}[${uid}] - finished`)
    }
}

module.exports = new CheckBlockBatchFinish()
