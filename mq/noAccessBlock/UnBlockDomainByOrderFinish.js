/*
 * @Author: william.qian <EMAIL>
 * @Date: 2022-11-14 16:43:57
 * @LastEditors: william.qian <EMAIL>
 * @LastEditTime: 2023-03-15 16:06:03
 * @FilePath: /newicp/mq/noAccessBlock/UnBlockDomainByOrderFinish.js
 * @Description:
 * 收到域名列表，查询封禁情况
 * 如果不是违规被封禁的，解封
 * 然后更新跃文的表 operator	sealed : 0	unseal_times=unseal_times + 1 remark
 */
'use strict'

const Common = require('../Common')

const { sleep } = require('../../fns/kits')
const {
    getToken,
    unSealDomain,
    getSealStatus,
} = require('../../fns/aodun/DomainService.js')
const { literal } = require('sequelize')

const logger = require('../../libs/logger').getLogger('mqCustomer')
const uuid = require('uuid/v4')
const { DomainWhiteListModel } = require('../../models/')
const moment = require('moment')

/*
 * @class
 * @extent Common
 */
class UnBlockDomainByOrderFinish extends Common {
    constructor(params) {
        super(params)
        this.topics = ['UnBlockDomainByOrderFinish']
        this.type = 'icp'
        this.init()
        this.name = 'UnBlockDomainByOrderFinish'
    }
    async handle(msg) {
        await sleep(500)
        let self = this
        let token
        let { Domain } = this.parseContent(msg)
        let uid = uuid()
        logger.info(`${this.name}[${uid}] - start`, msg.content.toString())

        // 开始执行，确定当前几个域名的封禁情况
        try {
            //确定是否封禁，如果是结束
            token = await getToken()

            let domainInBlock = await getSealStatus(token, {
                domain: Domain,
            })
            if (domainInBlock && domainInBlock.length > 0) {
                if (
                    (domainInBlock[0].reason &&
                        domainInBlock[0].reason === '已备案未接入') ||
                    !domainInBlock[0].reason
                ) {
                    // 已备案未接入 / 未备案 解封
                    token = await getToken()

                    let result = await unSealDomain(token, [Domain])

                    // 解封成功后，如果是未接入 更新白名单表
                    if (domainInBlock[0].reason) {
                        await DomainWhiteListModel.update(
                            {
                                UnsealTimes: literal('unseal_times + 1'),
                                Sealed: '0',
                                Remark: '备案完成解封',
                            },
                            { where: { Domain } }
                        )
                    }
                    logger.info(
                        `${self.name}[${uid}] - Result:` +
                            JSON.stringify(result)
                    )
                } else {
                    // 有内容，有记录，有封禁原因，封禁原因为命中非法关键词
                    // 不解封,由后面合规侧的定时任务补
                    logger.info(
                        `${self.name}[${uid}] - Result: 非未备案 或者未接入封禁，不解封`
                    )
                    return
                }
            } else {
                logger.info(`${self.name}[${uid}] - Result: 未查询到封禁 无需解封`)
            }
        } catch (error) {
            // 如果出错，标记，同时记Remark
            logger.info(`${self.name}[${uid}] - Error:` + error.toString())
        }

        logger.info(`${self.name}[${uid}] - finished`)
    }
}

module.exports = new UnBlockDomainByOrderFinish()

