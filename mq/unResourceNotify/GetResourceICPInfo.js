'use strict'

/**
 * 通过公司Id查询资源信息，
 * 并将无资源的公司Id插入至该批次的记录中
 */
const Common = require('../Common')
const {
    model: BatchModel,
    BatchStatusEnum,
} = require('../../models/t_notify_batch')
const { model: NotifyModel, NotifyTypeEnum } = require('../../models/t_notify')
const {
    model: RecordModel,
    ErrorReasonEnum,
} = require('../../models/t_notify_companyInfo')
const { model: ICPModel, ICPStatusEnum } = require('../../models/t_icp')
const { model: WebModel, WebStatusEnum } = require('../../models/t_web')
const { Op, BaseError } = require('sequelize')
const logger = require('../../libs/logger').getLogger('mqCustomer')
const uuid = require('uuid/v4')
const { parseJSON } = require('../../fns/kits')
const _ = require('lodash')
/*
 * @class
 * @extent Common
 */
class GetResourceICPInfo extends Common {
    constructor(params) {
        super(params)
        this.topics = ['GetResourceICPInfo']
        this.type = 'icp'
        this.init()
        this.name = 'GetResourceICPInfo'
    }
    async handle(msg) {
        await sleep(1000)
        let redis = this.redis.get()
        let { CompanyId, Id, BatchId, RetryTime } = this.parseContent(msg)
        let uid = uuid()
        logger.info(`${this.name}[${uid}] - start`, msg.content.toString())
        let result
        //查看有效的备案信息
        try {
            let icps = await ICPModel.findAll({
                attributes: ['Id', 'OrganizerName'],
                where: {
                    CompanyId,
                    IsDeleted: 0,
                    Status: {
                        [Op.notIn]: [
                            ICPStatusEnum.Logout,
                            ICPStatusEnum.Isdeleted,
                        ],
                    },
                },
            })
            icps = parseJSON(icps)
            let websites = await WebModel.findAll({
                attributes: ['MainId', 'ICPWebNo', 'Domain', 'Mobile', 'Email'],
                where: {
                    MainId: {
                        [Op.in]: _.map(icps, 'Id'),
                    },
                    IsDeleted: 0,
                    Status: {
                        [Op.notIn]: [
                            WebStatusEnum.Connlogout,
                            WebStatusEnum.Weblogout,
                        ],
                    },
                },
            })

            websites = parseJSON(websites)
            /**
             * 整理备案数据为
             * [{
                OrganizerName: "广州凡科", //主办单位名称
                Websites: [{//通知的域名和网站备案号的信息 从这里面拿
                    Domain: "domain1; domain2",域名信息
                    ICPWebNo: "xxxxx", //网站备案号
                }, {
                    Domain: "domain1; domain2"
                    ICPWebNo: "xxxxx"
                }]
            }]

             */
            result = _.map(icps, (icp) => {
                icp.CompanyId = CompanyId
                icp.Websites = _.filter(websites, ['MainId', icp.Id])
                if (icp.Websites.length > 0) {
                    icp.Websites.forEach((web) => {
                        web.Domain = _.map(web.Domain, 'Domain').join(';')
                    })
                }
                return icp
            })

            //更新备案数据 到记录表中
            await RecordModel.update(
                {
                    ICPInfo: result,
                },
                {
                    where: {
                        Id,
                    },
                }
            )
            // 添加通知数据 到通知表中
            let notifyInfoArr = []
            _.forEach(websites, (web) => {
                notifyInfoArr.push({
                    CompanyNotifyId: Id,
                    Contact: web.Mobile.replace('(86)', '').replace(
                        '（86）',
                        ''
                    ),
                    Type: NotifyTypeEnum.Phone,
                })
                notifyInfoArr.push({
                    CompanyNotifyId: Id,
                    Contact: web.Email,
                    Type: NotifyTypeEnum.Email,
                })
            })

            // 批量通知数据插入
            await NotifyModel.bulkCreate(notifyInfoArr, {
                ignoreDuplicates: true,
            })
        } catch (error) {
            // 如果出错，重新推送，允许重试3次
            if (RetryTime === undefined || RetryTime < 3) {
                RetryTime += 1

                logger.error(
                    `${this.name}[${uid}] - retry error: ${error}`,
                    msg.content.toString()
                )
                return await this.producer.send({
                    type: 'icp',
                    topic: 'GetResourceICPInfo',
                    data: {
                        Id,
                        BatchId,
                        CompanyId,
                        RetryTime,
                    },
                })
            } else {
                // 多次失败,标记原因，以及 从redis中删除该公司Id
                logger.info(`${this.name}[${uid}] - 查看备案信息 failed`, {
                    Id,
                    CompanyId,
                    Reason: ErrorReasonEnum.GetICPInfo,
                })
                await RecordModel.update(
                    {
                        IsError: true,
                        Reason: ErrorReasonEnum.GetICPInfo,
                    },
                    {
                        where: {
                            Id,
                        },
                    }
                )
            }
        }

        //处理记录状态
        try {
            // 从记录中删除 已经查询过的公司信息
            await redis.srem(`${BatchId}_unresource_icp`, CompanyId)

            let [companyCount, icpCount] = await Promise.all([
                redis.scard(`${BatchId}_unresource_companyInfo`),
                redis.scard(`${BatchId}_unresource_icp`),
            ])
            // 判断所有的记录都已经处理完毕，修改批次状态
            if (companyCount === 0 && icpCount === 0) {
                // 使用redis锁 保证只有一个进程 处理批次状态
                let lock = await redis.set(
                    `${BatchId}_lock_search_compAndicp`,
                    1,
                    'EX',
                    5
                )
                if (lock) {
                    await BatchModel.update(
                        { Status: BatchStatusEnum.Parsed },
                        { where: { Id: BatchId } }
                    )
                }
            }
        } catch (err) {
            logger.error(
                `${this.name}[${uid}] - 删除已查询记录或者处理批次状态出错 error,错误信息：${err}`
            )
        }
        logger.info(`${this.name}[${uid}] - finished`)
    }
}

const sleep = (millseconds = 3000) => {
    return new Promise((rs) => {
        setTimeout(() => rs(), millseconds)
    })
}

module.exports = new GetResourceICPInfo()
