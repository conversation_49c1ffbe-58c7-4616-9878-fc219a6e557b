'use strict'

const Common = require('../Common')
const {
    model: BatchModel,
    BatchStatusEnum,
} = require('../../models/t_notify_batch')
const {
    model: RecordModel,
    ErrorReasonEnum,
} = require('../../models/t_notify_companyInfo')
const logger = require('../../libs/logger').getLogger('mqCustomer')
const uuid = require('uuid/v4')
const { parseJSON } = require('../../fns/kits')
const ucloudinternalapi = require('../../libs/ucloudinternalapi')
const { Op } = require('sequelize')
const _ = require('lodash')

/**
 * 通过公司Id查询资源信息，
 * 并将无资源的公司Id插入至该批次的记录中
 */
class GetResourceFromCompanyId extends Common {
    constructor(params) {
        super(params)
        this.topics = ['GetResourceFromCompanyId']
        this.type = 'icp'
        this.init()
        this.name = 'GetResourceFromCompanyId'
    }
    async handle(msg) {
        // await sleep(1000)
        let self = this
        let redis = this.redis.get()
        let { CompanyId, BatchId, Id, RetryTime } = this.parseContent(msg)
        let uid = uuid()
        logger.info(`${this.name}[${uid}] - start`, msg.content.toString())
        let result
        // GetResourceErr 用来记录 是否查询出错
        let GetResourceErr = false
        //查看资源信息
        try {
            result = await ucloudinternalapi({
                Backend: 'UResource',
                Action: 'IGetResourceCount',
                TopOrganizationId: CompanyId,
                // 增加验证云主机，需要查询云主机资源
                ResourceType: ['1', '10', '102', '142', '370', '118', '20'], // 10: EIP, 102:混合云外网IP,142:全球加速产品id，370: 轻量云主机  118 natgw 20: 物理云主机
                ZoneId: Object.keys(zoneMap), //只需要查境内可用区
            })
            if (result.RetCode !== 0) {
                let err = new Error(
                    `罗盘查询公司资源失败，公司Id: ${CompanyId},错误信息：${result.Message}`
                )
                throw err
            }
        } catch (error) {
            // 如果出错，重新推送，允许重试3次
            if (RetryTime === undefined || RetryTime < 3) {
                RetryTime += 1

                logger.error(
                    `${this.name}[${uid}] - retry, error: ${error}`,
                    msg.content.toString()
                )
                return await this.producer.send({
                    type: 'icp',
                    topic: 'GetResourceFromCompanyId',
                    data: {
                        BatchId,
                        Id,
                        CompanyId,
                        RetryTime,
                    },
                })
            } else {
                // 多次失败,标记原因，以及 从redis中删除该公司Id
                logger.error(`${this.name}[${uid}] - 查看资源信息 failed`, {
                    BatchId,
                    CompanyId,
                    Reason: ErrorReasonEnum.GetResource,
                })
                let promiseArr = [
                    redis.srem(
                        `${BatchId}_search_resource_companyIds`,
                        CompanyId
                    ),
                ]
                // 存在记录则更新 不存在则创建
                promiseArr.push(
                    Id
                        ? RecordModel.update(
                              {
                                  IsError: true,
                                  Reason: ErrorReasonEnum.GetResource,
                              },
                              {
                                  where: { Id },
                              }
                          )
                        : redis.sadd(
                              `${BatchId}_waiting_insertToRecord`,
                              JSON.stringify({
                                  BatchId,
                                  CompanyId,
                                  IsError: true,
                                  Reason: ErrorReasonEnum.GetResource,
                              })
                          )
                )
                await Promise.all(promiseArr)
                GetResourceErr = true
            }
        }
        //处理资源信息
        try {
            if (!GetResourceErr) {
                let promiseArr = [
                    redis.srem(
                        `${BatchId}_search_resource_companyIds`,
                        CompanyId
                    ),
                ]
                // 此处理逻辑更新，原来只验证资源总数不为0，现更新如下
                // 如（有PathX  或者 有混合云外网IP）或者 （有EIP 与有 云主机）,增加下natgw
                const EIP =
                    _.filter(result.Infos, function (o) {
                        return o.ResourceType === 1
                    }).length > 0 &&
                    _.filter(result.Infos, function (o) {
                        return o.ResourceType === 10
                    }).length > 0
                const PathXORIP =
                    _.filter(result.Infos, function (o) {
                        return [102, 142, 118].includes(o.ResourceType)
                    }).length > 0
                // 轻量云+EIP
                const ULHost =
                    _.filter(result.Infos, function (o) {
                        return o.ResourceType === 370
                    }).length > 0 &&
                    _.filter(result.Infos, function (o) {
                        return o.ResourceType === 10
                    }).length > 0
                // 物理云主机+EIP
                const UPHost =
                    _.filter(result.Infos, function (o) {
                        return o.ResourceType === 20
                    }).length > 0 &&
                    _.filter(result.Infos, function (o) {
                        return o.ResourceType === 10
                    }).length > 0
                if (EIP || PathXORIP || ULHost || UPHost) {
                    //有资源 有记录则移除该公司记录
                    promiseArr.push(
                        Id
                            ? RecordModel.destroy({
                                  where: {
                                      Id,
                                  },
                              })
                            : Promise.resolve()
                    )
                } else {
                    //无资源，若无记录将数据添加至记录中，有则忽略
                    promiseArr.push(
                        Id
                            ? Promise.resolve()
                            : redis.sadd(
                                  `${BatchId}_waiting_insertToRecord`,
                                  JSON.stringify({ BatchId, CompanyId })
                              )
                    )
                    promiseArr.push(
                        // 将CompanyId 记录到基本信息检查的列表中
                        redis.sadd(`${BatchId}_unresource_icp`, CompanyId),
                        redis.sadd(
                            `${BatchId}_unresource_companyInfo`,
                            CompanyId
                        )
                    )
                }
                await Promise.all(promiseArr)
            }
        } catch (err) {
            logger.error(
                `${this.name}[${uid}] - 处理资源信息 error,错误信息：${err}`
            )
        }
        // 若获取资源信息失败 直接进入Redis 检查进度
        //剩余未查询检查
        try {
            let count = await redis.scard(
                `${BatchId}_search_resource_companyIds`
            )

            if (count === 0) {
                // 使用分布式锁 执行下列操作
                let lockBatch = await redis.set(
                    `${BatchId}_unresource`,
                    1,
                    'EX',
                    5
                )
                if (lockBatch) {
                    // 获取全部的记录的待插入的数据 并插入
                    let redisRecords = await redis.smembers(
                        `${BatchId}_waiting_insertToRecord`
                    )
                    redisRecords = redisRecords.map((r) => JSON.parse(r))
                    //所有公司id已查询完毕，更新批次状态,到解析中
                    await Promise.all([
                        BatchModel.update(
                            { Status: BatchStatusEnum.Parsing },
                            {
                                where: {
                                    Id: BatchId,
                                },
                            }
                        ),
                        redisRecords.length > 0
                            ? RecordModel.bulkCreate(redisRecords)
                            : Promise.resolve(),
                    ])
                    // 获取到 该批次下所有待解析的CompanyId, 删除记录record的key
                    let [companyIds, delkey] = await Promise.all([
                        redis.smembers(`${BatchId}_unresource_icp`),
                        redis.del(`${BatchId}_waiting_insertToRecord`),
                    ])
                    // 查看批次下 待解析基本信息的
                    let records = await RecordModel.findAll({
                        where: {
                            BatchId,
                            CompanyId: {
                                [Op.in]: companyIds,
                            },
                        },
                    })
                    records = parseJSON(records)
                    if (records.length > 0) {
                        for (let i = 0; i < records.length; i++) {
                            await Promise.all([
                                // 推入公司信息查询 队列
                                self.producer.send({
                                    type: 'icp',
                                    topic: 'GetResourceCompanyInfo',
                                    data: {
                                        BatchId,
                                        Id: records[i].Id,
                                        CompanyId: records[i].CompanyId,
                                    },
                                }),
                                // 推入备案信息查询队列
                                self.producer.send({
                                    type: 'icp',
                                    topic: 'GetResourceICPInfo',
                                    data: {
                                        BatchId,
                                        Id: records[i].Id,
                                        CompanyId: records[i].CompanyId,
                                    },
                                }),
                            ])
                        }
                    } else {
                        //没有任何的记录，直接修改批次状态为已解析
                        await BatchModel.update(
                            { Status: BatchStatusEnum.Parsed },
                            {
                                where: {
                                    Id: BatchId,
                                },
                            }
                        )
                    }
                }
            }
        } catch (err) {
            logger.error(
                `${this.name}[${uid}] - 剩余未查询检查 error,错误信息：${err}`
            )
        }
        logger.info(`${this.name}[${uid}] - finished`)
    }
}

const sleep = (millseconds = 3000) => {
    return new Promise((rs) => {
        setTimeout(() => rs(), millseconds)
    })
}
// 境内 可用区map
const zoneMap = {
    0: 'pathx通用可用区',
    1: '浙江可用区A',
    1001: '北京一可用区A',
    //3001: '香港可用区A', // 去掉香港可用区，只需要境内资源
    //3002: '香港可用区B',
    4001: '北京二可用区B',
    5001: '北京二可用区C',
    7001: '广东可用区B',
    7101: '广东二可用区A',
    8001: '上海一可用区A',
    8002: '上海一可用区B',
    8003: '上海一可用区C',
    8100: '上海二可用区A',
    8200: '上海二可用区B',
    8300: '上海二可用区C',
    8400: '上海二可用区D',
    8401: '上海三可用区A',
    8501: '上海四可用区A',
    9001: '北京二可用区D',
    9002: '北京二可用区E',
    10019: '杭州可用区A',
    10020: '嘉兴可用区A',
    10021: '泉州可用区A',
    10027: '乌兰察布可用区A',
    110100: '浪潮华北一可用区A',
    110200: '浪潮华北二可用区A',
}
module.exports = new GetResourceFromCompanyId()
