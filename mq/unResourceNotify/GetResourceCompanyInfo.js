'use strict'

/**
 * 通过公司Id查询公司信息以及主账号联系人信息
 */
const Common = require('../Common')
const {
    model: BatchModel,
    BatchStatusEnum,
} = require('../../models/t_notify_batch')
const {
    model: NotifyModel,
    SendStatusEnum,
    NotifyTypeEnum,
} = require('../../models/t_notify')
const {
    model: RecordModel,
    ErrorReasonEnum,
} = require('../../models/t_notify_companyInfo')
const axios = require('../../libs/axiosApi')
const logger = require('../../libs/logger').getLogger('mqCustomer')
const _ = require('lodash')
const uuid = require('uuid/v4')
const ucloudinternalapi = require("../../libs/ucloudinternalapi");
const { model: ICPModel, ICPStatusEnum } = require('../../models/t_icp')
const { model: WebModel, WebStatusEnum } = require('../../models/t_web')
const {Op} = require("sequelize");
const {parseJSON} = require("../../fns/kits");
/*
 * @class
 * @extent Common
 */
class GetResourceCompanyInfo extends Common {
    constructor(params) {
        super(params)
        this.topics = ['GetResourceCompanyInfo']
        this.type = 'icp'
        this.init()
        this.name = 'GetResourceCompanyInfo'
    }
    async handle(msg) {
        await sleep(1000)
        let self = this
        let redis = this.redis.get()
        let { CompanyId, Id, BatchId, RetryTime } = this.parseContent(msg)
        let uid = uuid()
        logger.info(`${this.name}[${uid}] - start`, msg.content.toString())
        let result

        //查看公司信息
        try {
            let options = {
                method: 'POST',
                url: global.CONFIG.dataDDApi,
                headers: {
                    'Content-Type': 'application/json',
                },
                data: {
                    Action: 'Account.GetCompanyInfo',
                    Token: global.CONFIG.DataDDToken,
                    CompanyId,
                },
                json: true,
            }
            result = await axios(options)
            if (result.status !== 200 || result.data.RetCode !== 0) {
                // 这里没有考虑到用户注销的场景，需要去判断一下客户是否注销
                // 如果注销了，那么需要将result数据进行重组
                if (await IsCustomDeregistered(CompanyId)) {
                    result = await HandleResData(CompanyId)
                } else {
                    let err = new Error(
                        `账户查询公司信息失败，公司Id: ${CompanyId},错误信息：${
                            result.status !== 200
                                ? result.status
                                : result.data.Message
                        }`
                    )
                    throw err
                }
            }
        } catch (error) {
            // 如果出错，重新推送，允许重试3次
            if (RetryTime === undefined || RetryTime < 3) {
                RetryTime += 1

                logger.error(
                    `${this.name}[${uid}] - retry error: ${error}`,
                    msg.content.toString()
                )
                return await this.producer.send({
                    type: 'icp',
                    topic: 'GetResourceCompanyInfo',
                    data: {
                        Id,
                        BatchId,
                        CompanyId,
                        RetryTime,
                    },
                })
            } else {
                // 多次失败,标记原因，以及 从redis中删除该公司Id
                logger.error(`${this.name}[${uid}] - 查看公司信息 failed`, {
                    Id,
                    CompanyId,
                    Reason: ErrorReasonEnum.GetCompanyInfo,
                })
                await RecordModel.update(
                    {
                        IsError: true,
                        Reason: ErrorReasonEnum.GetCompanyInfo,
                    },
                    {
                        where: {
                            Id,
                        },
                    }
                )
            }
        }
        //若获取到公司信息，更新公司信息
        result = result.data.Data

        let notifyInfo = _.find(result?.main_contacts, (contact) => {
            return (
                contact.admin === 1 &&
                !/^spt.*@ucloud\.cn$/.test(contact.user_email)
            )
        })
        if (result) {
            try {
                await RecordModel.update(
                    {
                        CompanyName: result.company_name,
                        Manager: result.manager,
                        BU: result.bu ? result.bu : '',
                        Level: result.vip_level,
                        MainMail: notifyInfo.user_email,
                    },
                    {
                        where: {
                            Id,
                        },
                    }
                )
            } catch (err) {
                logger.error(
                    `${this.name}[${uid}] - 更新公司信息 error,错误信息：${err}`
                )
            }

            // 将主联系人加入通知列表中
            try {
                //所有公司id已查询完毕，更新批次状态,到解析中
                let notifies = [
                    {
                        CompanyNotifyId: Id,
                        Contact: notifyInfo.user_email,
                        Type: NotifyTypeEnum.Email,
                    },
                    {
                        CompanyNotifyId: Id,
                        Contact: notifyInfo.user_phone
                            .replace('(86)', '')
                            .replace('（86）', ''),
                        Type: NotifyTypeEnum.Phone,
                    },
                ]
                if (result.manager.trim() !== '') {
                    notifies.push({
                        CompanyNotifyId: Id,
                        Contact: result.manager.trim(),
                        Type: NotifyTypeEnum.Email,
                    })
                }
                await NotifyModel.bulkCreate(notifies, {
                    ignoreDuplicates: true,
                })
            } catch (err) {
                logger.error(
                    `${this.name}[${uid}] - 公司主联系人信息加入通知出现 error,错误信息：${err}`
                )
            }
        }

        //处理记录状态
        try {
            // 从记录中删除 已经查询过的公司信息
            await redis.srem(`${BatchId}_unresource_companyInfo`, CompanyId)

            let [companyCount, icpCount] = await Promise.all([
                redis.scard(`${BatchId}_unresource_companyInfo`),
                redis.scard(`${BatchId}_unresource_icp`),
            ])
            // 判断所有的记录都已经处理完毕，修改批次状态
            if (companyCount === 0 && icpCount === 0) {
                // 使用redis锁 保证只有一个进程 处理批次状态
                let lock = await redis.set(
                    `${BatchId}_lock_search_compAndicp`,
                    1,
                    'EX',
                    5
                )
                if (lock) {
                    await BatchModel.update(
                        { Status: BatchStatusEnum.Parsed },
                        {
                            where: {
                                Id: BatchId,
                            },
                        }
                    )
                }
            }
        } catch (err) {
            logger.error(
                `${this.name}[${uid}] - 删除已查询记录或者处理批次状态出错 error,错误信息：${err}`
            )
        }
        logger.info(`${this.name}[${uid}] - finished`)
    }
}

const sleep = (millseconds = 3000) => {
    return new Promise((rs) => {
        setTimeout(() => rs(), millseconds)
    })
}
// 通过该接口判断用户是否注销，和罗盘同事沟通，调用此接口如查询不到则说明已注销
async function IsCustomDeregistered(CompanyId) {
    let res = await ucloudinternalapi({
        Backend: 'UAccount',
        Action: 'IGetCompanyInfoForInner',
        CompanyIds: [CompanyId],
    })
    if (res.RetCode !== 0) {
        return false
    }
    return res.CompanyInfo.length === 0;
}

async function GetManagerById(CompanyId) {
    let res = await ucloudinternalapi({
        Backend: 'UAccount',
        Action: 'IGetCompanyInfo',
        CompanyIds: [CompanyId],
    })
    if (res.RetCode !== 0) {
        return ""
    }
    return res.DataSet[0].Manager;
}
async function HandleResData(CompanyId) {
    try {
        let icps = await ICPModel.findAll({
            attributes: ['Id'],  // 只选择 Id 字段
            where: {
                CompanyId: CompanyId,  // 查询 CompanyId 为 1 的记录
            },
        });
        icps = parseJSON(icps);
        // 去读取t_web信息
        let websites = await WebModel.findAll({
            attributes: ['Name', 'Email', 'Mobile'],
            where: {
                MainId: icps[0].Id,
            },
        })
        websites = parseJSON(websites)
        let webInfo = websites[0]
        let manager = await GetManagerById(CompanyId)

        let res = {
            data: {
                Data: {
                    manager,
                    company_name: webInfo.Name,
                    bu: "SML",
                    vip_level: "",
                    main_contacts: [
                        {
                            user_email: webInfo.Email,
                            user_phone: webInfo.Mobile,
                            admin: 1,
                            user_name: webInfo.Name,
                        }
                    ]
                }
            }
        }
        logger.info(`the deregistered companyid is: ${CompanyId}, the handle result is ${JSON.stringify(res)}`)
        return res
    } catch (e) {
        let err = new Error(
            `处理注销公司信息失败，公司Id: ${CompanyId},错误信息, ${JSON.stringify(e)}`
        )
        throw err
    }
}
module.exports = new GetResourceCompanyInfo()
