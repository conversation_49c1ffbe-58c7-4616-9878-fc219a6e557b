'use strict'

/**
 * 得到消息，根据类型不同，类型0，查询大客户与代理表中没有网站备案号的记录，查询后，同步到icp_in_other,类型1，查询全部正常的信息，同步到icp_in_other
 */
const Common = require('../Common')
const axios = require('../../libs/axiosApi')
const logger = require('../../libs/logger').getLogger('mqCustomer')
const uuid = require('uuid/v4')
const henganApi = require('../../libs/henganApiPromise')
const { getTableModel, parseJSON } = require('../../fns/kits')
const { Op } = require('sequelize')
const GetDomainPublicICP = require('../../methods/common/GetDomainPublicICP')

/*
 * @class
 * @extent Common
 */
class GetResourceCompanyInfo extends Common {
    constructor(params) {
        super(params)
        this.topics = ['SyncProxyCompanyWhiteToOtherRedis']
        this.type = 'icp'
        this.init()
        this.name = 'SyncProxyCompanyWhiteToOtherRedis'
    }
    async handle(msg) {
        let self = this

        let { Type } = this.parseContent(msg)
        let uid = uuid()
        logger.info(`${this.name}[${uid}] - start`, msg.content.toString())
        let icpDatabase = self.db.get('icp')
        let WhiteDomainModel = getTableModel(
            't_proxy_company_white_domain',
            icpDatabase
        )
        let redis = this.redis.get()
        let result

        // 查看需要同步的记录
        try {
            result = await WhiteDomainModel.findAll({
                where: getSelectQuery(Type),
                attributes: ['Domain', 'ICPWebNo'],
            })
            result = parseJSON(result)
        } catch (error) {
            // 如果出错，不重试，打日志
            logger.error(
                `${this.name}[${uid}] - retry error: ${error}`,
                msg.content.toString()
            )
        }

        if (result === undefined || result.length === 0) {
            return 0
        }

        let redisInsertResult = {}
        // 如果是类型0，查询备案号
        if (Type === 0) {
            for (let index = 0; index < result.length; index++) {
                let icpInfo
                let method = new GetDomainPublicICP((RetCode, data) => {
                    icpInfo = {
                        RetCode,
                        ...data,
                    }
                })
                await method.exec({ Domain: result[index].Domain })
                // 出错或者其它情况
                if (
                    icpInfo.RetCode !== 0 ||
                    !icpInfo.ICPInfos ||
                    icpInfo.ICPInfos.length !== 1
                ) {
                    continue
                }
                result[index].ICPWebNo = icpInfo.ICPInfos[0].webSiteNum

                redisInsertResult[result[index].Domain] = result[index].ICPWebNo
                await WhiteDomainModel.update(
                    { ICPWebNo: icpInfo.ICPInfos[0].webSiteNum },
                    {
                        where: {
                            Domain: result[index].Domain,
                        },
                    }
                )
            }
        } else {
            result.forEach((element) => {
                redisInsertResult[element.Domain] = element.ICPWebNo
            })
        }

        if (JSON.stringify(redisInsertResult) !== '{}') {
            try {
                // 保存记录
                await redis.hmset('domain_icp_in_other_map', redisInsertResult)
            } catch (err) {
                logger.error(
                    `${this.name}[${uid}] - 删除已查询记录或者处理批次状态出错 error,错误信息：${err}`
                )
            }
        }

        logger.info(`${this.name}[${uid}] - finished`)
    }
}

async function getSelectQuery(type) {
    let queryArr = [{ ICPWebNo: '' }, { ICPWebNo: { [Op.ne]: '' } }]

    return queryArr[parseInt(type)]
}

module.exports = new GetResourceCompanyInfo()
