/*
 * @Date: 2022-09-05 11:16:55
 * @LastEditors: william.qian <EMAIL>
 * @LastEditTime: 2023-10-18 11:55:01
 * @FilePath: /newicp/mq/finishH5/PushWebSocketForFinshH5.js
 */
/*
 * @file 订阅type为icp topics为 PushWebSocketForFinshH5 的事件队列
 */

const Common = require('../Common')
const producer = require('../../libs/producer')
const { getUrl } = require('../../fns/verifyFun')
const { PostWebSocket } = require('../../fns/FinishH5')
const uuid = require('uuid/v4')
const logger = require('../../libs/logger').getLogger('mqCustomer')
/*
 * @class
 * @extent Common
 */
class PushWebSocketForFinshH5 extends Common {
    constructor(params) {
        console.log(params)

        super(params)

        this.topics = ['PushWebSocketForFinshH5']
        this.name = 'PushWebSocketForFinshH5'
        this.type = 'icp'
        this.init()
    }
    async handle(msg) {
        await sleep(3000)
        // let self = this
        let uid = uuid()
        logger.info(`${this.name}[${uid}] - start`, msg.content.toString())
        let { CompanyId, Timeout, OrderNo, LicenseId, Picture, UUID } =
            this.parseContent(msg)
        // 超时控制
        Timeout = Timeout + 1

        try {
            // 通过图片名取URL
            let Url = getUrl(null, Picture, null)

            await Promise.all([
                PostWebSocket({
                    EventName: 'ICP.UploadPictureSuccess',
                    Response: { Picture, Url, LicenseId, OrderNo },
                    CompanyId,
                }),
                PostWebSocket({
                    EventName: 'ICP.UploadPicture',
                    Response: { Picture, Url, LicenseId, OrderNo },
                    CompanyId,
                }),
            ])
            logger.info(`${this.name}[${uid}] - finish with success`)
        } catch (error) {
            // 如果出错，重新执行一次，次数不能超过5次
            logger.error(
                `${this.name}[${uid}] - retry error: ${error}`,
                msg.content.toString()
            )
            if (Timeout < 5) {
                producer.send({
                    type: 'icp',
                    topic: 'PushWebSocketForFinshH5',
                    data: {
                        CompanyId,
                        Timeout,
                        OrderNo,
                        LicenseId,
                        Picture,
                        UUID,
                    },
                })
            } else {
                logger.error(
                    `${this.name}[${uid}] - finish with error: ${error}`,
                    msg.content.toString()
                )
            }
        }
    }
}

const sleep = (millseconds = 3000) => {
    return new Promise((rs) => {
        setTimeout(() => rs(), millseconds)
    })
}

module.exports = new PushWebSocketForFinshH5()
