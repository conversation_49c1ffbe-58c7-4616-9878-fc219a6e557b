/*
 * @Date: 2022-09-05 11:16:55
 * @LastEditors: l<PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2022-12-07 14:48:44
 * @FilePath: /newicp/mq/finishH5/SavePicture.js
 */
/*
 * @file 订阅type为seal topics为 checksmsStatus 的事件队列
 */

const Common = require('../Common')
const moment = require('moment')
const { getTableModel } = require('../../fns/kits')
const producer = require('../../libs/producer')
const { changeCurtainStatus } = require('../../fns/FinishH5')
const uuid = require('uuid/v4')
const logger = require('../../libs/logger').getLogger('mqCustomer')

/*
 * @class
 * @extent Common
 */
class SavePicture extends Common {
    constructor(params) {
        console.log(params)

        super(params)

        this.topics = ['SavePicture']
        this.type = 'icp'
        this.name = 'SavePicture'
        this.init()
    }
    async handle(msg) {
        await sleep(1000)
        let uid = uuid()
        logger.info(`${this.name}[${uid}] - start`, msg.content.toString())
        let self = this
        let { CompanyId, Timeout, OrderNo, LicenseId, Picture, UUID } =
            this.parseContent(msg)
        // 超时控制
        Timeout = Timeout + 1

        try {
            let icpDatabase = self.db.get('icp')
            let OrderWebModel = getTableModel('t_order_web', icpDatabase)
            let OrderModel = getTableModel('t_order', icpDatabase)
            let VerifyLogModel = getTableModel('t_verify_log', icpDatabase)

            // 直接更新
            await OrderWebModel.update(
                {
                    CurtainPicture: [Picture],
                    CurtainUUID: UUID,
                    CurtainStatus: 1,
                },
                {
                    where: {
                        OrderNo,
                        LicenseId: LicenseId,
                    },
                }
            )

            // 幕布状态锁根据网站情况更新状态锁
            await changeCurtainStatus(
                OrderModel,
                OrderWebModel,
                OrderNo,
                CompanyId
            )
            logger.info(`${this.name}[${uid}] - finish with success`)
        } catch (error) {
            // 如果出错，重新执行一次，次数不能超过5次
            if (Timeout < 5) {
                logger.error(
                    `${this.name}[${uid}] - retry error: ${error}`,
                    msg.content.toString()
                )
                producer.send({
                    type: 'icp',
                    topic: 'SavePicture',
                    data: {
                        CompanyId,
                        Timeout,
                        OrderNo,
                        LicenseId,
                        Picture,
                        UUID,
                    },
                })
            } else {
                logger.error(
                    `${this.name}[${uid}] - finish with error: ${error}`,
                    msg.content.toString()
                )
            }
        }
        await sleep(2000)
    }
}

const sleep = (millseconds = 3000) => {
    return new Promise((rs) => {
        setTimeout(() => rs(), millseconds)
    })
}

module.exports = new SavePicture()
