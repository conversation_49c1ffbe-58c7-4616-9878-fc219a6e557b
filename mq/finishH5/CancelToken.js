/*
 * @Date: 2022-09-05 11:16:55
 * @LastEditors: l<PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2022-12-07 15:16:51
 * @FilePath: /newicp/mq/finishH5/CancelToken.js
 */
/*
 * @file 订阅type为seal topics为 checksmsStatus 的事件队列
 */

const Common = require('../Common')
const { getTableModel } = require('../../fns/kits')
const producer = require('../../libs/producer')
const uuid = require('uuid/v4')
const logger = require('../../libs/logger').getLogger('mqCustomer')
/*
 * @class
 * @extent Common
 */
class CancelToken extends Common {
    constructor(params) {
        super(params)
        this.topics = ['CancelToken']
        this.type = 'icp'
        this.name = 'CancelToken'
        this.init()
    }
    async handle(msg) {
        await sleep(3000)
        let uid = uuid()
        logger.info(`${this.name}[${uid}] - start`, msg.content.toString())
        let self = this
        let { Token, Timeout, CompanyId, UUID } = this.parseContent(msg)
        Timeout = Timeout + 1

        try {
            let icpDatabase = self.db.get('icp')
            let TokenTable = getTableModel('t_token', icpDatabase)
            // 超时控制器
            await TokenTable.update(
                { Status: 1 },
                {
                    where: {
                        Token: Token || UUID,
                        CompanyId,
                    },
                }
            )
            logger.info(`${this.name}[${uid}] - finish with success`)
        } catch (error) {
            // 如果出错，重新执行一次，次数不能超过5次
            if (Timeout < 5) {
                logger.error(
                    `${this.name}[${uid}] - retry error: ${error}`,
                    msg.content.toString()
                )
                producer.send({
                    type: 'icp',
                    topic: 'CancelToken',
                    data: { Token, Timeout, CompanyId },
                })
            } else {
                logger.error(
                    `${this.name}[${uid}] - finish with error: ${error}`,
                    msg.content.toString()
                )
            }
        }
    }
}

const sleep = (millseconds = 3000) => {
    return new Promise((rs) => {
        setTimeout(() => rs(), millseconds)
    })
}

module.exports = new CancelToken()
