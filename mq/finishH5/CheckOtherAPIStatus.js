/*
 * @file icp topics为 CheckOtherAPIStatus 的事件队列
 *	逻辑：取最佳成像照片的API好了后，生成这个MQ,
 */

const Common = require('../Common')
const { getTableModel, parseJSON } = require('../../fns/kits')
const producer = require('../../libs/producer')
const { FinishH5 } = require('../../fns/FinishH5')
const { Op } = require('sequelize')
const uuid = require('uuid/v4')
const logger = require('../../libs/logger').getLogger('mqCustomer')
/*
 * @class
 * @extent Common
 */
class CheckOtherAPIStatus extends Common {
    constructor(params) {
        console.log(params)

        super(params)

        this.topics = ['CheckOtherAPIStatus']
        this.type = 'icp'
        this.name = 'CheckOtherAPIStatus'
        this.init()
    }
    async handle(msg) {
        let self = this
        let data = this.parseContent(msg)
        let uid = uuid()
        logger.info(`${this.name}[${uid}] - start`, msg.content.toString())
        // 超时控制
        data.Timeout = data.Timeout || 0
        data.Timeout = data.Timeout + 1

        if (data.Timeout > 7) {
            return
        }
        await sleep(data.Timeout * 2500)

        try {
            let icpDatabase = this.db.get('icp')

            let VerifyLogModel = getTableModel('t_verify_log', icpDatabase)

            // to do 解决唯一性
            let verifyLogInfo = await VerifyLogModel.findAll({
                limit: 1,
                order: [['Id', 'DESC']],
                where: {
                    DataContent: { [Op.like]: `%${data.VideoName}%` },
                    UUID: data.UUID,
                    OrderNo: data.OrderNo,
                    Type: 4,
                },
            })

            verifyLogInfo = parseJSON(verifyLogInfo)

            console.log(verifyLogInfo, 311, data)
            if (verifyLogInfo.length === 0) {
                // 长度为0，说明还没好,再异常类，等1秒，然后推送
                logger.info(
                    `${this.name}[${uid}] - finish`,
                    '验证日志未生成。。。'
                )
                await producer.send({
                    type: 'icp',
                    topic: 'CheckOtherAPIStatus',
                    data: data,
                })
                return
            }
            verifyLogInfo = verifyLogInfo[0]
            // 如果成功，推3个MQ异步

            if (verifyLogInfo.Result === 1) {
                // 失败，直接结束
                logger.info(
                    `${this.name}[${uid}] - finish with success`,
                    '验证失败,任务结束'
                )

                return
            }

            if (
                verifyLogInfo.Result === 0 &&
                JSON.stringify(verifyLogInfo.Response) === '{}'
            ) {
                // 没有返回值，说明在执行中
                logger.info(
                    `${this.name}[${uid}] - finish`,
                    '验证未完成，稍后重试。。。'
                )
                await sleep(2000)
                await producer.send({
                    type: 'icp',
                    topic: 'CheckOtherAPIStatus',
                    data: data,
                })
            }

            if (
                verifyLogInfo.Result === 0 &&
                JSON.stringify(verifyLogInfo.Response) !== '{}' &&
                verifyLogInfo.Response.RetCode === 0
            ) {
                logger.info(
                    `${this.name}[${uid}] - finish`,
                    '验证完成准备推送FinishH5'
                )
                await FinishH5(data)
                logger.info(
                    `${this.name}[${uid}] - finish`,
                    '推送FinishH5成功，任务完成'
                )
            }
        } catch (error) {
            await sleep(2000)
            if (data.Timeout < 7) {
                logger.error(
                    `${this.name}[${uid}] - retry error: ${error}`,
                    msg.content.toString()
                )
                await producer.send({
                    type: 'icp',
                    topic: 'CheckOtherAPIStatus',
                    data: data,
                })
                return
            } else {
                logger.error(
                    `${this.name}[${uid}] - finish with error: ${error}`,
                    msg.content.toString()
                )
            }
        }
    }
}

const sleep = (millseconds = 3000) => {
    return new Promise((rs) => {
        setTimeout(() => rs(), millseconds)
    })
}

module.exports = new CheckOtherAPIStatus()
