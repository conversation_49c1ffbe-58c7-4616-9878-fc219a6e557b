/*
 * @file 订阅type为icp topics为 CheckCompany 的事件队列
 * 通过公司名，取详细，查询后更新到记录中
 * {"OrganizerName":"优刻得科技股份有限公司","OrganizerLicenseArea":"上海市杨浦区隆昌路619号10#B号楼201室","PICMainName":"季昕华","PICMainLicenseId":"332502197903295170","Id":1}
 * todo 查询结果更新到t_verify_log中
 * todo 多次失败的任务，更新掉内
 *
 */

const Common = require('../Common')
const { getTableModel, parseJSON } = require('../../fns/kits')
const producer = require('../../libs/producer')
const ucloudinternalapi = require('../../libs/ucloudinternalapi')
const uuid = require('uuid/v4')
const logger = require('../../libs/logger').getLogger('mqCustomer')
const RegStatus = [
    '开业',
    '存续',
    '迁往市外',
    '在业',
    '在营',
    '正常',
    '登记成立',
    '在营',
    '迁移异地',
    '在营',
    '已迁出企业',
    '成立',
    '待迁入',
    '仍注册',
    '正常在业',
    '开业登记中',
    '已开业',
    '开业/正常经营',
    '经营',
    '在营（开业）企业',
    '市内迁出',
]

/*
 * @class
 * @extent Common
 */
class CheckCompany extends Common {
    constructor(params) {
        super(params)
        this.topics = ['CheckCompanyInfo']
        this.type = 'icp'
        this.name = 'CheckCompanyInfo'
        this.init()
    }
    async handle(msg) {
        await sleep(1000)
        let uid = uuid()
        logger.info(`${this.name}[${uid}] - start`, msg.content.toString())
        let self = this
        let icpDatabase = this.db.get('icp')
        let RecordModel = getTableModel(
            't_company_info_check_record',
            icpDatabase
        )

        let resultINTYC,
            OrganizerNameResult,
            OrganizerLicenseAreaResult,
            PICMainNameResult,
            OrganizerLicenseIdResult,
            RegResult
        let {
            OrganizerName,
            OrganizerLicenseArea,
            PICMainName,
            PICMainLicenseId,
            OrganizerLicenseId,
            Id,
            Timeout,
        } = this.parseContent(msg)

        try {
            // 发出请求
            try {
                resultINTYC = await ucloudinternalapi({
                    Type: 'TYC_817',
                    Source: 'ICP',
                    CompanyName: OrganizerName,
                    Backend: 'IdAuth',
                    Action: 'GetBusinessInfoByTYC',
                })
            } catch (error) {
                // 请求类的出错允许重试
                throw new Error('调用接口系统发生错误' + error.toString())
            }

            // 系统逻辑处理
            if (resultINTYC.RetCode !== 0) {
                // return await Promise.Rej
                throw new Error('调用接口请求内容错误' + resultINTYC.Message)
            }

            // 业务逻辑处理，此错误需要写入数据库，不匹配不做重试

            // 4项检查
            OrganizerNameResult =
                OrganizerName === resultINTYC.Result.DataSet.name
            OrganizerLicenseAreaResult =
                OrganizerLicenseArea === resultINTYC.Result.DataSet.regLocation
            PICMainNameResult =
                PICMainName === resultINTYC.Result.DataSet.legalPersonName
            OrganizerLicenseIdResult =
                OrganizerLicenseId === resultINTYC.Result.DataSet.creditCode

            // 存续状态
            RegResult = false

            if (resultINTYC.Result.DataSet.regStatus) {
                RegStatus.forEach((item) => {
                    if (
                        resultINTYC.Result.DataSet.regStatus != null ||
                        resultINTYC.Result.DataSet.regStatus.indexOf(item) >= 0
                    ) {
                        RegResult = true
                    }
                })
            }

            let CheckResult =
                OrganizerNameResult &&
                OrganizerLicenseAreaResult &&
                PICMainNameResult &&
                OrganizerLicenseIdResult &&
                RegResult
                    ? 1
                    : -1
            let Message = JSON.stringify({
                OrganizerNameResult,
                OrganizerLicenseAreaResult,
                PICMainNameResult,
                OrganizerLicenseIdResult,
                RegResult,
            })
            // let ReturnObject = resultINTYC
            // 写入结果
            await RecordModel.update(
                {
                    CheckResult,
                    Message,
                    // ReturnObject
                    Status: 2,
                },
                {
                    where: {
                        Id,
                    },
                }
            )
            logger.info(`${this.name}[${uid}] - finish with success`)
        } catch (error) {
            // 如果出错，重新推送，允许重试3次

            Timeout = Timeout + 1

            if (Timeout < 2) {
                logger.error(
                    `${this.name}[${uid}] - retry error: ${error}`,
                    msg.content.toString()
                )
                producer.send({
                    type: 'icp',
                    topic: 'CheckCompanyInfo',
                    data: {
                        OrganizerName,
                        OrganizerLicenseArea,
                        PICMainName,
                        PICMainLicenseId,
                        Id,
                        Timeout,
                    },
                })
            } else {
                // 多次失败,标记
                logger.error(
                    `${this.name}[${uid}] - finish with error: ${error}`,
                    msg.content.toString()
                )
                await RecordModel.update(
                    {
                        CheckResult: 2,
                        Message: error.toString(),
                        // ReturnObject
                        Status: 2,
                    },
                    {
                        where: {
                            Id,
                        },
                    }
                )
                return 0
            }
        }
    }
}

const sleep = (millseconds = 3000) => {
    return new Promise((rs) => {
        setTimeout(() => rs(), millseconds)
    })
}

module.exports = new CheckCompany()
