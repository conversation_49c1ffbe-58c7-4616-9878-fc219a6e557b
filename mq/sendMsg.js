/*
 * @Date: 2023-01-19 11:14:46
 * @LastEditors: l<PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2023-02-23 18:30:44
 * @FilePath: /newicp/mq/sendMsg.js
 */
global.CONFIG = require('../configs/local/config.json')
const producer = require('../libs/producer')

// const data = {
// 	id: 43205,
// 	batch_id:3,
// 	ip: '************',
// 	url:'http://www.u-rain.com:80/upfile/sitenap.asp?article_GjlY9'
// }

// producer.send({
// 	type: 'illegal',
// 	topic: 'getCompanyInfo',
// 	data,
// })

// const data = {
// 	CompanyId: 55969781,
// 	Timeout: 1,
// 	OrderNo: 'O20200709141000006705',
// 	LicenseId: '411325199510037826',
// 	Picture: 'b2bfe43f044dba1821e739c53ba7c1ef722d1087.jpg',
// 	UUID: '11223344'
// }

// producer.send({
// 	type: 'icp',
// 	topic: 'PushWebSocketForFinshH5',
// 	data,
// })

// const data = {
//     faceRange: [
//         '/Users/<USER>/Documents/111.jpeg',
//         '/Users/<USER>/Documents/222.jpeg',
//         '/Users/<USER>/Documents/333.jpeg',
//     ],
//     baseName: '887f92ffbe2b8fba0c43f8fdf306e72b1cc02e4b',
//     directory: 'tmp_picture',
//     UUID: 'c04a27ae-9169-4f04-83a8-240c98536e53',
// }
async function main() {
    // let z = await producer.send({
    //     type: 'icp',
    //     topic: 'GetNoAccessDomainStatus',
    //     data: {
    //         BatchId: 14,
    //     },
    // })
    // let z = await producer.send({
    //     type: 'icp',
    //     topic: 'CreateBillNotifyBatch',
    //     data: {
    //         BillInfos: [
    //             {
    //                 CompanyId: 65979125,
    //                 Notify: { Emails: ['<EMAIL>'] },
    //                 BillId: '63f5eb3952b43cdde0485b6b',
    //             },
    //         ],
    //         BillType: '域名信息查询',
    //         Type: 18,
    //     },
    // })

    let z = await producer.send({
        type: 'icp-delay',
        topic: 'CheckBatchSendAndPushBill',
        data: {
            BatchId: 193,
            BillInfos: [
                {
                    CompanyId: 65979125,
                    Notify: { Emails: ['<EMAIL>'] },
                    BillId: '63f5eb3952b43cdde0485b6b',
                },
            ],
            opts: {
                headers: { 'x-delay': 60 * 1000 },
            },
        },
    })
    console.log(z)
}
main()

// const data = {
// 	CompanyId: 55969781,
// 	Timeout: 1,
// 	Token: 'c04a27ae-9169-4f04-83a8-240c98536e51'
// }

// producer.send({
// 	type: 'icp',
// 	topic: 'CancelToken',
// 	data,
// })

// {
// 					"CompanyId":"3478"
//                      , "UUID":"4ac57307-821d-4bcf-9c54-24cd4ae19981",
//                      "Token":"4ac57307-821d-4bcf-9c54-24cd4ae19981", "LicenseId":"110104198309030076", "OrderNo":"O20210907175448006764", "VideoName":"bec7321e048d9112e3b5a24f6e7bdfe2e4ae0f1c.mp4" ,
// 					"Picture": "3ed6bc4a34a27f1cec0ccb233ec4c272616e390f.png",
// 				}
