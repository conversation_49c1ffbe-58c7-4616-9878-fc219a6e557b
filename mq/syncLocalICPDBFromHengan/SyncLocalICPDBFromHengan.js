/*
 * @Date: 2023-02-15 15:25:17
 * @LastEditors: l<PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2023-04-17 18:34:10
 * @FilePath: /newicp/mq/syncLocalICPDBFromHengan/SyncLocalICPDBFromHengan.js
 */
/**
 * 同步备案信息
 */

'use strict'
const logger = require('../../libs/logger').getLogger('mqCustomer')
const { parseJSON, sleep } = require('../../fns/kits')
const ICPSyncFromAodun = require('../../methods/common/ICPSyncFromAodun')
const Common = require('../Common')
const henganApi = require('../../libs/henganApiPromise')
const uuid = require('uuid/v4')
const _ = require('lodash')
class SyncLocalICPDBFromHengan extends Common {
    constructor(params) {
        super(params)
        this.topics = ['SyncLocalICPDBFromHengan']
        this.type = 'icp-delay'
        this.init()
        this.name = 'SyncLocalICPDBFromHengan'
    }
    async handle(msg) {
        await sleep(1000)
        let { Key } = this.parseContent(msg)
        let uid = uuid()
        const producer = this.producer
        const redisClient = this.redis.get()
        logger.info(`${this.name}[${uid}] - start`, msg.content.toString())
        let ICPMainNo
        try {
            ICPMainNo = await redisClient.spop(Key)
            if (ICPMainNo) {
                let method = new ICPSyncFromAodun((RetCode, data) => {
                    if (RetCode !== 0) logger.error('SyncLocalICPDBFromHengan 同步傲盾失败 ' + data?.Message || '')
                })
                await method.exec({
                    ICPMainNo,
                    __ssoUser: 'SyncLocalICPDBFromHengan Operator',
                })
                // await Promise.all([
                //     henganApi('ICPSyncFromHengAn', {
                //         ICPMainNo,
                //         timeout: 60 * 1000,
                //     }),
                //     henganApi('ICPPictureSyncFromHengAn', {
                //         ICPMainNo: ICPMainNo,
                //         timeout: 60 * 1000,
                //     }),
                // ])
                await producer.send({
                    type: 'icp-delay',
                    topic: 'SyncLocalICPDBFromHengan',
                    data: {
                        Key,
                    },
                    opts: {
                        headers: { 'x-delay': 1 * 60 * 1000 }, // 延迟1分钟检查时间
                    },
                })
            }
        } catch (err) {
            await redisClient.sadd(Key + 'Error', ICPMainNo)

            logger.error(`${this.name}[${uid}] - failed`, err.message)
        }
        logger.info(`${this.name}[${uid}] - finished`)
    }
}

module.exports = new SyncLocalICPDBFromHengan()
