const mysql = require('../libs/mysql')
const customer = require('../libs/customer')
const producer = require('../libs/producer')
const mongo = require('../libs/mongo')
const redis = require('../libs/redis')

class Common {
    constructor(params) {
        this.params = params
        this.mongo = mongo
        this.db = mysql
        this.redis = redis
        this.customer = customer
        this.producer = producer
    }
    init() {
        if (
            typeof this.handle !== 'function' ||
            !Array.isArray(this.topics) ||
            typeof this.type !== 'string'
        ) {
            throw Error('init failed')
        }
        this.customer.registerHandle(this.handle.bind(this), {
            type: this.type,
            topics: this.topics,
        })
    }
    parseContent(msg) {
        try {
            console.log(JSON.parse(msg.content.toString()))
            return JSON.parse(msg.content.toString())
        } catch (e) {
            throw Error('parse error:' + msg.content.toString())
        }
    }
}

module.exports = Common
