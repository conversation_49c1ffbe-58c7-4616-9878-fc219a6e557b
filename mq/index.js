/*
 * @Date: 2022-10-09 10:23:21
 * @LastEditors: william.qian <EMAIL>
 * @LastEditTime: 2023-08-21 16:31:16
 * @LastEditTime: 2023-01-30 13:51:58
 * @FilePath: /newicp/mq/index.js
 */
// process.env.UV_THREADPOOL_SIZE = 128
// const ENV = require('../configs/env')

// global.CONFIG = require('../configs/' + ENV + '/config')

//用户侧，采集完成后，图片保存与结束Token之用
require('./finishH5/CancelToken.js')
require('./finishH5/SavePicture.js')
require('./finishH5/PushWebSocketForFinshH5.js')
require('./finishH5/CheckOtherAPIStatus.js')

// 订单完成后，对已备案的记录做操作
require('./icpHook/ResolveHook.js')
require('./icpHook/AddDomainForResolveOrder.js')
require('./icpHook/DelDomainForResolveOrder.js')
// 检查Redis中域名备案是否有效果
require('./icpHook/UCloudDomainReCheckFromRedis.js')
require('./icpHook/XinNetDomainReCheckFromRedis.js')
require('./uploadFile.js')

require('./checkBatch/CheckCompanyInfo.js')

//无资源有备案 相关mq customer

// 通过公司ID获取资源信息
require('./unResourceNotify/GetResourceFromCompanyId')
// 通过公司ID获取备案信息
require('./unResourceNotify/GetResourceICPInfo')
// 通过公司ID获取公司信息
require('./unResourceNotify/GetResourceCompanyInfo')
// // // 检查短信或者邮件的发送结果
require('./common/CheckUMSSendStatus')
require('./common/CheckBatchSendStatus')

// 同步代理公司与大客户白名单到系统
require('./proxyCompanyWhiteSync/SyncProxyCompanyWhiteToOtherRedis')

//已备案未接入
require('./unConnNotify/AddDomainToWhiteList')
require('./unConnNotify/AuditPassAddDomainToWhiteList')
require('./unConnNotify/DelDomainFromWhiteList')

require('./notify/GetCompanyInfo.js')
require('./notify/NotifyEmailOrSms.js')

require('./icpHook/AddDomainForResolveOrder.js')
require('./icpHook/ResolveHook')
require('./icpHook/DelDomainForResolveOrder.js')

require('./common/CompareHenganOnlineData')
require('./common/CompareHenganRedisIcpWebNo')

//已备案未接入通知mq
require('./notify/NoAccessNotify')
require('./notify/GetNoAccessCompanyInfoByIp')
require('./notify/GetNoAccessDomainStatus')
require('./notify/NoAccessNotifyRetry')
require('./notify/NoAccessNotifyAutoCreate')
require('./notify/CheckAllParseIsFinished')
require('./notify/SetNoAccessDNSRedis')
require('./notify/GetNoAccessDomainWebNo')
require('./notify/CheckDomainICPInOther')

//增加封禁域名MQ
require('./noAccessBlock/CheckBlockBatchFinish')
require('./noAccessBlock/BlockDoaminForNoAccess')
require('./noAccessBlock/UnBlockDomainByOrderFinish')

// 收费订单自动通知扣费
require('./bill/CheckBatchSendAndPushBill')
require('./bill/CreateBillNotifyBatch')
require('./bill/NotifyUsedBill')
require('./bill/CheckBillPayStatus')

require('./order/CheckIPIsTwoLine')
require('./order/UpdateTwoLineIPsToICP')
require('./order/CheckAndClearOrder')
// 信息检查
require('./syncICPDomainRedisCache/AddDomainToCache')
require('./syncICPDomainRedisCache/ChangeDomainToCache')
require('./syncICPDomainRedisCache/DelDomainFromCache')

require('./syncLocalICPDBFromHengan/SyncLocalICPDBFromHengan')

// 数据上报给工信部
require('./common/GetICPInfoAndSendToMIIT')
