/*
 * @file
 * 接收UCloudDomainReCheckFromRedis的请求,查是否备案，如否，查是否在UCloud备案
 * 由每周的定时任务发起。
 * 查不到。删除Redis中的记录。查到，标记为异常。需要人确定
 * 一直使用游标scan查询，一次查一条
 * 2021年11月08日17:03:16 update 更新逻辑，前面随机推8个任务。由8个消费者开始处理
 * 第一轮随机取8个数，做为游标，开始请求，处理完成，从表内删除Key
 * 直到当前表记录空后，停止任务。否则一直请求
 */

const Common = require('../Common')
const producer = require('../../libs/producer')
const _ = require('lodash')
const punycode = require('punycode')
const { axiosRequest } = require('../../fns/axiosRequestNoLog')
const { CONNECTSYNCVALUE } = require('../../configs/')
const { sleep } = require('../../fns/kits')
const {
    updateConnectSyncLog,
} = require('../../fns/connect_check_fns/connect_change_log')
const uuid = require('uuid/v4')
const logger = require('../../libs/logger').getLogger('mqCustomer')
class UCloudDomainReCheckFromRedis extends Common {
    constructor(params) {
        super(params)
        this.topics = ['UCloudDomainReCheckFromRedis']
        this.type = 'icp'
        this.name = 'UCloudDomainReCheckFromRedis'
        this.init()
    }
    async handle(msg) {
        let uid = uuid()
        logger.info(`${this.name}[${uid}] - start`, msg.content.toString())

        let self = this
        let redis = self.redis.get()
        // 取出关键信息
        let {
            BatchName,
            Cursor, // 开始时，随机取
        } = this.parseContent(msg)


        let domainRecord,
            newCursor = 0,
            domain,
            punyDomain,
            icpWebNo,
            metaInfo = {}
        try {
            // 开始执行
            // 从Redis中取出一条记录
            try {
                // 返回值参考：['40960', ['mail-qiye163.com', '苏ICP备19020700号-1']] 第一个数为游标
                // 使用游标来控制任务的开始与结束，为0时任务结束
                domainRecord = await redis.hscan(BatchName, Cursor, 'COUNT', 1)
            } catch (error) {
                throw '执行Redis中获取域名操作出错：' + 'BatchName' + error
            }

            // 解构赋值
            let redisData
            ;[newCursor, redisData] = domainRecord

            // 流程结束的逻辑
            if (redisData.length === 0) {
                // 如果游标返回了0，代表迭代结束了，此时所以的key都被迭代过了
                // 执行删除Key？是否可以不删除，由Key自动过期
                let batchLen = await redis.hlen(BatchName)
                // 发现有异常，加个判断
                if (batchLen === 0) {
                    await redis.del(BatchName)
                    // 结束查询

                    return
                }
            }
            // 先推送
            await producer.send({
                type: 'icp',
                topic: 'UCloudDomainReCheckFromRedis',
                data: {
                    BatchName,
                    Cursor: parseInt(newCursor),
                },
            })

            if (redisData.length === 0) {
                return
            }
            ;[domain, icpWebNo] = redisData

            // 游标不为0，继续检查，查询是否备案
            var pattern = new RegExp('[\u4E00-\u9FA5]+')

            if (pattern.test(domain)) {
                punyDomain = punycode.toASCII(domain)
            } else {
                punyDomain = domain
            }

            // 执行查询
            let recordInICP

            try {
                recordInICP = await axiosRequest({
                    uri: global.CONFIG.aoDunICPQuery + punyDomain,
                })
                recordInICP = recordInICP.data
            } catch (error) {
                throw '查询域名时网络出错：' + domain + error
            }
            if (recordInICP === false) {
                // 查询发现备案已取消，消息推给DelDomainForResolveOrder做二次检查
                await producer.send({
                    type: 'icp',
                    topic: 'DelDomainForResolveOrder',
                    data: { Domain: domain, ICPWebNo: icpWebNo },
                })

                await sleep(500)
            }
            // 删除查询记录,无论成功失败，都需要删除

            // 继续执行
        } catch (error) {
            // 出错更新到日志，出错不重试
            // 失败的话，做下记录，保存到时数据库中等待手工处理
            // 重新推送
            logger.error(
                `${this.name}[${uid}] - finish with error: ${error}`,
                msg.content.toString()
            )
        }
        await redis.hdel(BatchName, domain)
    }
}

module.exports = new UCloudDomainReCheckFromRedis()
