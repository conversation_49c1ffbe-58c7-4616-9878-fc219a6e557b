/*
 * @file
 * 此mq处理是所有redis缓存删除更新的入口
 * 接收DelDomainForResolveOrder的请求
 * 在收到时，已处理过，入参为域名，确定域名是否备案就行。
 * 查不到。删除Redis中的记录。查到，标记为异常。需要人确定
 * update:2021年11月10日18:22:03    如果确定是失效，同步一次ICP信息
 */

const Common = require('../Common')
const producer = require('../../libs/producer')
const henganApi = require('../../libs/henganApiPromise')
const { getICPMainNoByICPWebNo, sleep } = require('../../fns/kits')
const {
    updateConnectSyncLog,
} = require('../../fns/connect_check_fns/connect_change_log')
const { CONNECTSYNCVALUE } = require('../../configs/')
const moment = require('moment')
const uuid = require('uuid/v4')
const ICPSyncFromAodun = require('../../methods/common/ICPSyncFromAodun')
const logger = require('../../libs/logger').getLogger('mqCustomer')
class DelDomainForResolveOrder extends Common {
    constructor(params) {
        super(params)
        this.topics = ['DelDomainForResolveOrder']
        this.type = 'icp'
        this.name = 'DelDomainForResolveOrder'
        this.init()
    }
    async handle(msg) {
        await sleep(1000)
        let uid = uuid()
        logger.info(`${this.name}[${uid}] - start`, msg.content.toString())
        let self = this
        let redis = self.redis.get()

        // 取出关键信息
        /**
         * Id 订单审核通过后 日志记录的Id,
         * 对于备案同步 先不记录日志 ，因为之前就没有记录，总的来说 目前的日志意义不大，后续再陆续完善对于缓存更新的日志记录处理
         */
        let { Domain, ICPWebNo } = this.parseContent(msg)


        let icpInfoInUcloud,
            metaInfo = {}
        try {
            // 开始执行
            // 如果是注销主体。检查出主体下的全部网站，然后推送检查
            try {
                icpInfoInUcloud = await henganApi('SelectICPInterface', {
                    KeyWordType: 4,
                    Keyword: Domain,
                })
            } catch (error) {
                throw '查询时网络出错：' + Domain + error
            }

            if (icpInfoInUcloud.RetCode !== 0) {
                throw '查询时,接口出错：' + Domain + icpInfoInUcloud.Message
            }
            // 增加 查询时，过滤APP域名备案，因为APP是根据包名来确定的不管域名的事
            if (
                icpInfoInUcloud.ICPInfos &&
                icpInfoInUcloud.ICPInfos.length !== 0 &&
                icpInfoInUcloud.ICPInfos[0].websiteList.find((web) => {
                    if (
                        web.domainList.find((list) => list.topdomain == Domain) &&
                        web.websiteserviceTypes == 1
                    ) {
                        return web
                    }
                })
            ) {
                throw '查询时,此域名存在备案：' + Domain
            }

            // 正常流程域名备案已取消，删除Redis记录
            try {
                await redis.hdel('domain_icp_in_ucloud_map', Domain)
                // 做下次同步
                if (ICPWebNo) {
                    let method = new ICPSyncFromAodun((RetCode, data) => {
                        if (RetCode !== 0)
                            logger.error(
                                'DelDomainForResolveOrder 同步傲盾失败 ' +
                                    data?.Message || ''
                            )
                    })
                    await method.exec({
                        ICPMainNo: getICPMainNoByICPWebNo(ICPWebNo),
                        __ssoUser: 'DelDomainForResolveOrder Operator',
                    })
                }
                metaInfo = {
                    Message: 'Delete Domain' + Domain,
                }
            } catch (error) {
                throw '执行Redis中删除操作出错：' + Domain
            }

            logger.info(`${this.name}[${uid}] - finish with success`)
        } catch (error) {
            // 更新到日志，出错
            logger.error(
                `${this.name}[${uid}] - finish with error: ${error}`,
                msg.content.toString()
            )

        }
    }
}

module.exports = new DelDomainForResolveOrder()
