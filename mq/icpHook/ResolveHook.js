/*
 * @file
 * 接收ResolveOrder的请求。入参为订单号
 * 如果是新增类的，成功的话，在Redis中增加域名，如果是注销的话，则删除域名。
 * 执行Redis更新前，需要核验信息在恒安是否可查
 */

const Common = require('../Common')
const { getTableModel, parseJSON } = require('../../fns/kits')
const producer = require('../../libs/producer')
const _ = require('lodash')
const { mongoResultCheck } = require('../../fns/kits')
const ObjectId = require('mongodb').ObjectId
const { CONNECTSYNCVALUE, CONNECTSYNCCOMMENT } = require('../../configs/')
const moment = require('moment')
const { Op } = require('sequelize')
const {
    updateConnectSyncLog,
} = require('../../fns/connect_check_fns/connect_change_log')
const uuid = require('uuid/v4')
const logger = require('../../libs/logger').getLogger('mqCustomer')
class ResolveHook extends Common {
    constructor(params) {
        super(params)
        this.topics = ['ResolveHook']
        this.type = 'icp'
        this.name = 'ResolveHook'
        this.init()
    }
    async handle(msg) {
        await sleep(10 * 1000)
        let uid = uuid()
        logger.info(`${this.name}[${uid}] - start`, msg.content.toString())
        let self = this

        let { OrderNo } = this.parseContent(msg)

        let icpDatabase = self.db.get('icp')
        let OrderTable = getTableModel('t_order', icpDatabase)
        let WebTable = getTableModel('t_web', icpDatabase)


        let thisLog
        try {
            // 查询订单信息，确定订单是否完成
            thisLog = {
                OrderNo,
                Status: CONNECTSYNCVALUE['INIT'],
                CreateTime: parseInt(moment().format('X')),
                UpdateTime: parseInt(moment().format('X')),
                Logs: [
                    {
                        Time: parseInt(moment().format('X')),
                        Action: 'INIT',
                    },
                ],
            }

            let orderInfo = await OrderTable.findAll({
                limit: 1,
                attributes: [
                    'Status',
                    'Type',
                    'ICPWebNo',
                    'ICPMainNo',
                    'OrganizerLicenseType',
                    'OrganizerLicenseId',
                ],
                where: {
                    OrderNo: OrderNo,
                },
            })

            orderInfo = parseJSON(orderInfo)

            if (orderInfo.length !== 1) {
                throw '未找到订单'
            }

            if (orderInfo[0]['Status'] !== 12) {
                throw '未完成的订单'
            }

            // 拼接
            orderInfo = orderInfo[0]

            // 根据类型，推送不同的执行方案，增加或者取消
            if (_.indexOf([1, 2, 3], orderInfo.Type) != -1) {
                // 增加类Type 1\2\3 MQ  addDomainForResolveOrder
                // 取主办单位证件号,供后续查询之用
                let data = _.pick(orderInfo, [
                    'OrganizerLicenseType',
                    'OrganizerLicenseId',
                ])

                // 执行状态更新
                await producer.send({
                    type: 'icp',
                    topic: 'AddDomainForResolveOrder',
                    data,
                })
                // 增加数据库记录
            } else if (_.indexOf([4, 5, 6], orderInfo.Type) != -1) {
                // 删除类Type 4\5\6 MQ  delDomainForResolveOrder
                // 注销主体就查主体备案号，注销网站与接入，就查网站备案号
                let queryJson = {}
                if (orderInfo.Type === 4) {
                    // 查询此主体在UCloud下全部网站
                    queryJson = {
                        ICPWebNo: {
                            [Op.like]: orderInfo.ICPMainNo + '%',
                        },
                    }
                } else {
                    queryJson = {
                        ICPWebNo: orderInfo.ICPWebNo,
                    }
                }

                // 查到这个主体下目前可能的全部网站
                let webList = await WebTable.findAll({
                    attributes: ['Domain'],
                    where: queryJson,
                })
                webList = parseJSON(webList)

                let DomainList = []

                webList.forEach((web) => {
                    web.Domain.forEach((Domain) => {
                        DomainList.push(Domain.Domain)
                    })
                })


                for (const iterator of DomainList) {
                    console.log({ Domain: iterator })
                    await producer.send({
                        type: 'icp',
                        topic: 'DelDomainForResolveOrder',
                        data: { Domain: iterator },
                    })
                }

                // 增加数据库记录
            }
            logger.info(`${this.name}[${uid}] - finish with success`)
        } catch (error) {
            // 更新到日志，出错
            logger.error(
                `${this.name}[${uid}] - finish with error: ${error}`,
                msg.content.toString()
            )


            // 失败的话，做下记录，保存到时数据库中等待手工处理
        }
    }
}

const sleep = (millseconds = 3000) => {
    return new Promise((rs) => {
        setTimeout(() => rs(), millseconds)
    })
}


module.exports = new ResolveHook()
