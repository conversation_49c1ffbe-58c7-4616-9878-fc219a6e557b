/*
 * @file
 * XinNetDomainReCheckFromRedis
 * 得到请求，从Redis待查列表取出记录，
 * 查新网的备案接口，已备案不动，未备案的删除redis map中的数据
 * 无论是否有，都从待查列表删除
 * 2023年04月11日17:48:25   重构，使用spop解决游标为空的问题
 */

const Common = require('../Common')
const producer = require('../../libs/producer')
const ObjectId = require('mongodb').ObjectId
const henganApi = require('../../libs/henganApiPromise')
const {
    getDomainICPInfoInXinnet,
} = require('../../fns/connect_check_fns/xinNet')
const { sleep, getPunyDomain } = require('../../fns/kits')
const {
    updateConnectSyncLog,
} = require('../../fns/connect_check_fns/connect_change_log')
const { CONNECTSYNCVALUE } = require('../../configs/')
const uuid = require('uuid/v4')
const logger = require('../../libs/logger').getLogger('mqCustomer')
class XinNetDomainReCheckFromRedis extends Common {
    constructor(params) {
        super(params)
        this.topics = ['XinNetDomainReCheckFromRedis']
        this.type = 'icp'
        this.name = 'XinNetDomainReCheckFromRedis'
        this.init()
    }
    async handle(msg) {
        await sleep(1000)
        let uid = uuid()
        logger.info(`${this.name}[${uid}] - start`, msg.content.toString())
        let self = this
        let redis = self.redis.get()


        // 取出消息
        let { BatchName } = this.parseContent(msg)
        console.log({ BatchName })
        let icpInfoInXinNet,
            domain,
            metaInfo = {}

        try {
            try {
                // 使用游标来控制任务的开始与结束，为0时任务结束
                domain = await redis.spop(BatchName)
                console.log(domain)
            } catch (error) {
                throw '执行Redis获取记录过程中出错：' + error
            }

            if (domain === null) {
                // 如果pop出的为null说明没有数据
                return
            }

            // 使用punycode后的域名查询，但删除进需要使用旧域名
            let punyDomain = getPunyDomain(domain)

            // 开始执行
            // 如果是注销主体。检查出主体下的全部网站，然后推送检查
            try {
                icpInfoInXinNet = await getDomainICPInfoInXinnet(punyDomain)
            } catch (error) {
                throw '查询时出错：' + domain + error
            }

            let promiseList = []
            // 如果是没查到接入，则从Map中删除，且记录日志
            if (icpInfoInXinNet.localRecord !== true) {
                promiseList.push(redis.hdel('domain_icp_in_other_map', domain))

            }

            // 无论是否存在都要做的操作，删除查询List中的记录，继续任务
            promiseList.push(continueSelect(producer, BatchName))

            await Promise.all(promiseList)
            return
        } catch (error) {
            // 更新到日志，出错
            // 失败需要继续推
            logger.error(
                `${this.name}[${uid}] - finish with error: ${error}`,
                msg.content.toString()
            )

            await redis.srem(BatchName, domain)
            await continueSelect(producer, BatchName)
            return
        }
    }
}

/**
 * @description: 推送MQ 继续执行查询
 * @param {*} producer
 * @param {string} checkBatchName 检查批次名
 * @param {string} insertId  插入id
 * @param {*} Cursor
 * @return {*}
 */
async function continueSelect(producer, checkBatchName, Cursor = 0) {
    return producer.send({
        type: 'icp',
        topic: 'XinNetDomainReCheckFromRedis',
        data: {
            BatchName: checkBatchName,
        },
    })
}

module.exports = new XinNetDomainReCheckFromRedis()
