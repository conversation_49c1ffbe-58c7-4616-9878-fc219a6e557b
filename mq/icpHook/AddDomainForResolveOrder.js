/*
 * @file
 * 此mq处理是所有redis缓存添加更新的入口
 * 接收AddDomainForResolveOrder的请求
 * 收到后map取出需要更新的域名。与线上Redis对比
 * 更新Mongo中Meta信息，批量设置域名
 */

const Common = require('../Common')
const { sleep } = require('../../fns/kits')
const _ = require('lodash')
const henganApi = require('../../libs/henganApiPromise')
const {
    updateConnectSyncLog,
} = require('../../fns/connect_check_fns/connect_change_log')
const { CONNECTSYNCVALUE } = require('../../configs/')
const uuid = require('uuid/v4')
const logger = require('../../libs/logger').getLogger('mqCustomer')
class AddDomainForResolveOrder extends Common {
    constructor(params) {
        super(params)
        this.topics = ['AddDomainForResolveOrder']
        this.type = 'icp'
        this.name = 'AddDomainForResolveOrder'
        this.init()
    }
    async handle(msg) {
        await sleep(1000)
        let uid = uuid()
        logger.info(`${this.name}[${uid}] - start`, msg.content.toString())

        let self = this
        let redis = self.redis.get()

        // 取出关键信息
        /**
         * Id 订单审核通过后 日志记录的Id,
         * 对于备案同步 先不记录日志 ，因为之前就没有记录，总的来说 目前的日志意义不大，后续再陆续完善对于缓存更新的日志记录处理
         * 此mq会把 整个主体下的所有域名加入缓存，所以不管入参是主办单位证件信息，还是域名，一个主体只需要推送一次即可
         */
        let { OrganizerLicenseType, OrganizerLicenseId, Domain } =
            this.parseContent(msg)


        let icpInfoInUcloud,
            metaInfo = {}
        try {
            try {
                // 如果域名不为空值，取域名，如果为空取主体证件信息
                let selectObject = !Domain
                    ? {
                          KeyWordType: 3,
                          CertificateType: parseInt(OrganizerLicenseType),
                          Keyword: OrganizerLicenseId,
                      }
                    : {
                          KeyWordType: 4,
                          Keyword: Domain,
                      }
                icpInfoInUcloud = await henganApi(
                    'SelectICPInterface',
                    selectObject
                )
            } catch (error) {
                throw '查询时网络出错' + error
            }
            // 查询通过主体信息查询恒安数据库，确定在我司备案是否存在.网络类出错会被下面Catch到

            if (icpInfoInUcloud.RetCode !== 0) {
                throw '查询时,接口出错' + icpInfoInUcloud.Message
            }

            if (
                !icpInfoInUcloud.ICPInfos ||
                icpInfoInUcloud.ICPInfos.length !== 1
            ) {
                throw '查询时,未查询到数据'
            }

            // 如果有标准的数据，取出域名与备案号，完成同步至Redis
            icpInfoInUcloud = icpInfoInUcloud.ICPInfos[0]

            // 示例[{Domain:xxxx,ICPWebNo:xxxx}]
            let webResult = [],
                domainsList = []

            // 记录网站与域名是为了方便查询
            icpInfoInUcloud.websiteList.forEach((web) => {
                if (web.websiteserviceTypes == 1) {
                    // 只处理网站备案的数据 APP备案的 不缓存域名
                    web.domainList.forEach((domain) => {
                        webResult.push({
                            Domain: domain.topdomain,
                            ICPWebNo: web.phylicnum,
                        })
                        domainsList.push(domain.topdomain)
                    })
                }
            })
            metaInfo.DomainList = domainsList
            // 开始同步到Redis
            // 得到目前域名与备案号的对应情况
            let domainMatchResult
            try {
                domainMatchResult = await redis.hmget(
                    'domain_icp_in_ucloud_map',
                    domainsList
                )
            } catch (error) {
                throw '检查Redis中结果出错'
            }

            // 与目前线上的结果做匹配
            for (let index = 0; index < domainsList.length; index++) {
                // 找到webResult中网站对应的备案号，确定是否相同。此主体下其它网站被注销的，由别一接口完成同步
                if (domainMatchResult[index] === null) {
                    // 此网站不存在，记录将执行新增加工作
                    metaInfo[domainsList[index]] = domainsList[index] + ':新增'
                } else {
                    // 确定是否相同
                    let thisDomainInHengan = _.find(webResult, {
                        Domain: domainsList[index],
                    })
                    if (
                        thisDomainInHengan.ICPWebNo === domainMatchResult[index]
                    ) {
                        metaInfo[domainsList[index]] =
                            domainsList[index] + ':匹配'
                    } else {
                        metaInfo[domainsList[index]] =
                            domainsList[index] +
                            ':不匹配' +
                            [
                                '旧备案号',
                                domainMatchResult[index],
                                '新备案号',
                                thisDomainInHengan.ICPWebNo,
                            ].join(' ')
                    }
                }
            }

            let domainMap = {}
            let domainList = []
            // 在此增加，域名
            webResult.map((row) => {
                domainList.push(row.Domain)
                domainMap[row.Domain] = row.ICPWebNo
            })

            if (domainList.length !== 0) {
                for (let index = 0; index < domainList.length; index++) {
                    await this.producer.send({
                        type: 'icp',
                        topic: 'UnBlockDomainByOrderFinish',
                        data: { Domain: domainList[index] },
                    })
                }
            }

            try {
                domainMatchResult = await redis.hmset(
                    'domain_icp_in_ucloud_map',
                    domainMap
                )
            } catch (error) {
                throw '执行Redis中结果出错'
            }

            logger.info(`${this.name}[${uid}] - finish with success`)
            // 执行更新操作
        } catch (error) {
            // 更新到日志，出错
            logger.error(
                `${this.name}[${uid}] - finish with error: ${error}`,
                msg.content.toString()
            )

            // 失败的话，做下记录，保存到时数据库中等待手工处理
            return
        }
    }
}

module.exports = new AddDomainForResolveOrder()
