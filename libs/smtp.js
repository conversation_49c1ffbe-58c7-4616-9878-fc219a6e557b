let smtp = null
const _ = require('lodash')
const nodemailer = require('nodemailer')

class SMTP {
    constructor() {
        if (!smtp) {
            this.init()
        }
    }
    init() {
        smtp = {}

        _.each(global.CONFIG.sendMail, (config, name) => {
            config.pool = true
            smtp[name] = nodemailer.createTransport(config)
        })
    }
    get(name) {
        if (!smtp[name]) {
            throw new Error('No Such SMTP Config')
        }

        return smtp[name]
    }
}

module.exports = new SMTP()
