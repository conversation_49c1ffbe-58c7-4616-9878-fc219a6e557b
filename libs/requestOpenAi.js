const logger = require('./logger')
const uuid = require('uuid/v4')
const moment = require('moment')
const { AzureOpenAI } = require('openai')

// modele留着 防止字段传输错乱 
async function callOpenAIGPT4(messages, modele = 'gpt4o', options) {
    const uid = uuid()

    // let config
    // if (modele === 'gpt4') {
    //     config = global.CONFIG.openAIConfig
    // } else if (modele === 'gpt4o') {
    let config = global.CONFIG.openAIGPT4oConfig
    // }

    const client = new AzureOpenAI(config)

    const startTime = moment()
    // gpt4o有文件类操作，不保存日志
    logger
        .getLogger('api')
        .info(
            '[' +
                uid +
                ']' +
                '请求OpenAI的GPT-4o模型的参数：' +
                JSON.stringify(messages)
        )

    let result
    try {
        result = await client.chat.completions.create({
            messages,
            model: '',
            ...options,
        })
    } catch (error) {
        console.error('获取OpenAI聊天完成出错:', error)
    }

    const customTime = moment().diff(startTime, 'milliseconds')
    logger
        .getLogger('api')
        .info(
            '[' +
                uid +
                ']' +
                'OpenAI的GPT-4o模型' +
                '请求耗时：' +
                customTime +
                'ms返回的结果：' +
                JSON.stringify(result)
        )

    return result
}

module.exports = callOpenAIGPT4
