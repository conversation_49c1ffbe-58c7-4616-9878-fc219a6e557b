var req = require('../index.js');

opt={ category: 'Account',
method: 'GET',
session:
 { public_key: 'ucloudwilliam.qian@ucloud.cn1476072002381557',
   secret_key: '81520503ae6186215158b5ec13355fb82b0eaad8' },
data:
 { CompanyName: 'Ucloud',
   Backend: 'Account',
   Action: 'GetCompanyInfoFuzzy' },
action: 'GetCompanyInfoFuzzy' }


console.log(typeof(opt))
req.request(opt, function (err, data) {
    console.log("111")
    if (err) {
        console.log('Test failed', err, data);
        return;
    }
    console.log(data)
});
