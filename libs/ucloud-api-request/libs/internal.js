var crypto = require('crypto');

var _ = require("underscore");
var request = require("request");
var uuid = require('uuid');

var defaultOptions;
var workingOptions;

var UserInfoKeys = {
    user_id: '_u_account_id',
    user_name: '_u_user_name',
    user_email: '_u_email',
    job_number: '_u_job_number'
};

module.exports.init = function(options) {
    defaultOptions = options;
    workingOptions = options;
};

module.exports.configure = function(options) {
    workingOptions = _.extend({}, defaultOptions, options);
};

/*
 * sha1
 * */
var sha1 = function(str) {
    var hash = crypto.createHash('sha1');
    return hash.update(str, 'utf8').digest('hex');
};

/*
 * 生成签名
 * key-value 先按 key 排序
 * 然后组装字符串，最后加上 privateKey
 * 然后算 sha1
 * */
var generateSign = function(params, privateKey) {
    var paramsData = [];

    var keys = Object.keys(params);
    keys.sort();
    keys.forEach(function(key) {
        Array.prototype.push.call(paramsData, key, params[key]);
    });

    var paramsStr = paramsData.join('');
    var toSignStr = paramsStr + privateKey;

    if (workingOptions.debug) {
        workingOptions.logger.info('To sign string', toSignStr);
    }
    var generated = sha1(toSignStr);

    return generated;
};

module.exports.sign = generateSign;

var responseError= function(callback, msg, res) {
    if (!!callback) {
        callback(msg);
    } else {
        res.json({
            RetCode: -1,
            Message: msg
        });
    }
};

var UUID_HEADER_NAME = 'ucloud-internal-request-uuid';
module.exports.gatewayRequest = function gatewayRequest(args, callback, pipeRes, direct, usingForm) {
    // 检查传入的参数
    if (!_.isFunction(callback) && !pipeRes) {
        workingOptions.logger.error('No call back provided for gatewayRequest');
        return;
    }

    if (!args || !_.isObject(args)) {
        process.nextTick(function() {
            responseError(callback, 'Invalid args for gatewayRequest', pipeRes);
        });
        return;
    }

    // 检查必要的参数是否存在
    var keys = ['category', 'action', 'method', 'session', 'data'];
    var missingKeys = [];
    keys.forEach(function(key) {
        var hasProperty = args.hasOwnProperty(key);
        if (!hasProperty) {
            missingKeys.push(key);
        }
    });

    // 如果必要的参数不存在，则返回错误
    if (missingKeys.length > 0) {
        process.nextTick(function() {
            responseError(callback, 'Missing args for gatewayRequest: ' + missingKeys.join(', '), pipeRes);
        });
        return;
    }

    var category = args.category;
    var action = args.action;
    var method = args.method || 'POST';
    var session = args.session;
    var data = args.data;

    if (!session.public_key || !session.secret_key) {
        process.nextTick(function() {
            responseError(callback, 'Missing public_key or secret_key', pipeRes);
        });
        return;
    }

    // 组装 action
    var namedAction = category + '.' + action;
    var url = workingOptions.url;

    // 允许配置的直接访问开发者的测试机器，而不用连到 API Gateway
    if (workingOptions.debugURLs && workingOptions.debugURLs[category]) {
        url = workingOptions.debugURLs[category];
        namedAction = action;

        // 从 session 中获取用户的信息，直接传到对应的 ip 端口
        var info = _.pick(session, 'user_id', 'user_name', 'user_email', 'job_number');
        var addtionInfo = {};
        _.each(info, function(val, key) {
            addtionInfo[UserInfoKeys[key]] = val;
        });

        _.extend(data, addtionInfo);
    }

    var params = _.extend({
        Action: namedAction,
        PublicKey: session.public_key
    }, data);

    var sign = generateSign(params, session.secret_key);
    params.Signature = sign;

    var options = _.extend({}, args.options, {
        uri: url,
        method: method
    });

    if (!options.headers || !options.headers[UUID_HEADER_NAME]) {
        options.headers = options.headers || {};
        options.headers[UUID_HEADER_NAME] = uuid.v4();
    }

    if (method === 'POST' || method === 'PUT') {
        if (usingForm) {
            options.form = params;
        } else {
            options.body = params;
            options.json = true;
        }
    } else {
        options.qs = params;
        // 设置 query parameter 的 stringify 的选项
        options.qsStringifyOptions = options.qsParseOptions = {
            sep: '&',
            eq: '=',
            options: {
                arrayFormat: 'brackets'
            }
        };
    }

    if (workingOptions.debug) {
        workingOptions.logger.info('Options', options);
    }

    // 发起请求
    var requestObj = request(options, function(error, response, body){
        if (error) {
            workingOptions.logger.error('Request failed', error, body);

            responseError(callback, 'NETWORK_OR_SERVER_ERROR', pipeRes);
            return;
        }

        if (callback) {
            // 如果返回的即是 JSON object
            if (typeof body === 'object') {
                callback(null, body);
                return;
            }

            var data;
            try {
                // 返回 JSON object
                data = JSON.parse(body);
            } catch (e) {
                workingOptions.logger.error('Parse json failed', e, body);

                responseError(callback, 'JSON_ERROR', pipeRes);
                return;
            }

            callback(null, data);
            return;
        }

        if (pipeRes && !direct ) {
            pipeRes.send(body);
        }
    });

    // 如果提供了 pipe res, 则把响应 pipe 出去
    if (pipeRes && direct) {
        requestObj.pipe(pipeRes);
    }
};
