var _ = require('underscore')

var internal = require('./libs/internal')

//    url: "http://internal.api.pre.ucloudadmin.com/",
//     url: "http://internal.api.ucloud.cn/",

// 默认的 gateway 的地址
var defaultOptions = {
    url: global.CONFIG.ucloudApi,
    debug: false,
    logger: console,
    debugURLs: {},
}

internal.init(defaultOptions)

module.exports.configure = internal.configure
module.exports.request = internal.gatewayRequest
module.exports.sign = internal.sign

function constructParams(req, session) {
    var method = req.method ? req.method.toUpperCase() : 'POST'
    var rawParams = method === 'GET' ? req.query : req.body
    session = session || req.session
    var params = _.extend({}, rawParams, {
        method: rawParams.method || method,
        session: _.pick(
            session,
            'public_key',
            'secret_key',
            'user_id',
            'user_name',
            'user_email',
            'job_number'
        ),
    })

    return params
}

/*
 * 提供一个接口可以直接 pipe response
 * 方便在网页前端直接调用 API
 *
 * 在 express 里，还需要加一个类似如下路由的处理
 * app.post('/gateway/api', function(req, res) {
 *    request.pipeRequest(req, res);
 * });
 *
 * */
module.exports.pipeRequest = function (req, res, session, usingForm) {
    var params = constructParams(req, session)
    internal.gatewayRequest(params, null, res, false, usingForm)
}

module.exports.pipeRequestv2 = function (req, res, session, usingForm) {
    var params = constructParams(req, session)
    internal.gatewayRequest(params, null, res, true, usingForm)
}

module.exports.pipeRawResponse = function (req, res, session, usingForm) {
    var params = constructParams(req, session)
    params.options = {
        encoding: null,
    }

    internal.gatewayRequest(params, null, res, false, usingForm)
}

/*
 * OA 系统的 API
 * */
module.exports.requestOA = function (args, callback) {
    args = _.extend(args, {
        category: 'OA',
    })

    internal.gatewayRequest(args, callback)
}

/*
 * 工单系统的 API
 * */
module.exports.requestTicket = function (args, callback) {
    args = _.extend(args, {
        category: 'Ticket',
    })

    internal.gatewayRequest(args, callback)
}

/*
 * 账户系统的 API
 * */
module.exports.requestAccount = function (args, callback) {
    args = _.extend(args, {
        category: 'Account',
    })

    internal.gatewayRequest(args, callback)
}

/*
 * 计费系统的 API
 * */
module.exports.requestBill = function (args, callback) {
    args = _.extend(args, {
        category: 'Bill',
    })

    internal.gatewayRequest(args, callback)
}

/*
 * 资源相关API
 * */
module.exports.requestResource = function (args, callback) {
    args = _.extend(args, {
        category: 'Resource',
    })

    internal.gatewayRequest(args, callback)
}
