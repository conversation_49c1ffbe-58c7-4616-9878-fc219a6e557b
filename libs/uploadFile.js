/*
 * @Date: 2022-09-05 11:16:55
 * @LastEditors: l<PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2023-06-06 11:19:17
 * @FilePath: /newicp/libs/uploadFile.js
 */
const moment = require('moment')
const crypto = require('crypto')
const request = require('request')

function sha1(str) {
    let hash = crypto.createHash('sha1')
    return hash.update(str, 'utf8').digest('hex')
}

function decodeBase64Image(dataString) {
    var response = {}
    var matches = dataString.split(';base64,')
    if (matches.length !== 2) {
        return new Error('Invalid input string')
    }

    response.type = matches[0].replace('data:', '')

    response.data = Buffer.from(matches[1], 'base64')

    return response
}

function generateFileName(pic, type) {
    // jpeg转jpg
    return (
        sha1(pic) +
        '.' +
        (type.split('/')[1] === 'jpeg' ? 'jpg' : type.split('/')[1])
    )
}

function upload(picture, fileName) {
    // 拼接 Auth Token
    let now = moment().format('x')

    let file = decodeBase64Image(picture)
    file.type = file.type !== 'image/jpeg' ? file.type : 'image/jpg'
    let filename = fileName ?? generateFileName(picture, file.type)

    if (!filename) {
        throw new Error('filename不能为空')
    }

    let auth =
        'UCloud ' +
        global.CONFIG.ufile.publicKey +
        ':' +
        crypto
            .createHmac('sha1', global.CONFIG.ufile.privateKey)
            .update('PUT\n\n' + file.type + '\n\n' + '/icp/' + filename)
            .digest()
            .toString('base64')
    return new Promise((resolve, reject) => {
        request.put(
            {
                url: global.CONFIG.ufile.target + '/' + filename,
                headers: {
                    'Content-Type': file.type,
                    Authorization: auth,
                },
                body: file.data,
            },
            (err, response, body) => {
                err || response.statusCode !== 200
                    ? reject(err || response.statusCode)
                    : resolve([filename, file.data])
            }
        )
    })
}

module.exports = { upload }
