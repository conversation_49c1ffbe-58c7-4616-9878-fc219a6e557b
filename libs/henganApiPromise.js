/*
 * @Date: 2023-05-08 14:39:31
 * @LastEditors: l<PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2023-05-08 14:39:45
 * @FilePath: /newicp/libs/henganApiPromise.js
 */
const uuid = require('uuid/v4')
const request = require('request')
const logger = require('./logger')
const defaultTimeout = 20 * 1000

// 内部API
module.exports = function henganApiPromise(action, options) {
    return new Promise((resolve, reject) => {
        let uid = uuid()
        if (options === undefined) {
            logger
                .getLogger('error')
                .error('[' + uid + ']NO ACTION OR CATEGORY IN OPTIONS')
            reject(new Error('NO ACTION OR CATEGORY IN OPTIONS'), null)
        }

        logger
            .getLogger('api')
            .info('[' + uid + '] Options:' + JSON.stringify({...options, Action: action}))
        let timeout = options.timeout || defaultTimeout
        delete options.timeout

        var params = {
            method: 'POST',
            timeout,
            url: global.CONFIG.verifyAPI.epicEntPersonRelationUrl,
            qs: {
                Action: action,
            },
            headers: {
                'Content-Type': 'application/json',
            },
            body: options,
            json: true,
        }

        request(params, function (error, response, body) {
            error
                ? logger.getLogger('api').error('[' + uid + ']', error.message)
                : logger
                      .getLogger('api')
                      .info('[' + uid + ']', JSON.stringify(body))

            error ? reject(error) : resolve(body)
        })
    })
}
