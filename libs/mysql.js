const { Sequelize } = require('sequelize')
const _ = require('lodash')
const moment = require('moment')

let db = null
let dbMethodCache = {}
class DB {
    constructor() {
        if (!db) {
            this.init()
        }
    }
    init() {
        db = {}
        console.log(global.CONFIG.db)
        _.each(global.CONFIG.db, (config, name) => {
            // db[name] = new Mysql.Adapter(config)

            db[name] = new Sequelize(
                config.database,
                config.username,
                config.password,
                {
                    host: config.server,
                    dialect: 'mysql',
                    // logging: false,
                    define: {
                        timestamps: false,
                        createdAt: 'create_time',
                        updatedAt: 'update_time',
                        hooks: {
                            beforeUpdate: (instances) => {
                                instances.UpdateTime = moment().format('X')
                            },
                            beforeBulkUpdate: (option) => {
                                option.individualHooks = true
                            },
                            beforeCreate: (instances) => {
                                instances.CreateTime = moment().format('X')
                                instances.UpdateTime = moment().format('X')
                            },
                            beforeBulkCreate: (instances) => {
                                for (const instance of instances) {
                                    instance.CreateTime = moment().format('X')
                                    instance.UpdateTime = moment().format('X')
                                }
                            },
                        },
                    },
                    timezone: '+08:00', //  时区设置
                    pool: {
                        max: 10,
                        min: 1,
                        acquire: 30000,
                        idle: 10000,
                    },
                }
            )
        })
    }
    get(name) {
        if (!db[name]) {
            throw new Error('No Such DB Config')
        }

        return db[name]

        // const sequelize = new Sequelize('database', 'username', 'password', {
        // 	host: 'localhost',
        // 	dialect: /* 选择 'mysql' | 'mariadb' | 'postgres' | 'mssql' 其一 */
        //   });
    }
    getDBMethod(name) {
        if (dbMethodCache[name]) return dbMethodCache[name]
        dbMethodCache[name] = dbMethodWrap(db[name])
        return dbMethodCache[name]
    }
}

function dbMethodWrap(db) {
    return {
        find(tName, selector, options = {}) {
            return new Promise((rs, rj) => {
                db.select(options.fields)
                    .where(selector)
                    .group_by(options.group_by)
                    .order_by(options.order_by)
                    .limit(options.limit, options.offset)
                    .get(tName, (err, records) => (err ? rj(err) : rs(records)))
            })
        },
        findOne(tName, selector, options = {}) {
            return new Promise((rs, rj) => {
                db.select(options.fields)
                    .where(selector)
                    .limit(1)
                    .get(tName, (err, records) => {
                        if (err) return rj(err)
                        if (records.length === 0) return rs(null)
                        rs(records[0])
                    })
            })
        },
        insert(tName, docs) {
            return new Promise((rs, rj) => {
                db.insert(tName, docs, (err, data) =>
                    err ? rj(err) : rs(data)
                )
            })
        },
        update(tName, selector, updates) {
            return new Promise((rs, rj) => {
                db.where(selector).update(tName, updates, (err, updateRes) =>
                    err ? rj(err) : rs(updateRes)
                )
            })
        },
        insertIgnore(tName, doc) {
            return new Promise((rs, rj) => {
                db.insert_ignore(tName, doc, (err, data) =>
                    err ? rj(err) : rs(data)
                )
            })
        },
        count(tName, selector) {
            return new Promise((rs, rj) => {
                db.select('count(*) As count')
                    .where(selector)
                    .get(tName, (err, body) => {
                        if (err) return rj(err)
                        rs(body[0].count)
                    })
            })
        },
        delete(tName, selector) {
            return new Promise((rs, rj) => {
                db.where(selector).delete(tName, (err) =>
                    err ? rj(err) : rs()
                )
            })
        },
    }
}

module.exports = new DB()
