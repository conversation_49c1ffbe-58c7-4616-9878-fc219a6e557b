/**
 * 任务发送封装类
 * 目前 rabbitmq 的分配策略是：
 * 一种发送渠道(如 email)对应一个 producer
 * 而一个 producer 下面对应一个 conn 一个 channel 一个 exchange
 * 这样当某种发送服务不可用的情况下，不至于导致于影响到其他发送服务
 */

const assert = require('assert')
const Producer = require('./rabbitmqProducer')
const debug = require('debug')('mqProducer')
const logger = require('../logger').getLogger('mqProducer')
const logger4Warn = require('../logger').getLogger('error')
const { context, trace } = require('@opentelemetry/api')
module.exports = class TaskSender {
    constructor(conf = {}) {
        const { serverUrl, tasks } = conf

        assert(serverUrl, 'connUrl cannot be empty')

        // 配置信息
        this.conf = {
            connUrl: serverUrl,
            exchanges: [
                ...new Set(
                    tasks.map((item) =>
                        typeof item === 'object' ? item.type : item
                    )
                ),
            ],
            tasks,
        }

        // 已创建的 taskProducer
        this.taskProducers = {}
    }

    // 初始化, 预分配 taskProducer
    async init() {
        let { exchanges = [] } = this.conf

        await Promise.all(exchanges.map(this.assert.bind(this)))
    }

    // 清除所有 taskProducer 并关闭连接
    async clean() {
        const producers = Object.values(this.taskProducers).filter(
            (taskProducer) => !!taskProducer
        )

        await Promise.all(producers.map((producer) => producer.close()))

        this.taskProducers = {}
    }

    // 分配特定类型的 taskProducer
    async assert(type) {
        if (this.taskProducers[type]) {
            return this.taskProducers[type]
        }

        const { connUrl, tasks } = this.conf

        const currTask = tasks[tasks.findIndex((info) => info.type === type)]
        const exchange_type = currTask?.exchange_type || 'direct'

        const producer = new Producer({
            connUrl,
            defaultAE: {
                name: 'unrouted-ex',
                type: 'fanout',
                processor: this.aeProcessor,
            },
        })

        // 建立与 MQ Server 连接
        await producer.init()
        // 分配相应类型的 exchange
        await producer.assertExchange(type, exchange_type)
        // 缓存复用已分配的 producer
        this.taskProducers[type] = producer

        debug('-- assert task sender --', type)

        return producer
    }

    // 发送消息
    async send({ type, topic, id, data, opts = {} }) {
        let producer = this.taskProducers[type]
        if (!producer) {
            // producer = await this.assert({ type })
            producer = await this.assert(type)
        }
        // const tracer = trace.getTracer(`newicp-console`)
        // const parentSpan = trace.getActiveSpan().spanContext().spanId
        // const span = tracer.startSpan('send message', {
        //     parent: parentSpan,
        // })
        // span.setAttribute('queueName', `${type}-${topic}`)
        // // 将当前span Context序列化 传入mq中 ，以便在消费端反序列化该对象，来继续追踪该请求的调用链路
        // let currSpanContext = trace.wrapSpanContext(
        //     trace.getActiveSpan().spanContext()
        // )
        // let spanHeader = {
        //     currSpanContext: JSON.stringify(currSpanContext),
        //     'trace-id': span.spanContext().traceId,
        //     'span-id': span.spanContext().spanId,
        // }
        // if (opts.headers) {
        //     opts.headers = Object.assign(opts.headers, spanHeader)
        // } else {
        //     opts.headers = spanHeader
        // }
        producer.publish(
            type,
            topic,
            data,
            Object.assign(
                {
                    messageId: id,
                    contentType: 'application/json',
                },
                opts
            ),
            this.producerAck
        )
        // span.end()
        return
    }

    /**
     * 发送到 MQ Server 状态确认回调
     * （待验证）不管 server 有没有接受到，unroutable 的情况照样 ack 正常发送
     * @param {Error} err MQ Server 返回的发送失败信息
     */
    producerAck(err) {
        logger.info(`senderAck ${JSON.stringify(err)}`)
    }

    /**
     * 不可路由消息处理
     * @param {Object} msg unroutable 消息
     */
    aeProcessor(msg) {
        logger4Warn.warn(
            `<AE processor> messageId: ${
                msg.properties.messageId
            } - ${msg.content.toString()}`
        )
        return true
    }
}
