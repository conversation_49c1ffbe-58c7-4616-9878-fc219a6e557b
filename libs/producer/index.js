// 任务发送接口

const TaskSender = require('./taskSender')
const logger = require('../logger').getLogger('mqProducer')

const MQ = global.CONFIG.mq

// 初始化 taskSender
const taskSender = new TaskSender(MQ)
taskSender.init().catch((err) => {
    logger.error(`taskSender init fail: ${err}`)

    // 启动时 MQ 连接失败，程序退出
    console.log('mq init fail, process exit')

    process.nextTick(process.exit)
})

module.exports.taskSender = taskSender

/**
 * 发送消息给 MQ
 * @param {String} type   消息推送渠道 ['email', 'sms', ...]
 * @param {String} topic  消息归类 ['common', ...]
 * @param {String} id     消息ID
 * @param {Object} data   消息实体
 */
module.exports.send = async ({ type, topic, id, data, opts }) => {
    return taskSender.send({
        type,
        topic,
        id: String(id),
        data,
        opts,
    })
}
