/**
 * RabbitMQ 发送方
 * - MQ Server 连接、channel、exchange 封装
 * - publisher 端 confirm 机制及未被路由的消息处理
 * - 任务投递状态反馈
 */

const assert = require('assert')
const amqp = require('amqplib')
const debug = require('debug')('mq')
const logger = require('../logger').getLogger('mqProducer')

class Producer {
    /**
     * mq producer 构造函数
     * @param {any} {
     * 	connUrl             [必填] MQ server 连接 URL
     * 	socketOpts = {}			[可选] 其他连接参数
     * 	defaultAE = true    [可选] alternateExchange 配置，不设会默认配
     * 	publishConfirm      [可选] 消息发送后的回调函数
     *  unroutableHandler   [可选] 不可路由的消息处理函数
     * } 配置参数
     * @memberof Producer
     */
    constructor({
        connUrl,
        socketOpts = {},
        defaultAE = true,
        publishConfirm,
        unroutableHandler,
        retryOpts = {},
    }) {
        // 参数校验
        assert(connUrl, 'connUrl cannot be empty')
        assert(
            !publishConfirm || typeof publishConfirm === 'function',
            'publishConfirm must be a function'
        )
        assert(
            !unroutableHandler || typeof unroutableHandler === 'function',
            'unroutableHandler must be a function'
        )

        // mq server 连接相关参数
        this.connUrl = connUrl
        this.socketOpts = socketOpts

        // 配置默认的 alternateExchange 配置参数
        this.defaultAE = defaultAE
        if (defaultAE) {
            if (typeof defaultAE === 'boolean') defaultAE = {}
            let {
                name = 'unrouted-ex',
                queue,
                type = 'fanout',
                processor,
                opts = {},
            } = defaultAE
            queue = typeof queue === 'string' ? queue : `queue-${name}-${type}`
            this.defaultAE = { name, queue, type, processor, opts }
        }

        // 消息发送后的回调函数
        this.publishConfirm = publishConfirm

        // 不可路由的消息处理函数
        this.unroutableHandler = unroutableHandler

        // 当前 producer 的一些实例
        this.conn = null // 连接实例
        this.ch = null // channel 实例
        this.AE = null // alternateExchange 实例
        this.exchanges = {} // 当前 producer 上已分配的所有 exchange (目前策略一个 producer 实际只分配一个普通 exchange + 一个 alternateExchange)

        // 重连相关参数
        const { retryMax = 20, retryDelay = 3000, forceExit = true } = retryOpts
        this.retring = false
        this.retryCount = 0
        this.retryMax = retryMax
        this.retryDelay = retryDelay
        this.retryForceExit = forceExit
    }

    /**
     * producer 初始化
     * 建立连接、分配 channel、分配 alternateExchange
     * @returns
     * @memberof Producer
     */
    async init() {
        await this.connect()
        await this.assertChannel()
        await this.assertAE()

        logger.info('mq producer init success')
        return this
    }

    /**
     * 建立连接
     * 建立连接，监听连接状态，出错重连
     * @returns
     * @memberof Producer
     */
    async connect() {
        // 连接已建立，直接 return
        if (this.conn) {
            return this.conn
        }

        console.log('connnect start')
        console.log(this.connUrl, this.socketOpts)
        // 建立连接
        const conn = await amqp.connect(this.connUrl, this.socketOpts)
        this.conn = conn
        console.log('connnect success')
        // 连接报错时，重连
        conn.on('error', (err) => {
            logger.info('connect error', err)
            debug('connect error', err)

            this.reconnect()
        })

        // 某些情况，连接未报错，但 close 了，则需要延时重连
        // 注：不可直接调用重连方法，程序正常关闭时不需要重连
        conn.on('close', () => {
            logger.info('connect close')
            debug('connect close')

            // 3秒后检测是否需要 reconnect
            if (!this.retring) {
                setTimeout(() => {
                    this.reconnect()
                }, this.retryDelay)
            }
        })

        return conn
    }

    /**
     * 重连
     * 重连失败，会继续重连操作，直至连接成功或者达到重连次数上限
     * @returns
     * @memberof Producer
     */
    async reconnect() {
        // 已经处于重连状态，直接 return
        if (this.retring) {
            return false
        }

        this.retring = true

        // 尝试关闭(一些情况导致调用 close 无返回，暂弃用)
        // await this.close()
        this.__clear()

        this.retryCount++

        // 超出最大重连次数退出程序
        if (this.retryCount > this.retryMax) {
            this.retryCount--
            logger.info(
                `reconnect fail: try ${this.retryCount} times, close process`
            )
            debug(`reconnect fail: try ${this.retryCount} times, close process`)

            if (this.retryForceExit) {
                console.log('mq reconnect fail, process exit')
                process.nextTick(process.exit)
            }
            return false
        }

        debug(`reconnect: NO.${this.retryCount}`)

        // 延时执行重连操作，返回重连结果
        const ret = await new Promise((resolve) => {
            setTimeout(async () => {
                try {
                    // 重新初始化
                    await this.init()

                    // 重新申请 exchange
                    await Promise.all(
                        Object.values(this.exchanges).map(
                            ({ name, type, opts }) =>
                                this.assertExchange(name, type, opts)
                        )
                    )

                    // 重连成功
                    resolve(true)
                } catch (e) {
                    // 重连失败
                    resolve(false)
                }
            }, this.retryDelay)
        })

        logger.info(
            `reconnect: NO.${this.retryCount} - ${ret ? 'success' : 'fail'}`
        )
        debug(`reconnect: NO.${this.retryCount} - ${ret ? 'success' : 'fail'}`)

        this.retring = false

        // 重连失败，继续重连
        if (!ret) {
            return this.reconnect()
        }

        // 重连成功，retryCount 清零
        this.retryCount = 0

        return ret
    }

    /**
     * 清除当前实例的属性信息
     * (内部调用)
     * @memberof Producer
     */
    __clear() {
        this.conn = null
        this.ch = null
        this.AE = null

        // 清除当前 producer 已分配 exchange 的实例信息
        // 但保留 exchange 的配置信息 以供重连时使用
        this.exchanges = Object.values(this.exchanges).reduce(
            (exchanges, { name, type, opts }) => {
                exchanges[name] = { name, type, opts }
                return exchanges
            },
            {}
        )
    }

    /**
     * 关闭连接
     * 同时会清除掉连接上的 channel exchange
     * @returns
     * @memberof Producer
     */
    async close() {
        if (!this.conn) {
            this.__clear()

            return false
        }

        await this.conn.close().catch(() => logger.info('close conn error'))

        this.__clear()

        return true
    }

    /**
     * 申请有确认机制的 channel
     * @returns
     * @memberof Producer
     */
    async assertChannel() {
        // channel 已申请，直接 return
        if (this.ch) {
            return this.ch
        }

        const conn = await this.connect()

        // 申请有确认机制的 channel
        const ch = await conn.createConfirmChannel()
        this.ch = ch

        // channel 报错 重连
        // channel 报错时 conn 也会报错，故在 reconnect 函数中有重连状态的限制，避免重复调用
        ch.on('error', (err) => {
            logger.error(`channel got an error: ${err}`)
            debug(`channel got an error: ${err}`)

            this.reconnect()
        })

        // channel 意外 close 时 延时重连
        ch.on('close', () => {
            logger.info('channel close')
            debug('channel close')

            // 3秒后检测是否需要 reconnect
            if (!this.retring) {
                setTimeout(() => {
                    this.reconnect()
                }, this.retryDelay)
            }
        })

        // 不可路由的信息 会被 channel return 回来
        // 注：如果配置了 alternateExchange（默认有配置） 则消息流向 AE，不会成为 unroutable msg return 到这里
        ch.on('return', (msg) => {
            logger.info('unroutable msg', msg.fields, msg.properties.messageId)
            if (typeof this.unroutableHandler === 'function') {
                this.unroutableHandler(msg)
            }
        })

        return ch
    }

    // 根据 name 获取已申请 exchange 实例
    getExchange(name) {
        return (this.exchanges[name] && this.exchanges[name].exchange) || null
    }

    /**
     * 申请 exchang
     * @param {any} exchange 						[必选] exchange 名称
     * @param {string} [type='direct']  [可选] exchange 类型 默认 direct
     * @param {any} [opts={}] 					[可选] exchange 其他配置参数
     * @returns
     * @memberof Producer
     */
    async assertExchange(exchange, type = 'direct', opts = {}) {
        // 参数校验
        assert(exchange, 'exchange name cannot be empty')

        // 该 exchange 已申请 直接 return
        if (this.getExchange(exchange)) {
            return this.getExchange(exchange)
        }

        // exchange 配置信息
        const defaultOpts = {
            durable: true, // exchange 信息持久化
        }

        // 配置当前 exchange 的 alternateExchange
        if (
            this.defaultAE &&
            exchange !== this.defaultAE.name &&
            type !== 'x-delayed-message'
        ) {
            defaultOpts.alternateExchange = this.defaultAE.name
        }

        if (type === 'x-delayed-message') {
            opts['arguments'] = {}
            opts['arguments']['x-delayed-type'] = 'direct'
        }

        const finalOpts = Object.assign({}, defaultOpts, opts)

        const ch = await this.assertChannel()

        // 当前 channel 申请 exchange
        const ex = await ch.assertExchange(exchange, type, finalOpts)

        // 缓存已申请的 exchange
        this.exchanges[exchange] = {
            exchange: ex, // exchage 实例
            name: exchange,
            type,
            opts: finalOpts,
        }

        debug('-- assert exchange --', ex, Object.assign(defaultOpts, opts))
        return ex
    }

    /**
     * 申请 alternateExchange
     * 不可路由到任何 queue 的消息，会流向 alternateExchange
     * @returns
     * @memberof Producer
     */
    async assertAE() {
        // 已申请，return
        if (this.AE) {
            return this.AE
        }

        // 不需要 AE，return
        if (!this.defaultAE) {
            return null
        }

        // 申请一个 exchange
        const { name, queue, type, opts, processor } = this.defaultAE
        const ch = await this.assertChannel()
        const ex = await this.assertExchange(name, type, opts)

        // 绑定 AE 默认的订阅队列及 handler
        const qu = await ch.assertQueue(queue || '', {
            durable: true,
        })
        await ch.bindQueue(qu.queue, name, '')

        // 消费 alternateExchange 过来的 msg
        ch.consume(qu.queue, (msg) => {
            logger.info(
                `get unroutable msg from ${qu.queue} messageId: ${
                    msg.properties && msg.properties.messageId
                }`
            )
            if (typeof processor === 'function') {
                processor(msg)
            }

            ch.ack(msg)
        })

        this.AE = ex

        debug(`-- assert ae -- ${ex.exchange} binding queue: ${qu.queue}`)
        return ex
    }

    /**
     * 发布消息
     * @param {String} exchange    [必填] 交换器名
     * @param {String} routingKey  [必填] 队列 route 规则，fanout 类型 ex 则为空
     * @param {JSON} msg           [必填] 原始消息
     * @param {Object} opts        [可选] 更多配置参数，注意 application 相关的参数使用
     * @param {Function} ackCB     [可选] MQ server 端接受到消息的回执
     * @returns {Boolean} 发送状态 true - 已发送，false - exchange 非法 or channel buffer 已满
     * @memberof Producer
     */
    async publish(exchange = '', routingKey = '', msg, opts = {}, ackCB) {
        // 参数校验
        assert(
            !ackCB || typeof ackCB === 'function',
            'ackCB must be a function'
        )

        // 消息发送回调 首选 ackCB 传参，再次 new Producer 时的 publishConfirm，若没有则传空函数
        ackCB =
            ackCB ||
            this.publishConfirm ||
            ((err, ok) => {
                debug('-- publish msg ack --', err, ok)
            })

        // 无相应的 exchange return
        if (exchange && !this.getExchange(exchange)) {
            return false
        }

        const ch = await this.assertChannel()

        // 发送消息
        return ch.publish(
            exchange,
            routingKey,
            Buffer.from(JSON.stringify(msg)), // 消息内容为 buffer 类型
            Object.assign(
                {
                    persistent: true, // msg 持久化
                    mandatory: true, // 无法路由的消息将被 channel return 事件监听到
                },
                opts
            ),
            ackCB
        )
    }
}

module.exports = Producer
