/*
 * @Date: 2023-06-12 18:29:15
 * @LastEditors: william.qian <EMAIL>
 * @LastEditTime: 2023-09-04 18:05:25
 * @FilePath: /newicp/libs/tracer.js
 */
'use strict'

const opentelemetry = require('@opentelemetry/api')
const { registerInstrumentations } = require('@opentelemetry/instrumentation')
const { NodeTracerProvider } = require('@opentelemetry/sdk-trace-node')
const { Resource } = require('@opentelemetry/resources')
const {
    ResourceAttributes,
    SemanticResourceAttributes,
} = require('@opentelemetry/semantic-conventions')
const {
    SimpleSpanProcessor,
    ConsoleSpanExporter,
} = require('@opentelemetry/tracing')
const { ZipkinExporter } = require('@opentelemetry/exporter-zipkin')
const _ = require('lodash')
const {
    ExpressInstrumentation,
    ExpressLayerType,
} = require('@opentelemetry/instrumentation-express')
const {
    AmqplibInstrumentation,
} = require('@opentelemetry/instrumentation-amqplib')
const { HttpInstrumentation } = require('@opentelemetry/instrumentation-http')
var os = require('os')
var hostname = os.hostname()

const {
    AsyncHooksContextManager,
} = require('@opentelemetry/context-async-hooks')

module.exports = function (SERVICE_NAME) {
    const provider = new NodeTracerProvider({
        resource: new Resource({
            [SemanticResourceAttributes.SERVICE_NAME]: `newicp-${SERVICE_NAME}-${global.CONFIG.env}`,
            [SemanticResourceAttributes.DEPLOYMENT_ENVIRONMENT]: `${global.CONFIG.env}`,
            [SemanticResourceAttributes.SERVICE_VERSION]: `v1.0.0`,
            [SemanticResourceAttributes.SERVICE_NAMESPACE]: `prj-icp`,
            [SemanticResourceAttributes.HOST_NAME]: hostname,
        }),
    })
    provider.register()

    registerInstrumentations({
        instrumentations: [
            new HttpInstrumentation({
                applyCustomAttributesOnSpan: (span, request, response) => {
                    let params = {}
                    _.extend(params, request.query)
                    _.extend(params, request.body)
                    _.extend(params, request.params)
                    span.setAttributes({
                        params: JSON.stringify(params),
                    })
                    span.updateName(`${params.Action}`)
                },
                contextManager: new AsyncHooksContextManager(),
                //启用上下文注入
                // propagateTraceContext: true,
                // propagateBaggage: true,
            }),
            new ExpressInstrumentation({
                ignoreLayersType: [ExpressLayerType.MIDDLEWARE],
            }),
            // 先关闭我们mq的追踪 因为链路太长了 直接去日志搜索吧
            // new AmqplibInstrumentation(),
        ],
        tracerProvider: provider,
    })

    // 创建 console exporter，方便本地测试和调试
    const consoleExporter = new ConsoleSpanExporter()

    // 创建 SimpleSpanProcessor，并添加 console exporter 和 Zipkin exporter
    const consoleSpanProcessor = new SimpleSpanProcessor(consoleExporter)
    // 创建 Zipkin exporter，并设置服务器地址
    const zipkinExporter = new ZipkinExporter({
        url: global.CONFIG.zipkinURL,
    })
    const zipkinSpanProcessor = new SimpleSpanProcessor(zipkinExporter)

    provider.addSpanProcessor(zipkinSpanProcessor)
    if (global.CONFIG.env !== 'production') {
      //  provider.addSpanProcessor(consoleSpanProcessor)
    }
    provider.register()
}
