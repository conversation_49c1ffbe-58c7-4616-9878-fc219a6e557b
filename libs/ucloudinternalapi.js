const uuid = require('uuid/v4')
const request = require('request')
const logger = require('./logger')
const defaultTimeout = 15 * 1000

// 内部API
module.exports = function ucloudinternalapi(options) {
    return new Promise((resolve, reject) => {
        let uid = uuid()
        if (options === undefined) {
            logger
                .getLogger('error')
                .error('[' + uid + ']NO ACTION OR CATEGORY IN OPTIONS')
            reject(new Error('NO ACTION OR CATEGORY IN OPTIONS'), null)
        }

        logger
            .getLogger('api')
            .info('[' + uid + '] Options:' + JSON.stringify(options))

        let timeout = options.timeout || defaultTimeout
        delete options.timeout

        var params = {
            timeout: timeout,
            method: 'POST',
            url: global.CONFIG.ucloudInternalApi,
            body: options,
            headers: {
                'SourceEnv': global.CONFIG.env,
                'remote_user': 'ICPService',
                'Content-Type': 'application/json',
                ...options.headers
            },
            json: true,
        }
        request(params, function (error, response, body) {
            console.log(error, body)
            error
                ? logger.getLogger('api').error('[' + uid + ']', error.message)
                : logger
                      .getLogger('api')
                      .info('[' + uid + ']', JSON.stringify(body))

            error ? reject(error) : resolve(body)
        })
    })
}
