
## ucloud-internal-api-request

### v0.0.8

* 增加 `ucloud-internal-request-uuid` header 的配置

* 如果设置了此 header，那么在 api-gateway 里面会重用该 request-uuid

### 功能

* 主要是封装了向 api-gateway-internal 发请求的方法

* 在发请求前，会添加 PublicKey 和 Signature，以及重命名 Action 参数，其他参数保持不变


### 主要提供的方法

#### `request(args, callback)`

* `args` 是一个 Objct, 包含必填的 category / action / method / session / data 项，以及可选的 options 项

*  category 表示发送给哪个 API，相当于命名空间或者 API 分类，如不清楚时，请发送邮件至 api-gateway-internal 的维护者询问，String

*  action 表示对应 API 的名字，String

*  method 表示请求的方法，暂时只支持 GET / POST，String

*  session 是一个 Object，Object 至少要包含两个数据，`public_key` 和 `secret_key`，如果已经使用了 ucloud-auth-internal，那可以直接使用 req.session 来作为此项的值

*  data 表示真正传给 API 的数据，method 是 GET 时，data 作为 query parameter, data 支持一级的数组参数

*  options 表示传给 request module 的选项，一般来讲不需要传

*  `callback` 是回调函数，格式是 `callback(err, data)`，data 表示从 API 返回的 JSON Object 对象

* Example:
	
```javascript
    var request = require('ucloud-internal-api-request');
	request.request({
		category: 'OA',
		action: 'GetMember',
		method: 'GET',
		session: req.session,
		data: {
			UserName: 'xxxx'
		},
		options: {
		}
	}, function(err, data) {
		// TODO
	});
```

### configure

* `configure(options)`, options 示例如下
    ```javascript
    {
        url: "http://api.ucloudadmin.com/", // 内部 API gateway 的地址，一般调试时需要
        debug: false, // 是否打印出调试信息
        logger: console, // 允许传入由 log4js 配置的 logger, 默认使用 console
        debugURLs: { // 设置了每个类型对应的配置后，会直接发送请求到对应的 URL，而不会通过线上的 API gateway 来走，主要用于测试
            'Ticket': 'http://127.0.0.1:10010' // Ticket 类别的的 API 请求都会直接发送到 http://127.0.0.1:10010
        }
    }
    ```

### pipeRequestv2

* `pipeRequestv2(req, res, session)`

* sesssion 是带有 `public_key` 和 `secret_key` 的对象

* 可以直接返回数据到前端

### pipeRawResponse

* `pipeRawResponse(req, res, session)`

* 主要适用于返回图片数据
