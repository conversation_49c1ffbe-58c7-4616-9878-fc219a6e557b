'use strict'

const BaseCron = require('bull')
/**
 * 使用 redis结合队列 实现定时任务
 */
class Cron {
    constructor() {
        this.BullCron = this.init()
    }
    init() {
        return new BaseCron('BullCron', {
            redis: global.CONFIG.redis,
        })
    }
    /**
     *
     * @param {*} name 重复的任务名称
     * @param {*} job 重复执行的cron, 和开始执行的时间startDate, data,携带执行的参数 若非重复性定时任务，不需要cron和startDate
     */
    setCron(name, job) {
        let realJob = {
            name,
            data: job.data,
            opts: {
                attempts: 0, //重试次数
            },
        }
        if (job.cron) {
            // 重复性定时任务
            realJob.opts.repeat = {
                cron: job.cron,
                startDate: job.startDate || new Date(),
            }
            realJob.opts.removeOnComplete = 10
            realJob.opts.removeOnFail = 10 //只留下最新的10个记录
        } else if (job.delay) {
            // 延时任务
            realJob.opts.delay = job.delay
            realJob.opts.removeOnComplete = true // 完成后删除该延时任务
            realJob.opts.removeOnFail = true
        }
        this.BullCron.add(name, realJob.data, realJob.opts)
    }
    /**
     * 删除所有重复任务，防止重复任务时间更改造成影响
     */
    async removeAll() {
        const repeatableJobs = await this.BullCron.getRepeatableJobs()
        await Promise.all(
            repeatableJobs.map((rj) => {
                return this.BullCron.removeRepeatableByKey(rj.key)
            })
        )
    }
    /**
     * 消费重复任务
     * @param {*} name
     * @param {*} processor
     */
    process(name, processor) {
        this.BullCron.process(name, async (job) => processor(job))
    }
    /**
     * 获取所有的延迟任务
     */
    getDelayed() {
        return this.BullCron.getDelayed()
    }
}

module.exports = new Cron()
