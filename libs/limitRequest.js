'use strict'

const redis = require('./redis')
const _ = require('lodash')
/**
 * 接口调用控制中间件
 * 目前只控制 从公司网关调用过来的客户侧接口
 * 通过在redis中配置一些接口的访问者来控制api调用权限，配置格式为hash，Action: companyid1,companyid2,companyid1
 * 未配置则默认所有人可调用
 * 配置了 则只有配置过的id可用 主要用于控制收费接口
 * @param {*} req
 * @param {*} res
 * @param {*} next
 */
module.exports = async function (req, res, next) {
    let redisFun = redis.get()
    let allLimit = await redisFun.hgetall('UserRequestApiLimit')
    let apis = Object.keys(allLimit)
    if (apis.length > 0) {
        let params = {}
        _.extend(params, req.query)
        _.extend(params, req.body)
        _.extend(params, req.params)

        if (apis.includes(params.Action)) {
            let limitIds = allLimit[params.Action].split(',')
            if (limitIds.includes(params.company_id.toString())) {
                next()
            } else {
                return res.json({
                    RetCode: 403,
                    Message: 'Forbidden',
                })
            }
        } else {
            next()
        }
    } else {
        next()
    }
}
