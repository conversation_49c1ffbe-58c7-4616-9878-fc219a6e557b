const logger = require('./logger')

module.exports = class CronLogger {
    constructor(cronName, sid) {
        this.cronName = cronName
        this.sid = sid
        this.logger = logger.getLogger('cron')
    }
    info(msg) {
        this.logger.info(this.cronName + '[' + this.sid + '] - ' + msg)
    }
    error(msg) {
        this.logger.error(this.cronName + '[' + this.sid + '] - ' + msg)
    }
}
