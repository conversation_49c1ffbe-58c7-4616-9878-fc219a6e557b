const log4js = require('log4js')
const path = require('path')

const logPath = global.CONFIG.logPath

const apis = {
    api: {
        appenders: ['api', 'print'],
        level: 'info',
    },
    DataDDapi: {
        appenders: ['api', 'print'],
        level: 'info',
    },
    mqProducer: {
        appenders: ['mq', 'print'],
        level: 'info',
    },
    mqCustomer: {
        appenders: ['mq', 'print'],
        level: 'info',
    },
}

const errors = {
    error: {
        appenders: ['error', 'print'],
        level: 'info',
    },
}

const debugs = {
    debug: {
        appenders: ['debug', 'print'],
        level: 'info',
    },
    default: {
        appenders: ['debug', 'print'],
        level: 'info',
    },
}

const accesses = {
    access: {
        appenders: ['access', 'print'],
        level: 'info',
    },
}

const crons = {
    cron: {
        appenders: ['cron', 'print'],
        level: 'info',
    },
}

log4js.configure({
    pm2InstanceVar: 'INSTANCE_ID',
    pm2: true,
    appenders: {
        api: {
            type: 'dateFile',
            filename: path.join(logPath, 'api.log'),
            pattern: '.yyyy-MM-dd',
            backups: 30,
            // compress: true,
        },
        cron: {
            type: 'dateFile',
            filename: path.join(logPath, 'cron.log'),
            pattern: '.yyyy-MM-dd',
            backups: 30,
            // compress: true,
        },
        mq: {
            type: 'dateFile',
            filename: path.join(logPath, 'mq.log'),
            pattern: '.yyyy-MM-dd',
            backups: 30,
            // compress: true,
        },
        error: {
            type: 'dateFile',
            filename: path.join(logPath, 'error.log'),
            pattern: '.yyyy-MM-dd',
            backups: 30,
        },
        access: {
            type: 'dateFile',
            filename: path.join(logPath, 'access.log'),
            pattern: '.yyyy-MM-dd',
            backups: 30,
            // compress: true,
        },
        debug: {
            type: 'stdout',
        },
        print: {
            type: 'stdout',
        },
    },
    categories: {
        ...apis,
        ...errors,
        ...debugs,
        ...accesses,
        ...crons,
    },
})

module.exports = log4js
