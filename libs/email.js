const logger = require('../libs/logger')
const { sendEmail } = require('./send')

const send = (order, type) => {
    logger
        .getLogger('api')
        .info('[' + order.OrderNo + '] Data:' + JSON.stringify(order))

    return sendEmail(order, type)
        .then((res) => {
            logger
                .getLogger('api')
                .info('[' + order.OrderNo + '] Data:' + JSON.stringify(res))
        })
        .catch((e) => {
            logger
                .getLogger('error')
                .info('[' + order.OrderNo + '] Data:' + JSON.stringify(e))
        })
}

module.exports = {
    send,
}
