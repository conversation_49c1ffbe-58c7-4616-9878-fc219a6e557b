// 任务拉取初始化

const TaskFetcher = require('./taskFetcher')
const logger = require('../logger').getLogger('mqCustomer')

const mq = global.CONFIG.mq

const taskFetcher = new TaskFetcher(mq)
taskFetcher.init().catch((err) => {
    logger.error(`taskSender init fail: ${err}`)

    // 启动时 MQ 连接失败，程序退出
    console.log('mq init fail, process exit')

    process.nextTick(process.exit)
})

module.exports = taskFetcher
