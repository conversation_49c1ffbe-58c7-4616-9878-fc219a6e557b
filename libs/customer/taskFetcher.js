/**
 * 消息拉取封装
 */

const assert = require('assert')
const Customer = require('./rabbitmqCustomer')
const logger = require('../logger').getLogger('mqCustomer')
const errorLogger = require('../logger').getLogger('error')
const { ROOT_CONTEXT, context, trace, SpanKind, propagation } = require('@opentelemetry/api')
module.exports = class TaskFetcher {
    constructor(conf = {}) {
        const { processorUrl } = conf

        assert(processorUrl, 'connUrl cannot be empty')

        // 配置信息
        this.conf = {
            connUrl: conf.processorUrl,
            tasks: conf.tasks,
        }

        // 已创建的 taskProcessor
        this.taskProcessors = {}
    }

    // 初始化, 预分配 taskProcessor
    async init() {
        const { tasks = [] } = this.conf

        await Promise.all(tasks.map(this.assert.bind(this)))
    }

    async close() {
        const customers = Object.values(this.taskProcessors).map(
            ({ customer }) => customer
        )

        await Promise.all(customers.map((customer) => customer.close()))

        this.taskProcessors = {}

        return true
    }

    getKey({ type, topics }) {
        return `${type}_${topics.join('-')}`
    }

    getProcessor({ type, topics }) {
        const key = this.getKey({ type, topics })

        return this.taskProcessors[key]
    }

    setProcessor({ type, topics, customer, queue }) {
        const key = this.getKey({ type, topics })

        this.taskProcessors[key] = {
            type,
            topics,
            queue: queue.queue,
            customer,
        }

        return this.taskProcessors[key]
    }

    // 分配特定类型的 taskProcessor
    async assert({ type, topics, dead_exchange, dead_routing_key }) {
        let processor = this.getProcessor({ type, topics })
        if (processor) {
            return processor
        }

        const { connUrl, tasks } = this.conf
        const currTask = tasks[tasks.findIndex((info) => info.type === type)]
        const exchange_type = currTask?.exchange_type || 'direct'

        const customer = new Customer({
            connUrl,
        })

        await customer.init()

        await customer.assertExchange(type, exchange_type)

        let queueConfig = { exchange: type, severity: topics }
        if (dead_exchange && dead_routing_key) {
            queueConfig.dead_exchange = dead_exchange
            queueConfig.dead_routing_key = dead_routing_key
        }

        const queue = await customer.assertQueue(queueConfig)

        // 设置一个队列的话，目前只能同时处理一个任务
        customer.ch.prefetch(1)

        processor = this.setProcessor({
            type,
            topics,
            customer,
            queue,
        })

        logger.info(`assert processor for ${type} success`)

        return processor
    }

    // 主动拉取相应类型的消息，并处理
    async fetch(handler, { type, topics }) {
        const processor = this.getProcessor({ type, topics })
        if (!processor) {
            return false
        }

        const { customer, queue } = processor

        // 拉取队列中的消息
        const msg = await customer.get(queue)
        if (!msg) {
            return msg
        }

        const content = JSON.parse(msg.content.toString())

        // 消息处理
        let ret = true
        if (typeof handler === 'function') {
            ret = await handler(content, msg.properties).catch((err) => {
                logger.error('fetch msg handler fail', err)
                return false
            })
        }

        // 反馈处理结果给 MQ
        if (ret) {
            customer.ch.ack(msg)
        } else {
            customer.ch.nack(msg, false, false)
        }

        msg.content = content
        return msg
    }

    /*
     * 注册接收消息的处理程序
     *
     */
    async registerHandle(
        handler,
        { type, topics, dead_exchange, dead_routing_key },
        prune = false,
        noAck = false
    ) {
        const processor = await this.assert({
            type,
            topics,
            dead_exchange,
            dead_routing_key,
        })
        if (!processor) {
            throw Error('processor')
        }

        const { customer, queue } = processor

        customer.ch.consume(
            queue,
            async (msg) => {
                try {
                    // const traceId = msg.properties.headers['trace-id']
                    // const parentId = msg.properties.headers['span-id']
                    // const spanContext = trace.setSpanContext(context.active(), {
                    //     traceId,
                    //     spanId: parentId,
                    // })
                    // let spanContext = msg.properties.headers['currSpanContext']
                    // spanContext = JSON.parse(spanContext)
                    // const myContext = context.setValue(
                    //     context.active(),
                    //     trace.setSpanContext(spanContext)
                    // )
                    // let activeContext = propagation.extract(context.active(), spanContext)

                    // const tracer = trace.getTracer('newicp-console')
                    // const span = tracer.startSpan(
                    //     'handle message',
                    //     { kind: SpanKind.CONSUMER },
                    //     activeContext
                    // )
                    // let curContext = trace.setSpan(spanContext, span)
                    // context.with(curContext)
                    // span.setAttribute('queueName', queue)
                    await handler(prune ? msg.content.toString() : msg)
                    // 结束 Span
                    // span.end()
                    customer.ch.ack(msg)
                } catch (e) {
                    console.log(e)
                    // 如果出错，目前消息队列的处理机制是会阻塞的，一定要重启连接才能再去消费
                    // customer.ch.nack(msg)
                    msg.content = msg.content.toString()
                    errorLogger.error(
                        'handle msg error',
                        e.message,
                        JSON.stringify(msg, null, 2)
                    )
                }
            },
            { noAck }
        )
    }
}
