/**
 * RabbitMQ 接收方
 * - MQ Server 创建连接、分配 channel、exchange、queue 等配置封装
 * - subscriber 端 ack 机制及出错重试机制
 * - 任务处理状态反馈
 */

const assert = require('assert')
const amqp = require('amqplib')
const debug = require('debug')('mq')
const logger = require('../logger').getLogger('mqCustomer')

class Customer {
    /**
     * mq customer 构造函数
     * @param {any} {
     * 	connUrl             [必填] MQ server 连接 URL
     * 	socketOpts = {}			[可选] 其他连接参数
     * 	defaultAE = true    [可选] alternateExchange 配置，不设会默认配
     * } 配置参数
     * @memberof Customer
     */
    constructor({
        connUrl,
        socketOpts = {},
        defaultAE = true,
        retryOpts = {},
    }) {
        // 参数校验
        assert(connUrl, 'connUrl cannot be empty')

        // mq server 连接相关参数
        this.connUrl = connUrl
        this.socketOpts = socketOpts

        // 配置默认的 alternateExchange 配置参数
        this.defaultAE = defaultAE
        if (defaultAE) {
            if (typeof defaultAE === 'boolean') defaultAE = {}
            const {
                name = 'unrouted-ex',
                type = 'fanout',
                opts = {},
            } = defaultAE
            this.defaultAE = { name, type, opts }
        }

        // 当前 customer 的一些实例
        this.conn = null // 连接实例
        this.ch = null // channel 实例
        this.AE = null // alternateExchange 实例
        this.exchanges = {} // 当前 customer 上已分配的所有 exchange (目前 customer 未有 exchange 分配需求)
        this.queues = {} // 当前 customer 上已分配的所有 queue

        // 重试相关参数
        const { retryMax = 20, retryDelay = 3000, forceExit = true } = retryOpts
        this.retring = false
        this.retryCount = 0
        this.retryMax = retryMax
        this.retryDelay = retryDelay
        this.retryForceExit = forceExit
    }

    /**
     * customer 初始化
     * 建立连接、分配 channel
     * @returns
     * @memberof Customer
     */
    async init() {
        await this.connect()
        await this.assertChannel()

        logger.info('mq customer init success')
        return this
    }

    /**
     * 建立连接
     * 建立连接，监听连接状态，出错重连
     * @returns
     * @memberof Customer
     */
    async connect() {
        // 连接已建立，直接 return
        if (this.conn) {
            return this.conn
        }

        // 建立连接
        const conn = await amqp.connect(this.connUrl, this.socketOpts)
        this.conn = conn

        // 连接报错时，重连
        conn.on('error', (err) => {
            logger.info('connect error', err)
            debug('connect error', err)

            this.reconnect()
        })

        // 某些情况，连接未报错，但 close 了，则需要延时重连
        // 注：不可直接调用重连方法，程序正常关闭时不需要重连
        conn.on('close', () => {
            logger.info('connect close')
            debug('connect close')

            // 3秒后检测是否需要 reconnect
            if (!this.retring) {
                setTimeout(() => {
                    this.reconnect()
                }, this.retryDelay)
            }
        })

        return conn
    }

    /**
     * 重连
     * 重连失败，会继续重连操作，直至连接成功或者达到重连次数上限
     * @returns
     * @memberof Customer
     */
    async reconnect() {
        // 已经处于重连状态
        if (this.retring) {
            return false
        }

        this.retring = true

        // 尝试关闭(一些情况导致调用 close 无返回，暂弃用)
        // await this.close()
        this.__clear()

        this.retryCount++

        // 超出最大重连次数退出程序
        if (this.retryCount > this.retryMax) {
            this.retryCount--
            logger.info(
                `reconnect fail: try ${this.retryCount} times, close process`
            )
            debug(`reconnect fail: try ${this.retryCount} times, close process`)

            if (this.retryForceExit) {
                console.log('mq reconnect fail, process exit')
                process.nextTick(process.exit)
            }
            return false
        }

        debug(`reconnect: NO.${this.retryCount}`)

        // 延时执行重连操作，返回重连结果
        const ret = await new Promise((resolve) => {
            setTimeout(async () => {
                try {
                    // 重新初始化
                    await this.init()

                    // 重新申请 exchange
                    await Promise.all(
                        Object.values(this.exchanges).map(
                            ({ name, type, opts }) =>
                                this.assertExchange(name, type, opts)
                        )
                    )

                    // 重新申请 queue
                    await Promise.all(
                        Object.values(this.queues).map(
                            ({ exchange, severity, name }) =>
                                this.assertQueue({ exchange, severity, name })
                        )
                    )

                    // 重连成功
                    resolve(true)
                } catch (e) {
                    // 重连失败
                    resolve(false)
                }
            }, this.retryDelay)
        })

        logger.info(
            `reconnect: NO.${this.retryCount} - ${ret ? 'success' : 'fail'}`
        )
        debug(`reconnect: NO.${this.retryCount} - ${ret ? 'success' : 'fail'}`)

        this.retring = false

        // 重连失败，继续重连
        if (!ret) {
            return this.reconnect()
        }

        // 重连成功，retryCount 清零
        this.retryCount = 0

        return ret
    }

    /**
     * 清除当前实例的属性信息
     * (内部调用)
     * @memberof Customer
     */
    __clear() {
        this.conn = null
        this.ch = null
        this.AE = null

        // 清除当前 customer 已分配 exchange 的实例信息
        // 但保留 exchange 的配置信息 以供重连时使用
        this.exchanges = Object.values(this.exchanges).reduce(
            (exchanges, exchange) => {
                delete exchange.exchange
                exchanges[exchange.name] = exchange
                return exchanges
            },
            {}
        )

        // 清除当前 customer 已分配 queue 的实例信息
        // 但保留 queue 的配置信息 以供重连时使用
        this.queues = Object.values(this.queues).reduce((queues, queue) => {
            delete queue.queue
            queues[queue.name] = queue
            return queues
        }, {})
    }

    /**
     * 关闭连接
     * 同时会清除掉连接上的 channel exchange queue
     * @returns
     * @memberof Customer
     */
    async close() {
        if (!this.conn) {
            this.__clear()

            return false
        }

        await this.conn.close().catch(() => logger.info('close conn error'))

        this.__clear()

        return true
    }

    /**
     * 申请有确认机制的 channel
     * @returns
     * @memberof Customer
     */
    async assertChannel() {
        // channel 已申请，直接 return
        if (this.ch) {
            return this.ch
        }

        const conn = await this.connect()

        // 申请 channel
        const ch = await conn.createChannel()
        this.ch = ch

        // channel 报错 重连
        // channel 报错时 conn 也会报错，故在 reconnect 函数中有重连状态的限制，避免重复调用
        ch.on('error', (err) => {
            logger.error(`channel got an error: ${err}`)
            debug(`channel got an error: ${err}`)

            this.reconnect()
        })

        // const count = 200 // 待确定合适的 count 值
        // ch.prefetch(count)

        // channel 意外 close 时 延时重连
        ch.on('close', () => {
            logger.info('channel close')
            debug('channel close')

            // 3秒后检测是否需要 reconnect
            if (!this.retring) {
                setTimeout(() => {
                    this.reconnect()
                }, this.retryDelay)
            }
        })

        return ch
    }

    // 根据 name 获取已申请 exchange 实例
    getExchange(name) {
        return (this.exchanges[name] && this.exchanges[name].exchange) || null
    }

    async assertExchange(exchange, type = 'direct', opts = {}) {
        assert(exchange, 'exchange name cannot be empty')

        if (this.getExchange(exchange)) return this.getExchange(exchange)

        const defaultOpts = {
            durable: true, // 持久化
        }

        if (
            this.defaultAE &&
            exchange !== this.defaultAE.name &&
            type !== 'x-delayed-message'
        ) {
            defaultOpts.alternateExchange = this.defaultAE.name
        }
        if (type === 'x-delayed-message') {
            opts['arguments'] = {}
            opts['arguments']['x-delayed-type'] = 'direct'
        }

        const ch = await this.assertChannel()

        // 创建 exchange
        const finalOpts = Object.assign({}, defaultOpts, opts)
        const ex = await ch.assertExchange(exchange, type, finalOpts)
        this.exchanges[exchange] = {
            exchange: ex,
            name: exchange,
            type,
            opts: finalOpts,
        }

        debug('-- assert exchange --', ex, finalOpts)
        return ex
    }

    async assertDLEx() {}

    // 根据 name 获取已申请 queue 实例
    getQueue(name) {
        return (this.queues[name] && this.queues[name].queue) || null
    }

    /**
     * 申请 queue
     * @param {String} exchange 			[必填] exchange name
     * @param {String|array} severity [必填] 路由规则
     * @param {String} name 					[可选] 队列名称，不填会自动生成一套
     */
    async assertQueue({
        exchange,
        severity,
        name,
        dead_exchange,
        dead_routing_key,
    }) {
        if (typeof severity === 'string') {
            severity = [severity]
        }

        assert(Array.isArray(severity), 'severity must be a string or array')

        const quName = name || `queue-${exchange}-${severity.join('_')}`

        if (this.getQueue(quName)) return this.getQueue(quName)

        const ch = await this.assertChannel()

        // const qu = await ch.assertQueue('', {
        // 	exclusive: true, // queue name 唯一
        // 	durable: true // 持久化
        // 	// deadLetterExchange: 'retry-ex' // 死信 exchange (任务消费失败后，去往的 exchage)
        // })

        // 申请指定名称的 queue
        // 为啥不使用 exclusive 参数生成 queue 唯一值？
        // 这样会导致一条 msg 同时发给多个 queue 重复处理
        let queueOption = {
            durable: true, // 持久化
        }
        if (dead_exchange && dead_routing_key) {
            queueOption.deadLetterExchange = dead_exchange
            queueOption.deadLetterRoutingKey = dead_routing_key
        }

        const qu = await ch.assertQueue(quName, queueOption)

        // 将 queue 绑定到指定 exchange 相应路由规则下
        await Promise.all(
            severity.map((pattern = '') =>
                ch.bindQueue(qu.queue, exchange, pattern)
            )
        )

        this.queues[quName] = {
            queue: qu,
            name: quName,
            exchange,
            severity,
        }

        if (dead_exchange && dead_routing_key) {
            this.queues[quName].deadLetterExchange = dead_exchange
            this.queues[quName].deadLetterRoutingKey = dead_routing_key
        }

        debug('-- assert queue --', qu.queue, exchange, severity)

        return qu
    }

    /**
     * 主动拉取消息
     * @param {string} queue 对列名称
     */
    async get(queue) {
        if (!this.getQueue(queue)) {
            return false
        }

        const ch = await this.assertChannel()

        // 注：拉取数据时，channel 若已经 close，则会报错
        return ch.get(queue).catch((err) => {
            logger.error(`channel fetch msg fail: ${err}`)
            return false
        })
    }
}

module.exports = Customer
