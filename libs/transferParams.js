const _ = require('lodash')

function setRes(keyArr, value, res) {
    if (keyArr[keyArr.length - 1] === '') {
        throw new Error('Invalid Params')
    }

    if (keyArr.length === 1) {
        res[keyArr[0]] = value
        return
    }

    const key = String(keyArr.pop())
    if (!res[key]) {
        // 此处是因为error的数据结构中包含了 order 中website的id，是个对象 不能解析为数组
        res[key] = _.isInteger(parseInt(keyArr[keyArr.length - 1])) && parseInt(keyArr[keyArr.length - 1]) < 1000 ? [] : {}
    }

    setRes(keyArr, value, res[key])
}

module.exports = function transferParams(params) {
    const reg = /\./
    for (const key in params) {
        if (!key.match(reg)) {
            continue
        }

        setRes(key.split('.').reverse(), params[key], params)
        delete params[key]
    }
    return params
}
