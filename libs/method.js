const mysql = require('./mysql')
const mongo = require('./mongo')
const redis = require('./redis')
const logger = require('./logger')

const MyError = require('./error')
const errorCode = require('../configs/error')

const producer = require('./producer')

module.exports = class Method {
    constructor(cb) {
        this.db = mysql
        this.mongo = mongo
        this.redis = redis
        this.logger = logger
        this.cb = cb
        this.Err = MyError
        this.producer = producer
    }

    // 错误处理
    eh(myErr) {
        logger.getLogger('error').error(myErr.err.message)
        this.cb(myErr.code, {
            Message: myErr.err.message || this.getErrorMessage(myErr.code),
        })
    }
    // 直接报错
    err(err) {
        logger.getLogger('error').error(err.message)
        this.cb(err.code, {
            Message: err.message || this.getErrorMessage(err.code),
        })
    }

    getErrorMessage(code) {
        return errorCode[code] || 'Internal Error'
    }
    /*
     * info 两种格式 1. error 2.[code,error]
     */
    handleError(info) {
        let e, c

        // 未按预期抛出的错误
        if (info instanceof Error) {
            e = info
            c = 10004
            logger.getLogger('error').error(e.message)
            return this.cb(c, e)
        }

        // 未正确使用改方法的错误
        if (!Array.isArray(info)) {
            e = Error(`unassign info: ${JSON.stringify(info)}`)
            c = 10005
            logger.getLogger('error').error(e.message)
            return this.cb(c, e)
        }

        ;[c, e] = info

        if (typeof c !== 'number') {
            e = Error(`code is not a number: ${c}`)
            c = 10006
            logger.getLogger('error').error(e.message)
            return this.cb(c, e)
        }

        if (e == undefined) {
            e = Error(this.getErrorMessage(c))
        } else if (!(e instanceof Error)) {
            e = Error(e)
        }

        logger.getLogger('error').error(e.message)

        return this.cb(c)
    }
}
