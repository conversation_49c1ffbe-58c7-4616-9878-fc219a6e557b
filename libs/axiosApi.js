'use strict'

const uuid = require('uuid/v4')
const axios = require('axios')
const logger = require('./logger')
const moment = require('moment')
module.exports = function axiosApi(config) {
    const uid = uuid()
    const curDate = new Date()
    if (!config.timeout) {
        config.timeout = 10 * 1000
    }
    if (!config.headers) {
        config.headers = {}
    }
    config.headers['SourceEnv'] = global.CONFIG.env
    return new Promise((resolve, reject) => {
        logger
            .getLogger('api')
            .info(
                `[${uid}] [${new Date()}]request config :${JSON.stringify(
                    config
                )}`
            )
        axios(config)
            .then((res) => {
                if (res.status !== 200) {
                    reject(new Error('http status is not 200'))
                }

                logger
                    .getLogger('api')
                    .info(
                        `[${uid}] [${new Date()}][consume] ${moment().diff(
                            curDate,
                            'ms'
                        )}ms response :${JSON.stringify(res.data)}`
                    )
                resolve(res)
            })
            .catch((err) => {
                logger
                    .getLogger('api')
                    .error(
                        `[${uid}] [${new Date()}][consume] ${moment().diff(
                            curDate,
                            'ms'
                        )}ms response :${err}`
                    )
                reject(err)
            })
    })
}
