const mongodb = require('mongodb').MongoClient

let db = null
const _ = require('lodash')

class DB {
    constructor() {
        if (!db) {
            this.init()
        }
    }
    init() {
        db = {}
        _.each(global.CONFIG.mongo, (config, name) => {
            mongodb.connect(config, function (err, client) {
                if (err) {
                    throw new Error(`mongodb connect error,${err}`)
                }
                db[name] = client.db(name)
            })
        })
    }
    get(name) {
        if (!db[name]) {
            throw new Error('No Such DB Config')
        }

        return db[name]
    }
}

module.exports = new DB()
