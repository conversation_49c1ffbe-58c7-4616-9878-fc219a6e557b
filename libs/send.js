const moment = require('moment')
const rp = require('request-promise')
const _ = require('lodash')
const { sendMail } = require('../fns/umsMsgSend')
const sendColudConfig = global.CONFIG.sendColud
moment.locale('zh-cn')

const whiteList = [
    '27693',
    '34278',
    '7196',
    '34115',
    '39492',
    '31662',
    '55969781',
    '50606503',
    '55999704',
    '50120009',
    '56033886',
    '56033871',
    '62922122',
    '50140109',
    '55904570',
    '56033886',
    '62935358',
    '65980679',
]

const emailTemplate = (order, type) => {
    let info
    console.log(order, type, 'order, type')
    switch (type) {
        case 'audit':
            info = getAuditEmailInfo(order)
            break
        case 'curtain':
            info = getCurtainEmailInfo(order)
            break
        default:
            info = false
            break
    }
    if (!info) {
        throw new Error('no matching email template')
    }
    let { content, subject } = info
    return {
        html: `
            <p>尊敬的用户您好！</p>
            <p style='text-indent: 2em;'>
                ${content}
            </p>
            <p style='text-align: right;'>
               备案组
            </p>
            <p style='text-align: right;'>
                ${moment().format('ll')}
            </p>
        `,
        subject,
    }
}

const getAuditEmailInfo = (order) => {
    if (!order) {
        return false
    }
    let channel = order.Channel || 1
    let beianUrl = global.CONFIG.beianUrlMap[channel]
    let content, subject
    if (order.Version == 1) {
        // 旧版本的订单
        switch (+order.Status) {
            case 3:
                content = `您在我司提交的备案申请（订单号：${order.OrderNo}）已通过审核。请您登陆系统<a href='https://${beianUrl}'>${beianUrl}</a>完成真实性核验，提交复审。感谢您的配合！`
                subject = '初审通过'
                break
            case 4:
                content = `您在我司提交的备案申请（订单号：${order.OrderNo}），初审不能完全符合要求，现已退回。请您登陆系统<a href='https://${beianUrl}'>${beianUrl}</a>查看原因，并按提示修改重新提交。感谢您的配合！`
                subject = '初审退回'
                break
            case 8:
                content = `您在我司提交的备案申请（订单号：${order.OrderNo}）已通过复审。我司会尽快为您上报管局审核。`
                subject = '接入商审核通过'
                break
            case 9:
                content = `您在我司提交的备案申请（订单号：${order.OrderNo}），复审不能完全符合要求，现已退回。请您登陆系统<a href='https://${beianUrl}'>${beianUrl}</a>查看原因，并根据提示修改重新提交。感谢您的配合！`
                subject = '复审退回'
                break
            case 11:
                content = `您在我司提交的备案申请（订单号：${order.OrderNo}），我司已为您上报管局审核。几分钟后管局将会发送短信验证码到您的手机，请按要求完成验证，以便顺利提交管局审核。感谢您的配合！`
                subject = '提交管局审核'
                break
            case 12:
                content = `您在我司提交的备案申请（订单号：${order.OrderNo}），已通过管局审核，现已备案成功。请知悉！`
                subject = '管局审核通过'
                break
            case 13:
                content = `您在我司提交的备案申请（订单号：${order.OrderNo}），经管局审核不能完全符合要求，现已退回，请您登陆系统<a href='https://${beianUrl}'>${beianUrl}</a>查看退回原因，并按提示修改重新提交。感谢您的配合！`
                subject = '管局审核退回'
                break
            default:
                return false
        }
    } else {
        switch (+order.Status) {
            case 3:
                // 审核通过
                content = `您在我司提交的备案申请（订单号：${order.OrderNo}）已通过我司审核。我司会尽快为您上报管局审核。我司提交管局后，请尽快完成短信验证，否则备案将被退回。`
                subject = '审核通过'
                break
            case 4:
                // 退回
                content = `您在我司提交的备案申请（订单号：${order.OrderNo}），不能完全符合我司审核要求，现已退回。请登陆备案系统<a href='https://${beianUrl}'>${beianUrl}</a>在我的备案进度中查看原因，并按提示修改重新提交。感谢您的配合！`
                subject = '审核退回'
                break
            case 8:
                content = `您在我司提交的备案申请（订单号：${order.OrderNo}）已通过我司审核。我司会尽快为您上报管局审核。我司提交管局后，请尽快完成短信验证，否则备案将被退回。`
                subject = '接入商审核通过'
                break
            case 9:
                content = `您在我司提交的备案申请（订单号：${order.OrderNo}），不能完全符合我司审核要求，现已退回。请登陆备案系统<a href='https://${beianUrl}'>${beianUrl}</a>在我的备案进度中查看原因，并按提示修改重新提交。感谢您的配合！`
                subject = '审核退回'
                break
            case 11:
                content = `您在我司提交的备案申请（订单号：${order.OrderNo}），已上报管局，请留意相关负责人手机，需要在24小时内完成工信部短信验证，否则备案将会退回，感谢您的配合！`
                subject = '提交管局审核'
                break
            case 12:
                content = `您在我司提交的备案申请（订单号：${order.OrderNo}），已通过管局审核，现已备案成功。请知悉！`
                subject = '管局审核通过'
                break
            case 13:
                content = `您在我司提交的备案申请（订单号：${order.OrderNo}）不能完全符合管局审核要求，现已退回，请您登陆系统<a href='https://${beianUrl}'>${beianUrl}</a>在我的备案进度中查看原因，并按提示修改重新提交。感谢您的配合！`
                subject = '管局审核退回'
                break
            default:
                return false
        }
    }

    return {
        content,
        subject,
    }
}

const getCurtainEmailInfo = (order) => {
    if (!order || !order.Curtain) {
        return false
    }
    return {
        content: `您申请的幕布已安排邮寄，请注意查收快递！快递公司：${order.Curtain.ExpressCompany}；快递单号：${order.Curtain.ExpressNo}`,
        subject: '幕布邮寄',
    }
}

const sendEmail = (order, type = 'audit') => {
    const companyId = order.CompanyId
    if (_.findIndex(whiteList, (whiteId) => whiteId == companyId) >= 0) {
        return Promise.resolve(
            `order ${order.OrderNo}'s owner is ${companyId}, which in the white list, no need to send email'`
        )
    }
    const { html, subject } = emailTemplate(order, type)

    if (order.Channel != 1) {
        // 非UCloud渠道的使用UMS发送邮件
        return sendMail({
            email: order.Website[0]
                ? order.Website[0].Email
                : order.PICMainEmail, //邮箱
            content: html, //正文内容
            title: subject, //邮件标题
            channel: order.Channel || 1, // 渠道
        })
    } else {
        const request = {
            uri: sendColudConfig.url,
            formData: {
                apiUser: sendColudConfig.apiUser,
                apiKey: sendColudConfig.apiKey,
                from: sendColudConfig.from,
                to:
                    // 如果是正式环境，发给第一个网站的负责人，如果不是正式，发给钱俊烨
                    global.CONFIG.env === 'production'
                        ? order.Website[0]
                            ? order.Website[0].Email
                            : order.PICMainEmail
                        : sendColudConfig.default,
                subject,
                html: html,
            },
            json: true,
        }

        return rp.post(request)
    }
}
const sendSMS = () => {}

module.exports = { sendEmail, sendSMS }
