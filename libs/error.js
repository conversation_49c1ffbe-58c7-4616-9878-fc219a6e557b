const _ = require('lodash')

module.exports = class MyError {
    constructor(err, code) {
        if (!_.isError(err)) {
            this.err = new Error('Err param require an Error')
            this.code = 10010
            return
        }

        if (!_.isInteger(code)) {
            this.err = new Error('Code param require an Integer')
            this.code = 10011
            return
        }

        this.err = err
        this.code = code
    }
}
