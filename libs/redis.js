const Redis = require('ioredis')
let db = null

class DB {
    constructor() {
        if (!db) {
            this.init()
        }
    }
    init() {
        // 目前是通过虚IP实现的高可用
        db = new Redis(global.CONFIG.redis)
        // db = new Redis({
        //     port: 6379,
        //     host: '*************',
        //     password: 'info.yaobuqi.top',
        //     _remark: '防止弱密码，用发现的违规域名当密码',
        // })
        // 哨兵模式
        // new Redis({
        // 	sentinels: [
        // 	  { host: "127.0.0.1", port: 26379 },
        // 	  { host: "127.0.0.1", port: 26380 },
        // 	],
        // 	name: "mymaster",
        // 	role: "slave",
        // 	preferredSlaves: preferredSlaves,
        //   });
    }
    get() {
        return db
    }
}

module.exports = new DB()
