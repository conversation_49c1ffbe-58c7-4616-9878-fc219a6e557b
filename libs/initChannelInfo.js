'use strict'
const axiosApi = require('./axiosApi')
const logger = require('./logger')

/**
 * 通过获取渠道信息 全局变量初始化
 * @returns
 */
module.exports = async () => {
    axiosApi({
        url: global.CONFIG.ucloudInternalApi,
        method: 'POST',
        data: {
            Action: 'IGetChannelInfo',
            Backend: 'UAccountManager',
        },
    }).then((res) => {
        if (!global.CONFIG.beianUrlMap) {
            global.CONFIG.beianUrlMap = { 1: 'console.ucloud.cn/icp/' }
        }
        if (res && res.data && res.data.Data?.length > 0) {
            res.data.Data.forEach((item) => {
                if (item.ChannelID && item.ExtendData?.ConsoleURL) {
                    global.CONFIG.beianUrlMap[item.ChannelID] =
                        item.ExtendData?.ConsoleURL + '/icp/'
                }
            })
        }
        logger
            .getLogger('api')
            .info(
                `[${new Date()}]init beianUrlMap Success :${JSON.stringify(
                    global.CONFIG.beianUrlMap
                )}`
            )
    }).catch((err) => {
        logger.getLogger('api').error(
            `[${new Date()}]init beianUrlMap Error :${JSON.stringify(err)}`
        )
        // 防止API出错后，导致全局变量未初始化
        global.CONFIG.beianUrlMap = {
            1: 'console.ucloud.cn/icp/',
            127: 'console.ucdctest.com/icp/',
            129: 'console-xcloud.xincache.cn/icp/',
            130: 'console.cosmos-ucloud.com.cn/icp/',
            132: 'console.cloud.dd1010.com/icp/',
            133: 'console.ucdctest-intl.com/icp/',
            135: 'console.bjzzxxjs.com/icp/',
            136: 'console.usuanova.com/icp/',
            137: 'console.sigcalcloud.com/icp/',
            138: 'console.spiderscloud.xyz/icp/',
            139: 'console.cloud.uspeedo.com/icp/',
            140: 'console.humeng.cn/icp/',
            141: 'console.isuanova.com/icp/',
        }
    })
}
