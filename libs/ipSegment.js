'use strict'

let Netmask = require('netmask').Netmask
const axios = require('../libs/axiosApi')
const { URL } = require('url')
const defaultTimeout = 60 * 1000
const _ = require('lodash')
const { Resolver } = require('dns')
const resolver = new Resolver()
const logger = require('../libs/logger').getLogger('mqCustomer')

class ipSegment {
    constructor() {
        // this.init(IPList)
        // this.test()
    }
    async init(IPList) {
        // 原来是重新查询一次，现在是从配制中读，任务开始是会统一查询一次。
        let result = global.CONFIG.IPSEG
        let UcloudIPSegment = result.map((item) => item.ipseg)
        // 转换成网络结构
        let netBlock = []
        let allIP = IPList
        allIP = _.uniq(allIP)
        for (let k = 0; k < UcloudIPSegment.length; k++) {
            netBlock.push(new Netmask(UcloudIPSegment[k]))
        }
        logger.info('IP段数量：', netBlock.length)
        let match = []
        for (let i = 0; i < allIP.length; i++) {
            for (let j = 0; j < netBlock.length; j++) {
                const IP = allIP[i].IP
                try {
                    if (IP && netBlock[j].contains(IP)) {
                        match.push(allIP[i])
                    }
                } catch (err) {
                    logger.info('判断是否在ip段失败：' + IP + err)
                }
            }
        }
        logger.info('匹配到的情况', _.uniq(match.map((item) => item.IP)).length)
        return match
    }
}
const getDomainDns = async (DomainList, DomianIPMap) => {
    let promise = []
    //域名数组去重
    DomainList = _.uniq(DomainList)
    for (let i = 0; i < DomainList.length; i++) {
        const domain = DomainList[i]
        promise.push(
            new Promise((resolve) =>
                resolver.resolve4(domain, (err, address) => {
                    console.log('domain,address', domain, address)
                    DomianIPMap[domain] = address || ''
                    resolve(address || '')
                })
            )
        )
    }
    await Promise.all(promise)
    return DomianIPMap
}

const getUcloudIPSegment = async () => {
    //const token = await getKey();
    //console.log(token,'token')
    // 超级恶心的拼接
    try {
        let params =
            '?page=1&perPage=1000&level=%5B%221%22%5D&supplier=%5B%22CNNIC%22%2C%22%E4%B8%8A%E6%B5%B7%E5%A4%A9%E5%9C%B0%E7%A5%A5%E4%BA%91%22%2C%22%E5%B9%BF%E5%B7%9E%E5%A4%A9%E5%9C%B0%E7%A5%A5%E4%BA%91%22%2C%22%E5%8C%97%E4%BA%AC%E5%A4%A9%E5%9C%B0%E7%A5%A5%E4%BA%91%22%2C%22%E5%B9%BF%E5%B7%9E%E8%81%94%E9%80%9A%22%2C%22%E5%B9%BF%E5%B7%9E%E7%A7%BB%E5%8A%A8%22%2C%22%E5%B9%BF%E5%B7%9E%E7%94%B5%E4%BF%A1%22%2C%22%E4%B8%8A%E6%B5%B7%E7%94%B5%E4%BF%A1%22%2C%22%E4%B8%8A%E6%B5%B7%E8%81%94%E9%80%9A%22%2C%22%E5%8C%97%E4%BA%AC%E7%94%B5%E4%BF%A1%22%2C%22%E5%8C%97%E4%BA%AC%E7%A7%BB%E5%8A%A8%22%2C%22%E5%8C%97%E4%BA%AC%E8%81%94%E9%80%9A%22%2C%22CTCC%22%2C%22%E5%94%AF%E4%B8%80%E7%BD%91%E7%BB%9C%22%2C%22%E6%9D%AD%E5%B7%9E%E4%BC%98%E4%BA%91%E7%A7%91%E6%8A%80%E6%9C%89%E9%99%90%E5%85%AC%E5%8F%B8%22%2C%22%E4%B9%8C%E5%85%B0%E5%AF%9F%E5%B8%83%E8%81%94%E9%80%9A%22%2C%22%E4%B9%8C%E5%85%B0%E5%AF%9F%E5%B8%83%E7%A7%BB%E5%8A%A8%22%2C%22%E4%B9%8C%E5%85%B0%E5%AF%9F%E5%B8%83%E7%94%B5%E4%BF%A1%22%5D'
        let options = {
            method: 'GET',
            url: global.CONFIG.ucloudIPSegmentApi + params,
            timeout: defaultTimeout,
            headers: {
                'Content-Type': 'application/json',
                'api-key': 'ec08cad8c40d43829611030415cb0663',
            },
        }

        const result = await axios(options)
        if (result?.data?.code === -1) {
            let error = new Error(result?.data?.msg)
            error.code = 34004
            throw error
        } else {
            global.CONFIG.IPSEG = result.data?.data
            return result.data?.data
        }
    } catch (e) {
        let error = new Error(e)
        error.code = 34004
        throw error
    }
}

const getKey = async () => {
    const params = {}
    const url = new URL(global.CONFIG.ucloudIPSegmentTokenApi).href
    let options = {
        method: 'GET',
        url,
        timeout: defaultTimeout,
        headers: {
            'Content-Type': 'application/json',
        },
        data: params,
    }
    const result = await axios(options)
    if (result?.data?.code === -1) {
        let error = new Error(result?.data?.msg)
        error.code = 34004
        throw error
    } else {
        return result.data?.['api-key'] || 'ec08cad8c40d43829611030415cb0663'
    }
}
module.exports = new ipSegment()
module.exports.getUcloudIPSegment = getUcloudIPSegment
