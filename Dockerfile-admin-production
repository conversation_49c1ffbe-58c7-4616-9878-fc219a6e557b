# dependency 
# FROM hub.ucloudadmin.com/tpl_crd_icp/node-buster:14.18-buster
FROM uhub.service.ucloud.cn/org_bdks4e/node:19-bullseye
# MAINTAINER yuewen.li "<EMAIL>"

RUN apt-get -y install gcc 

# RUN wget http://ftp.icm.edu.pl/pub/unix/graphics/GraphicsMagick/1.3/GraphicsMagick-1.3.21.tar.gz
COPY static/GraphicsMagick-1.3.21.tar.gz .

RUN tar zxvf GraphicsMagick-1.3.21.tar.gz
RUN cd GraphicsMagick-1.3.21 \
    && ./configure \
    && make -j8 \
    && make install

RUN sed -i "s/deb.debian.org/mirrors.aliyun.com/g" /etc/apt/sources.list

RUN apt-get update && \
    apt-get -y install python && \
    apt-get -y install apt-file && \
    apt-get -y install software-properties-common

RUN add-apt-repository ppa:kirillshkrogalev/ffmpeg-next

RUN apt-get install -y ffmpeg

# Bundle app source
COPY package.json /var/node/newicp/package.json

COPY *.js /var/node/newicp/
COPY configs /var/node/newicp/configs
COPY fns /var/node/newicp/fns
COPY libs /var/node/newicp/libs
COPY methods /var/node/newicp/methods
COPY models /var/node/newicp/models
COPY mq /var/node/newicp/mq

# 设置时间区
ENV TZ=Asia/Shanghai \
    SERVER_ENV=production

RUN ln -fs /usr/share/zoneinfo/${TZ} /etc/localtime  && \
    echo ${TZ} > /etc/timezone

WORKDIR /var/node/newicp

RUN npm install  --registry https://registry.npm.taobao.org 

RUN mkdir -p /data/logs/new && \
    mkdir -p /data/logs/new/shotPic \
    mkdir -p  /data/logs/new/tmpFile/
# console
EXPOSE  6161

CMD ["node", "/var/node/newicp/admin.js"]

# admin
# EXPOSE 6161

# CMD ["node", "/var/node/newicp/admin.js"]