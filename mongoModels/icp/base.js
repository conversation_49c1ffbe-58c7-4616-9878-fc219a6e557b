/*
 * @Date: 2022-10-09 10:24:31
 * @LastEditors: l<PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2022-10-09 10:26:16
 * @FilePath: /newicp/mongoModels/icp/base.js
 */
'use strict'

const { default: mongoose } = require('mongoose')

exports.baseOptions = {
    // timestamps: true,
    CreatedTime: {
        type: Number,
        default: mongoose.now,
    },
    UpdatedTime: {
        type: Number,
        default: mongoose.now,
    },
}
