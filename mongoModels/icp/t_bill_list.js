/*
 * @Date: 2022-10-09 10:25:21
 * @LastEditors: l<PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2023-08-09 16:43:55
 * @FilePath: /newicp/mongoModels/icp/t_bill_list.js
 */
'use strict'
const mongoose = require('mongoose')
const { Schema } = mongoose
const { baseOptions } = require('./base')
const { Types } = Schema

/**
 * 表格字段属性介绍
 */
const BillOrderStatusEnum = {
    UNPAID_NEW: 0, // 新建中
    UNPAID_FINISHED: 1, //	待支付
    UNPAID_DELETED: 2, // 订单出错删除
    UNPAID_PAID: 3, // 已支付
    UNPAID_CANCELLED: 4, // 订单金额取消
    UNPAID_REVOKED: 5, // 订单已支付，不可回撤
}
exports.BillOrderStatusEnum = BillOrderStatusEnum
const BillListSchema = new Schema(
    {
        BillType: {
            type: Types.Number,
            trim: true,
        },
        Type: {
            type: Types.String,
            trim: true,
        },
        BillNameCN: {
            type: Types.String,
            trim: true,
        },
        BillName: {
            type: Types.String,
            trim: true,
        },
        CompanyId: {
            type: Types.Number,
        },
        OrganizationId: {
            type: Types.Number,
        },
        Count: {
            type: Types.Number,
        },
        StartTime: {
            type: Types.Number,
        },
        EndTime: {
            type: Types.Number,
        },
        Amount: {
            type: Types.Number,
        }, // '实际价格'
        TotalAmount: {
            type: Types.Number,
        }, // '原价',
        OrderStatus: {
            type: Types.Number,
            enum: BillOrderStatusEnum,
            default: BillOrderStatusEnum.UNPAID_NEW,
        }, // '订单扣费状态',
        Remark: {
            type: Types.String,
            trim: true,
        },
        WhetherToPush: {
            type: Types.Number,
            default: 0,
        },
        NotifyRecordId: {
            type: Types.Number,
            default: 0,
            required: true,
        },
        ...baseOptions,
    },
    {
        collection: 't_bill_list',
    }
)

exports.BillListSchema = BillListSchema
