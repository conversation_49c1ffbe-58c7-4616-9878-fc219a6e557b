/**
 * @Date: 2025-07-10 10:25:21
 * @LastEditors: <EMAIL>
 */
'use strict'
const mongoose = require('mongoose')
const { Schema } = mongoose
const { baseOptions } = require('./base')
const { Types } = Schema

const ReportIPSegRecordSchema = new Schema(
    {
        Operator: {
            type: Types.String,
            required: false,
        },
        BatchId: {
            type: Types.String,
            required: true,
        },
        IPType: {
            type: Types.Number,
            default: 0,
            required: true,
        },
        IPStart: {
            type: Types.String,
            required: true,
        },
        IPEnd: {
            type: Types.String,
            required: true,
        },
        ReportMessage: {
            type: Types.String,
            required: false,
        },
        CreateTime: {
            type: Number,
            default: mongoose.now,
        },
        UpdateTime: {
            type: Number,
            default: mongoose.now,
        },
    },
    {
        collection: 't_report_ip_seg_log_record',
    }
)

exports.ReportIPSegRecordSchema = ReportIPSegRecordSchema
