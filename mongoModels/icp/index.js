/*
 * @Date: 2022-10-09 10:23:59
 * @LastEditors: li<PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2023-03-06 17:46:10
 * @FilePath: /newicp/mongoModels/icp/index.js
 */
'use strict'
const mongoose = require('mongoose')
const mongoConn = mongoose.createConnection(
    global.CONFIG.mongo.icp,
    {
        maxPoolSize: 10,
        autoIndex: true,
    }
)

const { NotifyTypeSchema } = require('./notify_type')
const { NotifyTemplateSchema } = require('./notify_template')
const { BillListSchema, BillOrderStatusEnum } = require('./t_bill_list')
const { UChatLogSchema } = require('./t_uchat_log')
const { ICPWebPlatInfoSchema } = require('./t_web_plat_info')
const { OrderWebPlatInfoSchema } = require('./t_order_web_plat_info')
const { IpSegSchema } = require('./t_ip_seg')
const { ReportIPSegBatchSchema } = require('./t_report_ip_seg_log_batch')
const { ReportIPSegRecordSchema } = require('./t_report_ip_seg_log_record')
exports.NotifyTypeModel = mongoConn.model('NotifyType', NotifyTypeSchema)
exports.NotifyTemplateModel = mongoConn.model(
    'NotifyTemplate',
    NotifyTemplateSchema
)
exports.BillListModel = mongoConn.model('t_bill_list', BillListSchema)
exports.BillOrderStatusEnum = BillOrderStatusEnum
exports.UChatLogModel = mongoConn.model('t_uchat_log', UChatLogSchema)
exports.ICPWebPlatInfoModel = mongoConn.model('t_web_plat_info', ICPWebPlatInfoSchema)
exports.OrderWebPlatInfoModel = mongoConn.model('t_order_web_plat_info', OrderWebPlatInfoSchema)
exports.IPSegModel = mongoConn.model('t_ip_seg', IpSegSchema)
exports.ReportIPSegBatchModel = mongoConn.model('t_report_ip_seg_log_batch', ReportIPSegBatchSchema)
exports.ReportIPSegRecordModel = mongoConn.model('t_report_ip_seg_log_record', ReportIPSegRecordSchema)
