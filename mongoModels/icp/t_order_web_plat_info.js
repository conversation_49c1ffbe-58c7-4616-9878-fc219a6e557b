'use strict'
const mongoose = require('mongoose')
const { Schema } = mongoose
const { baseOptions } = require('./base')
const { Types } = Schema

// App平台信息表
const OrderWebPlatInfoSchema = new Schema(
    {
        OrderWebId: Types.Number,
        AppPlatformInformationList: [{
            _id: false, // 禁用_id生成
            AppPlatformTypeID: Types.Number,
            TraitInfo: [{
                _id: false,
                AppPackageName: Types.String,
                AppSignedMd5: Types.String,
                AppPlatformPublicKey: Types.String,
            }],
            AppPlatformName: Types.String,
            AppPlatformDomains: [Types.String],
        }],
        ...baseOptions,
    },
    {
        collection: 't_order_web_plat_info',
    }
)

OrderWebPlatInfoSchema.index({ OrderWebId: 1 }, { unique: true })

exports.OrderWebPlatInfoSchema = OrderWebPlatInfoSchema