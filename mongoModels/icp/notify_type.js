/*
 * @Date: 2022-10-09 10:25:21
 * @LastEditors: l<PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2022-10-09 14:44:24
 * @FilePath: /newicp/mongoModels/icp/notify_type.js
 */
'use strict'
const mongoose = require('mongoose')
const { Schema } = mongoose
const { baseOptions } = require('./base')
const { Types } = Schema
/**
 * @class NotifyTemplateSchema
 * @property {Number} Type 通知类型
 * @property {String} Name 通知类型名称
 */
const NotifyTypeSchema = new Schema({
    Name: {
        type: Types.String,
        trim: true,
        required: true,
    },
    Type: {
        type: Types.Number,
        required: true,
    },
    UpdatedBy: {
        type: Types.String,
    },
    ...baseOptions,
})

NotifyTypeSchema.index({ Name: 1 }, { unique: true })
NotifyTypeSchema.index({ Type: 1 }, { unique: true })
exports.NotifyTypeSchema = NotifyTypeSchema
