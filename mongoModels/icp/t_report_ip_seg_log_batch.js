/**
 * @Date: 2025-07-10 10:25:21
 * @LastEditors: <EMAIL>
 */
'use strict'
const mongoose = require('mongoose')
const { Schema } = mongoose
const { baseOptions } = require('./base')
const { Types } = Schema

const ReportIPSegBatchSchema = new Schema(
    {
        Operator: {
            type: Types.String,
            required: true,
        },
        IPSegCount: {
            type: Types.Number,
            default: 0,
            required: true,
        },
        CreateTime: {
            type: Number,
            default: mongoose.now,
        },
        UpdateTime: {
            type: Number,
            default: mongoose.now,
        },
    },
    {
        collection: 't_report_ip_seg_log_batch',
    }
)

exports.ReportIPSegBatchSchema = ReportIPSegBatchSchema
