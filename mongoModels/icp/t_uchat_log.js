/**
 * @Date: 2022-10-09 10:25:21
 * @LastEditors: william.qian <EMAIL>
 * @LastEditTime: 2023-06-14 16:22:51
 * @FilePath: /newicp/mongoModels/icp/t_bill_list.js
 * @description:用来保存UChat的回复记录，内容包含用户提问，各模型生成的回复，用户实际的回复
 * 可供分析分析调优使用模型列表"chatglm_6b" "alpaca_7b" "chatgpt-3.5"
 * Chatgpt-3.5保存到数据库后，字段实际只保存为Chatgpt-3
 */
'use strict'
const mongoose = require('mongoose')
const { Schema } = mongoose
const { baseOptions } = require('./base')
const { Types } = Schema

const UChatLogSchema = new Schema(
    {
        Request: {
            type: Types.String,
            trim: true,
        },
        TicketId: {
            type: Types.String,
            trim: true,
        },
        TicketIndex: {
            type: Types.String,
            trim: true,
        },
        Chatglm_6b: {
            type: Types.String,
            trim: true,
        },
        Alpaca_7b: {
            type: Types.String,
            trim: true,
        },
        'Chatgpt-3': {
            type: Types.String,
            trim: true,
        },
        /*jshint -W119*/
        ...baseOptions,
        /*jshint +W119*/
    },
    {
        collection: 't_uchat_log',
    }
)

exports.UChatLogSchema = UChatLogSchema
