'use strict'
const mongoose = require('mongoose')
const { Schema } = mongoose
const { baseOptions } = require('./base')
const { Types } = Schema

// App平台信息表
const ICPWebPlatInfoSchema = new Schema(
    {
        ICPWebId: Types.Number,
        AppPlatformInformationList: [{
            _id: false, // 禁用_id生成
            AppPlatformTypeID: Types.Number,
            TraitInfo: [{
                _id: false,
                AppPackageName: Types.String,
                AppSignedMd5: Types.String,
                AppPlatformPublicKey: Types.String,
            }],
            AppPlatformName: Types.String,
            AppPlatformDomains: [Types.String],
        }],
        ...baseOptions,
    },
    {
        collection: 't_web_plat_info',
    }
)
ICPWebPlatInfoSchema.index({ ICPWebId: 1 }, { unique: true })

exports.ICPWebPlatInfoSchema = ICPWebPlatInfoSchema