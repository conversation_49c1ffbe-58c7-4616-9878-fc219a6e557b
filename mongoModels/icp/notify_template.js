/*
 * @Date: 2022-10-09 10:25:07
 * @LastEditors: l<PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2022-10-19 15:44:38
 * @FilePath: /newicp/mongoModels/icp/notify_template.js
 */
'use strict'
const mongoose = require('mongoose')
const { Schema } = mongoose
const { baseOptions } = require('./base')
const { Types } = Schema

/**
 * @class NotifyTemplateSchema
 * @property {Number} TypeId 通知类型 对应notify_type的Id
 * @property {String} Title 邮件发送title
 * @property {String} Version 模板版本 暂未使用
 * @property {Number} NotifyType 通知类型 短信/邮件
 * @property {String} SmsContent 短信通知内容
 * @property {String} EmailContent 邮件通知内容
 * @property {Object} TableTitle 邮件通知内容中 包含表格的Title 中英文map
 */
const NotifyTemplateSchema = new Schema({
    TypeId: {
        type: Types.ObjectId,
        ref: 'NotifyType',
        required: true,
    },
    Title: {
        type: Types.String,
        required: false,
        default: '',
        trim: true,
    },
    Version: {
        type: Types.String,
        trim: true,
        default: 'v1',
        required: true,
    },
    SmsContent: {
        type: Types.String,
        default: '',
        required: false,
    },
    EmailContent: {
        type: Types.String,
        default: '',
        required: false,
    },
    AttachFiles: {
        type: Types.Array,
        required: false,
        default: [],
    },
    TableTitle: {
        type: Types.Map,
        required: false,
        default: {},
    },
    ...baseOptions,
})
NotifyTemplateSchema.index({ TypeId: 1, Version: 1 })
exports.NotifyTemplateSchema = NotifyTemplateSchema
