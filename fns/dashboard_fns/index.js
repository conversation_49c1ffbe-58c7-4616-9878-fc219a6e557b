const moment = require('moment')
const stepList = ['DAY', 'WEEK', 'MONTH', 'YEAR'] // 验证步长是否正确、得到值

// 统一日期，如某个审核周一没有工作，他一周的开始是周二，因此按步长，统一周、月开始的数据
function unificationDate(rows, step) {
    rows = rows.map((row) => {
        if (step !== 'DAY') {
            row.Date = moment(row.Date).startOf(step).format('YYYY-MM-DD')
        }
        return row
    })
    return rows
}

// 传入指定日期的全部类型的数组，根据类型的单价，计算得到日期的总价
function sumThisDayAllTypeAmount(rows) {
    let amount = 0
    rows.map((row) => {
        // [2, 4, 5, 7, 10] 收费的接口
        switch (row.Type) {
            case 2:
                // "双脸匹配": 2 单价  0.1
                amount = amount + row.Count * 0.1
                break
            case 4:
                // "验证视频": 4, 单价 0.8
                amount = amount + row.Count * 0.8
                break
            case 5:
                // "验证个人信息（三要素）": 5  单价 0.4,
                amount = amount + row.Count * 0.4
                break
            case 7:
                // "检查企业法人四要素信息": 7,
                amount = amount + row.Count * 1.5
                break
            case 10:
                // "视频换脸与光线过暗": 单价  0.1  实际就是双脸
                amount = amount + row.Count * 1.1
                break
            default:
        }
    })

    return amount
}

// 统一时间，步长的取值与出错
function timeBaseCheck(BeginTime, EndTime, Step) {
    if (BeginTime > EndTime) {
        // 结束时间减开始时间，超过1年，出错 366天，考虑闰年的情况
        throw {
            err: new Error('开始时间不能大于结束时间'),
            code: 67112,
        }
    }

    if (Step !== 'YEAR' && EndTime - BeginTime - 1 > 3600 * 24 * 366) {
        // 结束时间减开始时间，超过1年，出错 366天，考虑闰年的情况
        throw {
            err: new Error(
                '除非查询步长为年维度，否则查询不能超过1年跨度的数据'
            ),
            code: 67110,
        }
    }

    if (stepList.indexOf(Step) === -1) {
        throw {
            err: new Error('不存在的步长配置'),
            code: 67111,
        }
    }
    return 0
}
module.exports = {
    unificationDate,
    sumThisDayAllTypeAmount,
    timeBaseCheck,
}
