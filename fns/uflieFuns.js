const crypto = require('crypto')
const ffmpeg = require('fluent-ffmpeg')
const _ = require('lodash')
const { convertObjectKeysToCamel } = require('../fns/parseModels')
var request = require('request')
var gm = require('gm')
const { URL } = require('url')
const axios = require('../libs/axiosApi')
const logger = require('../libs/logger')
const { decodeBase64Image, generateFileName } = require('./kits')
/**
 * 获取带有有效期的文件连接，注意icp 是bucket名字 不是目录哦！！！！
 * 如果要使用目录直接写在文件名称中就可！！！
 * @param {*} filename  文件名
 * @param {*} expires 过期时间
 * @returns
 */
function signature(filename, expires) {
    let auth = crypto
        .createHmac('sha1', global.CONFIG.ufile.privateKey)
        .update('GET' + '\n\n\n' + expires + '\n' + '/icp/' + filename)
        .digest()
        .toString('base64')

    return (
        '?UCloudPublicKey=' +
        encodeURIComponent(global.CONFIG.ufile.publicKey) +
        '&Signature=' +
        encodeURIComponent(
            crypto
                .createHmac('sha1', global.CONFIG.ufile.privateKey)
                .update('GET' + '\n\n\n' + expires + '\n' + '/icp/' + filename)
                .digest()
                .toString('base64')
        ) + // sha1 Hmac + base64
        '&Expires=' +
        expires
    )
}

function uploadFile(picture) {
    let file = decodeBase64Image(picture)

    file.type = file.type !== 'image/jpeg' ? file.type : 'image/jpg'

    let filename = generateFileName(picture, file.type)

    // 文件类型判断，忽略大小写
    if (
        ['html', 'htm', 'txt', 'doc', 'docx', 'svg'].indexOf(
            file.type.split('/')[1].toLowerCase()
        ) > -1 ||
        !filename
    ) {
        return Promise.reject(new Error('Error Type'))
    }
    let auth = crypto
        .createHmac('sha1', global.CONFIG.ufile.privateKey)
        .update('PUT\n\n' + file.type + '\n\n' + '/icp/' + filename)
        .digest()
        .toString('base64')

    auth = 'UCloud ' + global.CONFIG.ufile.publicKey + ':' + auth

    return new Promise((resolve, reject) => {
        request.put(
            {
                url: global.CONFIG.ufile.target + '/' + filename,
                headers: {
                    'Content-Type': file.type,
                    Authorization: auth,
                },
                body: file.data,
            },
            (err, response) => {
                err || response.statusCode !== 200
                    ? reject(err || response.statusCode)
                    : resolve(filename)
            }
        )
    })
}

async function getPictureBase64({ pictureName, url }) {
    if (pictureName) {
        url = getPictureUrls([pictureName], false)[pictureName]
    }
    let res = await axios({
        url,
        method: 'GET',
        responseType: 'arraybuffer',
    })
    return (
        'data:' +
        res.headers['content-type'] +
        ';base64,' +
        Buffer.from(res.data).toString('base64')
    )
}

// 传入图片的数组，返回结构，图片名对应的Ufile地址
function getPictureUrls(pictureList, Resize) {
    let pictures = {}

    pictureList = pictureList.filter(
        (item) => item !== null && item !== '' && item !== undefined
    )

    pictureList.forEach((element) => {
        pictures[element] =
            global.CONFIG.ufile.target +
            '/' +
            element +
            signature(element, Math.floor(Date.now() / 1000) + 600) +
            (Resize ? `&iopcmd=convert&q=${Resize}&dst=jpg` : '')
    })
    return pictures
}

// 传入图片或视频URL得到细节信息
// function getPictureMeta(fileUrl) {
// 	return new Promise((resolve, reject) => {
// 		ffmpeg.ffprobe(fileUrl, function (err, metadata) {
// 			if (err) {
// 				return reject(err)
// 			}
// 			let rato = _.find(metadata.streams, function (o) { return o.height !== 0 })
// 			rato.size = metadata.format.size

// 			rato = _.pick(rato, ['size', 'width', 'height']);
// 			rato = convertObjectKeysToCamel(rato)

// 			return resolve(rato)
// 		})
// 	})
// }

function getPictureMeta(fileUrl, fileBuffer, requireBuffer = false) {
    return new Promise((resolve, reject) => {
        if (fileBuffer) {
            return resolve(fileBuffer)
        } else {
            request(fileUrl, { encoding: null }, (err, response, body) => {
                if (err) {
                    return reject(err)
                }
                if (response.statusCode !== 200) {
                    return reject(new Error('file not exist'))
                }

                let buffer = new Buffer(body)
                return resolve(buffer)
            })
        }
    }).then((buffer) => {
        return new Promise((resolve, reject) => {
            gm(buffer, 'image.jpg').size(function (error, rato) {
                console.log(error)
                if (error) {
                    reject(error)
                }
                // 统一输出
                let resInfo = {}
                resInfo.Size = buffer.length
                resInfo.Height = rato.height
                resInfo.Width = rato.width
                if (requireBuffer) {
                    resInfo.Buffer = buffer
                }
                resolve(resInfo)
            })
        })
    })
}

// 图片压缩处理
/**若在均范围区间 直接返回
 * 若尺寸不在区间 像素在区间，1.直接并发生成多个压缩 取符合要求的
 * 若像素不在区间 尺寸在区间 1.先调像素 2.再判断尺寸 需要等比放大还是缩小
 * 若均不再范围区间 和上条操作一致
 * 默认按照长边 等比例压缩
 * @param {String} fileName 文件名
 * @param {Object} fileMetaInfo Size kb  Height Width
 * @param {Object} sizeRange minSize maxSize
 * @param {Int64} targetLen
 */
async function getTargetPicture(fileName, fileMetaInfo, sizeRange, targetLen) {
    // size 在要求范围内 看像素是否在要求范围内  “√”

    if (!fileName) {
        throw new Error('文件名不能为空')
    }

    let resURL = ''
    let baseUrl =
        global.CONFIG.ufile.target +
        '/' +
        fileName +
        signature(fileName, Math.floor(Date.now() / 1000) + 1200)
    let staticLen =
        fileMetaInfo.Height < fileMetaInfo.Width
            ? { Width: fileMetaInfo.Width }
            : { Height: fileMetaInfo.Height }
    let changBian = Object.keys(staticLen)[0] // 要控制的边
    // 最小大于最小 最大小于最大
    if (
        changBian > targetLen &&
        Math.floor(fileMetaInfo.Size) > sizeRange.minSize &&
        Math.ceil(fileMetaInfo.Size) < sizeRange.maxSize
    ) {
        return [baseUrl, 0]
    } else {
        // 只要像素不符合要求 就指定尺寸
        baseUrl +=
            changBian.toLowerCase() === 'width'
                ? `&iopcmd=thumbnail&type=4&width=${targetLen}`
                : `&iopcmd=thumbnail&type=5&height=${targetLen}`
    }

    // 获取当前处理后的file 判断size是否符合要求
    let oprMeta = await getPictureMeta(baseUrl)
    fileMetaInfo.Size = (oprMeta.Size / 1024).toFixed(0)
    fileMetaInfo.Height = oprMeta.Height
    fileMetaInfo.Width = oprMeta.Width
    // 在上面的处理后 像素符合要求了
    let retry = 1
    if (
        Math.floor(fileMetaInfo.Size) < sizeRange.minSize ||
        Math.ceil(fileMetaInfo.Size) > sizeRange.maxSize
    ) {
        // 尺寸不符合要求 处理尺寸
        ;[resURL, retry] = await getSimple(
            baseUrl,
            fileMetaInfo.Size,
            sizeRange,
            []
        )
    } else {
        resURL = baseUrl
    }
    return [resURL, retry]
}
/**
 *
 * @param {*} currSize 当前尺寸
 * @param {*} targetSizeRange 目标尺寸范围
 * @param {*} scaleRange scale区间 us3中的func 方便取样 100为原始 > 100为放大 <100为缩小 （size）
 * <100 压缩使用convert > 100
 * >100 扩展使用thumbnail
 */
async function getSimple(
    curURL,
    currSize,
    targetSizeRange,
    scaleRange = [],
    retry = 1
) {
    // 取样 目前使用同时 4个样本处理
    let resUrl = ''
    let testSimple = []
    if (scaleRange.length === 0) {
        // 首次取样
        if (currSize < targetSizeRange.minSize) {
            // 放大 此处使用
            testSimple = [120, 140, 160, 180]
        } else if (currSize > targetSizeRange.maxSize) {
            // 缩小
            testSimple = [20, 40, 60, 80]
        }
    } else {
        let len = scaleRange[1] - scaleRange[0]
        let stepSize = (len / 4).toFixed(0) < 1 ? 1 : (len / 4).toFixed(0)
        for (let i = 1; i < 5; i++) {
            testSimple.push(scaleRange[0] + stepSize * i)
        }
    }
    // 样本处理
    let promiseArr = []
    if (curURL.indexOf('iopcmd') !== -1 && !_.endsWith(curURL, '|')) {
        curURL += '|'
    } else {
        curURL += '&'
    }
    for (let v of testSimple) {
        let url =
            curURL +
            (v < 100
                ? `iopcmd=convert&dst=jpg&Q=${v}`
                : `iopcmd=thumbnail&type=1&scale=${v}`)
        promiseArr.push(getPictureMeta(url))
    }
    let res = await Promise.all(promiseArr)
    let minVal, maxVal
    for (let i = 0; i < res.length; i++) {
        res[i].Size = (res[i].Size / 1024).toFixed(2)
        if (
            Math.floor(res[i].Size) > targetSizeRange.minSize &&
            Math.ceil(res[i].Size) < targetSizeRange.maxSize
        ) {
            // 找到最佳了 不取最大的是因为保存后 发现可能会超过最大要求的大小
            resUrl =
                curURL +
                (testSimple[i] < 100
                    ? `iopcmd=convert&dst=jpg&Q=${testSimple[i]}`
                    : `iopcmd=thumbnail&type=1&scale=${testSimple[i]}`)
            break
        } else if (res[i].Size <= targetSizeRange.minSize) {
            // 由于压缩是有递增的 只需要记录最后(最大的)一个小于的即可
            minVal = testSimple[i]
            if (i === 3) {
                // 最后一个最大的都比 最小值小
                // 可能是：放大放少了 || 缩小缩多了   >100 在放大 < 100在缩小
                maxVal =
                    testSimple[i] - 100 > 0
                        ? testSimple[i] + 100 < 1000
                            ? testSimple[i] + 100 < 1000 // 最大值限高为1000 最小值为1
                            : 1000
                        : 100
            }
        } else {
            // 大于最大值  只需要找到最早（最小的）一个大于
            if (maxVal === undefined) {
                maxVal = testSimple[i]
                if (i === 0) {
                    // 第一个都比最大的大 也没必要比后面的了
                    // 放大放过了（从100开始放大） 或者缩小缩少了（从1开始缩）
                    minVal = testSimple[i] - 100 > 0 ? 100 : 1
                }
            }
        }
    }
    if (resUrl === '' && maxVal - minVal < 4) {
        // 已经无法再细分了 取最大的 作为url
        resUrl =
            curURL +
            (maxVal < 100
                ? `iopcmd=convert&dst=jpg&Q=${maxVal}`
                : `iopcmd=thumbnail&type=1&scale=${maxVal}`)
    }
    if (resUrl === '') {
        return await getSimple(
            curURL,
            currSize,
            targetSizeRange,
            [minVal, maxVal],
            retry++
        )
    } else {
        return [resUrl, retry]
    }
}

/**
 * 根据前置字符搜索文件
 * @param {*} prefix 模糊搜索的前置条件
 * @param {*} directory 模糊搜索的目录
 * @param {*} timeout 超时时间
 * @returns
 */
async function getPictureFromPrefix(prefix, timeout = 30 * 1000) {
    if (!prefix) {
        throw new Error('prefix不能为空')
    }

    let auth = crypto
        .createHmac('sha1', global.CONFIG.ufile.privateKey)
        .update('GET' + '\n\n\n\n' + '/icp/')
        .digest()
        .toString('base64')
    auth = `UCloud ${global.CONFIG.ufile.publicKey}:${auth}`
    const option = {
        url: new URL(`/?list&prefix=${prefix}`, global.CONFIG.ufile.target)
            .href,
        method: 'GET',
        headers: {
            Authorization: auth,
        },
        json: true,
        timeout,
    }
    const result = await axios(option)
        .then((res) => {
            return res.data
        })
        .catch((err) => {
            logger.getLogger('error').error('get us3 pictures error', err)
            throw new Error('get us3 pictures error')
        })

    let fileArray = []
    if (result.DataSet.length > 0) {
        logger.getLogger('access').info(JSON.stringify(result.DataSet))
        result.DataSet.forEach((data) => {
            fileArray.push(data.FileName)
        })
    }

    return fileArray
}
/**
 * 查看文件基本信息
 * @param {*} filename
 * @returns
 */
async function headerFile(filename) {
    if (!filename) {
        throw new Error('filename不能为空')
    }
    let auth = crypto
        .createHmac('sha1', global.CONFIG.ufile.privateKey)
        .update('HEAD\n\n\n\n' + '/icp/' + filename)
        .digest()
        .toString('base64')

    auth = `UCloud ${global.CONFIG.ufile.publicKey}:${auth}`

    const option = {
        url: global.CONFIG.ufile.target + `/${filename}`,
        method: 'HEAD',
        headers: {
            Authorization: auth,
        },
        json: true,
        timeout: 30 * 1000,
    }
    return await axios(option)
}
/** 内部bucket 已不支持该接口
 * 快速上传 在US3中已经存在的文件,
 * @param {*} Hash 已存在的文件的Etag
 * @param {*} FileName 要命名的名字
 * @param {*} FileSize 文件的大小
 * @param {*} FileType 文件的类型image/png等
 */
async function uploadHit(Hash, FileName, FileSize, FileType) {
    if (!FileName) {
        throw new Error('FileName不能为空')
    }
    let auth = crypto
        .createHmac('sha1', global.CONFIG.ufile.privateKey)
        .update('POST\n\n' + FileType + '\n\n' + '/icp/' + FileName)
        .digest()
        .toString('base64')
    auth = `UCloud ${global.CONFIG.ufile.publicKey}:${auth}`
    const option = {
        url:
            global.CONFIG.ufile.target +
            `/uploadhit?Hash=${Hash}&FileName=${FileName}&FileSize=${FileSize}`,
        method: 'POST',
        headers: {
            'Content-Type': FileType,
            Authorization: auth,
        },
        timeout: 30 * 1000,
    }
    return await axios(option)
}

async function copyFile(SourceFile, TargetFile, FileType) {
    if (!SourceFile || !TargetFile) {
        throw new Error('SourceFile和TargetFile不能为空')
    }
    if (typeof SourceFile !== 'string' || typeof TargetFile !== 'string') {
        throw new Error('SourceFile和TargetFile必须为字符串')
    }

    let auth = crypto
        .createHmac('sha1', global.CONFIG.ufile.privateKey)
        .update('PUT\n\n' + FileType + '\n\n' + '/icp/' + TargetFile)
        .digest()
        .toString('base64')
    auth = `UCloud ${global.CONFIG.ufile.publicKey}:${auth}`
    const option = {
        url: global.CONFIG.ufile.target + `/${TargetFile}`,
        method: 'PUT',
        headers: {
            'Content-Type': FileType,
            Authorization: auth,
            'X-Ufile-Copy-Source': `/icp/${SourceFile}`,
        },
        timeout: 30 * 1000,
    }
    return await axios(option)
}

module.exports = {
    signature,
    getPictureMeta,
    getPictureUrls,
    getPictureBase64,
    getPictureFromPrefix,
    headerFile,
    uploadHit,
    copyFile,
    uploadFile,
    getTargetPicture,
}
