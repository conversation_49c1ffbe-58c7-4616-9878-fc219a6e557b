const _ = require('lodash')
const crypto = require('crypto')
const ucloudinternalapi = require('../libs/ucloudinternalapi')

function getSignatureString(baseOptions) {
    // 根据官网签名策略，获取签名
    let signatureString, keyList
    signatureString = ''
    keyList = []
    _.mapKeys(baseOptions, function (value, key) {
        keyList.push(key)
    })
    keyList.sort()
    // 排序,按公司公网API请求方式做加密与签名
    // 规则,Key排序 key+value+私钥得到签名，签名做为参数传入
    keyList.forEach((key) => {
        signatureString = signatureString + key + baseOptions[key]
    })

    signatureString =
        signatureString + global.CONFIG.IAssumeRoleToken.PrivateKey

    return crypto.createHash('sha1').update(signatureString).digest('hex')
}

async function getSTSToken(CompanyId, self) {
    let baseOptions = {
        Action: 'IAssumeRole',
        Backend: 'UAccountSTS',
        CharacterID: global.CONFIG.IAssumeRoleToken.CharacterID,
        CompanyID: CompanyId, //网关传入
        PublicKey: global.CONFIG.IAssumeRoleToken.PublicKey,
    }
    try {
        baseOptions.Signature = getSignatureString(baseOptions)
    } catch (error) {
        // 出错
        return Promise.reject(new self.Err(error, 67036))
    }
    try {
        let resp = await ucloudinternalapi(baseOptions)
        return Promise.resolve(resp)
    } catch (error) {
        return Promise.reject(new self.Err(error, 67011))
    }
}
module.exports = {
    getSignatureString,
    getSTSToken,
}
