const InternalApiReq = require('../../libs/ucloudinternalapi')
const {formatDateToYMD} = require("../kits");
const {checkAllDuplicateMobileEmailFields} = require("../checkAllDuplicateMobileEmailFields");
const RegStatus = [
    '开业',
    '存续',
    '迁往市外',
    '在业',
    '在营',
    '正常',
    '登记成立',
    '在营',
    '迁移异地',
    '在营',
    '已迁出企业',
    '成立',
    '待迁入',
    '仍注册',
    '正常在业',
    '开业登记中',
    '已开业',
    '开业/正常经营',
    '经营',
    '在营（开业）企业',
    '市内迁出',
]
async function CheckEpicEntPersonRelationFunction(order, checkRes) {
// 四要素检查，忽略证件类型问题，如果真的是证件类型不过 那就不过好了 不区分是否校验
    let epicCheckParam = {
        CompanyName: order.OrganizerName,
        CompanyCode: order.OrganizerLicenseId,
        LegalEntityName: order.PICMainName,
        LegalEntityId: order.PICMainLicenseId,
        CompanyId: order.CompanyId,
        Remark: '检查企业法人四要素',
        OrderNo: order.OrderNo,
        Action: 'CheckEpicEntPersonRelation',
        Backend: 'ICPV4',
    }
    let data = await InternalApiReq(epicCheckParam)
    if (data.RetCode === 0) {
        if (data.IsMatch === true) {
            // 企业四要素匹配通过
            checkRes.OrganizerName['企业四要素'] = true
            checkRes.OrganizerLicenseId['企业四要素'] = true
            checkRes.PICMainName['企业四要素'] = true
            checkRes.PICMainLicenseId['企业四要素'] = true
        } else {
            // 企业四要素匹配不通过
            checkRes.OrganizerName['企业四要素'] = data
                .DataSet?.companyName
                ? data.DataSet?.companyName
                : data.Message
            checkRes.OrganizerLicenseId['企业四要素'] = data
                .DataSet?.regNo
                ? data.DataSet?.regNo
                : data.Message
            checkRes.PICMainName['企业四要素'] = data
                .DataSet?.name
                ? data.DataSet?.name
                : data.Message
            checkRes.PICMainLicenseId['企业四要素'] = data
                .DataSet?.idCard
                ? data.DataSet?.idCard
                : data.Message
        }
    }
}

// 营业执照ocr，并返回对应的ocr结果
async function CheckBusinessLicenseDataFunction(order, checkRes, BusinessLicensePicOCRInfo) {
    let businessPicCheckParam = {
        CompanyName: order.OrganizerName,
        CompanyCode: order.OrganizerLicenseId,
        LegalEntityName: order.PICMainName,
        LegalEntityId: order.PICMainLicenseId,
        BusinessLicensePic: order.OrganizerLicensePicture[0],
        CompanyId: order.CompanyId,
        Remark: '营业执照OCR并对比',
        OrderNo: order.OrderNo,
        Action: 'CheckBusinessLicenseData',
        Backend: 'ICPV4',
    }
    let data = await InternalApiReq(businessPicCheckParam)
    if (data.RetCode === 0 && data.IsMatch === true) {
        // 营业执照OCR and check通过
        checkRes.OrganizerLicensePicture[
            '营业执照OCR'
            ] = true
    } else {
        // 营业执照OCR and check不通过
        checkRes.OrganizerLicensePicture['营业执照OCR'] =
            data.Message
    }
    // 记录OCR结果
    if (data.OCRInfo) {
        BusinessLicensePicOCRInfo.OCRInfo = data.OCRInfo
    }
}

// 天眼查查询结果，并返回对应的天眼查信息
async function CheckBusinessInfoWithTYCFunction(order, checkRes, BusinessTYCInfo) {
    let businessCheckWithTYCParam = {
        CompanyId: order.CompanyId,
        OrderNo: order.OrderNo,
        ForceSelect: true,
        CompanyName: order.OrganizerName,
        CompanyCode: order.OrganizerLicenseId,
        LegalEntityName: order.PICMainName,
        CompanyAddr: order.OrganizerLicenseArea, // 企业地址
        Remark: '天眼查信息查询对比',
        Action: 'CheckBusinessInfoWithTYC',
        Backend: 'ICPV4',
    }
    let data = await InternalApiReq(businessCheckWithTYCParam)
    if (data.RetCode === 0) {
        // 天眼查对比通过
        if (data.IsMatch === true) {
            // 天眼查对比匹配通过
            checkRes.OrganizerLicenseId['天眼查对比'] = true
            checkRes.PICMainName['天眼查对比'] = true
            checkRes.OrganizerLicenseArea[
                '天眼查对比'
                ] = true
        } else {
            let dataMessage = data.Message.split('。')
            let legalMessage = ''
            let licenseIdMessage = ''
            let companyAddrMessage = ''
            dataMessage.forEach((item) => {
                if (item.includes('法人')) {
                    legalMessage = item
                }
                if (item.includes('公司地址')) {
                    companyAddrMessage = item
                }
                if (item.includes('统一社会信用代码')) {
                    licenseIdMessage = item
                }
            })
            checkRes.OrganizerLicenseId['天眼查对比'] = data
                .Info.CompanyCode
                ? data.Info.CompanyCode
                : licenseIdMessage
            checkRes.PICMainName['天眼查对比'] = data.Info
                .LegalEntityName
                ? data.Info.LegalEntityName
                : legalMessage
            checkRes.OrganizerLicenseArea['天眼查对比'] =
                data.Info.CompanyAddr
                    ? data.Info.CompanyAddr
                    : companyAddrMessage
        }
        BusinessTYCInfo.TYCInfo = data.TYCInfo
    } else {
        // 天眼查对比不通过
        checkRes.OrganizerLicenseId['天眼查对比'] =
            data.Message
        checkRes.PICMainName['天眼查对比'] = data.Message
        checkRes.OrganizerLicenseArea['天眼查对比'] =
            data.Message
    }
}

// 身份证正面校验检查
async function GetIdenityOCRInfoFrontFunction(order, checkRes, web) {
    let webStr = JSON.stringify(web)
    let notifyStr = webStr === '{}' ? '主体' : '网站'
    let ocrCheckFrontParam = {
        Action: 'GetIdenityOCRInfo',
        Backend: 'ICPV4',
        PictureName: webStr === '{}' ? order.PICMainLicensePicture[0] : web.LicensePicture[0],
        CompanyId: order.CompanyId,
        OrderNo: order.OrderNo,
        Name: webStr === '{}' ? order.PICMainName : web.PICName,
        IdCardNumber: webStr === '{}' ? order.PICMainLicenseId : web.LicenseId,
        Side: 'Front',
        Remark: `${notifyStr}负责人${order.PICMainLicenseId}_${order.PICMainName}_正面`,
    }
    let data = await InternalApiReq(ocrCheckFrontParam)
    // 主体负责人正面ocr
    if (webStr === '{}') {
        if (data.RetCode === 0 && data.IsMatch == true) {
            checkRes.PICMainName['身份证OCR（正面）'] = true
            checkRes.PICMainLicensePicture[
                '身份证OCR（正面）'
                ] = true
        } else {
            checkRes.PICMainName['身份证OCR（正面）'] =
                data.Message
            checkRes.PICMainLicensePicture[
                '身份证OCR（正面）'
                ] = data.Message
        }
    } else {
        // 网站负责人正面ocr
        if (data.RetCode === 0 && data.IsMatch == true) {
            checkRes.Web[web.Id].LicensePicture[
                '身份证OCR（正面）'
                ] = true
        } else {
            checkRes.Web[web.Id].LicensePicture[
                '身份证OCR（正面）'
                ] = data.Message
        }
    }
}

// 身份证反面校验检查
async function GetIdenityOCRInfoBackFunction(order, checkRes, web) {
    let webStr = JSON.stringify(web)
    let notifyStr = webStr === '{}' ? '主体' : '网站'
    let ocrCheckBackParam = {
        Action: 'GetIdenityOCRInfo',
        Backend: 'ICPV4',
        PictureName: webStr === '{}' ? order.PICMainLicensePicture[1] : web.LicensePicture[1],
        CompanyId: order.CompanyId,
        OrderNo: order.OrderNo,
        Name: webStr === '{}' ? order.PICMainName : web.PICName,
        IdCardNumber: webStr === '{}' ? order.PICMainLicenseId : web.LicenseId,
        Side: 'Back',
        Remark: `${notifyStr}负责人${order.PICMainLicenseId}_${order.PICMainName}_反面`,
    }
    if (webStr === '{}') {
        ocrCheckBackParam.LicenseDate = order.PICMainLicenseDate || ''
    } else {
        ocrCheckBackParam.LicenseDate = web.LicenseDate || ''
    }
    let data = await InternalApiReq(ocrCheckBackParam)
    // 主体负责人反面ocr
    if (webStr === '{}') {
        if (data.RetCode === 0 && data.IsMatch === true) {
            checkRes.PICMainName['身份证OCR（反面）'] = true
            checkRes.PICMainLicenseDate[
                '身份证OCR（反面）'
                ] = true
            checkRes.PICMainLicensePicture[
                '身份证OCR（反面）'
                ] = true
        } else {
            checkRes.PICMainName['身份证OCR（反面）'] =
                data.Message
            checkRes.PICMainLicenseDate['身份证OCR（反面）'] =
                data.Message

            checkRes.PICMainLicensePicture[
                '身份证OCR（反面）'
                ] = data.Message
        }
    } else {
        // 网站负责人反面ocr
        if (data.RetCode === 0 && data.IsMatch === true) {
            checkRes.Web[web.Id].LicensePicture[
                '身份证OCR（反面）'
                ] = true
            checkRes.Web[web.Id].LicenseDate[
                '身份证OCR（反面）'
                ] = true
        } else {
            checkRes.Web[web.Id].LicensePicture[
                '身份证OCR（反面）'
                ] = data.Message
            checkRes.Web[web.Id].LicenseDate[
                '身份证OCR（反面）'
                ] = data.Message
        }
    }

}

// 检查身份证三要素
async function CheckPersonalInfoLicensePicture(order, checkRes, web) {
    let personCheckParam = {
        Action: 'CheckPersonalInfo',
        Backend: 'ICPV4',
        Name: web.PICName,
        Id: web.LicenseId,
        PictureName: web.LicensePicture[0],
        CompanyId:order.CompanyId,
        OrderNo: order.OrderNo,
        Remark: `网站负责人${web.LicenseId}_${web.PICName}`,
    }
    let data = await InternalApiReq(personCheckParam)
    if (data.RetCode === 0 && data.IsMatch === true) {
        checkRes.Web[web.Id].PICName[
            '三要素校验（身份证照片）'
            ] = true
        checkRes.Web[web.Id].LicenseId[
            '三要素校验（身份证照片）'
            ] = true
        checkRes.Web[web.Id].LicensePicture[
            '三要素校验（身份证照片）'
            ] = true
    } else {
        checkRes.Web[web.Id].PICName[
            '三要素校验（身份证照片）'
            ] = data.Message
        checkRes.Web[web.Id].LicenseId[
            '三要素校验（身份证照片）'
            ] = data.Message
        checkRes.Web[web.Id].LicensePicture[
            '三要素校验（身份证照片）'
            ] = data.Message
    }
}

// 检查最佳成像照三要素
async function CheckPersonalInfoCurtainPicture(order, checkRes, web) {
    let personCheckParam = {
        Action: 'CheckPersonalInfo',
        Backend: 'ICPV4',
        Name: web.PICName,
        Id: web.LicenseId,
        PictureName: web.CurtainPicture[0],
        CompanyId: order.CompanyId,
        OrderNo: order.OrderNo,
        Remark: `网站负责人${web.LicenseId}_${web.PICName}`,
    }
    let data = await InternalApiReq(personCheckParam)
    if (data.RetCode === 0 && data.IsMatch === true) {
        checkRes.Web[web.Id].PICName[
            '三要素校验（最佳成像照）'
            ] = true
        checkRes.Web[web.Id].LicenseId[
            '三要素校验（最佳成像照）'
            ] = true
        checkRes.Web[web.Id].CurtainPicture[
            '三要素校验（最佳成像照）'
            ] = true
    } else {
        checkRes.Web[web.Id].PICName[
            '三要素校验（最佳成像照）'
            ] = data.Message
        checkRes.Web[web.Id].LicenseId[
            '三要素校验（最佳成像照）'
            ] = data.Message
        checkRes.Web[web.Id].CurtainPicture[
            '三要素校验（最佳成像照）'
            ] = data.Message
    }
}

// 网站负责人幕布照片调用AnalyzePhotoRequirements分析数据
async function AnalyzePhotoRequirementsFunction(order, checkRes, web) {
    let personCheckParams = {
        Action: 'AnalyzePhotoRequirements',
        Backend: 'ICPV4',
        Picture: web.CurtainPicture[0],
        CompanyId: order.CompanyId,
        OrderNo: order.OrderNo,
        Remark: `网站负责人${web.LicenseId}_${web.PICName}`,
    }
    let data = await InternalApiReq(personCheckParams)
    if (data.RetCode === 0) {
        let result = {}
        const aiRes = data.AIRes
        if (aiRes['光线是否合适'] !== 2) {
            result['光线问题'] =
                aiRes['光线是否合适'] === 0
                    ? '光线过暗'
                    : '光线过亮'
        }
        if (aiRes['穿着是否应季'] !== 1) {
            result['穿着问题'] =
                aiRes['穿着是否应季'] === 0
                    ? '穿着不符合季节'
                    : '穿着季节不确定'
        }
        const booleanChecks = {
            嘴巴是否闭合: '嘴巴未闭合',
            照片是否清晰: '照片模糊',
            背景是否为白色: '背景色问题',
            是否仅有一个人物: '多余人物',
            是否穿衣: '未穿衣',
            是否睁眼: '未睁眼',
            是否免冠: '未免冠',
            IsHeadFullyVisible: '头部不完全可见',
        }
        Object.keys(booleanChecks).forEach((key) => {
            if (!aiRes[key]) {
                result[
                    booleanChecks[key]
                    ] = `${key}不符合`
            }
        })

        let resultString =
            Object.keys(result).length === 0
                ? '审核通过'
                : Object.entries(result)
                    .map(([key, value]) => `${value}`)
                    .join(', ')

        checkRes.Web[web.Id].CurtainPicture[
            '幕布照片AI分析'
            ] = resultString
    } else {
        checkRes.Web[web.Id].CurtainPicture[
            '幕布照片AI分析'
            ] = data.Message
    }
}

// TYC和OCR对比
function CompareTYCWithOCR(checkRes, BusinessTYCInfo, BusinessLicensePicOCRInfo) {
    if (BusinessTYCInfo && BusinessLicensePicOCRInfo) {
        let res = ''
        let tycApprovedTime = formatDateToYMD(BusinessTYCInfo.approvedTime, 'timestamp')
        let ocrDateTime = formatDateToYMD(BusinessLicensePicOCRInfo.registration_date, 'dateString')
        const isSameDate = (a, b) =>
            a.year === b.year && a.month === b.month && a.day === b.day;
        // BusinessTYCInfo 'creditCode', 'legalPersonName', 'regLocation'
        // BusinessLicensePicOCRInfo  'addr' 'code' 'name' 'pers'
        if (
            BusinessTYCInfo.creditCode !==
            BusinessLicensePicOCRInfo.code
        )
            res += `统一社会信用代码不一致，OCR:${BusinessLicensePicOCRInfo.code}, TYC:${BusinessTYCInfo.creditCode}`
        if (
            BusinessTYCInfo.legalPersonName !==
            BusinessLicensePicOCRInfo.pers
        )
            res += `法人姓名不一致，OCR:${BusinessLicensePicOCRInfo.pers}, TYC:${BusinessTYCInfo.legalPersonName}`
        if (
            BusinessTYCInfo.regLocation !==
            BusinessLicensePicOCRInfo.addr
        )
            res += `注册地址不一致，OCR:${BusinessLicensePicOCRInfo.addr}, TYC:${BusinessTYCInfo.regLocation}`

        if ( tycApprovedTime.year !== 0 && isSameDate(tycApprovedTime, ocrDateTime)) {
            checkRes.OrganizerLicensePicture['营业执照核准日期'] = true
        } else {
            const format = ({ year, month, day }) => `${year}年${month}月${day}日`;
            checkRes.OrganizerLicensePicture['营业执照核准日期'] =
                `营业执照核准日期不一致，OCR: ${BusinessLicensePicOCRInfo.registration_date}，TYC: ${format(tycApprovedTime)}`
        }

        checkRes.OrganizerLicensePicture['天眼查对比'] = !res
            ? true
            : res
    } else {
        checkRes.OrganizerLicensePicture['天眼查对比'] = `${
            !BusinessTYCInfo ? '未查到天眼查信息' : ''
        }${!BusinessLicensePicOCRInfo ? '未查到营业执照信息' : ''}`
    }
}

// 检查一下经营状态
function CheckTycRegStatus(checkRes, BusinessTYCInfo) {
    if (!BusinessTYCInfo || !BusinessTYCInfo.regStatus) {
        checkRes.OrganizerLicensePicture['经营状态'] = '未查到天眼查信息'
        return
    }
    if (RegStatus.includes(BusinessTYCInfo.regStatus)) {
        checkRes.OrganizerLicensePicture['经营状态'] = true
        return
    }
    if (BusinessTYCInfo.regStatus === '吊销') {
        checkRes.OrganizerLicensePicture['经营状态'] = `经营状态为【吊销】，不通过`
        return
    }
    checkRes.OrganizerLicensePicture['经营状态'] = `经营状态为【经营异常】，不通过；天眼查返回经营状态信息：${BusinessTYCInfo.regStatus}`
}

function CheckPersonAgeByLicenseId(order, checkRes, web) {
    let webStr = JSON.stringify(web)

    let isWebCheck = webStr === '{}' ? false : true
    let LicenseId = isWebCheck ? web.LicenseId : order.PICMainLicenseId

    let checkResult = checkLicenseId(LicenseId)
    if (!isWebCheck) {
        checkRes.PICMainLicenseId['ICP负责人年龄核验']= checkResult
    } else {
        checkRes.Web[web.Id].LicenseId['ICP负责人年龄核验']= checkResult
    }
}

function checkLicenseId(LicenseId) {
    if (!LicenseId || LicenseId.length !== 18) {
        return '证件号码格式不正确，不通过'
    }
    const birthYear = parseInt(LicenseId.substr(6, 4), 10);
    const birthMonth = parseInt(LicenseId.substr(10, 2), 10);
    const birthDay = parseInt(LicenseId.substr(12, 2), 10);

    const today = new Date();
    let age = today.getFullYear() - birthYear;

    // 判断生日是否过了，如果还没过，则减一岁
    if (
        today.getMonth() + 1 < birthMonth ||
        (today.getMonth() + 1 === birthMonth && today.getDate() < birthDay)
    ) {
        age--;
    }
    if (age >= 60 ) {
        return '年龄超过60岁，不通过'
    } else if (age < 18) {
        return '年龄小于18岁，不通过'
    }
    return true
}

function CheckLicenseDateVaild(order, checkRes, web) {
    let webStr = JSON.stringify(web)

    let isWebCheck = webStr === '{}' ? false : true
    let LicenseDate = isWebCheck ? web.LicenseDate : order.PICMainLicenseDate

    let checkResult = checkLicenseDate(LicenseDate)
    if (!isWebCheck) {
        checkRes.PICMainLicenseDate['主体负责人身份证有效期核验']= checkResult
    } else {
        checkRes.Web[web.Id].LicenseDate['网站负责人身份证有效期核验']= checkResult
    }
}

function checkLicenseDate(LicenseDate) {
    // 定义正则表达式
    var regex = /^\d{4}\.\d{2}\.\d{2}$/

    let dateArr = LicenseDate.split('-')
    const startDate = dateArr[0] || ''
    const endDate = dateArr[1] || ''

    // 使用正则表达式进行匹配
    if (
      !regex.test(startDate) ||
      (!regex.test(endDate) && endDate !== '长期')
    ) {
        return '身份证有效期格式不符合要求'
    }
    const currentDate = new Date()
    const startDateObj = new Date(startDate)
    const endDateObj = new Date(endDate)
    // 判断开始时间是否有效
    if (currentDate < startDateObj) {
        return '身份证尚未生效'
    }

    // 判断开始日期和结束日期是否有效
    if (
      startDateObj.toString() === 'Invalid Date' ||
      (endDateObj.toString() === 'Invalid Date' && endDate !== '长期')
    ) {
        return '日期格式无效'
    }

    // 判断结束日期是否为长期
    if (endDate === '长期') {
        return true
    }

    // 判断结束日期是否早于开始日期
    if (endDateObj < startDateObj) {
        return '结束日期早于开始日期'
    }

    const threeMonthsLater = new Date(currentDate);
    threeMonthsLater.setMonth(threeMonthsLater.getMonth() + 3);

    if (endDateObj <= threeMonthsLater) {
        return '身份证有效期将在三个月内到期';
    }

    return true
}


function OperateCheckRes(checkResult, checkRes, MainEn2CnMap, WebEn2CnMap) {
    for (let key in checkResult) {
        if (key === 'OrganizerName') {
            continue
        }
        if (key !== 'Web') {
            if (checkResult[key].OrderMatch.length === 0 && checkResult[key].ICPMatch.length === 0) {
                checkRes[key][`主体负责人${MainEn2CnMap[key]}检测通过`] = true
                continue
            }
            for (let orderMatchInfo of checkResult[key].OrderMatch) {
                let inputKey = `${MainEn2CnMap[key]}与主体（${orderMatchInfo.OrganizerName}）订单号（${orderMatchInfo.OrderNo}）重复的字段有：`
                checkRes[key][inputKey] = ``
                for (let enityMatchInfo of orderMatchInfo.EnityMatch) {
                    checkRes[key][inputKey] += `${MainEn2CnMap[enityMatchInfo]};`
                }
                for (let webMatchInfo of orderMatchInfo.WebMatch) {
                    checkRes[key][inputKey] += `网站（${webMatchInfo.Name}）${WebEn2CnMap[webMatchInfo.Field]};`
                }
            }
            for (let icpMatchInfo of checkResult[key].ICPMatch) {
                let inputKey = `${MainEn2CnMap[key]}与已有主体（${icpMatchInfo.IcpName}）备案号（${icpMatchInfo.IcpNo}）重复的字段有：`
                checkRes[key][inputKey] = ``
                for (let enityMatchInfo of icpMatchInfo.EnityMatch) {
                    checkRes[key][inputKey] += `${MainEn2CnMap[enityMatchInfo]};`
                }
                for (let webMatchInfo of icpMatchInfo.WebMatch) {
                    checkRes[key][inputKey] += `网站（${webMatchInfo.Name}）${WebEn2CnMap[webMatchInfo.Field]};`
                }
            }
        } else {
            for (let webSite of checkResult[key]) {
                let id = webSite.Id
                for (let webKey in webSite) {
                    if (webKey === 'Id') {
                        continue
                    }
                    if (webSite[webKey].OrderMatch.length === 0 && webSite[webKey].ICPMatch.length === 0) {
                        checkRes[key][id][webKey][`网站负责人${WebEn2CnMap[webKey]}检测通过`] = true
                        continue
                    }
                    for (let orderMatchInfo of webSite[webKey].OrderMatch) {
                        let inputKey = `${WebEn2CnMap[webKey]}与主体（${orderMatchInfo.OrganizerName}）订单号（${orderMatchInfo.OrderNo}）重复的字段有：`
                        checkRes[key][id][webKey][inputKey] = ``
                        for (let enityMatchInfo of orderMatchInfo.EnityMatch) {
                            checkRes[key][id][webKey][inputKey] += `${MainEn2CnMap[enityMatchInfo]};`
                        }
                        for (let webMatchInfo of orderMatchInfo.WebMatch) {
                            checkRes[key][id][webKey][inputKey] += `网站（${webMatchInfo.Name}）${WebEn2CnMap[webMatchInfo.Field]};`
                        }
                    }
                    for (let icpMatchInfo of webSite[webKey].ICPMatch) {
                        let inputKey = `${WebEn2CnMap[webKey]}与已有主体（${icpMatchInfo.IcpName}）备案号（${icpMatchInfo.IcpNo}）重复的字段有：`
                        checkRes[key][id][webKey][inputKey] = ``
                        for (let enityMatchInfo of icpMatchInfo.EnityMatch) {
                            checkRes[key][id][webKey][inputKey] += `${MainEn2CnMap[enityMatchInfo]};`
                        }
                        for (let webMatchInfo of icpMatchInfo.WebMatch) {
                            checkRes[key][id][webKey][inputKey] += `网站（${webMatchInfo.Name}）${WebEn2CnMap[webMatchInfo.Field]};`
                        }
                    }
                }
            }
        }
    }
}

function fieldMappingFunction(field) {
    // 使用对象映射字段组到对应的函数名，更易于维护和扩展
    const fieldToFunctionMap = {
        CheckEpicEntPersonRelation: ['OrganizerName', 'OrganizerLicenseId', 'PICMainName', 'PICMainLicenseId'],
        CheckBusinessLicenseData: ['OrganizerName', 'OrganizerLicenseId', 'PICMainName', 'OrganizerLicensePicture'],
        CheckBusinessInfoWithTYC: ['OrganizerName', 'OrganizerLicenseId', 'PICMainName', 'OrganizerLicenseArea', 'OrganizerLicensePicture'],
        GetIdenityOCRInfoFront: ['PICMainLicensePicture', 'PICMainName', 'PICMainLicenseId', 'LicensePicture', 'PICName', 'LicenseId'],
        GetIdenityOCRInfoBack: ['PICMainLicensePicture', 'PICMainName', 'PICMainLicenseId', 'PICMainLicenseDate', 'LicensePicture', 'PICName', 'LicenseId', 'LicenseDate'],
        CheckPersonalInfo: ['PICName', 'LicenseId', 'LicensePicture'],
        CheckPersonalInfoCurtainPicture: ['PICName', 'LicenseId', 'CurtainPicture'],
        AnalyzePhotoRequirements: ['CurtainPicture'],
        MobileOrganizerNameCheck: ['OrganizerName', 'PICMainMobile', 'PICMainLicenseId', 'EmergencyPhone', 'PICMainEmail', 'Mobile', 'LicenseId', 'Email'],
        CompareTYCWithOCR: ['OrganizerLicensePicture'],
        CheckTycRegStatus: ['OrganizerLicensePicture'],
        CheckPersonAgeByLicenseId: ['LicenseId', 'PICMainLicenseId'],
        CheckLicenseDateVaild: ['LicenseDate', 'PICMainLicenseDate']
    };

    let matchedFunctions = []
    // 查找匹配的函数名
    matchedFunctions = Object.entries(fieldToFunctionMap)
        .filter(([_, fields]) => fields.includes(field))
        .map(([funcName]) => global.CONFIG.checkAuditFunction[funcName])

    return matchedFunctions
}

function isNeedCheckAllDuplicateMobileEmailFields(AuditType, checkOrderFuncArray, webCheckFuncArray) {
    if (AuditType === 'FullAudit') {
        return true
    }
    if (checkOrderFuncArray.indexOf(global.CONFIG.checkAuditFunction.MobileOrganizerNameCheck) !== -1 ) {
        return true
    }
    for (let webKey of Object.keys(webCheckFuncArray)) {
        if (webCheckFuncArray[webKey].indexOf(global.CONFIG.checkAuditFunction.MobileOrganizerNameCheck) !== -1) {
            return true
        }
    }
    return false
}

module.exports = {
    fieldMappingFunction,
    OperateCheckRes,
    CompareTYCWithOCR,
    AnalyzePhotoRequirementsFunction,
    CheckPersonalInfoCurtainPicture,
    CheckPersonalInfoLicensePicture,
    GetIdenityOCRInfoBackFunction,
    GetIdenityOCRInfoFrontFunction,
    CheckBusinessInfoWithTYCFunction,
    CheckBusinessLicenseDataFunction,
    CheckEpicEntPersonRelationFunction,
    isNeedCheckAllDuplicateMobileEmailFields,
    CheckTycRegStatus,
    CheckPersonAgeByLicenseId,
    CheckLicenseDateVaild
}