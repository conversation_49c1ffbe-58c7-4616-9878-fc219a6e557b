/*
 * @file   根据OrgId 获取告警组联系人的数据
 */

const util = require('util')
const ucloudinternalapi = require('../../libs/ucloudinternalapi')
/*
 * @params {interger} orgId 组织ID
 * @params {interger?} groupTopicId 设置的告警组的ID，默认为合规部门 100024
 *
 *
 * @return {Array.<Object>} contacts 用户联系信息的数据
 * [
        {
            "switch_flag" : 1,  是否允许发送
            "send_type" : 3,    发送类型，仅邮件？仅短信？都可以？
            "mobile" : "15366261200",
            "email" : "<EMAIL>",
            "recv_name" : ""
        }
    ]
 * @return {number}  contacts.item.mobile
 * @return {string}  contacts.item.email
 * @return {string}  contacts.item.status 'enable' 表示 用户允许发送，'disable'表示用户禁止发送
 */

const getContactsByOrgId = async (orgId, groupTopicId = 100024) => {
    // 查询组
    let groupQuery = {
        Action: 'DescribeUSNSTopic',
        Backend: 'UTopic',
        organization_id: orgId,
        TopicId: groupTopicId,
    }
    try {
        let groupData = await ucloudinternalapi(groupQuery)

        // 请求出错让后面的catch获取
        if (
            groupData.RetCode !== 0 ||
            !groupData.Data ||
            groupData.Data.length === 0
        ) {
            throw 'request DescribeUSNSTopic  Error'
        }

        // 在限定groupTopicId的情况下groupData.Data会固定为1，如果不限制，则会返回全部。根据我们这的需求，只查为1的结果
        // 按旧标准取switch_flag 与 send_type SendTypeAvailable

        let switch_flag = groupData.Data[0].Status === 'Enable' ? 1 : 0
        let send_type = getSendType(groupData.Data[0].SendTypeAvailable)

        // 使用组Id查询
        let recvList = await ucloudinternalapi({
            Action: 'GetContact',
            Backend: 'UMON3-INNER-API',
            GroupID: groupData.Data[0].Group.Id,
            OrganizationID: orgId,
        })

        if (recvList.RetCode !== 0 || !recvList.Data) {
            throw 'request GetContact  Error'
        }

        let result = []
        recvList.Data.forEach((element) => {
            result.push({
                switch_flag,
                send_type,
                mobile: element.Phone,
                email: element.Mail,
                recv_name: element.ReceiveName,
            })
        })
        return Promise.resolve([result, null])
    } catch (error) {
        return Promise.resolve([
            null,
            { err: `getGroupContact failed:  orgId: ${orgId}`, code: 30406 },
        ])
    }
}
/*
 * @params { Mail, Sms, Callback, Phone } 取DescribeUSNSTopicResponse的接口的返回
    {
        "Mail": "Enable",
        "Sms": "Enable",
        "Phone": "Disable",
        "Callback": "Disable"
    }


*
 * @return Type int 开关情况
 * 0 不通知  1只邮件 2只短信 3邮件加短信
 */
function getSendType({ Mail, Sms, Callback, Phone }) {
    let sendTypeCode

    if (Mail === 'Disable' && Sms === 'Disable') {
        sendTypeCode = 0
    } else if (Mail === 'Enable' && Sms === 'Disable') {
        sendTypeCode = 1
    } else if (Mail === 'Disable' && Sms === 'Enable') {
        sendTypeCode = 2
    } else if (Mail === 'Enable' && Sms === 'Enable') {
        sendTypeCode = 3
    }

    return sendTypeCode
}
module.exports.getContactsByOrgId = getContactsByOrgId
