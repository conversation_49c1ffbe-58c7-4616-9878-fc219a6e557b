/*
 * @Date: 2022-09-15 15:51:31
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2023-04-24 11:57:40
 * @FilePath: /newicp/fns/notify/GetNotifyContent.js
 */
'use strict'

const { NotifyTypeEnum } = require('../../models/t_notify')
const {
    NotifyTemplateModel,
    NotifyTypeModel,
} = require('../../mongoModels/icp')
const moment = require('moment')
const _ = require('lodash')
/**
 * 此处利用 mongo中定义的通知模板，利用Title中的中英文对应的字段信息，将通知内容 对应的按照titile中  key顺序 加入到表格中
 * 利用中文对应的英文，从数据库中的字段中取出该 字段， 此处需要数据库写入的字段 与这个的名称对应
 * @param {*} NotifyTemplateModel
 * @param {*} ICPInfo
 * @param {*} Type 通知类型 如： 过期通知 或者 备案与实名不一致通知等
 * @param {*} Version
 * @returns
 */
/**
 * 同一公司下
 * A 网站联系人是a
 * B网站联系人是a   a收到一条 包含AB的
 * C网站联系人是b b收到一条只有c的
 * 同一主体，多个网站，相同负责人，这时网站负责只会收到一条通知
 * 主账号邮箱 和 BU 收到一个公司下全部的信息
 * ，，，所以 要用email和是否是主账号来过滤 收到指定的信息
 */
async function getMailContent(
    ICPInfo,
    Type,
    isAdmin = true,
    email = '',
    Version = 'v1'
) {
    // 获取通知内容
    let temp = await NotifyTypeModel.aggregate([
        { $match: { Type } },
        {
            $lookup: {
                from: NotifyTemplateModel.collection.name,
                localField: '_id',
                foreignField: 'TypeId',
                as: 'temp',
            },
        },
        { $unwind: '$temp' },
        { $match: { 'temp.Version': Version } },
        { $replaceRoot: { newRoot: '$temp' } },
    ])
    temp = temp[0]
    if (!temp || !temp?.Title?.trim() || !temp.EmailContent?.trim()) {
        return {
            Title: '',
            Content: '',
        }
    }
    let content = temp.EmailContent
    //此处根据定义的模板 对表格进行处理，
    if (!temp.TableTitle || Object.keys(temp.TableTitle).length > 0) {
        // 通知内容中包含表格
        let mainInfo = ''
        let tableHead = '<thead>'
        Object.values(temp.TableTitle).forEach((title) => {
            tableHead += `<th>`
            tableHead += title
            tableHead += `</th>`
        })
        ICPInfo.forEach((info) => {
            // 兼容各种格式 无资源通知 info下有web
            if (info.Websites) {
                // 如果不是admin 则只收到自己允许收到的信息
                if (!isAdmin) {
                    info.Websites = info.Websites.filter(
                        (website) => website.Email === email
                    )
                }
                info.Websites?.forEach((website) => {
                    mainInfo += `<tr>`
                    Object.keys(temp.TableTitle).forEach((title) => {
                        mainInfo += `<td>${
                            website[title] || info[title] || '无'
                        }</td>`
                    })
                    mainInfo += `</tr>`
                })
            } else {
                // 如果是 主账号邮箱， 或者该记录是该联系人下的用户 才加入到通知内容中
                if (isAdmin || Object.values(info).includes(email)) {
                    mainInfo += `<tr>`
                    Object.keys(temp.TableTitle).forEach((title) => {
                        mainInfo += `<td>${info[title] || '无'}</td>`
                    })
                    mainInfo += `</tr>`
                }
            }
        })
        content = temp.EmailContent.replace(
            '${table}',
            '<table  width:"100%" border="1"  cellspacing="0" >' +
                tableHead +
                mainInfo +
                '</table>'
        )
    }
    // 修改日期
    content = content.replace('${Date}', moment().format('YYYY-MM-DD HH:mm:ss'))
    content = content.replace(
        '${LastMonth}',
        moment().subtract(1, 'months').format('YYYY年MM月')
    )
    content = dateReplace(content)
    let res = {
        Title: temp.Title || '',
        Content: content,
    }
    if (temp.AttachFiles) {
        // 发送前调整file
        let attr = temp.AttachFiles.map((attachfile) => {
            attachfile.AttachContent =
                attachfile.AttachContent.split(';base64,')[1]
            return attachfile
        })
        res.AttachFiles = attr
    }
    return res
}
async function getSmsContent(Type, MainMail, Version = 'v1') {
    // 获取通知内容
    let temp = await NotifyTypeModel.aggregate([
        { $match: { Type } },
        {
            $lookup: {
                from: NotifyTemplateModel.collection.name,
                localField: '_id',
                foreignField: 'TypeId',
                as: 'temp',
            },
        },
        { $unwind: '$temp' },
        { $match: { 'temp.Version': Version } },
        { $replaceRoot: { newRoot: '$temp' } },
    ])
    temp = temp[0]
    if (temp.SmsContent.trim() === '') {
        return ''
    }
    let content = temp.SmsContent.replace('${mail}', MainMail)
    content = dateReplace(content)
    return content
}

function dateReplace(content) {
    // 处理 短信模板中含有日期的站位符
    /**
     * 占位符使用 例子 ${add（固定） 单位（hours...） “-”分隔符  时间 }
     * 以当前时间为例  2022-12-06 14:38:00
     * ${addminutes-20} 2022-12-06 14:58:00
     * ${addhours-9} 2022-12-06 23:38:00
     * ${adddays-8} 2022-12-14 00:00:00
     * ${addweeks-2} 2022-12-20 00:00:00
     * ${addmonths-6} 2023-06-06 00:00:00
     * ${addyears-5} 2027-12-06 00:00:00
     */
    let currDate,
        currString = ''
    let beginIndex = content.indexOf('${add')
    if (beginIndex !== -1) {
        let tmpDate
        let tmp = content.split('${add')[1]
        let tmpIndex = tmp.indexOf('-')
        let tmpIndex1 = tmp.indexOf('}')
        if (tmpIndex !== -1 && tmpIndex1 !== -1) {
            // 截取时间单位
            tmpDate = tmp.substring(0, tmpIndex)
            let dateUnit = [
                'minutes',
                'hours',
                'days',
                'weeks',
                'months',
                'years',
            ]
            // 截取时间数字
            let tmpNum = tmp.substring(tmpIndex + 1, tmpIndex1)
            tmpNum = parseInt(tmpNum)
            // 校验 时间单位 和时间数字的规范
            if (!isNaN(tmpNum) && dateUnit.includes(tmpDate)) {
                switch (tmpDate) {
                    case 'minutes':
                    case 'hours':
                        currDate = moment()
                            .startOf('minutes')
                            .add(tmpNum, tmpDate)
                            .format('YYYY-MM-DD HH:mm:ss')
                        break
                    case 'days':
                    case 'weeks':
                    case 'months':
                    case 'years':
                        currDate = moment()
                            .startOf('days')
                            .add(tmpNum, tmpDate)
                            .format('YYYY-MM-DD HH:mm:ss')
                        break
                }
                currString = '${add' + tmpDate + '-' + tmpNum + '}'
            }
        }
    }
    if (currString !== '') {
        content = content.replace(currString, currDate)
    }
    return content
}

module.exports = {
    getMailContent,
    getSmsContent,
    dateReplace,
}
