import _ from 'underscore'
import { mainFormLabelMap } from './formLabelMap'
import { getProvinceId, findArea, getPhonePre } from './area'

const parseDomain = require('parse-domain')
const lodash = require('lodash')
const isNotEmpty = (value) => {
    if (_.isNumber(value) || _.isBoolean(value)) return true
    return !_.isEmpty(value)
}

const ruleTest = (ruleName, order = {}, ...rest) => {
    const rule = rules[ruleName]
    if (!rule) {
        console.warn(`There is no rule named ${ruleName}`)
        return false
    }

    const {
        organizerTypes,
        notOrganizerTypes,
        provinces,
        notProvinces,
        types,
        notTypes,
        test,
    } = rule

    if (
        !_.isEmpty(organizerTypes) &&
        _.findIndex(
            organizerTypes,
            (organizerType) => organizerType === order.OrganizerType
        ) < 0
    ) {
        return false
    }
    if (
        !_.isEmpty(notOrganizerTypes) &&
        (_.findIndex(
            notOrganizerTypes,
            (organizerType) => organizerType === order.OrganizerType
        ) > -1 ||
            order.OrganizerType === undefined)
    ) {
        return false
    }
    if (!_.isEmpty(provinces) && !findArea(order, provinces)) {
        return false
    }
    if (!_.isEmpty(notProvinces) && findArea(order, notProvinces)) {
        return false
    }

    if (
        !_.isEmpty(types) &&
        _.findIndex(types, (type) => type === order.Type) < 0
    ) {
        return false
    }
    if (
        !_.isEmpty(notTypes) &&
        (_.findIndex(notTypes, (type) => type === order.Type) > -1 ||
            order.Type === undefined)
    ) {
        return false
    }

    if (test) {
        return test(order, ...rest)
    }

    return true
}

const ruleCheck = (ruleName, value, order = {}, ...rest) => {
    if (!ruleTest(ruleName, order, ...rest)) {
        return true
    }
    const rule = rules[ruleName]
    const { check } = rule
    if (check) {
        return check(value, order, ...rest)
    }

    return true
}

const rules = {
    // 座机需为备案地号码（可强制填写区号）
    PHONE_MUST_BE_ICP_LOCAL: {
        check: (phonePre, order = {}) => {
            let provinceId = getProvinceId(order)
            let localPhonePre = getPhonePre(provinceId)

            if (!localPhonePre) {
                return true
            }
            if (
                phonePre &&
                _.indexOf(localPhonePre, phonePre.substr(1, 2)) === -1
            ) {
                return '座机必须为备案地号码'
            }
            return true
        },
    },
    THE_WEB_NUMBER_OF_ORDER_TYPE: {
        check: (value, order = {}) => {
            if (order.Type == 7) {
                // 变更不限制网站数据量
                return true
            } else if (value.length > 1 && order.Type != 1) {
                return '只能有一个网站'
            } else if (order.Type == 1 && value.length > 3) {
                return '最多只能添加3个网站'
            }
            return true
        },
    },
    THE_WEB_NUMBER_OF_AREA: {
        check: (value, order = {}) => {
            if (order.Type == 1 && value.length > 1) {
                return '您所在的省份只能提交一个网站，请删除订单后重试'
            }
            return true
        },
    },
    SOME_AREA: {
        provinces: [42, 43],
    },
    // 网站名称不能包含（公司、论坛、交流、视频、游戏、官网、影视、医疗、教育、新闻、资讯、购物、金融、股票、基金、资讯、投资、交易、支付、出版）
    WEBSITE_NAME_CAN_NOT_CONTAIN_KEYWORDS: {
        organizerTypes: [5],
        check: (value, order = {}) => {
            if (
                /公司|论坛|交流|视频|游戏|官网|影视|医疗|教育|新闻|资讯|购物|金融|股票|基金|资讯|投资|交易|支付|出版/.test(
                    value
                )
            ) {
                return '您提交的网站名称已超过个人备案范畴'
            }
            return true
        },
    },
    // 网站负责人处备注，必须写明网站内容
    MUST_FILL_WEBSITE_REMARK: {
        notTypes: [3],
        organizerTypes: [5],
    },
    //网站备注非个人，内蒙古必填
    WEBSITE_NEI_MENG_GU_REQUIRED_REMARK_NOT_PERSONAL: {
        notTypes: [3],
        provinces: [15],
        notOrganizerTypes: [5],
    },
    // 主体负责人与网站负责人不同时，不能填写同一手机号，黑龙江除外
    IF_PERSONAL_IS_NOT_SAME_MOBILE_CAN_NOT_BE_SAME: {
        types: [1, 7],
        check: (value, order = {}, website = {}) => {
            if (
                !ruleTest('HEI_LONG_JIANG_SPECIAL', order) &&
                website.Mobile === website.EmergencyPhone
            ) {
                return '网站的手机号码与应急电话不可重复'
            }
            if (
                order.PICMainLicenseId === undefined ||
                order.PICMainLicenseId === '' ||
                website.LicenseId === undefined ||
                website.LicenseId === ''
            ) {
                return true
            }

            if (website.LicenseId === order.PICMainLicenseId) {
                return true
            } else if (
                ruleTest('HEI_LONG_JIANG_SPECIAL', order) &&
                website.EmergencyPhone === order.EmergencyPhone
            ) {
                return '主体负责人与网站负责人不同时，不能填写同一应急联系电话'
            }
            if (website.Mobile === order.PICMainMobile) {
                return '主体负责人与网站负责人不同时，不能填写同一手机号'
            }
            return true
        },
    },

    IF_MAIN_EMERGENCYPHONE_SAME_MOBILE: {
        check: (value, order = {}, website = {}) => {
            if (
                !ruleTest('HEI_LONG_JIANG_SPECIAL', order) &&
                order.PICMainMobile === order.EmergencyPhone
            ) {
                return '手机号码与应急电话不可重复'
            } else {
                return true
            }
        },
    },

    // 主体负责人与网站负责人不同时，不能填写同一邮箱
    IF_PERSONAL_IS_NOT_SAME_EMAIL_CAN_NOT_BE_SAME: {
        check: (value, order = {}, website = {}) => {
            if (
                order.PICMainLicenseId === undefined ||
                order.PICMainLicenseId === '' ||
                website.LicenseId === undefined ||
                website.LicenseId === ''
            ) {
                return true
            }
            if (website.LicenseId === order.PICMainLicenseId) {
                return true
            }
            if (website.Email === order.PICMainEmail) {
                return '主体负责人与网站负责人不同时，不能填写同一邮箱'
            }
            return true
        },
    },
    // 主体负责人与网站负责人不同时需提供授权书
    NEED_AUTH_PAPER: {
        notOrganizerTypes: [5],
        notProvinces: [11],
        test: (order = {}, website = {}) => {
            if (
                order.PICMainName === undefined ||
                order.PICMainName === '' ||
                website.PICName === undefined ||
                website.PICName === ''
            ) {
                return false
            }
            if (website.PICName === order.PICMainName) {
                return false
            }
            return true
        },
    },
    // 复审时需要邮寄纸质材料
    MUST_POST_FILE_IN_SOME_AREA: {
        provinces: [12, 61],
    },
    // 一个网站只能备案一个域名
    CAN_ONLY_HAVE_ONE_DOMAIN: {
        provinces: [50],
    },
    // 一个网站只能对应一个IP
    CAN_ONLY_HAVE_ONE_IP: {
        provinces: [50, 12],
        check: (value = '', order) => {
            let ips
            if (_.isString(value)) {
                ips = value.split(';')
            } else {
                ips = value
            }
            if (ips.length > 1) {
                return '一个网站只能对应一个IP地址，不能填写多个ip'
            }
            return true
        },
    },
    URL_IN_DOMAIN: {
        check: (value, website, order) => {
            let domain = website.Domain
            domain = lodash.map(domain, 'Domain')
            for (let i = 0; i < domain.length; i++) {
                domain[i] = domain[i].trim().toLowerCase()
            }
            let urls = value.split(';')
            let urlsuniq = lodash.uniqWith(urls, _.isEqual)
            if (urlsuniq.length < urls.length) {
                return '不应填写重复的url'
            }
            for (let i = 0; i < urls.length; i++) {
                let body = parseDomain(urls[i])
                if (body === null) {
                    return '该网址不正确'
                }
                let urlToDomain = body.domain + '.' + body.tld
                if (lodash.indexOf(domain, urlToDomain) === -1)
                    return '该网址不存在于域名中'
            }
            return true
        },
    },

    // 多个IP请用英文分号;分开
    CAN_HAVE_MULTIPLE_IP: {
        notProvinces: [50, 12],
    },
    // 个人可不提供座机号码
    DO_NOT_NEED_PHONE: {
        provinces: [
            11, 13, 14, 15, 21, 22, 23, 31, 32, 33, 34, 35, 36, 37, 41, 42, 43,
            44, 45, 46, 50, 51, 52, 53, 54, 61, 62, 63, 64, 65,
        ],
        organizerTypes: [5],
    },
    // 非备案地户籍需提供居住证或者暂住证
    NEED_ORGANIZER_RESIDENCE_PERMIT_PICTURE: {
        provinces: [22, 31, 32, 33, 36, 37, 42, 44, 51, 62],
        organizerTypes: [5],
    },
    // 一个网站下最多备案5个域名
    CANNOT_OVER_5_DOMAIN: {
        provinces: [
            11, 12, 13, 14, 15, 21, 22, 23, 31, 32, 33, 34, 35, 36, 37, 41, 42,
            43, 44, 45, 46, 51, 52, 53, 54, 62, 63, 64, 65,
        ],
        types: [1, 2],
        check: (otherDomains = [], order) => {
            if (otherDomains.length < 5) {
                return true
            } else {
                return '一个网站下最多备案5个域名'
            }
        },
    },
    // 网站负责人与主体负责人不要填写同一个人
    WEBSITE_MAIN_CANNOT_BE_SAME_FZR: {
        notOrganizerTypes: [5],
        provinces: [12, 41],
    },
    // 如果法人是外国人，必须授权给中国国籍人员作为主体负责人，并且网站负责人必须为同一人（授权书模板与普通授权不一致）
    SHOW_FOREIGNER_AUTH_PAPER_TIP: {
        notOrganizerTypes: [5],
        provinces: [37, 62],
    },
    // 主体负责人、网站负责人必须使用备案地身份证备案、不能使用其他证件备案
    MUST_USE_LOCAL_ID_CARD: {
        provinces: [12, 14, 41, 43, 50, 54, 63, 64, 65],
        organizerTypes: [5],
        check: (licenseType, order, website) => {
            if (+licenseType !== 2) {
                return '请选择身份证'
            }
            return true
        },
    },
    // 手机需为备案地号码
    MOBILE_MUST_BE_LOCAL: {
        provinces: [
            11, 12, 13, 14, 15, 21, 23, 32, 34, 35, 37, 41, 42, 43, 50, 52, 53,
            54, 61, 62, 64, 65,
        ],
        types: [1, 2],
    },
    // 新疆特殊规则
    XJ_SPECIAL_RULE: {
        provinces: [65],
    },
    HEI_LONG_JIANG_SPECIAL: {
        provinces: [23],
    },
    // 需上传营业执照副本
    NEED_YYZZFB: {
        provinces: [21, 41, 62],
        notOrganizerTypes: [5],
    },
    // 需上传竖版营业执照
    NEED_SBYYZZ: {
        provinces: [37],
        notOrganizerTypes: [5],
    },
    // 如负责人为香港人，请提供港澳居民来往内地通行证彩色原件扫描件，不得使用护照。
    IS_HK: {
        provinces: [12],
        notOrganizerTypes: [5],
    },
    // 主体和网站备注中需填写一位应急联系人及联系电话，确保应急联系人的电话保持畅通（应急联系人可以是家人或者朋友。）
    MUST_FILL_CONCAT_IN_REMARK: {
        provinces: [50, 3],
    },
    // 主体和网站备注中需填写一位应急联系人及联系电话，确保应急联系人的电话保持畅通（应急联系人可以是家人或者朋友。）
    MUST_FILL_CONCAT_IN_REMARK_NOT_PERSONAL: {
        provinces: [41],
        notOrganizerTypes: [5],
    },
    // 主体备注：非个人内蒙古必填
    MUST_FILL_CONCAT_IN_REMARK_OF_NMG: {
        provinces: [21],
        notOrganizerTypes: [5],
    },
    //备注：甘肃、河南、重庆、天津必填 2020-04-26去掉黑龙江、23,
    REQUIRES_REMARK_PROVINCES: {
        notTypes: [3],
        provinces: [62, 41, 50, 12],
    },
    MUST_FILL_CONCAT_IN_REMARK_TJ: {
        provinces: [3],
    },
    //是否是存量订单
    OLD_ORDER_MODIFY: {
        check: (value, order) => {
            if (![2, 3].includes(order.Type)) {
                return false
            }
            let orderTime = order.CreateTime
                ? new Date(order.CreateTime * 1000).getTime()
                : new Date().getTime()
            let online = new Date('2020-03-09 19:00:00').getTime()
            if (online > orderTime) {
                return true
            }
            return false
        },
    },
    // 单位网站名称需与主办单位有关联，建议用主办单位全称或者简称做网站名称
    WEBSITE_NAME_SPECIAL_RULE: {
        provinces: [51, 61],
        notOrganizerTypes: [5],
    },
    // 域名所有者需与主办单位名称或者法人姓名保持一致，如不一致请联系域名注册商进行过户。
    DOMAIN_OWNER_MUST_BE_SAME_WITH_ORG_OR_MAIN: {
        provinces: [31],
    },
    // 域名所有者需与主办单位名称或法人姓名或网站负责人姓名保持一致。如不一致，请联系域名注册商进行过户。
    DOMAIN_OWNER_MUST_BE_SAME_WITH_ORG_OR_MAIN_OR_WEBSITE: {
        provinces: [32],
    },
    // 域名所有者需与主办单位名称保持一致。
    DOMAIN_OWNER_MUST_BE_SAME_WITH_ORG: {
        notProvinces: [31, 32],
    },
    // 如该主体下备案的域名超过5（包含5个）需在复审阶段将所有加盖公司公章的域名证书复印件和真实性核验材料一起邮寄我司。
    DOMAIN_OVER_5_NEED_POST: {
        provinces: [61],
    },
    // 同一个网站下备案多个域名的，首页网址需要一一对应。即几个域名填写几个首页地址，多个地址间用分号隔开。
    DOMAIN_HOME_URL_IS_PAIR: { provinces: [51, 61] },
    // 同一个网站下备案多个域名的，首页网址仅需填写一个
    DOMAIN_HOME_URL_IS_SINGLE: {
        provinces: [
            11, 12, 13, 14, 15, 21, 22, 23, 31, 32, 33, 34, 35, 36, 37, 41, 42,
            43, 44, 45, 46, 50, 52, 53, 54, 62, 63, 64, 65,
        ],
    },
    // 一个网站负责人只能作为一个主体的网站负责人，不允许作为多个主体的网站负责人。
    FZR_CANNOT_FOR_MUTIL_MAIN: {
        provinces: [12, 41],
    },
    // 主体负责人作为网站负责人，需要主体负责人（法人）拍摄备案照片并签署备案资料
    MAIN_BE_WEBSITE_FZR: {
        provinces: [
            11, 13, 14, 15, 21, 22, 23, 31, 32, 33, 34, 35, 36, 37, 42, 43, 44,
            45, 46, 50, 51, 52, 53, 54, 61, 62, 63, 64, 65,
        ],
        notOrganizerTypes: [5],
    },

    // 新增备案
    SPECIAL_FOR_NEW_ICP: {
        types: [1],
    },
    // 新增备案和新增网站个人
    SPECIAL_FOR_NEW_ICP_AND_NEW_WEBSITE_PERSONAL: {
        types: [1, 2],
        organizerTypes: [5],
    },
    // 新增备案和新增网站非个人
    SPECIAL_FOR_NEW_ICP_AND_NEW_WEBSITE_NOT_PERSONAL: {
        types: [1, 2],
        notOrganizerTypes: [5],
    },
    // 新增备案和新增网站
    SPECIAL_FOR_NEW_ICP_AND_NEW_WEBSITE: {
        types: [1, 2],
    },
    // 新增网站和新增接入
    SPECIAL_FOR_TRANSFER_AND_NEW_WEBSITE: {
        types: [2, 3],
    },
    // 新增接入
    SPECIAL_FOR_TRANSFER: {
        types: [3],
    },
    // 复审特殊规则1
    SPECIAL_VERIFY_RULE_1: {
        provinces: [43],
    },
    // 复审特殊规则2
    SPECIAL_VERIFY_RULE_2: {
        provinces: [12, 61],
    },
    // 复审特殊规则3
    SPECIAL_VERIFY_RULE_3: {
        notProvinces: [43, 12, 61],
    },
    // 隐藏上海地址
    HIDE_ADDRESS_OF_SHANGHAI: {
        provinces: [61],
    },
}

rules.CANNOT_OVER_5_DOMAIN_FOR_DOMAIN = {
    ...rules.CANNOT_OVER_5_DOMAIN,
    check: (domains = []) => {
        if (domains.length < 6) {
            return true
        } else {
            return '一个网站下最多备案5个域名'
        }
    },
}
rules.CAN_ONLY_HAVE_ONE_DOMAIN_FOR_DOMAIN = {
    ...rules.CAN_ONLY_HAVE_ONE_DOMAIN,
    check: (domains = []) => {
        if (domains.length < 2) {
            return true
        } else {
            return '一个网站只能备案一个域名'
        }
    },
}

rules.OLD_ORDER_NOT_REQUIRED = {
    // 检查订单的创建时间是否晚于指定的时间，类型是否是指定的类型
    // 目前的规则：新增网站与接入，创建时间晚于发布时间的，不做必填检查
    check: (value, order, key = {}) => {
        if (
            (order.Type === 2 || order.Type === 3) &&
            order.CreateTime < 1583748000
        ) {
            return true
        }
        return value != 0 && isNotEmpty(value)
            ? true
            : `${mainFormLabelMap[key] || '该字段'}不能为空！`
    },
}

export { rules, ruleTest, ruleCheck }
