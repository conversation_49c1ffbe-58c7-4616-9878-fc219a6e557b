const ucloudinternalapi = require('../libs/ucloudinternalapi')

// 通过WebSocket向前端推送消息
async function PostWebSocket(
    {
        EventName,
        Response,
        CompanyId,
        ExpireTime = null,
        ChannelList = [1],
        Pusher = '<EMAIL>',
    },
    self
) {
    let baseOptions = {
        Backend: 'UWebMsg',
        Action: 'PushMessage',
        Body: { EventName: EventName, Response: Response },
        Receiver: { CompanyId: CompanyId },
        Pusher: Pusher,
        ExpireTime: ExpireTime,
        ChannelList: ChannelList,
    }

    try {
        console.log(baseOptions)
        let resp = await ucloudinternalapi(baseOptions)
        return Promise.resolve(resp)
    } catch (error) {
        return Promise.reject(new self.Err(error, 67011))
    }
}
module.exports = {
    PostWebSocket,
}
