/*
 * @Author: william.qian <EMAIL>
 * @Date: 2022-02-16 11:11:48
 * @LastEditors: li<PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2023-06-21 14:22:32
 * @FilePath: /newicp/fns/order/OrderService.js
 * @Description: 2023-04-13 modify,审核侧订单查询模式，增加了区域的多选功能
 */
'use strict'
const { Op } = require('sequelize')
const { CompanyBlockListModel } = require('../../models')
const OrderService = {}
const _ = require('lodash')

/**
 *
 * @param {*} db
 * @param {*} params
 * @param {*} order  排序方式
 * @returns
 */
OrderService.getOrderInfo = function (
    db,
    params,
    offset,
    limit,
    order = [['update_time', 'asc']]
) {
    let cond = Object.assign({}, params)

    if (cond.AreaId && typeof cond.AreaId === 'string') {
        let AreaId = ('' + cond.AreaId).replace(/(0{2}|0{4})$/, '')
        cond.AreaId = { [Op.like]: `${AreaId}%` }
    } else if (
        cond.AreaId &&
        typeof cond.AreaId === 'object' &&
        cond.AreaId.length !== 0
    ) {
        // 数组结构的处理
        let arrayCron = []
        for (var i = 0; i < cond.AreaId.length; i++) {
            let AreaId = ('' + cond.AreaId[i]).replace(/(0{2}|0{4})$/, '')
            arrayCron.push({ AreaId: { [Op.like]: `${AreaId}%` } })
        }
        cond[Op.or] = arrayCron
    }
    delete cond.AreaId
    if (cond.StartTime && cond.EndTime) {
        cond.UpdateTime = { [Op.between]: [cond.StartTime, cond.EndTime] }
    }
    if (cond.ICPMainNo) {
        let ICPMainNo = cond.ICPMainNo
        cond.ICPMainNo = { [Op.like]: `${ICPMainNo}%` }
    }
    if (offset === undefined || limit === undefined) {
        offset = 0
        limit = 20
    }

    delete cond.StartTime
    delete cond.EndTime

    return db.findAll({ where: cond, offset, limit, order })
}

OrderService.countOrderInfo = function (db, params) {
    let cond = Object.assign({}, params)

    if (cond.AreaId && typeof cond.AreaId === 'string') {
        let AreaId = ('' + cond.AreaId).replace(/(0{2}|0{4})$/, '')
        cond.AreaId = { [Op.like]: `${AreaId}%` }
    } else if (
        cond.AreaId &&
        typeof cond.AreaId === 'object' &&
        cond.AreaId.length !== 0
    ) {
        // 数组结构的处理
        let arrayCron = []
        for (var i = 0; i < cond.AreaId.length; i++) {
            let AreaId = ('' + cond.AreaId[i]).replace(/(0{2}|0{4})$/, '')
            arrayCron.push({ AreaId: { [Op.like]: `${AreaId}%` } })
        }
        cond[Op.or] = arrayCron
    }
    delete cond.AreaId
    if (cond.StartTime && cond.EndTime) {
        cond.UpdateTime = { [Op.between]: [cond.StartTime, cond.EndTime] }
    }
    if (cond.ICPMainNo) {
        let ICPMainNo = cond.ICPMainNo
        cond.ICPMainNo = { [Op.like]: `${ICPMainNo}%` }
    }
    delete cond.StartTime
    delete cond.EndTime

    return db.count({ where: cond })
}
/**
 * 根据条件获取 网站订单信息
 * @param {*} db
 * @param {*} params
 * @returns
 */
OrderService.getOrderWebInfo = function (db, params) {
    let where = {}
    if (params.Domain) {
        where.Domain = { [Op.like]: `%${params.Domain}%` }
    }
    if (params.IP) {
        where.IP = { [Op.like]: `%${params.IP}%` }
    }
    if (params.LicenseId) {
        where.LicenseId = params.LicenseId
    }
    // 通过订单查询网站  OrderNos 是数组哦
    if (params.OrderNos) {
        where.OrderNo = {
            [Op.in]: params.OrderNos,
        }
    } else if (params.OrderNo) {
        where.OrderNo = params.OrderNo
    }
    // GetAll 1：获取所有网站订单 0:
    // 如果不是 查询全部的，  并且没有任何条件查询 直接退出
    if (JSON.stringify(where) === '{}' && !params.GetAll) {
        return Promise.resolve()
    }

    return db.findAll({ where })
}

/**
 * 订单创建与检查前，确定此公司是否有执行权限
 * @param {*} db
 * @param {*} params
 * @returns
 */
OrderService.checkCompanyOrderPermissions = async function (companyId) {
    let count = await CompanyBlockListModel.count({
        where: {
            CompanyId: companyId,
        },
    })
    return count !== 0
}

module.exports = OrderService
