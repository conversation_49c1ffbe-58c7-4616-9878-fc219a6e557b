'use strict'
const { Op } = require('sequelize')

const CurtainService = {}

/**
 * 根据条件获取 网站订单信息
 * @param {*} db
 * @param {*} params
 * @returns
 */
CurtainService.getCurtainInfo = async function (db, params) {
    let where = {}

    if (params.CurtainStatus === 1) {
        where.express_no = ''
        where.recipient = { [Op.ne]: '' }
    } else if (params.CurtainStatus === 2) {
        where.express_no = { [Op.ne]: '' }
        where.recipient = { [Op.ne]: '' }
    } else {
        return Promise.resolve()
    }
    return db.findAll({ where })
}

module.exports = CurtainService
