/*
 * @file
 * 接收uploadFileToUS3的请求
 * 收到后map取出需要上传的文件
 * 处理文件
 * 上传文件
 * 此功能 主要针对给予baseName 上传多个文件使用
 */

const fs = require('fs')
const _ = require('lodash')
const { upload } = require('../../libs/uploadFile')
const mineType = require('mime-types')
const moment = require('moment')
const logger = require('../../libs/logger').getLogger('access')

module.exports = async ({ faceRange, baseName, directory }) => {
    faceRange.forEach(async (filePath, index) => {
        let imageData = fs.readFileSync(filePath)
        let tmp = filePath.split('.')
        let ext = tmp[tmp.length - 1] ? tmp[tmp.length - 1] : 'jpg'
        let fileName = `${
            directory ? directory + '/' : ''
        }${baseName}_${index}.${ext}`
        await upload(
            'data:' +
                mineType.lookup(filePath) +
                ';base64,' +
                imageData.toString('base64'),
            fileName
        )
            .then(([filename, data]) => {
                // 此处记录 带目录的路径，到时候删除 也直接删除这个路径 下的
                logger.info(
                    `${baseName} about upload success,filename:${filename}`
                )
            })
            .catch((err) => {
                logger.error('uploadFileToUS3 custom error', err)
            })
    })
}
