'use strict'

const axios = require('../../libs/axiosApi')
const { URL } = require('url')
const defaultTimeout = 5 * 1000
const redis = require('../../libs/redis')
const henganApi = require('../../libs/henganApiPromise')
const GetDomainPublicICP = require('../../methods/common/GetDomainPublicICP')
const moment = require('moment')
redis.init()

const getToken = async () => {
    let redisInstace = redis.get()

    let tokenString = await redisInstace.get('adToken')
    if (tokenString === null) {
        // 拿锁
        let lock = await redisInstace.setnx('adTokenLock', 1)
        if (lock) {
            // 设置锁的过期时间
            await redisInstace.expire('adTokenLock', 5) // 5秒后自动释放锁
            tokenString = await getAodunToken()
            await redisInstace.set('adToken', tokenString, 'EX', 60 * 10)
        } else {
            // 等待锁释放
            let ttl = await redisInstace.ttl('adTokenLock')
            if (ttl > 0) {
                // 再次获取token
                await new Promise((resolve) => {
                    setTimeout(resolve, ttl * 1000)
                })
            }
            tokenString = await redisInstace.get('adToken')
        }
    }

    return tokenString
}

// 获取信任域名（傲盾白名单）
const addTrustDomain = async (token, domainList) => {
    try {
        const url = new URL(
            global.CONFIG.aodun.addTrustDomain,
            global.CONFIG.aodun.url
        ).href
        const options = {
            method: 'POST',
            url,
            timeout: defaultTimeout,
            headers: {
                'Content-Type': 'application/json',
                Authorization: token,
            },
            data: {
                domains: domainList,
            },
        }
        const result = await axios(options)
        if (result?.data?.code !== 0) {
            return {
                data: result?.data?.data, //如果存在错误的规则时，会返回错误规则列表，正确规则已经添加
            }
        } else {
            return {
                data: [],
            }
        }
    } catch (err) {
        let error = new Error('傲顿添加信任域名出错')
        error.code = 34002
        throw error
    }
}
// 删除信任域名（傲盾白名单）
const delTrustDomain = async (token, domainList) => {
    try {
        const url = new URL(
            global.CONFIG.aodun.delTrustDomain,
            global.CONFIG.aodun.url
        ).href
        const options = {
            method: 'POST',
            url,
            timeout: defaultTimeout,
            headers: {
                'Content-Type': 'application/json',
                Authorization: token,
            },
            data: {
                domains: domainList,
            },
        }
        const result = await axios(options)
        if (result?.data?.code !== 0) {
            let error = new Error(result?.data?.msg)
            error.code = 34003
            throw error
        }
        return true
    } catch (err) {
        let error = new Error('傲顿删除信任域名出错')
        error.code = 34003
        throw error
    }
}

const getTrustDomain = async (token, params) => {
    try {
        const url = new URL(
            global.CONFIG.aodun.getTrustDomain,
            global.CONFIG.aodun.url
        ).href
        let options = {
            method: 'POST',
            url,
            timeout: defaultTimeout,
            headers: {
                'Content-Type': 'application/json',
                Authorization: token,
            },
            data: {},
        }
        if (params.task_id) {
            options.data.task_id = params.task_id
        } else {
            if (params.domain) {
                options.data.domain = params.domain
            }
            if (params.c_name) {
                options.data.c_name = params.c_name
            }
            if (Object.keys(params).includes('icp_status')) {
                options.data.icp_status = params.icp_status
            }
            if (params.c_start_time && params.c_end_time) {
                options.data.c_start_time = params.c_start_time
                options.data.c_end_time = params.c_end_time
            }
        }

        const result = await axios(options)
        if (result?.data?.code !== 0) {
            let error = new Error(result?.data?.msg)
            error.code = 34004
            throw error
        } else {
            return result?.data?.data
        }
    } catch (err) {
        let error = new Error('傲顿查看信任域名出错')
        error.code = 34004
        throw error
    }
}

// 封禁域名
const sealDomain = async (token, domains) => {
    domains = domains.map((record) => {
        // 生成环境才可以封正常域名
        if (global.CONFIG.env !== 'production') {
            return '*' + '.qianjunye.com'
        } else {
            return '*.' + record
        }
    })
    let params = { timeout: defaultTimeout, data: { domains } }
    params.method = 'POST'
    params.url = new URL(
        global.CONFIG.aodun.add_filter_domain,
        global.CONFIG.aodun.url
    ).href
    params.reason = '已备案未接入'
    params.headers = {
        'Content-Type': 'application/json',
        Authorization: token,
    }
    let blockData = await axios(params)
    if (blockData?.data?.code !== 0) {
        throw new Error(blockData?.data?.msg)
    } else {
        return blockData?.data?.data
    }
}

// 封禁域名
const unSealDomain = async (token, domains) => {
    domains = domains.map((record) => {
        // 生成环境才可以封正常域名
        if (global.CONFIG.env !== 'production') {
            return '*' + '.qianjunye.com'
        } else {
            return '*.' + record
        }
    })
    let params = { timeout: defaultTimeout, data: { domains } }
    params.method = 'POST'
    params.headers = {
        'Content-Type': 'application/json',
        Authorization: token,
    }
    params.url = new URL(
        global.CONFIG.aodun.del_filter_domain,
        global.CONFIG.aodun.url
    ).href

    let unBlockData = await axios(params)
    if (unBlockData?.data?.code !== 0) {
        throw new Error(unBlockData?.data?.msg)
    } else {
        return unBlockData?.data?.data
    }
}

// 确定域名封禁状态
const getSealStatus = async (token, options) => {
    if (options.domain) {
        options.domain =
            global.CONFIG.env === 'production'
                ? '*.' + options.domain
                : '*' + '.qianjunye.com'
    }
    let params = { timeout: defaultTimeout, data: { ...options } }
    params.method = 'POST'
    params.headers = {
        'Content-Type': 'application/json',
        Authorization: token,
    }
    params.url = new URL(
        global.CONFIG.aodun.get_filter_domain,
        global.CONFIG.aodun.url
    ).href

    let unBlockData = await axios(params)
    if (unBlockData?.data?.code !== 0) {
        throw new Error(unBlockData?.data?.msg)
    } else {
        return unBlockData?.data?.data
    }
}

//获取傲盾不良信息，已备案未接入查询
const getAodunNoAccessDomainIPList = async (token, params) => {
    try {
        const url = new URL(
            global.CONFIG.aodun.get_unaccess_data,
            global.CONFIG.aodun.url
        ).href
        let options = {
            method: 'POST',
            url,
            timeout: 60 * 1000,
            headers: {
                'Content-Type': 'application/json',
                Authorization: token,
            },
            data: {},
        }
        if (Object.keys(params).includes('task_id')) {
            options.data.task_id = params.task_id
        } else {
            if (Object.keys(params).includes('ip_rang')) {
                options.data.ip_rang = params.ip_rang
            }
            if (Object.keys(params).includes('domain')) {
                options.data.domain = params.domain
            }
            if (Object.keys(params).includes('domain_ip_range')) {
                options.data.domain_ip_range = params.domain_ip_range
            }
            if (params.catch_start_time && params.catch_end_time) {
                options.data.catch_start_time = params.catch_start_time
                options.data.catch_end_time = params.catch_end_time
            }
            if (Object.keys(params).includes('filter_status')) {
                options.data.filter_status = params.filter_status
            }
            if (Object.keys(params).includes('auth_status')) {
                options.data.auth_status = params.auth_status
            }
        }

        const result = await axios(options)
        if (result?.data?.code === -1) {
            let error = new Error(result?.data?.msg)
            error.code = 34004
            throw error
        } else {
            return result?.data
        }
    } catch (err) {
        let error = new Error('已备案未接入查看傲顿不良信息接口出错')
        error.code = 34004
        throw error
    }
}
const getAodunToken = async () => {
    try {
        const url = new URL(
            global.CONFIG.aodun.getToken,
            global.CONFIG.aodun.url
        ).href
        const options = {
            method: 'POST',
            url,
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            params: {
                client_id: global.CONFIG.aodun.client_id,
                grant_type: 'password',
                username: global.CONFIG.aodun.username,
                password: global.CONFIG.aodun.password,
            },
            timeout: defaultTimeout,
        }
        const result = await axios(options)
        if (result?.data?.code !== 0) {
            let error = new Error(result?.data?.mag)
            error.code = 34001
            throw error
        } else {
            return result?.data?.token_type + ' ' + result?.data?.access_token
        }
    } catch (err) {
        let error = new Error('get token error')
        error.code = 34001
        throw error
    }
}

const getDomainICPInGov = async (domain) => {
    try {
        let henganRes
        let method = new GetDomainPublicICP((RetCode, data) => {
            henganRes = {
                RetCode,
                ...data,
            }
        })
        await method.execute({ Domain: domain })

        if (henganRes.RetCode === 0 && henganRes.ICPInfos?.length > 0) {
            return {
                RetCode: 0,
                ICPMainNo: henganRes.ICPInfos[0]?.mainRecordNum,
                ICPWebNo: henganRes.ICPInfos[0]?.webSiteNum,
            }
        } else {
            return {
                Message: '无对应的ICP备案信息',
                RetCode: -1,
            }
        }
    } catch (err) {
        let config = {
            method: 'POST',
            url: global.CONFIG.LongYiAPI.url,
            headers: {
                'Content-Type': 'multipart/form-data',
            },
            data: {
                u: global.CONFIG.LongYiAPI.user,
                p: global.CONFIG.LongYiAPI.password,
                lx: 0,
                k: domain,
            },
        }
        try {
            let res = await axios(config)
            if (res.data.code !== 0) {
                return {
                    Message: res.data.msg || '无对应的ICP备案信息',
                    RetCode: -1,
                }
            } else {
                return {
                    RetCode: 0,
                    ICPMainNo: res.data.result.ztbah,
                    ICPWebNo: res.data.result.wzbah,
                }
            }
        } catch (err) {
            let e = new Error('查看域名在工信部备案信息出错' + err.message)
            e.code = 32605
            throw e
        }
    }
}
module.exports = {
    getToken,
    getAodunToken,
    addTrustDomain,
    delTrustDomain,
    getTrustDomain,
    sealDomain,
    unSealDomain,
    getSealStatus,
    getAodunNoAccessDomainIPList,
    getDomainICPInGov,
}
