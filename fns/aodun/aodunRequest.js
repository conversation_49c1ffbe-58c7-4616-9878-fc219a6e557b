/*
 * @Author: william.qian <EMAIL>
 * @Date: 2022-11-14 21:01:28
 * @LastEditors: william.qian <EMAIL>
 * @LastEditTime: 2022-11-15 12:00:59
 * @FilePath: /newicp/fns/aodun/aodunRequest.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
const logger = require('../../libs/logger')
const uuid = require('uuid/v4')
const request = require('request')
const { promisify } = require('util')
const defaultTimeout = 120 * 1000

/*
 * 傲盾token通用接口 action与token为必填
 */

function aodunRequest(params, cb) {
    let uid = uuid()
    logger.getLogger('api').info('[' + uid + '] Data:' + JSON.stringify(params))
    console.log(params)
    if (!params.url || !params.token) {
        logger.getLogger('error').error('[' + uid + ']NO ACTION OR TOKEN')
        return cb(new Error('NO ACTION OR TOKEN'))
    }

    let configPath =
        params.region === 'hk' ? global.CONFIG.aodunhk : global.CONFIG.aodun

    let timeout = params.timeout || defaultTimeout
    delete params.timeout

    let url = params.url
    delete params.url

    let token = params.token
    delete params.token

    var options = {
        method: 'POST',
        url: url,
        timeout: timeout,
        headers: {
            'Content-Type': 'application/json',
            Authorization: 'Bearer ' + token,
        },
        body: params,
        json: true,
    }
    logger
        .getLogger('api')
        .info('[' + uid + '] Options:' + JSON.stringify(options))

    request(options, function (error, response, body) {
        error
            ? logger.getLogger('api').error('[' + uid + ']', error.message)
            : logger
                  .getLogger('api')
                  .info('[' + uid + ']', JSON.stringify(body))

        cb(error, body)
    })
}

module.exports = aodunRequest
module.exports.rp = promisify(aodunRequest)
