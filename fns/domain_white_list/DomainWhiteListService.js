'use strict'
const { Op } = require('sequelize')
const _ = require('lodash')
const validator = require('validator')
const { parseJSON } = require('../kits')
const {
    DomainWhiteListModel,
    ProxyCompanyWhiteDomainModel,
} = require('../../models')
const moment = require('moment')
/**
 *
 * @param {*} value 域名
 * @param {*} allow_wildcard 是否允许以 *开头
 * @returns
 */
const domainCheck = function (value, allow_wildcard) {
    const hasProtocol = /https?:\/\//.test(value)
    if (hasProtocol) {
        return '域名不能包含http(s)'
    }
    const has3W = /^www\./.test(value)
    if (has3W) {
        return '请输入顶级域名（不包含www）'
    }
    const pass = validator.isFQDN(value, { allow_wildcard })
    if (!pass) {
        return '请输入正确的域名'
    }
    return true
}

/**
 * 检查域名是否符合规范
 * @param {*} domain 域名string或者数组
 * @returns true/false false:说明不符合*.主域名的格式
 */
const checkDomainFormat = (domain) => {
    let pass = true

    if (Array.isArray(domain)) {
        for (let i = 0; i < domain.length; i++) {
            if (domainCheck(domain[i], true) !== true) {
                pass = false
                break
            }
        }
    } else {
        if (domainCheck(domain, true) !== true) {
            pass = false
            return pass
        }
    }
    return pass
}

const countDomainWriteInfo = (db, params) => {
    if (params.Domain) {
        params.Domain = {
            [Op.like]: `%${params.Domain}%`,
        }
    }

    delete params.offset
    delete params.limit

    return db.count({ where: params })
}

/**
 * 获取有效域名
 * (域名有效 and 域名在有效期)  or (域名已接入)
 */
const getEffectiveDomain = async () => {
    let res = new Set()
    let domains = await DomainWhiteListModel.findAll({
        where: {
            [Op.or]: [
                {
                    IsDeleted: 0,
                    ExpiredTime: {
                        [Op.gt]: moment().format('X'),
                    },
                },
                { Connected: 1 },
            ],
        },
        attributes: ['Domain'],
    })

    domains = parseJSON(domains)

    domains.forEach((info) => res.add(info.Domain))
    return Array.from(res)
}

/**
 *
 * @param {*} db
 * @param {*} id  除去该id的查询
 * @param {*} domain 域名列表
 * @returns exist 是否存在相同域名，existAddWriteDomainList：存在的域名列表
 */
const checkDomainExistWrite = async (db, domain, Id) => {
    const domainSearchPromiseArray = []
    if (Array.isArray(domain)) {
        domain.forEach(async (d) => {
            let where = {}
            if (Id) {
                where = {
                    Domain: {
                        [Op.like]: `%${d}%`,
                    },
                    IsDeleted: 0,
                    Id: {
                        [Op.ne]: Id,
                    },
                }
            } else {
                where = { Domain: { [Op.like]: `%${d}%` }, IsDeleted: 0 }
            }
            domainSearchPromiseArray.push(db.findAll({ where }))
        })
    } else {
        let where = {}
        if (Id) {
            where = {
                Domain: { [Op.like]: `%${domain}%` },
                IsDeleted: 0,
                Id: {
                    [Op.ne]: Id,
                },
            }
        } else {
            where = { Domain: { [Op.like]: `%${domain}%` }, IsDeleted: 0 }
        }
        domainSearchPromiseArray.push(db.findAll({ where }))
    }

    let existAddWriteDomainList = []
    await Promise.all(domainSearchPromiseArray).then((result) => {
        for (let i = 0; i < result.length; i++) {
            const res = parseJSON(result[i])
            if (res.length > 0) {
                //此处可能 有ab  包含a的 所以要去除ab的情况
                // tmp为该项 查询时的入参domain
                const tmp = Array.isArray(domain) ? domain[i] : domain
                res.forEach((r) => {
                    if (r.Domain.includes(tmp)) {
                        existAddWriteDomainList.push(tmp)
                    }
                })
            }
        }
    })

    if (existAddWriteDomainList.length > 0) {
        return {
            exist: true,
            domainList: existAddWriteDomainList,
        }
    }
    return {
        exist: false,
    }
}
/**
 * 检查两个数组 先后的变化，新增或者删除数据
 * @param {Array} beforeInfo 改变前数据
 * @param {Array} afterInfo 改变后数据
 * @returns {Object} Add新增,Delete删除
 */
const checkDomainWriteChange = (beforeInfo, afterInfo) => {
    // check before in after
    //difference(a,b)   返回 a 删除 b 中内容之后的数组

    // after 去除 before 中的信息 ，剩下的 就是 新增的
    let Add = _.difference(afterInfo, beforeInfo)
    // before 去除 after 中的信息 ，剩下的  就是 删除的
    let Delete = _.difference(beforeInfo, afterInfo)

    return { Add, Delete }
}

module.exports = {
    checkDomainFormat,
    countDomainWriteInfo,
    getEffectiveDomain,
    checkDomainExistWrite,
    checkDomainWriteChange,
}
