const ucloudinternalapi = require('../libs/ucloudinternalapi')
const producer = require('../libs/producer')
const { parseJSON } = require('../fns/kits')
const _ = require('lodash')
const uuid = require('uuid/v4')

// 通过WebSocket向前端推送消息
async function PostWebSocket({
    EventName,
    Response,
    CompanyId,
    ExpireTime = null,
    ChannelList = [1],
    Pusher = '<EMAIL>',
}) {
    let baseOptions = {
        Backend: 'UWebMsg',
        Action: 'PushMessage',
        Body: { EventName: EventName, Response: Response },
        Receiver: { CompanyId: CompanyId },
        Pusher: Pusher,
        ExpireTime: ExpireTime,
        request_uuid: uuid(),
        ChannelList: ChannelList,
    }

    try {
        console.log(baseOptions)
        let resp = await ucloudinternalapi(baseOptions)
        return Promise.resolve(resp)
    } catch (error) {
        return Promise.reject(error)
    }
}

// 结束H5页面的工作
/**
 * 
 * @param {*} params CompanyId, Timeout, OrderNo, LicenseId, Picture, UUID, Token
 * @returns 
 */
async function FinishH5(params) {
    return Promise.all([
        // 根据订单号与公司Id,把图片写入各负责人
        producer.send({
            type: 'icp',
            topic: 'SavePicture',
            data: params,
        }),

        // 注销二维码
        producer.send({
            type: 'icp',
            topic: 'CancelToken',
            data: params,
        }),

        // 推WebSocket
        producer.send({
            type: 'icp',
            topic: 'PushWebSocketForFinshH5',
            data: params,
        }),
    ])
}

// 订单采集状态
// 未采集 0
// 采集中 1
// 待提交 3
// 已提交 4
// 退回待修改 5

// 更新幕布状态时，执行一次这个Fun,更新订单的幕布状态
async function changeCurtainStatus(
    OrderModel,
    OrderWebModel,
    OrderNo,
    CompanyId
) {
    // 查订单状态
    let orderInfo, orderWebInfo, allZero, allOne
    orderInfo = await OrderModel.findOne({
        where: { OrderNo, CompanyId },
    })

    orderWebInfo = await OrderWebModel.findAll({
        where: { OrderNo },
    })

    orderInfo = parseJSON(orderInfo)
    orderWebInfo = parseJSON(orderWebInfo)

    // 未找到1，则全是0
    allZero = _.findIndex(orderWebInfo, { CurtainStatus: 1 }) === -1
    // 未找到0，则全是1
    allOne = _.findIndex(orderWebInfo, { CurtainStatus: 0 }) === -1

    // 先判断，是否是驳回
    if (
        orderInfo.Status === 2 ||
        orderInfo.Status === 11 ||
        orderInfo.Status === 13
    ) {
        await OrderModel.update(
            { CurtainStatus: 4 },
            {
                where: {
                    OrderNo,
                    CompanyId,
                },
            }
        )

        await OrderWebModel.update(
            { CurtainStatus: 1 },
            {
                where: {
                    OrderNo,
                },
            }
        )
    } else if (!allOne && orderInfo.Status === 4) {
        // 不会是1，状态是打回，退回待修改 5
        // ？为啥状态为1的情况下，会是打回状态？想不起来了
        await OrderModel.update(
            { CurtainStatus: 5 },
            {
                where: {
                    OrderNo,
                    CompanyId,
                },
            }
        )
    } else if (allZero) {
        // 全是0，说明是待采集
        await OrderModel.update(
            { CurtainStatus: 0 },
            {
                where: {
                    OrderNo,
                    CompanyId,
                },
            }
        )
    } else if (!allZero && !allOne) {
        // 不全是0，也不全是1，说明是采集中
        await OrderModel.update(
            { CurtainStatus: 1 },
            {
                where: {
                    OrderNo,
                    CompanyId,
                },
            }
        )
    } else if (
        allOne &&
        (orderInfo.Status === 1 ||
            orderInfo.Status === 4 ||
            orderInfo.Status === 9)
    ) {
        // 待提交
        await OrderModel.update(
            { CurtainStatus: 3 },
            {
                where: {
                    OrderNo,
                    CompanyId,
                },
            }
        )
    }
}

module.exports = {
    PostWebSocket,
    FinishH5,
    changeCurtainStatus,
}
