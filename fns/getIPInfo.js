/*
 * @Author: william.qian <EMAIL>
 * @Date: 2022-06-01 10:29:02
 * @LastEditors: william.qian <EMAIL>
 * @LastEditTime: 2023-11-01 16:23:23
 * @FilePath: /newicp/fns/getIPInfo.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
const uuid = require('uuid/v4')
const request = require('request')
const logger = require('../libs/logger')
const defaultTimeout = 60 * 1000

function getOneIpInfo(options, data) {
    let uid = uuid()
    logger
        .getLogger('api')
        .info('[' + uid + '] Options:' + JSON.stringify(options))
    logger.getLogger('api').info('[' + uid + '] Data:' + JSON.stringify(data))

    if (options.Action === undefined) {
        logger.getLogger('error').error('[' + uid + ']NO ACTION IN OPTIONS')
        return Promise.reject(new Error('NO ACTION IN OPTIONS'))
    }

    let timeout = options.timeout || defaultTimeout

    return new Promise((rs, rj) => {
        request(
            {
                method: options.method || 'POST',
                url: 'http://galaxy.ucloudadmin.com',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: {
                    ...data,
                    TopOrganizationId: options.TopOrganizationId,
                    Action: options.Action,
                    Token: options.Token,
                    User: 'icp',
                },
                json: true,
                timeout,
            },
            (err, res, body) => {
                err
                    ? logger.getLogger('api').error('[' + uid + ']', err.message)
                    : logger
                          .getLogger('api')
                          .info('[' + uid + ']', JSON.stringify(body))
                if (err) {
                    return rj(err)
                }
                return rs({ ...body, ...data })
            }
        )
    })
}

module.exports = function getAllIpInfo(options, ips = []) {
    let promises = []
    ips.forEach((ip) => {
        promises.push(getOneIpInfo(options, { Ip: ip }))
    })
    return Promise.all(promises)
}
