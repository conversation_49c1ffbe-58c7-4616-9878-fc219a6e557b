/*
 * @Date: 2023-08-15 15:47:00
 * @LastEditors: l<PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2023-08-15 15:47:19
 * @FilePath: /newicp/fns/longYiAPIRequest.js
 */

'use strict'
const axiosApi = require('../libs/axiosApi')
const defaultTimeout = 20 * 1000

//查询类型	类型名称
// 0	网站域名
// 1	营业执照（个人或企业）-企业
// 2	居民身份证-个人
// 3	组织机构代码证-事业单位
// 4	事业单位法人证书-事业单位
// 5	部队代号-国防机构
// 6	社会团体法人登记证书-社会团体
// 7	护照-个人
// 9	组织机构代码证-政府机关
// 10	组织机构代码证-社会团体
// 11	台湾居民来往大陆通行证-个人
// 12	组织机构代码证-企业
// 13	统一社会信用代码证书-政府机关
// 14	港澳居民来往内地通行证-个人
// 16	组织机构代码证-民办非企业单位
// 17	民办非企业单位登记证书-民办非企业单位
// 18	组织机构代码证-基金会
// 19	基金会法人登记证书-基金会
// 20	组织机构代码证-律师执业机构
// 21	律师事务所执业许可证-律师执业机构
// 22	外国在华文化中心登记证-外国在华文化中心
// 23	军队单位对外有偿服务许可证-国防机构
// 24	统一社会信用代码证书-群众性团体组织
// 25	宗教活动场所登记证-宗教团体
// 27	外国企业常驻代表机构登记证-企业
// 28	司法鉴定许可证-司法鉴定机构
// 30	外国人永久居留身份证-个人
// 34	外国政府旅游部门常驻代表机构批准登记证-外国在华文化中心
// 35	境外机构证件-境外机构
// 36	社会服务机构登记证书-民办非企业单位
// 37	民办学校办学许可证-民办非企业单位
// 38	医疗机构执业许可证-医疗机构
// 39	公证机构执业证-公证机构
// 40	北京市外国驻华使馆人员子女学校办学许可证-外国在华文化中心
// 41	港澳居民居住证-个人
// 42	台湾居民居住证-个人
// 43	农村集体经济组织登记证-集体经济
// 44	仲裁委员会登记证-仲裁机构
// 101	APP的证件号码和域名 ⭐️
// 102	APP的证件号码和包名 ⭐️
// 103	小程序的证件号码和APPID
// 104	APP的证件号码和APP名称 ⭐️
// 105	小程序的证件号码和小程序名称
// 106	快应用的证件号码和APPID
// 107	快应用的证件号码和快应用名称
// 108	APP的域名 ⭐️
// 111	主体名称 ⭐️

function requestLongYi({
    Domain,
    OrganizerLicenseId,
    OrganizerLicenseType,
    AppInfo = {},
}) {
    return new Promise((resolve, reject) => {
        var options = {
            method: 'POST',
            url: global.CONFIG.LongYiAPI.url,
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            data: {
                u: global.CONFIG.LongYiAPI.user,
                p: global.CONFIG.LongYiAPI.password,
            },
            timeout: defaultTimeout,
        }

        if (Domain) {
            options.data.lx = '0'
            options.data.k = Domain
        } else if (OrganizerLicenseType && OrganizerLicenseId) {
            options.data.lx = OrganizerLicenseType + ''
            options.data.k = OrganizerLicenseId
        } else if (JSON.stringify(AppInfo) !== '{}') {
            if (AppInfo.OrganizerLicenseId && AppInfo.Domain) {
                // App 负责人证件号 和 域名 查询 101
                options.data.lx = 101
                options.data.k = AppInfo.OrganizerLicenseId
                options.data.content = AppInfo.Domain
            } else if (AppInfo.OrganizerLicenseId && AppInfo.PackageName) {
                // App 负责人证件号 和 包名 查询 102
                options.data.lx = 102
                options.data.k = AppInfo.OrganizerLicenseId
                options.data.content = AppInfo.PackageName
            } else if (AppInfo.OrganizerLicenseId && AppInfo.Name) {
                // App 负责人证件号 和 APP名称 查询 104
                options.data.lx = 104
                options.data.k = AppInfo.OrganizerLicenseId
                options.data.content = AppInfo.Name
            } else if (AppInfo.Domain) {
                // App 域名查询 108
                options.data.lx = 108
                options.data.k = AppInfo.Domain
            } else if (AppInfo.PICMainName) {
                // App 主体名称查询 111
                options.data.lx = 111
                options.data.k = AppInfo.PICMainName
            } else {
                return reject(new Error('参数错误'))
            }
        } else {
            return reject(new Error('参数错误'))
        }

        axiosApi(options)
            .then((res) => {
                if (
                    res.status !== 200 ||
                    !res.data ||
                    res.data.code === 999 ||
                    !res.data.result
                ) {
                    return reject(new Error('请求失败'))
                }
                if (res.data.code === 1) {
                    return resolve({
                        ICPInfos: [],
                        Message: '无对应的ICP备案信息',
                        RetCode: 0,
                    })
                } else {
                    let result = {
                        ICPInfos: [
                            {
                                mainRecordNum: res.data.result.ztbah,
                                webSiteNum: res.data.result.wzbah,
                            },
                        ],
                        RetCode: 0,
                    }
                    return resolve(result)
                }
            })
            .catch((err) => {
                return reject(err)
            })
    })
}

module.exports = {
    requestLongYi,
}
