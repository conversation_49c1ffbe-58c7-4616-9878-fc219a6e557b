const _ = require('lodash')
const { Op } = require('sequelize')
const { parseJSON } = require('../fns/kits')
const modelService = {}

modelService.getMsgFromWebService = async (From, ICPWeb, params) => {
    let mainId = []
    let webQuery = {}
    //query初始化
    if (params.Domain) {
        // Domain 搜索带着引号一起搜，以免查到的量太大,   那就是固定是这个域名？？？？？？
        webQuery.domain = { [Op.like]: `%${params.Domain}%` }
    }
    if (params.ICPWebNo) {
        webQuery.ICPWebNo = params.ICPWebNo
    }
    if (params.IP) {
        webQuery.ip = { [Op.like]: `%${params.IP}%` }
    }
    //判断来源是console还是admin
    if (From === 'console') {
        //客户侧不需要查询删除的web
        webQuery.IsDeleted = 0
        webQuery.Status = { [Op.notIn]: [1, 2] }
    }

    //根据条件查找mainId
    const webResults = await getMsgFromWebModel(ICPWeb, webQuery, ['MainId'])

    if (webResults.length > 0) {
        //设置主体id
        mainId = _.uniq(webResults.map((webResult) => webResult.MainId))
    }
    //如果mainId查找到或者传参有则返回 查找到 或者传参的值，否则 返回null
    return mainId
}

async function getMsgFromWebModel(
    ICPWeb,
    webQuery,
    attributes,
    offset = 0,
    limit = 50
) {
    //查询条件
    let query = {
        where: webQuery,
    }
    if (offset && limit) {
        query.offset = offset
        query.limit = limit
    }
    if (attributes.length !== 0) {
        //获取字段
        query.attributes = attributes
    }
    let results = await ICPWeb.findAll(query)

    return parseJSON(results)
}

modelService.getIcpAndWebMsgService = async (
    From = 'console',
    ICPModel,
    ICPWeb,
    generalJson,
    offset = 0,
    limit = 50,
    LogModel
) => {
    const { count, rows: icps } = await getListAndCountFromIcpModel(
        ICPModel,
        generalJson,
        [],
        offset,
        limit
    )
    if (count === 0) {
        //没有数据
        return {
            TotalCount: 0,
            ICP: [],
        }
    }

    //get所有的主体id
    const mainIds = icps.map((icp) => icp.Id)
    //根据主体信息 获取该主体下的网站信息
    let getAllwebQuery = {
        MainId: { [Op.in]: mainIds },
    }

    //客户侧不展示网站已注销和接入已注销和已删除的网站
    if (From === 'console') {
        // Website State: 1：网站已注销、2：接入已注销
        getAllwebQuery.Status = { [Op.notIn]: [1, 2] }
        getAllwebQuery.IsDeleted = 0
    }

    // 根据 主体信息，获取该主体下的所有 Website 信息
    let promiseList = []
    promiseList.push(
        getMsgFromWebModel(ICPWeb, getAllwebQuery, [], offset, limit)
    )

    //只有admin可以看log
    if (From === 'admin') {
        //根据主体id查询log
        let logQuery = {
            ICPId: { [Op.in]: mainIds },
        }
        promiseList.push(getMsgFromLogModel(LogModel, logQuery, []))
    }

    //获取主体下的网站列表和主体的log
    let [websites, logs] = await Promise.all(promiseList)
    // 确定主体是否可以执行操作
    // 确定主体下网站是否可以执行操作
    // o.Status > 4，网站接入状态异常
    icps.map((icp) => {
        icp.CanOperated =
            icp.Status === 0 &&
            _.findIndex(websites, function (o) {
                return o.Status > 4 && o.MainId === icp.Id
            }) === -1

        if (From === 'console') {
            //只有客户侧需要CanModify字段
            icp.CanModify =
                icp.Status === 0 &&
                _.findIndex(websites, function (o) {
                    return (
                        (o.Status === 9 ||
                            o.Status === 0 ||
                            o.Status === 10 ||
                            o.Status === 19) &&
                        o.MainId === icp.Id
                    )
                }) !== -1
        }
        return icp
    })

    // 网站状态判断
    websites.map((website) => {
        website.CanOperated =
            website.Status === 0 &&
            _.find(icps, { Id: website.MainId }).Status === 0
        // (_.indexOf([10, 1, 2, 5, 6], website.Status) === -1)  此不得在已注销，接应注销中，网站注销中执行操作
        if (From === 'console') {
            //只有客户侧返回ConnectCanModify，WebsiteCanModify字段
            website.ConnectCanModify =
                _.indexOf([10, 1, 2, 5, 6, 19], website.Status) === -1 &&
                _.find(icps, { Id: website.MainId }).Status !== 1 &&
                _.find(icps, { Id: website.MainId }).Status !== 4 &&
                _.find(icps, { Id: website.MainId }).Status !== 7

            website.WebsiteCanModify =
                _.indexOf([9, 1, 2, 5, 6, 19], website.Status) === -1 &&
                _.find(icps, { Id: website.MainId }).Status !== 1 &&
                _.find(icps, { Id: website.MainId }).Status !== 4 &&
                _.find(icps, { Id: website.MainId }).Status !== 7
        }
    })

    // 删除网站中空的前置审批照，与域名证书照片
    for (const website of websites) {
        // 网站中的域名Map
        website.Domain.map((domain) => {
            if (domain.CerificationPicture && domain.CerificationPicture[0] === '1.jpg') {
                domain.CerificationPicture = []
            }
        })
        // 网站中的前置审批Map
        if (_.isArray(website.PreAppoval && website.PreAppoval.length !== 0)) {
            website.PreAppoval.map((preAppoval) => {
                if (
                    preAppoval.Picture[0] === 'x.jpg' ||
                    preAppoval.Picture[0] === '1.jpg'
                ) {
                    preAppoval.Picture = []
                }
            })
        }
    }
    return {
        TotalCount: count,
        ICP: icps.map((icp) => {
            icp.Website = websites.filter(
                (website) => website.MainId === icp.Id
            )
            //只有admin可以看log，如果logs是undefine说明是console
            if (logs) {
                icp.Logs = logs.filter((log) => log.ICPId === icp.Id)
            }
            return icp
        }),
    }
}

async function getListAndCountFromIcpModel(
    ICPModel,
    icpQuery,
    attributes,
    offset = 0,
    limit = 50,
    order = [['id', 'desc']]
) {
    let query = {
        where: icpQuery,
        offset,
        limit,
        order,
    }

    if (attributes.length !== 0) {
        query.attributes = attributes
    }

    const result = await ICPModel.findAndCountAll(query)
    return parseJSON(result)
}

async function getMsgFromLogModel(LogModel, logQuery, attributes) {
    let query = {
        where: logQuery,
    }
    if (attributes.length !== 0) {
        query.attributes = attributes
    }
    const result = await LogModel.findAll(query)
    return parseJSON(result)
}
module.exports = modelService
