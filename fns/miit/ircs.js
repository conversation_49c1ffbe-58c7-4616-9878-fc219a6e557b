/*
 * @Author: william.qian <EMAIL>
 * @Date: 2023-08-07 11:20:54
 * @LastEditors: william.qian <EMAIL>
 * @LastEditTime: 2023-08-28 17:04:41
 * @FilePath: /newicp/fns/miit/ircs.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
const soap = require('soap')
const https = require('https')

async function dynamicResourceCommand({
    ircsId,
    randVal,
    pwdHash,
    command,
    commandHash,
    commandType,
    commandSequence,
    encryptAlgorithm,
    hashAlgorithm,
    compressionFormat,
    commandVersion,
}) {
    const url =
        'https://**************:8443/IRCSManager/DynamicResourceWebService/dynamic_resource_command?wsdl'
    const args = {
        ircsId,
        randVal,
        pwdHash,
        command,
        commandHash,
        commandType,
        commandSequence,
        encryptAlgorithm,
        hashAlgorithm,
        compressionFormat,
        commandVersion,
    }
  const options = {
      wsdl_options: {
          secureProtocol: 'TLSv1_1_method',
      },
  }
    const client = await soap.createClientAsync(url, options)

    return new Promise((resolve, reject) => {
        client.dynamic_resource_command.dynamic_resource_commandHttpPort.dynamic_resource_command(
            args,
            function (err, result) {
                if (err) {
                    reject(err)
                } else {
                    resolve(result)
                }
            },
            { time: true }
        )
    })
}

module.exports = {
    dynamicResourceCommand,
}
