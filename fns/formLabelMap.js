'use strict'
const formLabelMap = {}

formLabelMap.mainFormLabelMap = {
	AreaId: '主办单位所属区域',
	OrganizerType: '主办单位性质',
	OrganizerName: '主办单位名称',
	UnitCertificateExpDate: '主办单位证件有效期',
	OrganizerAddress: '主办单位通信地址',
	OrganizerLicenseType: '主办单位证件类型',
	OrganizerLicenseId: '主办单位代码',
	OrganizerLicenseArea: '主办单位证件所在地',
	PICMainName: '负责人姓名',
	PICMainOfficePhone: '办公室号码',
	PICMainOfficePhonePre: '办公室号码前缀',
	PICMainMobile: '负责人手机号',
	EmergencyPhone: '应急联系电话1111',
	PICMainEmail: '负责人邮箱',
	PICMainQQ: 'QQ',
	PICMainLicenseType: '负责人证件类型',
	PICMainLicenseId: '负责人证件号码',
	OrganizerLicensePicture: '主办单位证件',
	OrganizerResidencePermitPicture: '主办单位暂住证/居住证扫描件',
	PICMainLicensePicture: '主体负责人证件',
	OtherPicture: '其它',
	Remark: '备注',
	ICPMainNo: '主体备案号',
	ICPMainNo: '主体备案号',
	UnitSuperior: '上级主管单位',
	UnitCertificateExpDate: '主办单位有效期',
	ICPMainPassword: '备案密码'
}

formLabelMap.websiteFormLabelMap = {
	ICPWebNo: '网站备案号',
	Name: '网站名称',
	MainDomain: '网站域名',
	MainDomainDomain: '网站域名',
	MainDomainCerificationPicture: '网站域名证书',
	OtherDomains: '其他域名',
	IP: 'IP地址',
	Url: '网站首页地址',
	Language: '网站语言',
	ServiceType: '网站服务内容',
	InternetServiceType: '互联网服务类型',
	ServiceContent: '网站服务类型',
	PreAppoval: '前置或专项内容审批',
	PICName: '负责人姓名',
	Phone: '办公室电话',
	PhonePre: '电话前缀',
	Mobile: '手机号码',
	EmergencyPhone: '应急联系电话',
	Email: '电子邮箱地址',
	QQ: 'QQ',
	LicenseType: '负责人证件类型',
	LicenseId: '负责人证件号码',
	LicensePicture: '负责人证件',
	CurtainPicture: '幕布照片',
	AuthVerificationPicture: '网站真实性核验单',
	OtherPicture: '其它',
	Remark: '备注'
}

formLabelMap.getFormLabel = (name, type) => {
	let label
	if (type === 'main') {
		label = formLabelMap.mainFormLabelMap[name]
	} else if (type === 'website') {
		label = formLabelMap.websiteFormLabelMap[name]
	}
	return label || name
}

module.exports = formLabelMap
