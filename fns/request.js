/*
 * @Author: william.qian <EMAIL>
 * @Date: 2022-02-16 11:11:48
 * @LastEditors: william.qian <EMAIL>
 * @LastEditTime: 2023-05-16 11:19:23
 * @FilePath: /newicp/fns/request.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
const logger = require('../libs/logger')
const uuid = require('uuid/v4')
const request = require('request')
const { promisify } = require('util')
const defaultTimeout = 5 * 1000

// 基础request,封装日志功能支持复用
function requestCallBack(params, cb) {
    let uid = uuid()

    if (!params) {
        logger.getLogger('error').error('[' + uid + ']NO PARAMS')
        return cb(new Error('NO PARAMS'))
    }

    logger
        .getLogger('api')
        .info('[' + uid + '] Options:' + JSON.stringify(params))
    let timeout = params.timeout || defaultTimeout
    //headers必须加
    var options = {
        method: params.method || 'POST',
        url: params.url,
        timeout: timeout,
        headers: {
            'Content-Type': 'application/json',
        },
        json: true,
    }
    delete params.timeout
    delete params.method
    delete params.url

    options.body = params
    logger
        .getLogger('api')
        .info('[' + uid + '] Options:' + JSON.stringify(options.body))

    request(options, function (error, response, body) {
        error
            ? logger.getLogger('api').error('[' + uid + ']', error.message)
            : logger
                  .getLogger('api')
                  .info('[' + uid + ']', JSON.stringify(body))
        cb(error, body)
    })
}

module.exports = requestCallBack
module.exports.requestAsync = promisify(requestCallBack)
