const execShPromise = require('exec-sh').promise
const path = require('path')
const mineType = require('mime-types')
const fs = require('fs')
const request = require('request')
const _ = require('lodash')
// 文件处理函数，下载，保存，转码
async function runConvert(convertCommamder) {
    let out

    try {
        out = await execShPromise(convertCommamder, true)
    } catch (e) {
        return Promise.reject(e)
    }

    return Promise.resolve(out)
}

async function convertPicture(
    url,
    metaInfo,
    targetMinLen,
    sizeRange,
    pictureType = 'Common' //图片类型，将影响分辨率
) {
    // 获取文件名
    let fileName =
        Math.round(new Date().getTime() / 1000) + new URL(url).pathname
    fileName = fileName.replace('/', '')
    let destfile = path.join(global.CONFIG.logPath, 'tmpFile', fileName)
    // jpg会比png更小,做强格式转换
    destfile = destfile.replace('.png', '.jpg')
    let px = ''
    // 确定最长边
    // 取最长边，最长边为790，另一边，等比例压缩

    const IdCardPx = { maxLength: 1285, minLength: 725 }
    const LicensePx = { maxLength: 1505, minLength: 1105 }

    // /usr/local/bin/ffmpeg
    let currsize = 0
    let searchedMin = []
    let searchedMax = []
    let minSize = sizeRange.minSize * 1000
    let maxSize = sizeRange.maxSize * 1000
    do {
        //  "scale='min(1510,iw)':'min(1110,ih)'"
        if (metaInfo.Width < metaInfo.Height) {
            if (pictureType === 'IdCard') {
                px = ` -vf "scale='min(${IdCardPx.minLength},iw)':'min(${IdCardPx.maxLength},ih)'"`
            } else if (pictureType === 'License') {
                px = ` -vf "scale='min(${LicensePx.minLength},iw)':'min(${LicensePx.maxLength},ih)'"`
            } else {
                px = ` -vf scale=-2:${targetMinLen}`
            }
        } else {
            if (pictureType === 'IdCard') {
                px = ` -vf "scale='min(${IdCardPx.maxLength},iw)':'min(${IdCardPx.minLength},ih)'"`
            } else if (pictureType === 'License') {
                px = ` -vf "scale='min(${LicensePx.maxLength},iw)':'min(${LicensePx.minLength},ih)'"`
            } else {
                px = ` -vf scale=${targetMinLen}:-2`
            }
        }
        // let convertCommamder = `ffmpeg -threads 6 -i '${url}' ${px}  -fs 0.15MB ${destfile} -y  && ls -l ${destfile} | awk '{print $5}'`
        let convertCommamder = `ffmpeg -threads 6 -i '${url}' ${px}  -sws_flags bilinear  ${destfile} -y  && ls -l ${destfile} | awk '{print $5}'`

        let result = await runConvert(convertCommamder)
        currsize = parseInt(result.stdout.replace('\n', ''))
        // 如果不是通用的，就不再做处理
        if (pictureType !== 'Common') {
            // 确定大小
            if (currsize < minSize) {
                await runConvert(
                    `gm convert ${destfile}  -resize 120% ${destfile}`
                )
            }
            break
        }
        // currsize单位为字节

        if (currsize < minSize) {
            searchedMin.push(targetMinLen)
            // 控制如果小于 则加权 刚开始+100 如果
            if (_.min(searchedMax) >= targetMinLen + 100) {
                // 如果比它大100的像素超size了
                // 所以再加像素时，步长 要调整短一些
                if (_.min(searchedMax) >= targetMinLen + 20) {
                    // 此时说明 100 和200kb之间相差的像素极小 就返回当前接近最大的 不再做处理
                    break
                } else {
                    targetMinLen += 20
                }
            } else {
                targetMinLen += 100
            }
        }
        if (currsize > maxSize) {
            searchedMax.push(targetMinLen)
            // 说明小于最小值的测试过了
            if (_.max(searchedMin) >= targetMinLen - 100) {
                // 说明这个已经测试过了 是小于目标size的 所以减的步长 要缩短
                if (_.max(searchedMin) >= targetMinLen - 20) {
                    // 再小20像素 就变成小于了 没必要再细化了 直接返回
                    break
                } else {
                    targetMinLen = targetMinLen - 20
                }
            } else {
                targetMinLen = targetMinLen - 100
            }
        }
    } while (currsize < minSize || currsize > maxSize)

    let destfileBase64 = fileToBase64(destfile)
    // 删除临时文件
    fs.unlinkSync(destfile)
    return 'data:' + mineType.lookup(destfile) + ';base64,' + destfileBase64
}

function fileToBase64(filePath) {
    let data = fs.readFileSync(filePath)
    data = new Buffer(data).toString('base64')
    return data
}

/*
 *	文件保存，后得到保存地址与buffer
 *
 * @params params   URL
 *
 */
function saveFile(fileUrl) {
    return new Promise((rs, rj) => {
        let thisbase64, thisbuffer

        // 	生成目标文件名
        const urlObj = new URL(fileUrl)
        let sourceFilePath = urlObj.pathname
        sourceFilePath = path.join(
            global.CONFIG.logPath,
            'tmpFile',
            sourceFilePath
        )

        // 使用管道最快速保存文件，同时在下载完成时得到文件Buffer,供后续处理使用
        let writeStream = fs.createWriteStream(sourceFilePath)

        var options = {
            url: fileUrl,
            encoding: null,
        }

        request(options, function (error, response, body) {
            thisbuffer = new Buffer(body)
            thisbase64 = thisbuffer.toString('base64')
        })
            .on('error', function (err) {
                return rj(err)
            })
            .pipe(writeStream)

        //读取文件发生错误事件
        writeStream.on('error', (err) => {
            return rj(err)
        })

        //文件已经就写入完成事件
        writeStream.on('finish', () => {
            return rs({
                sourceFilePath,
                sourceBase64:
                    'data:' +
                    mineType.lookup(sourceFilePath) +
                    ';base64,' +
                    thisbase64,
                sourceBuffer: thisbuffer,
            })
        })
    })
}

module.exports = { runConvert, convertPicture, saveFile }
