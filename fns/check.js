const _ = require('underscore')
const {
	mainFormLabelMap,
	websiteFormLabelMap
} = require('./formLabelMap')
const {
	splitAreaId
} = require('./area')
const {
	ruleTest,
	ruleCheck
} = require()
const validator = require('validator')
// const Tyshyxdm = require('./validateRule')
const QUIT_TAG = 'TELL_SERIES_TO_QUIT_WITH_OUT_EXEC_REMAIN_FUNS'

const isNotEmpty = value => {
	if (_.isNumber(value) || _.isBoolean(value)) return true
	return !_.isEmpty(value)
}

const notRequiredCheck = value => {
	if (value === '' || value === undefined || value === null || (_.isArray(value) && value.length === 0)) {
		return QUIT_TAG
	} else {
		return true
	}
}


const requiredCheckForMain = key => value => {
	return isNotEmpty(value) ? true : `${mainFormLabelMap[key] || '该字段'}不能为空！`
}
const requiredTypeCheckForMain = key => value => {
	return value != 0 && isNotEmpty(value) ? true : `${mainFormLabelMap[key] || '该字段'}不能为空！`
}
const requiredCheckForWebsite = key => value => {
	return isNotEmpty(value) ? true : `${websiteFormLabelMap[key] || '该字段'}不能为空！`
}
const requiredTypeCheckForWebsite = key => value => {
	return value != 0 && isNotEmpty(value) ? true : `${websiteFormLabelMap[key] || '该字段'}不能为空！`
}

const series = (...funs) => (...args) => {
	const l = funs.length
	let i = 0
	for (; i < l; i++) {
		const fun = funs[i]
		const result = fun(...args)
		if (result === QUIT_TAG) {
			return true
		}
		if (result !== true) {
			return result
		}
	}
	return true
}

const charCheck = value => {
	const validChar = /[\s]{1}/.exec(value)
	if (validChar) {
		return '不能包含以下特殊字符："空格"'
	}
	return true
}

const numberCheck = value => {
	const hasNonNumber = /\D+/.test(value)
	if (hasNonNumber) {
		return '只能输入数字'
	}
	return true
}

const phoneCheck = value => {
	const pass = /^\d{7,8}$/.test(value)
	if (!pass) {
		return '请输入正确的电话号码'
	}
	return true
}

const mobileCheck = value => {
	const pass = /^\d{11}$/.test(value)
	if (!pass) {
		return '请输入正确的手机号码'
	}
	return true
}

const emailCheck = value => {
	const pass = validator.isEmail(value)
	if (!pass) {
		return '请输入正确的邮箱号码'
	}
	return true
}

const domainCheck = value => {
	const hasProtocol = /https?:\/\//.test(value)
	if (hasProtocol) {
		return '域名不能包含http(s)'
	}
	const has3W = /^www\./.test(value)
	if (has3W) {
		return '请输入顶级域名（不包含www）'
	}
	const pass = validator.isFQDN(value)
	if (!pass) {
		return '请输入正确的域名'
	}
	return true
}

const urlCheck = (value = '') => {
	const hasProtocol = /https?:\/\//.test(value)
	if (hasProtocol) {
		return '地址不能包含http(s)'
	}
	const urls = value.split(';')
	for (let i = 0, l = urls.length; i < l; i++) {
		const pass = /^(?:[\w\u4e00-\u9fa5]+[\w\u4e00-\u9fa5-]*\.)+(?:[\w\u4e00-\u9fa5]{2,7}){1}(\/.*)*(\?{1}.*)*$/.test(
			urls[i]
		)
		if (!pass) {
			return '请输入正确的url地址'
		}
	}
	return true
}

const qqCheck = value => {
	const pass = /^[1-9]{1}\d{4,20}$/.test(value)
	if (!pass) {
		return '请输入正确的QQ号'
	}
	return true
}

// const idCheck = (value, type) => {
// 	if (type === 2) {
// 		const pass = /^\d{17}[\dxX]$/.test(value)
// 		if (!pass) {
// 			return '请输入正确的身份证号码'
// 		}
// 		return pass
// 	}
// 	return charCheck(value)
// }
/*
 根据〖中华人民共和国国家标准 GB 11643-1999〗中有关公民身份号码的规定,公民身份号码是特征组合码,由十七位数字本体码和一位数字校验码组成.排列顺序从左至右依次为:六位数字地址码,八位数字出生日期码,三位数字顺序码和一位数字校验码.
 地址码表示编码对象常住户口所在县(市,旗,区)的行政区划代码.
 出生日期码表示编码对象出生的年,月,日,其中年份用四位数字表示,年,月,日之间不用分隔符.
 顺序码表示同一地址码所标识的区域范围内,对同年,月,日出生的人员编定的顺序号.顺序码的奇数分给男性,偶数分给女性.
 校验码是根据前面十七位数字码,按照ISO 7064:1983.MOD 11-2校验码计算出来的检验码.

 出生日期计算方法.
 15位的身份证编码首先把出生年扩展为4位,简单的就是增加一个19或18,这样就包含了所有1800-1999年出生的人;
 2000年后出生的肯定都是18位的了没有这个烦恼,至于1800年前出生的,那啥那时应该还没身份证号这个东东,⊙﹏⊙b汗...
 下面是正则表达式:
 出生日期1800-2099  (18|19|20)?\d{2}(0[1-9]|1[12])(0[1-9]|[12]\d|3[01])
 身份证正则表达式 /^\d{6}(18|19|20)?\d{2}(0[1-9]|1[12])(0[1-9]|[12]\d|3[01])\d{3}(\d|X)$/i
 15位校验规则 6位地址编码+6位出生日期+3位顺序号
 18位校验规则 6位地址编码+8位出生日期+3位顺序号+1位校验位

 校验位规则     公式:∑(ai×Wi)(mod 11)……………………………………(1)
 公式(1)中:
 i----表示号码字符从由至左包括校验码在内的位置序号;
 ai----表示第i位置上的号码字符值;
 Wi----示第i位置上的加权因子,其数值依据公式Wi=2^(n-1）(mod 11)计算得出.
 i 18 17 16 15 14 13 12 11 10 9 8 7 6 5 4 3 2 1
 Wi 7 9 10 5 8 4 2 1 6 3 7 9 10 5 8 4 2 1

 */
//身份证号合法性验证
//支持15位和18位身份证号
//支持地址编码,出生日期,校验位验证
const idCheck = (value, type) => {
	console.log('check', value, type)
	// 如果是身份证
	let connt = {}
	if (type === 2) {
		var city = {
			11: '北京',
			12: '天津',
			13: '河北',
			14: '山西',
			15: '内蒙古',
			21: '辽宁',
			22: '吉林',
			23: '黑龙江 ',
			31: '上海',
			32: '江苏',
			33: '浙江',
			34: '安徽',
			35: '福建',
			36: '江西',
			37: '山东',
			41: '河南',
			42: '湖北 ',
			43: '湖南',
			44: '广东',
			45: '广西',
			46: '海南',
			50: '重庆',
			51: '四川',
			52: '贵州',
			53: '云南',
			54: '西藏 ',
			61: '陕西',
			62: '甘肃',
			63: '青海',
			64: '宁夏',
			65: '新疆',
			71: '台湾',
			81: '香港',
			82: '澳门',
			91: '国外 '
		}
		var tip = ''
		var pass = true

		if (!value || !/^\d{6}(18|19|20)?\d{2}(0[1-9]|1[012])(0[1-9]|[12]\d|3[01])\d{3}(\d|X)$/i.test(value)) {
			tip = '身份证号格式错误'
			pass = false
		} else if (!city[value.substr(0, 2)]) {
			tip = '地址编码错误'
			pass = false
		} else {
			//18位身份证需要验证最后一位校验位
			if (value.length == 18) {
				value = value.split('')
				//∑(ai×Wi)(mod 11)
				//加权因子
				var factor = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2]
				//校验位
				var parity = [1, 0, 'X', 9, 8, 7, 6, 5, 4, 3, 2]
				var sum = 0
				var ai = 0
				var wi = 0
				for (var i = 0; i < 17; i++) {
					ai = value[i]
					wi = factor[i]
					sum += ai * wi
				}
				var last = parity[sum % 11]
				if (last != value[17]) {
					tip = '证件号录入错误'
					pass = false
				}
			}
		}
		//if(!pass) alert(tip);
		if (pass === true) {
			return true
		} else {
			return tip
		}
	} else if (_.indexOf([1, 4, 6, 13, 17, 19, 21, 22, 23, 24, 25, 27, 28, 34, 35, 36, 37, 38, 39, 40], type) !== -1) {
		let check
		if (value.length <= 15) {
			check = new Regex_BusinessLicenseNumber()
			if (check.isBusinesslicense(value) === false) {
				return '请输入正确的主办单位证件号码'
			}
		} else {
			check = new Tyshyxdm()
			if (!check.verify(value)) {
				return '请输入正确的主办单位证件号码'
			}
		}

		return true
	}

	return charCheck(value)
}


const ipCheck = (value = '') => {
	let ips
	if (_.isString(value)) {
		ips = value.split(';')
	} else {
		ips = value
	}

	for (let i = 0, l = ips.length; i < l; i++) {
		const pass = /^((25[0-5]|2[0-4]\d|1?\d?\d)\.){3}(25[0-5]|2[0-4]\d|1?\d?\d)$/.test(ips[i])
		if (!pass) {
			return '请按说明填写正确的IP格式'
		}
	}
	return true
}

const allwaysPass = () => true

const typeCheckForWebsite = (key, type) => value => {
	if (!_[`is${type}`](value)) {
		return `${websiteFormLabelMap[key]}必须为${type}`
	}
	return true
}

const hasLastOneMainDomain = domains => {
	const mainDomain = _.filter(domains, domain => domain.IsMain)
	if (mainDomain.length !== 1) {
		return '必须且只能包含一个主域名'
	}
	return true
}

const domainFormatCheck = domains => {
	for (let i = 0, l = domains.length; i < l; i++) {
		const domain = domains[i]
		if (!_.isObject(domain)) {
			return '域名格式错误'
		}
		const domainCheckResult = domainCheck(domain.Domain)
		const pictureCheckResult = requiredCheckForWebsite('CerificationPicture')(domain.CerificationPicture)
		if (domainCheckResult !== true) {
			return domainCheckResult
		}
		if (pictureCheckResult !== true) {
			return pictureCheckResult
		}
	}
	return true
}

const mainCheckMap = {
	AreaId: value => {
		let {
			city,
			province
		} = splitAreaId(value)
		if (!province) {
			return `请选择${mainFormLabelMap['AreaId']}`
		}
		// 可以只选省
		// if (!city) {
		// 	return '请选择城市'
		// }
		return true
	},
	OrganizerType: ((value, order) =>
		ruleCheck('OLD_ORDER_NOT_REQUIRED', value, order, 'OrganizerType')),
	OrganizerName: series(((value, order) =>
		ruleCheck('OLD_ORDER_NOT_REQUIRED', value, order, 'OrganizerName')), charCheck),
	UnitCertificateExpDate: series(requiredCheckForMain('UnitCertificateExpDate'), charCheck),
	OrganizerAddress: series(requiredCheckForMain('OrganizerAddress'), charCheck),
	OrganizerLicenseType: series((value, order) =>
		ruleCheck('MUST_USE_LOCAL_ID_CARD', value, order)
	),
	OrganizerLicenseId: series((value, order) =>
		idCheck(value, order['OrganizerLicenseType'])
	),
	OrganizerLicenseArea: series(requiredCheckForMain('OrganizerLicenseArea'), charCheck),
	PICMainName: series((value, order) =>
		ruleCheck('OLD_ORDER_NOT_REQUIRED', value, order, 'PICMainName')),
	PICMainMobile: series(requiredCheckForMain('PICMainMobile'), mobileCheck),
	EmergencyPhone: series(requiredCheckForMain('EmergencyPhone'), charCheck, (value, order) =>
		ruleCheck('IF_MAIN_EMERGENCYPHONE_SAME_MOBILE', value, order)
	),
	PICMainEmail: series(requiredCheckForMain('PICMainEmail'), emailCheck),
	PICMainQQ: series(notRequiredCheck, qqCheck),
	PICMainLicenseType: series(((value, order) =>
		ruleCheck('OLD_ORDER_NOT_REQUIRED', value, order, 'PICMainLicenseType')), (value, order) =>
		ruleCheck('MUST_USE_LOCAL_ID_CARD', value, order)
	),
	PICMainLicenseId: series(((value, order) =>
		ruleCheck('OLD_ORDER_NOT_REQUIRED', value, order, 'PICMainLicenseId')), (value, order) =>
		idCheck(value, order['PICMainLicenseType'])
	),
	OrganizerLicensePicture: requiredCheckForMain('OrganizerLicensePicture'),
	UnitSuperior: requiredCheckForMain('UnitSuperior'),
	OrganizerResidencePermitPicture: allwaysPass,
	PICMainLicensePicture: requiredCheckForMain('PICMainLicensePicture'),
	OtherPicture: allwaysPass,
	Remark: series(
		(value, order) =>
			ruleTest('REQUIRES_REMARK_PROVINCES', order) ? requiredCheckForMain('Remark')(value) : true),
	ICPMainNo: series(((value, order) =>
		ruleCheck('OLD_ORDER_NOT_REQUIRED', value, order, 'ICPMainNo')), charCheck),
	ICPMainPassword: requiredCheckForMain('ICPMainPassword'),
	SubmitReexamineType: (value, order) => {
		if (order.Status >= 7 && (!value || value == 0)) {
			return '请选择复审方案'
		}
		return true
	},
	Website: series(requiredCheckForMain('Website'), (value, order) => {
		if (ruleTest('SOME_AREA')) {
			return ruleTest('THE_WEB_NUMBER_OF_AREA', value, order) && ruleTest('THE_WEB_NUMBER_OF_ORDER_TYPE', value, order)
		}
		return ruleTest('THE_WEB_NUMBER_OF_ORDER_TYPE', value, order)
	})
}

const websiteCheckMap = {
	ICPWebNo: series(requiredCheckForWebsite('ICPWebNo'), charCheck),
	Name: series(requiredCheckForWebsite('Name'), charCheck, (value, website, order) =>
		ruleCheck('WEBSITE_NAME_CAN_NOT_CONTAIN_KEYWORDS', value, order)
	),
	MainDomainDomain: series(requiredCheckForWebsite('MainDomainDomain'), domainCheck),
	MainDomainCerificationPicture: requiredCheckForWebsite('MainDomainCerificationPicture'),
	OtherDomains: series(notRequiredCheck, domainFormatCheck, (value, website, order) =>
		ruleCheck('CANNOT_OVER_5_DOMAIN', value, order, website)
	),
	Domain: series(
		requiredCheckForWebsite('Domain'),
		typeCheckForWebsite('Domain', 'Array'),
		domainFormatCheck,
		(value, website, order) => order.Type + '' !== '7' ? hasLastOneMainDomain(value) : true,	//如果是变更，允许没有主域名，其它更建议下线主域名的概念
		(value, website, order) => ruleCheck('CANNOT_OVER_5_DOMAIN_FOR_DOMAIN', value, order, website),
		(value, website, order) => ruleCheck('CAN_ONLY_HAVE_ONE_DOMAIN_FOR_DOMAIN', value, order, website)
	),
	IP: series(
		(value, website, order) => ruleCheck('CAN_ONLY_HAVE_ONE_IP', value, order),
		(value, website) => {
			return website.AuthenticationCode === undefined ? ipCheck(value) : true
		}
	),
	Url: series(requiredCheckForWebsite('Url'),
		urlCheck,
		(value, website, order) => {
			return ruleCheck('URL_IN_DOMAIN', value, website, order)
		}),
	Language: requiredCheckForWebsite('Language'),
	ServiceType: requiredCheckForWebsite('ServiceType'),
	PreAppoval: series(notRequiredCheck, typeCheckForWebsite('PreAppoval', 'Array'), value => {
		let errorIndex = _.findIndex(
			value,
			_preAppoval => !_preAppoval.Type || !_preAppoval.No || _.isEmpty(_preAppoval.Picture)
		)
		if (errorIndex > -1) {
			return `请检查${websiteFormLabelMap['PreAppoval']}`
		}
		return true
	}),
	PICName: series(requiredCheckForWebsite('PICName'), charCheck),
	Phone: series(
		(value, website, order) =>
			ruleTest('DO_NOT_NEED_PHONE', order) ? notRequiredCheck(value) : notRequiredCheck(value),
		(value, order) => {
			return value.length >= 11 ? mobileCheck(value) : phoneCheck(value)
		}
	),
	EmergencyPhone: series(requiredCheckForWebsite('EmergencyPhone'), mobileCheck, (value, website, order) =>
		ruleCheck('IF_PERSONAL_IS_NOT_SAME_MOBILE_CAN_NOT_BE_SAME', value, order, website)
	),
	Mobile: series(requiredCheckForWebsite('Mobile'), mobileCheck, (value, website, order) =>
		ruleCheck('IF_PERSONAL_IS_NOT_SAME_MOBILE_CAN_NOT_BE_SAME', value, order, website)
	),
	Email: series(requiredCheckForWebsite('Email'), emailCheck, (value, website, order) =>
		ruleCheck('IF_PERSONAL_IS_NOT_SAME_EMAIL_CAN_NOT_BE_SAME', value, order, website)
	),
	QQ: series(notRequiredCheck, qqCheck),
	LicenseType: series(requiredTypeCheckForWebsite('LicenseType'), (value, website, order) =>
		ruleCheck('MUST_USE_LOCAL_ID_CARD', value, order, website)
	),
	LicenseId: series(requiredCheckForWebsite('LicenseId'), (value, website) => idCheck(value, website['LicenseType'])),
	LicensePicture: requiredCheckForWebsite('LicensePicture'),
	CurtainPicture: series(
		(value, website, order) =>
			order.SubmitReexamineType == 2 ? requiredCheckForWebsite('CurtainPicture')(value) : notRequiredCheck(value)
	),
	AuthVerificationPicture: series(
		(value, website, order) =>
			!ruleTest('MUST_POST_FILE_IN_SOME_AREA', order) && order.SubmitReexamineType == 2 ?
				requiredCheckForWebsite('AuthVerificationPicture')(value) :
				notRequiredCheck(value)
	),
	OtherPicture: allwaysPass,
	// Remark: series(
	// 	(value, website, order) =>
	// 		ruleTest('MUST_FILL_WEBSITE_REMARK', order) ||
	// 			ruleTest('	', order) ||
	// 			ruleTest('MUST_FILL_CONCAT_IN_REMARK_NOT_PERSONAL', order) ?
	// 			requiredCheckForWebsite('Remark')(value) : allwaysPass()
	// ),
	Remark: series(
		(value, website, order) =>
			!ruleCheck('OLD_ORDER_MODIFY', value, order) &&
				(ruleTest('MUST_FILL_WEBSITE_REMARK', order) ||
					(ruleTest('WEBSITE_NEI_MENG_GU_REQUIRED_REMARK_NOT_PERSONAL', order) ||
						ruleTest('REQUIRES_REMARK_PROVINCES', order))) ?
				requiredCheckForWebsite('Remark')(value) :
				allwaysPass()
	),
	Verify: (fileMap, website, order) => {
		let result = {}
		_.forEach(fileMap, fileName => {
			let _result = websiteCheckMap[fileName](website[fileName], website, order)
			if (_result !== true) {
				result[fileName] = _result
			}
		})
		if (_.isEmpty(result)) {
			return true
		}
		return result
	}
}
module.exports = {
	mainCheckMap,
	websiteCheckMap,
	domainCheck,
}

/*
 根据〖中华人民共和国国家标准 GB 11643-1999〗中有关公民身份号码的规定,公民身份号码是特征组合码,由十七位数字本体码和一位数字校验码组成.排列顺序从左至右依次为:六位数字地址码,八位数字出生日期码,三位数字顺序码和一位数字校验码.
 地址码表示编码对象常住户口所在县(市,旗,区)的行政区划代码.
 出生日期码表示编码对象出生的年,月,日,其中年份用四位数字表示,年,月,日之间不用分隔符.
 顺序码表示同一地址码所标识的区域范围内,对同年,月,日出生的人员编定的顺序号.顺序码的奇数分给男性,偶数分给女性.
 校验码是根据前面十七位数字码,按照ISO 7064:1983.MOD 11-2校验码计算出来的检验码.

 出生日期计算方法.
 15位的身份证编码首先把出生年扩展为4位,简单的就是增加一个19或18,这样就包含了所有1800-1999年出生的人;
 2000年后出生的肯定都是18位的了没有这个烦恼,至于1800年前出生的,那啥那时应该还没身份证号这个东东,⊙﹏⊙b汗...
 下面是正则表达式:
 出生日期1800-2099  (18|19|20)?\d{2}(0[1-9]|1[12])(0[1-9]|[12]\d|3[01])
 身份证正则表达式 /^\d{6}(18|19|20)?\d{2}(0[1-9]|1[12])(0[1-9]|[12]\d|3[01])\d{3}(\d|X)$/i
 15位校验规则 6位地址编码+6位出生日期+3位顺序号
 18位校验规则 6位地址编码+8位出生日期+3位顺序号+1位校验位

 校验位规则     公式:∑(ai×Wi)(mod 11)……………………………………(1)
 公式(1)中:
 i----表示号码字符从由至左包括校验码在内的位置序号;
 ai----表示第i位置上的号码字符值;
 Wi----示第i位置上的加权因子,其数值依据公式Wi=2^(n-1）(mod 11)计算得出.
 i 18 17 16 15 14 13 12 11 10 9 8 7 6 5 4 3 2 1
 Wi 7 9 10 5 8 4 2 1 6 3 7 9 10 5 8 4 2 1

 */
//身份证号合法性验证
//支持15位和18位身份证号
//支持地址编码,出生日期,校验位验证
const IdentityCodeValid = function (code) {
	var city = {
		11: '北京',
		12: '天津',
		13: '河北',
		14: '山西',
		15: '内蒙古',
		21: '辽宁',
		22: '吉林',
		23: '黑龙江 ',
		31: '上海',
		32: '江苏',
		33: '浙江',
		34: '安徽',
		35: '福建',
		36: '江西',
		37: '山东',
		41: '河南',
		42: '湖北 ',
		43: '湖南',
		44: '广东',
		45: '广西',
		46: '海南',
		50: '重庆',
		51: '四川',
		52: '贵州',
		53: '云南',
		54: '西藏 ',
		61: '陕西',
		62: '甘肃',
		63: '青海',
		64: '宁夏',
		65: '新疆',
		71: '台湾',
		81: '香港',
		82: '澳门',
		91: '国外 '
	}
	var tip = ''
	var pass = true

	if (!code || !/^\d{6}(18|19|20)?\d{2}(0[1-9]|1[012])(0[1-9]|[12]\d|3[01])\d{3}(\d|X)$/i.test(code)) {
		tip = '身份证号格式错误'
		pass = false
	} else if (!city[code.substr(0, 2)]) {
		tip = '地址编码错误'
		pass = false
	} else {
		//18位身份证需要验证最后一位校验位
		if (code.length == 18) {
			code = code.split('')
			//∑(ai×Wi)(mod 11)
			//加权因子
			var factor = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2]
			//校验位
			var parity = [1, 0, 'X', 9, 8, 7, 6, 5, 4, 3, 2]
			var sum = 0
			var ai = 0
			var wi = 0
			for (var i = 0; i < 17; i++) {
				ai = code[i]
				wi = factor[i]
				sum += ai * wi
			}
			var last = parity[sum % 11]
			if (last != code[17]) {
				tip = '证件号录入错误'
				pass = false
			}
		}
	}
	//if(!pass) alert(tip);
	return {
		pass,
		tip
	}
}
/*
  统一代码由十八位的阿拉伯数字或大写英文字母（不使用I,O,Z,S,V）组成.
  第1位:登记管理部门代码（共一位字符）
  第2位:机构类别代码（共一位字符）
  第3位~第8位:登记管理机关行政区划码（共六位阿拉伯数字）
  第9位~第17位:主体标识码（组织机构代码）（共九位字符）
  第18位:校验码（共一位字符)
*/
//统一社会信用代码
const Tyshyxdm = function () {
	this.firstarray = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z']
	this.firstkeys = [3, 7, 9, 10, 5, 8, 4, 2]
	this.secondarray = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'J', 'K', 'L', 'M', 'N', 'P', 'Q', 'R', 'T', 'U', 'W', 'X', 'Y']
	this.secondkeys = [1, 3, 9, 27, 19, 26, 16, 17, 20, 29, 25, 13, 8, 24, 10, 30, 28]
	this.verify = function (str) {
		var code = str.toUpperCase()
		if (code.length != 18) {
			return false
		}
		var reg = /^\w\w\d{6}\w{9}\w$/
		if (!reg.test(code)) {
			return false
		}
		/*
			登记管理部门代码:使用阿拉伯数字或大写英文字母表示.
			机构编制:1
			教育：3 
			公安：4
			民政:5
			司法：6
			交通运输：7
			文化：8
			工商:9
			其他:Y
		*/
		reg = /^[1,3,4,5,6,7,8,9,Y]\w\d{6}\w{9}\w$/
		if (!reg.test(code)) {
			return false
		}
		/*
			机构类别代码:使用阿拉伯数字或大写英文字母表示.
			机构编制机关:11打头
			机构编制事业单位:12打头
			机构编制中央编办直接管理机构编制的群众团体:13打头
			教育：31打头
			司法 ：34
			基础法律机构：33
			公安：41打头
			司法：61打头
			交通运输：71打头
			文化：81打头
			机构编制其他:19打头
			民政社会团体:51打头
			民政民办非企业单位:52打头
			民政基金会:53打头
			民政基金会:54打头街道办
			民政其他:59打头
			工商企业:91打头
			工商个体工商户:92打头
			工商农民专业合作社:93打头
			工商其他：99打头
			其他:Y1打头
		*/
		/*
			登记管理机关行政区划码:只能使用阿拉伯数字表示.按照GB/T 2260编码.
			例如:四川省成都市本级就是510100;四川省自贡市自流井区就是510302.
		*/
		reg = /^(11|12|13|19|31|33|34|41|51|52|53|54|55|59|61|71|81|91|92|93|99|Y1)\d{6}\w{9}\w$/
		if (!reg.test(code)) {
			return false
		}
		/*
			主体标识码（组织机构代码）:使用阿拉伯数字或英文大写字母表示.按照GB 11714编码.
			在实行统一社会信用代码之前,以前的组织机构代码证上的组织机构代码由九位字符组成.格式为XXXXXXXX-Y.前面八位被称为“本体代码”;最后一位被称为“校验码”.校验码和本体代码由一个连字号（-）连接起来.以便让人很容易的看出校验码.但是三证合一后,组织机构的九位字符全部被纳入统一社会信用代码的第9位至第17位,其原有组织机构代码上的连字号不带入统一社会信用代码.
			原有组织机构代码上的“校验码”的计算规则是:
			例如:某公司的组织机构代码是:59467239-9.那其最后一位的组织机构代码校验码9是如何计算出来的呢？
	
			第一步:取组织机构代码的前八位本体代码为基数.5 9 4 6 7 2 3 9
			提示:如果本体代码中含有英文大写字母.则A的基数是10,B的基数是11,C的基数是12,依此类推,直到Z的基数是35.
			第二步: 取加权因子数值.因为组织机构代码的本体代码一共是八位字符.则这八位的加权因子数值从左到右分别是:3,7,9,10,5,8,4,2.
			第三步:本体代码基数与对应位数的因子数值相乘.
			5×3＝15,9×7＝63,4×9＝36,6×10＝60,
			7×5＝35,2×8＝16,3×4=12,9×2＝18
			第四步:将乘积求和相加.
			15+63+36+60+35+16+12+18=255
			第五步:将和数除以11,求余数.
			255÷11=33,余数是2.
		*/
		var firstkey = this.calc(code.substr(8), this.firstarray, this.firstkeys, 11)
		/*
				第六步:用阿拉伯数字11减去余数,得求校验码的数值.当校验码的数值为10时,校验码用英文大写字母X来表示;当校验码的数值为11时,校验码用0来表示;其余求出的校验码数值就用其本身的阿拉伯数字来表示.
				11-2＝9,因此此公司完整的组织机构代码为 59467239-9.
		*/
		var firstword
		if (firstkey < 10) {
			firstword = firstkey
		}
		if (firstkey == 10) {
			firstword = 'X'
		} else if (firstkey == 11) {
			firstword = '0'
		}
		if (firstword != code.substr(16, 1)) {
			return false
		}
		/*
			校验码:使用阿拉伯数字或大写英文字母来表示.校验码的计算方法参照 GB/T 17710.
			例如:某公司的统一社会信用代码为91512081MA62K0260E,那其最后一位的校验码E是如何计算出来的呢？
			第一步:取统一社会信用代码的前十七位为基数.9 1 5 1 2 0 8 1 21 10 6 2 19 0 2 6 0提示:如果前十七位统一社会信用代码含有英文大写字母（不使用I,O,Z,S,V这五个英文字母）.则英文字母对应的基数分别为:A=10,B=11,C=12,D=13,E=14,F=15,G=16,H=17,J=18,K=19,L=20,M=21,N=22,P=23,Q=24,R=25,T=26,U=27,W=28,X=29,Y=30
			第二步:取加权因子数值.因为统一社会信用代码前面前面有十七位字符.则这十七位的加权因子数值从左到右分别是:1,3,9,27,19,26,16,17,20,29,25,13,8,24,10,30,28
			第三步:基数与对应位数的因子数值相乘.
			9×1=9,1×3=3,5×9=45,1×27=27,2×19=38,0×26=0,8×16=128
			1×17=17,21×20=420,10×29=290,6×25=150,2×13=26,19×8=152
			0×23=0,2×10=20,6×30=180,0×28=0
			第四步:将乘积求和相加.9+3+45+27+38+0+128+17+420+290+150+26+152+0+20+180+0=1495
			第五步:将和数除以31,求余数.
			1495÷31=48,余数是17.
		*/
		var secondkey = this.calc(code, this.secondarray, this.secondkeys, 31)
		/*
			第六步:用阿拉伯数字31减去余数,得求校验码的数值.当校验码的数值为0~9时,就直接用该校验码的数值作为最终的统一社会信用代码的校验码;如果校验码的数值是10~30,则校验码转换为对应的大写英文字母.对应关系为:A=10,B=11,C=12,D=13,E=14,F=15,G=16,H=17,J=18,K=19,L=20,M=21,N=22,P=23,Q=24,R=25,T=26,U=27,W=28,X=29,Y=30
			因为,31-17＝14,所以该公司完整的统一社会信用代码为 91512081MA62K0260E.
		*/
		var secondword = this.secondarray[secondkey]
		if (!secondword || secondword != code.substr(17, 1)) {
			return false
		}
		var word = code.substr(0, 16) + firstword + secondword
		if (code != word) {
			return false
		}
		return true
	}
	this.calc = function (code, array1, array2, b) {
		var count = 0
		for (var i = 0; i < array2.length; i++) {
			var a = code[i]
			count += array2[i] * array1.indexOf(a)
		}
		return (b - count % b === b) ? 0 : (b - count % b)// 如果count%b余数是0,则直接返回0
	}
}


const Regex_BusinessLicenseNumber = function () {

	/**
	 * 营业执照注册号校验正确返回码
	 */
	let true_Businesslicense = true
	let error_Businesslicense_Empty = '请输入营业执照注册号'
	let error_Businesslicense = '您输入的营业执照注册号有误，请核对后再输!'
	let error_Businesslicense_No = '您输入的营业执照注册号不足15位，请核对后再输!'

	// let test = "110108000000016" ;// 营业执照号

	// this.isBusinesslicense(test);

	/**
	 * 校验 营业执照注册号
	 * @param businesslicense
	 * @return
	 */
	this.isBusinesslicense = (businesslicense) => {

		if ('' === businesslicense || ' ' === businesslicense) {
			return error_Businesslicense_Empty
		} else if (businesslicense.length != 15) {
			return error_Businesslicense_No
		}
		let businesslicensePrex14 = businesslicense.substring(0, 14)// 获取营业执照注册号前14位数字用来计算校验码
		let businesslicense15 = businesslicense.substring(14, businesslicense.length)// 获取营业执照号的校验码
		let chars = [...businesslicensePrex14]
		let ints = new Array()
		for (let i = 0; i < chars.length; i++) {
			ints[i] = parseInt(chars[i])
		}
		let lastCheckNum = getCheckCode(ints)
		// console.log(lastCheckNum,businesslicense15)
		if (businesslicense15 === (lastCheckNum + '') || (businesslicense15 === '0' && lastCheckNum === 1)) {// 比较 填写的营业执照注册号的校验码和计算的校验码是否一致
			return true_Businesslicense
		}
		return error_Businesslicense
	}

	/**
	 * 获取 营业执照注册号的校验码
	 * @param ints
	 * @return
	 */
	const getCheckCode = (ints) => {
		if (null != ints && ints.length > 1) {
			let ti = 0
			let si = 0
			let cj = 0
			let pj = 10
			for (let i = 0; i < ints.length; i++) {
				ti = ints[i]
				pj = (cj % 11) == 0 ? 10 : (cj % 11)
				si = pj + ti
				cj = (0 == si % 10 ? 10 : si % 10) * 2
				if (i == ints.length - 1) {
					pj = (cj % 11) == 0 ? 10 : (cj % 11)
					return pj == 1 ? 1 : 11 - pj
				}
			}
		}
		return -1

	}

}

const remarkCheck = value => {
	if (value && value.trim().length === 0) {
		return '请输入有效字符'
	}
	// const validChar = /[:!@$%^&*'\\<>￥；]{1}/.exec(value);
	// if (validChar) {
	//     return `不能包含以下特殊字符："${validChar[0]}"`;
	// }
	return true
}