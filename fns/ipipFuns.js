/*
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2024-02-01 14:17:42
 * @LastEditors: <NAME_EMAIL>
 * @LastEditTime: 2024-02-04 15:34:55
 * @FilePath: /newicp/fns/ipipFuncs.js
 * @Description:
 */
var ipdb = require('ipip-ipdb')

/**
 * 传入函数名与IP，得到指定信息
 * @returns string
 */
var getIPIPInfo = function (funcName, ip) {
    var city = new ipdb[funcName](global.CONFIG.appPath + '/fns/ipv4_cn.ipdb')
    return city.findInfo(ip, 'CN')
}

module.exports = {
    getIPIPInfo,
}