const { OrderModel, OrderWebModel, OrderHistoryModel, ICPModel, ICPWebModel } = require('../models')
const {parseJSON} = require("./kits")
const logger = require("../libs/logger")
const {Op} = require("sequelize")
const _ = require("lodash")

// 字段映射配置
const FIELD_MAPPINGS = {
    // 模式1：Main字段自身检查
    MAIN_SELF: {
        PICMainMobile: 'PICMainMobile',
        PICMainLicenseId: 'PICMainLicenseId',
        EmergencyPhone: 'EmergencyPhone',
        PICMainEmail: 'PICMainEmail'
    },
    // 模式2：Web字段映射到Main字段
    WEB_TO_MAIN: {
        Mobile: 'PICMainMobile',
        LicenseId: 'PICMainLicenseId',
        EmergencyPhone: 'EmergencyPhone',
        Email: 'PICMainEmail'
    },
    // 模式3：Main字段映射到Web字段
    MAIN_TO_WEB: {
        PICMainMobile: 'Mobile',
        PICMainLicenseId: 'LicenseId',
        EmergencyPhone: 'EmergencyPhone',
        PICMainEmail: 'Email'
    },
    // 模式4：Web字段自身检查
    WEB_SELF: {
        Mobile: 'Mobile',
        LicenseId: 'LicenseId',
        EmergencyPhone: 'EmergencyPhone',
        Email: 'Email'
    }
}

const WEB_FIELDS = ['Mobile', 'LicenseId', 'EmergencyPhone', 'Email']
const MAIN_FIELDS = ['PICMainMobile', 'PICMainLicenseId', 'EmergencyPhone', 'PICMainEmail']

async function checkAllDuplicateMobileEmailFields(OrderNo) {
    try {
        let checkResult = {
            OrganizerName: "",
            PICMainMobileCheckResult: [], // 与哪个订单下的主体手机号重复或者已备案的主体手机号重复
            PICMainLicenseIdCheckResult: [],
            EmergencyPhoneCheckResult: [],
            PICMainEmailCheckResult: [],
            WebSitesCheckResult: []
        }

        // 查询当前订单中的主体信息
        let mainInfo = await OrderModel.findOne({
            attributes: [
                'PICMainMobile',
                'PICMainLicenseId',
                'EmergencyPhone',
                'PICMainEmail',
                'OrganizerName',
                'OrderNo'
            ],
            where: {
                OrderNo,
            },
        })
        mainInfo = parseJSON(mainInfo)
        if (!mainInfo) {
            logger.getLogger("access").info("No order information found.:", OrderNo)
            return {}
        }

        checkResult.OrganizerName = mainInfo.OrganizerName

        // 获取当前订单下的网站信息
        let orderWebInfo = await OrderWebModel.findAll({
            attributes: ['Mobile', 'LicenseId', 'EmergencyPhone', 'Email', 'Id'],
            where: {
                OrderNo,
            },
        })
        orderWebInfo = parseJSON(orderWebInfo)
        if (orderWebInfo.length === 0) {
            logger.getLogger("access").info("No website information found:", OrderNo)
        }

        // 初始化网站检查结果数据
        for (let web of orderWebInfo)  {
            let WebSitesCheckItem = {
                Id: web.Id,
                MobileCheckResult: [],
                LicenseIdCheckResult: [],
                EmergencyPhoneCheckResult: [],
                EmailCheckResult: []
            }
            checkResult.WebSitesCheckResult.push(WebSitesCheckItem)
        }

        let orderInfo = {
            mainInfo: mainInfo,
            webInfo: orderWebInfo
        }
        logger.getLogger("access").info(`Start checking ${OrderNo},${JSON.stringify(orderInfo)} duplicate information`)
        await checkOrderMainInfo(orderInfo, checkResult)
        await checkOrderWebMainInfo(orderInfo, checkResult)
        await checkIcpMainInfo(orderInfo, checkResult)
        await checkIcpWebMainInfo(orderInfo, checkResult)
        logger.getLogger("access").info(`The order ${OrderNo} provisional results is：${JSON.stringify(checkResult)}`)

        return transformData(checkResult)
    } catch (error) {
        logger.getLogger("error").error(`checkAllDuplicateMobileEmailFields error, the error is ${error}`)
        return {}
    }
}

function skipCheck(orderInfo, mainInfo, webInfo) {
    if (JSON.stringify(webInfo) !== `{}`) {
        return orderInfo.OrganizerName === mainInfo.OrganizerName && orderInfo.PICMainLicenseId === webInfo.LicenseId
    }
    return orderInfo.OrganizerName === mainInfo.OrganizerName && orderInfo.PICMainLicenseId === mainInfo.PICMainLicenseId
}

// 通用转换函数
function createSelectJSON(data, mode) {
    const mapping = FIELD_MAPPINGS[mode];
    if (!mapping) return [];

    return Object.entries(mapping)
        .filter(([srcField]) => data[srcField])
        .map(([srcField, targetField]) => ({ [targetField]: data[srcField] }))
}

// 查询订单表中和当前订单中主体信息和网站信息信息重复的数据
async function checkOrderMainInfo(OrderInfo, checkResult) {
    let mainInfo = OrderInfo.mainInfo
    let webInfo = OrderInfo.webInfo

    // 先检查主体信息
    let selectJSON = createSelectJSON(mainInfo, 'MAIN_SELF')
    // 单独处理一下手机号和紧急联系人手机号，因为有可能是相互的
    if (mainInfo.PICMainMobile) {
        selectJSON.push({ EmergencyPhone: mainInfo.PICMainMobile })
    }
    if (mainInfo.EmergencyPhone) {
        selectJSON.push({ PICMainMobile: mainInfo.EmergencyPhone })
    }
    let orderMainInfo = await getOrderMatchInfo(selectJSON) // 查出来主体信息的重复数据
    for (let orderInfo of orderMainInfo) {
        let item = {
            OrderNo: orderInfo.OrderNo,
            OrganizerName: orderInfo.OrganizerName,
            MatchType: "EnityMatch"
        }
        // 过滤当前订单和当前主体信息相同的数据
        if (orderInfo.OrderNo === mainInfo.OrderNo || skipCheck(orderInfo, mainInfo, {})) {
            continue
        }
        for (let field of MAIN_FIELDS) {
            if (orderInfo[field] === mainInfo[field]) {
                checkResult[`${field}CheckResult`].push({
                    ...item,
                    MatchField: field
                })
            }
        }
        // 特殊处理一下手机号和紧急联系人手机号，因为有可能是相互的
        if (orderInfo.PICMainMobile === mainInfo.EmergencyPhone) {
            checkResult.EmergencyPhoneCheckResult.push({
                ...item,
                MatchField: "PICMainMobile"
            })
        }
        if (orderInfo.EmergencyPhone === mainInfo.PICMainMobile) {
            checkResult.PICMainMobileCheckResult.push({
                ...item,
                MatchField: "EmergencyPhone"
            })
        }
    }

    // 再检查和当前订单下的网站信息和主体重复的数据
    for (let checkWebInfo of webInfo) {
        let selectJSON = createSelectJSON(checkWebInfo, 'WEB_TO_MAIN')
        // 单独处理一下手机号和紧急联系人手机号，因为有可能是相互的
        if (checkWebInfo.Mobile) {
            selectJSON.push({ EmergencyPhone: checkWebInfo.Mobile })
        }
        if (checkWebInfo.EmergencyPhone) {
            selectJSON.push({ PICMainMobile: checkWebInfo.EmergencyPhone })
        }
        let orderWebInfo = await getOrderMatchInfo(selectJSON)
        // 获取下标，避免出现检查错误的情况
        let index = checkResult.WebSitesCheckResult.findIndex(item => item.Id === checkWebInfo.Id)

        for (let orderInfo of orderWebInfo) {
            // 过滤当前订单和当前主体信息相同网站信息的数据
            if (orderInfo.OrderNo === mainInfo.OrderNo || skipCheck(orderInfo, mainInfo, checkWebInfo)) {
                continue
            }
            let item = {
                OrderNo: orderInfo.OrderNo,
                OrganizerName: orderInfo.OrganizerName,
                MatchType: "EnityMatch"
            }
            for (let i = 0;i<WEB_FIELDS.length;i++) {
                if (orderInfo[MAIN_FIELDS[i]] === checkWebInfo[WEB_FIELDS[i]]) {
                    checkResult.WebSitesCheckResult[index][`${WEB_FIELDS[i]}CheckResult`].push({
                        ...item,
                        MatchField: MAIN_FIELDS[i]
                    })
                }
            }
            // 特殊处理一下手机号和紧急联系人手机号，因为有可能是相互的
            if (orderInfo.PICMainMobile === checkWebInfo.EmergencyPhone) {
                checkResult.WebSitesCheckResult[index].EmergencyPhoneCheckResult.push({
                    ...item,
                    MatchField: 'PICMainMobile'
                })
            }
            if (orderInfo.EmergencyPhone === checkWebInfo.Mobile) {
                checkResult.WebSitesCheckResult[index].MobileCheckResult.push({
                    ...item,
                    MatchField: 'EmergencyPhone'
                })
            }
        }
    }

}

// 查询网站表中和当前订单中主体信息和网站信息信息重复的数据
async function checkOrderWebMainInfo(OrderInfo, checkResult) {
    let mainInfo = OrderInfo.mainInfo
    let webInfo = OrderInfo.webInfo

    // 先检查主体信息
    let selectJSON = createSelectJSON(mainInfo, 'MAIN_TO_WEB')
    if (mainInfo.PICMainMobile) {
        selectJSON.push({ EmergencyPhone: mainInfo.PICMainMobile })
    }
    if (mainInfo.EmergencyPhone) {
        selectJSON.push({ Mobile: mainInfo.EmergencyPhone })
    }
    let orderWebInfo = await getOrderWebMatchInfo(selectJSON) // 查出来主体信息的重复数据
    for (let orderWeb of orderWebInfo) {
        // 过滤当前订单和当前主体信息相同的数据
        if (orderWeb.OrderNo === mainInfo.OrderNo || skipCheck(orderWeb, mainInfo, {})) {
            continue
        }
        let item = {
            OrderNo: orderWeb.OrderNo,
            OrganizerName: orderWeb.OrganizerName,
            Name: orderWeb.Name,
            MatchType: "WebMatch"
        }
        for (let i = 0;i< WEB_FIELDS.length;i++) {
            if (orderWeb[WEB_FIELDS[i]] === mainInfo[MAIN_FIELDS[i]]) {
                checkResult[`${MAIN_FIELDS[i]}CheckResult`].push({
                    ...item,
                    MatchField: WEB_FIELDS[i]
                })
            }
        }
        if (orderWeb.Mobile === mainInfo.EmergencyPhone) {
            checkResult.EmergencyPhoneCheckResult.push({
                ...item,
                MatchField: 'Mobile'
            })
        }
        if (orderWeb.EmergencyPhone === mainInfo.PICMainMobile) {
            checkResult.PICMainMobileCheckResult.push({
                ...item,
                MatchField: 'EmergencyPhone'
            })
        }
    }

    // 再检查和当前订单下的网站信息和主体重复的数据
    for (let checkWebInfo of webInfo) {
        let selectJSON = createSelectJSON(checkWebInfo, 'WEB_SELF')
        if (checkWebInfo.Mobile) {
            selectJSON.push({ EmergencyPhone: checkWebInfo.Mobile })
        }
        if (checkWebInfo.EmergencyPhone) {
            selectJSON.push({ Mobile: checkWebInfo.EmergencyPhone })
        }
        let orderWebInfo = await getOrderWebMatchInfo(selectJSON)

        // 获取下标，避免出现检查添加错误的情况
        let index = checkResult.WebSitesCheckResult.findIndex(item => item.Id === checkWebInfo.Id)
        for (let orderWeb of orderWebInfo) {
            // 过滤当前订单和当前主体信息相同网站信息的数据
            if (orderWeb.OrderNo === mainInfo.OrderNo || skipCheck(orderWeb, mainInfo, checkWebInfo)) {
                continue
            }
            let item = {
                OrderNo: orderWeb.OrderNo,
                OrganizerName: orderWeb.OrganizerName,
                Name: orderWeb.Name,
                MatchType: "WebMatch"
            }
            for (let filed of WEB_FIELDS) {
                if (orderWeb[filed] === checkWebInfo[filed]) {
                    checkResult.WebSitesCheckResult[index][`${filed}CheckResult`].push({
                        ...item,
                        MatchField: filed
                    })
                }
            }
            // 特殊处理一下手机号和紧急联系人手机号，因为有可能是相互的
            if (orderWeb.Mobile === checkWebInfo.EmergencyPhone) {
                //item.MatchField = 'Mobile'
                checkResult.WebSitesCheckResult[index].EmergencyPhoneCheckResult.push({
                    ...item,
                    MatchField: 'Mobile'
                })
            }
            if (orderWeb.EmergencyPhone === checkWebInfo.Mobile) {
                //item.MatchField = 'EmergencyPhone'
                checkResult.WebSitesCheckResult[index].MobileCheckResult.push({
                    ...item,
                    MatchField: 'EmergencyPhone'
                })
            }
        }
    }

}

// 查询已备案表中和当前订单中主体信息和网站信息信息重复的数据
async function checkIcpMainInfo(OrderInfo, checkResult) {
    let mainInfo = OrderInfo.mainInfo
    let webInfo = OrderInfo.webInfo

    // 先检查主体信息
    let selectJSON = createSelectJSON(mainInfo, 'MAIN_SELF')
    if (mainInfo.PICMainMobile) {
        selectJSON.push({ EmergencyPhone: mainInfo.PICMainMobile })
    }
    if (mainInfo.EmergencyPhone) {
        selectJSON.push({ PICMainMobile: mainInfo.EmergencyPhone })
    }
    let icpMainInfo = await getIcpMatchInfo(selectJSON) // 查出来主体信息的重复数据
    for (let icpInfo of icpMainInfo) {
        // 当前订单和已备案完成的比对，主体名称和证件号一致也过滤
        if(skipCheck(icpInfo, mainInfo, {})) {
            continue
        }
        let item = {
            ICPMainNo: icpInfo.ICPMainNo,
            OrganizerName: icpInfo.OrganizerName,
            MatchType: "EnityMatch"
        }
        for (let filed of MAIN_FIELDS) {
            if (icpInfo[filed] === mainInfo[filed]) {
                checkResult[`${filed}CheckResult`].push({
                    ...item,
                    MatchField: filed
                })
            }
        }
        if (icpInfo.PICMainMobile === mainInfo.EmergencyPhone) {
            checkResult.EmergencyPhoneCheckResult.push({
                ...item,
                MatchField: 'PICMainMobile'
            })
        }
        if (icpInfo.EmergencyPhone === mainInfo.PICMainMobile) {
            checkResult.PICMainMobileCheckResult.push({
                ...item,
                MatchField: 'EmergencyPhone'
            })
        }
    }
    // 再检查和当前订单下的网站信息和主体重复的数据
    for (let checkWebInfo of webInfo) {
        let selectJSON = createSelectJSON(checkWebInfo, 'WEB_TO_MAIN')
        if (checkWebInfo.Mobile) {
            selectJSON.push({ EmergencyPhone: checkWebInfo.Mobile })
        }
        if (checkWebInfo.EmergencyPhone) {
            selectJSON.push({ PICMainMobile: checkWebInfo.EmergencyPhone })
        }
        let icpMainInfo = await getIcpMatchInfo(selectJSON)

        // 获取下标，避免出现检查添加错误的情况
        let index = checkResult.WebSitesCheckResult.findIndex(item => item.Id === checkWebInfo.Id)
        for (let icpInfo of icpMainInfo) {
            // 过滤当前订单和当前主体信息相同网站信息的数据
            if (skipCheck(icpInfo, mainInfo, checkWebInfo)) {
                continue
            }
            let item = {
                ICPMainNo: icpInfo.ICPMainNo,
                OrganizerName: icpInfo.OrganizerName,
                MatchType: "EnityMatch"
            }
            for (let i = 0;i<WEB_FIELDS.length;i++) {
                if (icpInfo[MAIN_FIELDS[i]] === checkWebInfo[WEB_FIELDS[i]]) {
                    checkResult.WebSitesCheckResult[index][`${WEB_FIELDS[i]}CheckResult`].push({
                        ...item,
                        MatchField: MAIN_FIELDS[i]
                    })
                }
            }
            // 特殊处理一下手机号和紧急联系人手机号，因为有可能是相互的
            if (icpInfo.PICMainMobile === checkWebInfo.EmergencyPhone) {
                checkResult.WebSitesCheckResult[index].EmergencyPhoneCheckResult.push({
                    ...item,
                    MatchField: 'PICMainMobile'
                })
            }
            if (icpInfo.EmergencyPhone === checkWebInfo.Mobile) {
                checkResult.WebSitesCheckResult[index].MobileCheckResult.push({
                    ...item,
                    MatchField: 'EmergencyPhone'
                })
            }
        }
    }

}

// 查询已备案网站中和当前订单中主体信息和网站信息信息重复的数据
async function checkIcpWebMainInfo(OrderInfo, checkResult) {
    let mainInfo = OrderInfo.mainInfo
    let webInfo = OrderInfo.webInfo

    // 先检查主体信息
    let selectJSON = createSelectJSON(mainInfo, 'MAIN_TO_WEB')
    if (mainInfo.PICMainMobile) {
        selectJSON.push({ EmergencyPhone: mainInfo.PICMainMobile })
    }
    if (mainInfo.EmergencyPhone) {
        selectJSON.push({ Mobile: mainInfo.EmergencyPhone })
    }
    let icpWebInfo = await getIcpWebMatchInfo(selectJSON) // 查出来主体信息的重复数据
    for (let icpWeb of icpWebInfo) {
        // 过滤当前订单和当前主体信息相同的数据
        if (skipCheck(icpWeb, mainInfo, {})) {
            continue
        }
        let item = {
            ICPMainNo: icpWeb.ICPMainNo,
            OrganizerName: icpWeb.OrganizerName,
            MatchType: "WebMatch",
            Name: icpWeb.Name
        }
        for (let i = 0;i< WEB_FIELDS.length;i++) {
            if (icpWeb[WEB_FIELDS[i]] === mainInfo[MAIN_FIELDS[i]]) {
                checkResult[`${MAIN_FIELDS[i]}CheckResult`].push({
                    ...item,
                    MatchField: WEB_FIELDS[i]
                })
            }
        }
        if (icpWeb.Mobile === mainInfo.EmergencyPhone) {
            checkResult.EmergencyPhoneCheckResult.push({
                ...item,
                MatchField: 'Mobile'
            })
        }
        if (icpWeb.EmergencyPhone === mainInfo.PICMainMobile) {
            checkResult.PICMainMobileCheckResult.push({
                ...item,
                MatchField: 'EmergencyPhone'
            })
        }
    }

    // 再检查和当前订单下的网站信息和主体重复的数据
    for (let checkWebInfo of webInfo) {
        let selectJSON = createSelectJSON(checkWebInfo, 'WEB_SELF')
        if (checkWebInfo.Mobile) {
            selectJSON.push({ EmergencyPhone: checkWebInfo.Mobile })
        }
        if (checkWebInfo.EmergencyPhone) {
            selectJSON.push({ Mobile: checkWebInfo.EmergencyPhone })
        }
        let icpWebInfo = await getIcpWebMatchInfo(selectJSON)

        // 获取下标，避免出现检查添加错误的情况
        let index = checkResult.WebSitesCheckResult.findIndex(item => item.Id === checkWebInfo.Id)
        for (let icpWeb of icpWebInfo) {
            // 过滤当前订单和当前主体信息相同网站信息的数据
            if (skipCheck(icpWeb, mainInfo, checkWebInfo)) {
                continue
            }
            let item = {
                ICPMainNo: icpWeb.ICPMainNo,
                OrganizerName: icpWeb.OrganizerName,
                MatchType: "WebMatch",
                Name: icpWeb.Name
            }
            for (let filed of WEB_FIELDS) {
                if (icpWeb[filed] === checkWebInfo[filed]) {
                    checkResult.WebSitesCheckResult[index][`${filed}CheckResult`].push({
                        ...item,
                        MatchField: filed
                    })
                }
            }
            // 特殊处理一下手机号和紧急联系人手机号，因为有可能是相互的
            if (icpWeb.Mobile === checkWebInfo.EmergencyPhone) {
                checkResult.WebSitesCheckResult[index].EmergencyPhoneCheckResult.push({
                    ...item,
                    MatchField: 'Mobile'
                })
            }
            if (icpWeb.EmergencyPhone === checkWebInfo.Mobile) {
                checkResult.WebSitesCheckResult[index].MobileCheckResult.push({
                    ...item,
                    MatchField: 'EmergencyPhone'
                })
            }
        }
    }
}

// 获取订单表中信息重复的数据
async function getOrderMatchInfo(selectJSON) {
    let orderList = await OrderModel.findAll({
        attributes: [
            'PICMainMobile',
            'PICMainLicenseId',
            'EmergencyPhone',
            'PICMainEmail',
            'OrderNo',
            'OrganizerName',
        ],
        where: {
            [Op.or]: selectJSON,
            IsDeleted: 0,
            type: {
                [Op.in]: [1, 2, 3, 7, 8, 9]
            },
            status: {
                [Op.in]: [1, 2, 11]
            }
        },
    });

    orderList = parseJSON(orderList)

    return orderList
}

// 获取订单网站表中信息重复的数据
async function getOrderWebMatchInfo(selectJSON) {
    // 获取网站数据
    let orderWebList = await OrderWebModel.findAll({
        attributes: [
            'Mobile',
            'LicenseId',
            'EmergencyPhone',
            'Email',
            'OrderNo',
            'Name',
        ],
        where: {
            [Op.or]: selectJSON,
            IsDeleted: 0,
        },
    });

    orderWebList = parseJSON(orderWebList)
    if (orderWebList.length === 0) {
        return []
    }

    let endResult = []
    // 再查一下数据库表，防止慢查询，获取主体名称和主体证件号码进行过滤处理
    for (let i = 0; i<orderWebList.length; i++) {
        let orderInfo = await OrderModel.findOne({
            attributes: [
                'PICMainLicenseId',
                'OrganizerName'
            ],
            where: {
                OrderNo: orderWebList[i].OrderNo,
                IsDeleted: 0,
                type: {
                    [Op.in]: [1, 2, 3, 7, 8, 9]
                },
                status: {
                    [Op.in]: [1, 2, 11]
                }
            },
        })
        orderInfo = parseJSON(orderInfo)
        if (!orderInfo) {
            continue
        }
        endResult.push({
            ...orderWebList[i],
            ...orderInfo
        })
    }

    return endResult
}

async function getIcpMatchInfo(selectJSON) {
    let icpList = await ICPModel.findAll({
        attributes: [
            'PICMainMobile',
            'PICMainLicenseId',
            'EmergencyPhone',
            'PICMainEmail',
            'ICPMainNo',
            'OrganizerName',
        ],
        where: {
            [Op.or]: selectJSON,
            Status: { [Op.ne]: 1 },
        },
    })
    icpList = parseJSON(icpList)
    return icpList
}

async function getIcpWebMatchInfo(selectJSON) {
    let icpWebList = await ICPWebModel.findAll({
        attributes: [
            'Mobile',
            'LicenseId',
            'EmergencyPhone',
            'Email',
            'ICPWebNo',
            'MainId',
            'Name',
        ],
        where: {
            [Op.or]: selectJSON,
            Status: { [Op.ne]: 1 },
        },
    })
    icpWebList = parseJSON(icpWebList)
    if (icpWebList.length === 0) {
        return []
    }

    let endResult = []
    // 再查一下数据库表，防止慢查询，获取主体名称和主体证件号码进行过滤处理
    for (let i = 0; i<icpWebList.length; i++) {
        let icpInfo = await ICPModel.findOne({
            attributes: [
                'ICPMainNo',
                'OrganizerName',
                'PICMainLicenseId'
            ],
            where: {
                Id: icpWebList[i].MainId,
                Status: { [Op.ne]: 1 },
            },
        })
        icpInfo = parseJSON(icpInfo)
        if (!icpInfo) {
            continue
        }
        endResult.push({
            ...icpWebList[i],
            ...icpInfo
        })
    }

    return endResult
}

// 解析数据并转换为目标格式
function transformData(data) {
    const transformed = {
        OrganizerName: data.OrganizerName,
        PICMainMobile: processCheckResult(data.PICMainMobileCheckResult),
        PICMainLicenseId: processCheckResult(data.PICMainLicenseIdCheckResult),
        EmergencyPhone: processCheckResult(data.EmergencyPhoneCheckResult),
        PICMainEmail: processCheckResult(data.PICMainEmailCheckResult),
        Web: processWebSitesCheckResult(data.WebSitesCheckResult)
    };
    return transformed;
}

function processCheckResult(results) {
    const orderMatch = [];
    const icpMatch = [];

    results.forEach(item => {
        if (item.OrderNo) {
            let existing = orderMatch.find(o => o.OrderNo === item.OrderNo && o.OrganizerName === item.OrganizerName);
            if (!existing) {
                existing = { OrderNo: item.OrderNo, OrganizerName: item.OrganizerName, EnityMatch: [], WebMatch: [] };
                orderMatch.push(existing);
            }
            if (item.MatchType === 'EnityMatch') existing.EnityMatch.push(item.MatchField);
            if (item.MatchType === 'WebMatch') existing.WebMatch.push({ Field: item.MatchField, Name: item.Name || '' });
        } else if (item.ICPMainNo) {
            let existingICP = icpMatch.find(i => i.IcpNo === item.ICPMainNo && i.IcpName === item.OrganizerName);
            if (!existingICP) {
                existingICP = { IcpNo: item.ICPMainNo, IcpName: item.OrganizerName, EnityMatch: [], WebMatch: [] };
                icpMatch.push(existingICP);
            }
            if (item.MatchType === 'EnityMatch') existingICP.EnityMatch.push(item.MatchField);
            if (item.MatchType === 'WebMatch') existingICP.WebMatch.push({ Field: item.MatchField, Name: item.Name || '' });
        }
    });

    return { OrderMatch: orderMatch, ICPMatch: icpMatch };
}

function processWebSitesCheckResult(webSites) {
    return webSites.map(site => ({
        Id: site.Id,
        Mobile: processCheckResult(site.MobileCheckResult),
        LicenseId: processCheckResult(site.LicenseIdCheckResult),
        EmergencyPhone: processCheckResult(site.EmergencyPhoneCheckResult),
        Email: processCheckResult(site.EmailCheckResult)
    }));
}

module.exports = { checkAllDuplicateMobileEmailFields }