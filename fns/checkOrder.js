// 状态核验相关代码
// 之前是静态检查代码，现在增加业务逻辑。增加异步的检查的支持
const { getKeyAsync } = require('../fns/redisFuns')
const _ = require('lodash')
const { Op } = require('sequelize')
const ucloudinternalapi = require('../libs/ucloudinternalapi')
const crypto = require('crypto')
const ruleVerify = require('../fns/orderVerify/ruleVerify')
const { WebMap } = require('../models/t_web')
const { MainMap } = require('../models/t_icp')

async function checkOrder(redis, order) {
    // 获取线上规则配置
    let orderRules
    try {
        orderRules = await getKeyAsync(redis, 'onlineConfig1')
        orderRules = JSON.parse(orderRules)

        let ICPConfig = await getKeyAsync(redis, 'DescribeICPConfig')
        ICPConfig = JSON.parse(ICPConfig) // 获取全局的项目枚举，入参进去到校验库
        global.CONFIG.ICPConfig = ICPConfig
    } catch (error) {
        return '获取检查规则失败'
    }
    let mainCheckRules = orderRules.main
    let webCheckRules = orderRules.web
    // 检查全部字段
    // 如果是Create,是提交到审核的
    // 如果是Modify，是原数据+上更新的数据
    // 手机是否通知过，异步核验用


    for (const i of Object.keys(mainCheckRules)) {
        console.log(i, 'main')
        const name = i
        const value = order[name]
        let interceptCheck = await getInterceptFuns({
            redis,
            value,
            name,
            order,
        })
        if (interceptCheck === null && mainCheckRules[name]?.length > 0) {
            // 未匹配到前置拦截检查，按正常流程来
            const checkResult = await ruleVerify({
                value,
                key: name,
                order,
                website: order.Website || [],
                rules: mainCheckRules[name],
            })

            if (checkResult !== true) {
                return stripOrderPrefix(checkResult, name)
            }
        } else if (interceptCheck !== true && interceptCheck !== null) {
            // 为null 并且不是func 那是个啥？ 不管了 先继续校验需要
            return interceptCheck
        }
    }
    order.Website = order.Website || []
    for (const i of order.Website) {
        const website = i
        for (const ruleName of Object.keys(webCheckRules)) {
            console.log(ruleName, 'website')
            const name = ruleName

            const value = website[name]
            if (webCheckRules[name]?.length > 0) {
                const checkResult = await ruleVerify({
                    value,
                    key: name,
                    order,
                    website,
                    rules: webCheckRules[name],
                })

                if (checkResult !== true) {
                    return `网站 ${website.Name}的 ${
                        WebMap[name] || ''
                    }: ${checkResult}`
                }
            }
        }
    }
}

async function checkPicture(Picture, params) {
    return new Promise((resolve, reject) => {
        let images = getPictures(params)
        if (images.length === 0) return resolve(0)
        Picture.findAll({
            where: {
                CompanyId: params.CompanyId,
                Url: { [Op.in]: images },
            },
        })
            .then((rows) => {
                rows = rows.map((row) => row.Url)

                for (var i in images) {
                    // 如果有图片不在结果里面，则报错
                    if (
                        _.indexOf(rows, images[i]) === -1 &&
                        images[i] !== global.CONFIG.PORT_UNIQUE
                    )
                        return reject({
                            message: 'No such picture: ' + images[i],
                        })
                }
                resolve(0)
            })
            .catch((err) => reject(err))
    })
}

function getPictures(params) {
    // 主体下的图片
    let images = [].concat(
        params.PICMainLicensePicture,
        params.AuthVerificationPicture,
        params.CurtainPicture,
        params.OrganizerLicensePicture,
        params.OrganizerResidencePermitPicture,
        params.PICWebLicensePicture,
        params.PromiseLetterPicture
    )

    // 其他图片
    params.OtherPicture &&
        params.OtherPicture.map((picture) => images.push(picture))

    // 网站信息中的图片
    if (params.Website && _.isArray(params.Website)) {
        params.Website.forEach((website) => {
            // 前置审批图片处理
            let PreAppoval = website.PreAppoval
            if (_.isArray(PreAppoval)) {
                PreAppoval.forEach((PreAppovalInfo) => {
                    if (_.isArray(PreAppovalInfo.Picture)) {
                        return (images = images.concat(PreAppovalInfo.Picture))
                    }
                })
            }
            let FetchInfo = website.FetchInfo
            if (FetchInfo && JSON.stringify(FetchInfo) !== '{}') {
                images = images.concat(FetchInfo.CommitmentPicture)
                images = images.concat(FetchInfo.CommitmentVedio)
                images = images.concat(FetchInfo.NotificationPicture)
            }
            images = images.concat(
                website.AppIcon,
                website.PICWebLicensePicture,
                website.LicensePicture,
                website.CurtainPicture,
                website.OtherPicture,
                website.AuthoriztionPicture
            )

            if (_.isArray(website.Domain)) {
                website.Domain.forEach((domain) => {
                    // 将 CerificationPicture 合并到 images
                    images = images.concat(domain.CerificationPicture);

                    // 如果 domainAuthenctionPicture 存在，也合并到 images
                    if (domain.domainAuthenctionPicture) {
                        images = images.concat(domain.domainAuthenctionPicture);
                    }
                })
            }
        })
    }

    images = _.flattenDeep(images)

    images = images.filter(
        (image) => image !== undefined && image !== '' && image !== null
    )

    return images
}
/**
 * 在静态检查之前做一层拦截，去覆盖字段检查中的异常部分
 * 当前主要为，厦门地区的营业执照非标检，后续增加IP等需要异步查询的功能
 */
async function getInterceptFuns({ redis, value, name, order, website }) {
    if (
        name === 'OrganizerLicenseId' &&
        isXiaMenCity(order.AreaId) &&
        order.OrganizerLicenseType === 1
    ) {
        // 厦门市的营业执照非标检
        try {
            let checkResult = await checkCompanyInfo({
                OrganizerName: order.OrganizerName,
                OrganizerLicenseId: value,
                redis,
            })
            if (checkResult !== true) {
                return '营业执照检查过程中出错'
            }
        } catch (e) {
            return '营业执照检查出错'
        }
    } else {
        return null
    }
}

/**
 * 判断是否为厦门市
 * @param {number} areaId - 区域ID
 * @returns {boolean} - 如果是厦门市返回true，否则返回false
 */
function isXiaMenCity(areaId) {
    let cityId = String(areaId).substring(0, 4)
    return cityId === '3502'
}

/**
 * 检查公司信息
 * @param {Object} params - 参数对象
 * @param {string} params.OrganizerName - 组织者名称
 * @param {string} params.OrganizerLicenseId - 组织者许可证ID
 * @param {Object} params.redis - Redis实例
 * @returns {Promise<boolean>} - 如果检查成功返回true，否则返回false
 */
async function checkCompanyInfo({ OrganizerName, OrganizerLicenseId, redis }) {
    // 入参中的OrganizerLicenseId，统一做大写转换
    OrganizerLicenseId = OrganizerLicenseId.toUpperCase()

    // 与公司名一起生成hash字符串
    const hash = crypto
        .createHash('md5')
        .update(OrganizerName + OrganizerLicenseId)
        .digest('hex')

    // 去Redis中查询
    let cache = await redis.get(hash)
    console.log(cache, 11, 'cache')
    if (cache) {
        // 如有，直接返回
        return JSON.parse(cache)
    }

    // 发出请求
    let resultINTYC = await ucloudinternalapi({
        Type: 'TYC_817',
        Source: 'ICP',
        CompanyName: OrganizerName,
        Backend: 'IdAuth',
        Action: 'GetBusinessInfoByTYC',
    })

    if (resultINTYC.RetCode !== 0) {
        throw new Error(resultINTYC.Message)
    }

    // 检查返回的营业执照号是否与入参一致
    let CheckResult =
        OrganizerLicenseId === resultINTYC.Result.DataSet.creditCode

    // 保存到Redis,同时设置一天的过期时间
    await redis.set(hash, JSON.stringify(CheckResult), 'EX', 24 * 60 * 60)

    return CheckResult
}

function stripOrderPrefix(checkResult, name) {
    // 有前缀特征值的直接返回原生提示，可以满足其他验证场景的提示
    for (const prefix of global.CONFIG.nativeOrderMessageFiled) {
        if (checkResult.startsWith(prefix)) {
            return checkResult.replace(prefix, '');
        }
    }
    return MainMap[name] + ': ' + checkResult
}
module.exports = {
    checkOrder,
    checkCompanyInfo,
    getPictures,
    checkPicture,
}
