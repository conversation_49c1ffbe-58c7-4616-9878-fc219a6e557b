/*
 * @Date: 2022-07-28 11:44:14
 * @LastEditors: l<PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2022-08-08 11:00:51
 * @FilePath: /newicp/fns/cross_border_apply/VerifyIdenityTwo.js
 */
const CheckIdenityTwo = require('../../methods/common/CheckIdenityTwo')
const logger = require('../../libs/logger')
const uuid = require('uuid/v4')
module.exports = async ({ CompanyId, Name, LicenseId }) => {
    let UUID = uuid()
    let Source = 'CrossBorder'
    let Error = ''
    try {
        let method1 = new CheckIdenityTwo((retCode, data) => {
            if (retCode === 0) {
                // 对比结果
                if (!data.IsMatch) {
                    Error = data.Message ?? '姓名与身份证号不匹配'
                }
            } else {
                Error = '身份证二要素匹配失败' + data.Message
            }
        })
        let m1JSON = {
            CompanyId,
            Name,
            LicenseId,
            Source,
            UUID,
        }
        await method1.exec(m1JSON)

        if (Error !== '') {
            return {
                IsMatch: false,
                Message: Error,
            }
        } else {
            return {
                IsMatch: true,
                Message: '匹配成功',
            }
        }
    } catch (err) {
        logger.getLogger('access').error('验证个人二要素出错' + err)
    }
}
