'use strict'

/* 验证营业执照（公司名称，统一信用代码，法人姓名）与输入是否一致
 * 同时验证 身份证照片 与 个人二要素（姓名，证件号）是否一致
 * 并将结果写入 对应的Error字段，方便审核员审核，若请求失败，则写入请求失败
 * @Date: 2022-07-28 11:44:14
 * @LastEditors: l<PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2022-08-02 18:30:15
 * @FilePath: /newicp/fns/cross_border_apply/VerifyAndUpdateInfo.js
 */
const {
    CrossBorderApplyModel,
    ApplyStatusEnum,
    CrossBorderAuditLogModel,
} = require('../../models')
const GetIdenityOCRInfo = require('../../methods/common/GetIdenityOCRInfo')
const CheckBusinessLicenseData = require('../../methods/common/CheckBusinessLicenseData')
const logger = require('../../libs/logger')
const uuid = require('uuid/v4')
module.exports = async ({
    Id,
    CompanyId,
    ManagerName,
    ManagerLicenseId,
    ManagerLicensePic,
    CompanyName,
    CompanyCode,
    LegalEntityName,
    BusinessLicensePic,
}) => {
    let UUID = uuid()
    let Source = 'CrossBorder'
    let Error = {}
    try {
        let method1 = new GetIdenityOCRInfo((retCode, data) => {
            if (retCode === 0) {
                // 对比结果
                if (!data.IsLegality) {
                    Error.ManagerLicensePic = '证件不合规，' + data.Message
                } else {
                    let errInfo = ''
                    if (ManagerName !== data.Info.Name) {
                        errInfo +=
                            '经办人名称与证件照不一致，证件照ocr值：' +
                            data.Info.Name
                    }
                    if (ManagerLicenseId !== data.Info.IdCardNumber) {
                        errInfo +=
                            '经办人身份证号与证件照不一致，证件照ocr值：' +
                            data.Info.IdCardNumber
                    }
                    if (errInfo !== '') {
                        Error.ManagerLicensePic = errInfo
                    }
                }
            } else {
                Error.ManagerLicensePic =
                    '经办人身份证图片ocr失败，' + data.Message
            }
        })
        let m1JSON = {
            CompanyId,
            PictureName: ManagerLicensePic,
            Name: ManagerName,
            IdCardNumber: ManagerLicenseId,
            Source,
            Side: 'Front',
            UUID,
        }
        let method2 = new CheckBusinessLicenseData((retCode, data) => {
            if (retCode === 0) {
                // 对比结果
                if (!data.IsMatch) {
                    Error.BusinessLicensePic =
                        '企业信息与营业执照不一致。' + data.Message
                }
            } else {
                Error.BusinessLicensePic = '营业执照ocr失败，' + data.Message
            }
        })
        let m2JSON = {
            CompanyId,
            CompanyName,
            CompanyCode,
            // LegalEntityName,
            BusinessLicensePic,
            Source,
            UUID,
        }

        await Promise.all([
            method1.exec(m1JSON).catch((err) => {
                Error.ManagerLicensePic = '经办人身份证图片ocr请求失败'
                logger
                    .getLogger('access')
                    .error('经办人身份证图片ocr请求失败' + err.message)
            }),
            method2.exec(m2JSON).catch((err) => {
                Error.BusinessLicensePic = '营业执照ocr请求失败'
                logger
                    .getLogger('access')
                    .error('营业执照ocr请求失败' + err.message)
            }),
        ])

        if (JSON.stringify(Error) !== '{}') {
            await CrossBorderApplyModel.update(
                {
                    Error,
                },
                {
                    where: {
                        Id,
                        Status: ApplyStatusEnum.Aduiting,
                    },
                }
            )
        }
    } catch (err) {
        logger
            .getLogger('access')
            .error('验证身份证三要素和企业证件照error' + err)
    }
}
