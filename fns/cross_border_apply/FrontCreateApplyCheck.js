/**
 * 针对境内渠道 上线前 需要做实名校验
 * 针对海外渠道 上线前需要做实名报备认证校验
 * 跨境 前置实名认证校验
 */
'use strict'
const ucloudinternalapi = require('../../libs/ucloudinternalapi')

/**
 * @description: 跨境 前置实名认证校验，需要：
 * 国内，需要企业实名
 * 海外，需要国内企业报备
 * 
 * 如果这个渠道不需要实名  然后做了报备
 * 然后又开启了实名  但是报备已经做了
 * 这个时候 我是看只要国内的企业身份信息 就允许 做跨境报备
 * 
 * 最终： 只要有国内的企业实名/报备信息 就认为有权限
 */
module.exports = async function ({ CompanyId, ChannelId }) {
    let authInfo = await ucloudinternalapi({
        Action: 'GetMainlandUserAuthInfo',
        Backend: 'NewIdAuth',
        ChannelId,
        CompanyId,
    })

    if (authInfo.RetCode !== 0) {
        let e = new Error('获取企业实名信息失败')
        e.code = 67029
        throw e
    } else {
        if (authInfo.Data !== null && authInfo.Data?.AuthType === '企业认证' && authInfo.Data?.AuditState === '已认证') {
            return true
        } else {
            return false
        }
    }
}
