const validator = require('validator')
const _ = require('underscore')
const {
    IdentityCodeValid,
    Tyshyxdm,
    Regex_BusinessLicenseNumber,
} = require('./validateRule')
const isNotEmpty = ({value}) => {
    if (_.isNumber(value) || _.isBoolean(value)) return true
    return !_.isEmpty(value) ? true : '不能为空'
}
const { pickMainDomain, getDomainInfo, checkPictureIsInDataBase, getCompanyIdByOrderNo, judgeDomainTypeIsRight } = require('../../fns/kits')
const logger = require("../../libs/logger")

const domainCheck = (value, suffixs = []) => {
    const hasProtocol = /https?:\/\//.test(value)
    if (hasProtocol) {
        return '域名不能包含http(s)'
    }
    const has3W = /^www\./.test(value)
    if (has3W) {
        return '请输入顶级域名（不包含www）'
    }
    const pass = validator.isFQDN(value)
    if (!pass) {
        return '请输入正确的域名'
    }
    // 确定是否是允许备案的域名
    let matchingSuffixs = []
    suffixs.forEach((suffix) => {
        if (!suffix.length) {
            // 后缀名单错误处理
            return '配制异常'
        }

        // let dotSuffix = '.' + suffix
        let lastIndex = value.lastIndexOf(suffix)

        // 包含后缀将匹配后缀暂存
        if (lastIndex > -1 && lastIndex + suffix.length === value.length) {
            matchingSuffixs.push(suffix)
        }
    })

    if (matchingSuffixs.length === 0) {
        return '请输入可备案的域名'
    }
    return true
}

const charCheck = ({value}) => {
    const validChar = /[:!@$%^&*'\\<>￥；]{1}/.exec(value) //'\s'是匹配空格的
    if (validChar) {
        return `不能包含以下特殊字符："${validChar.join(',')}"`
    }

    if (/^\s+|\s+$/.test(value)) {
        return '首尾不能出现空格'
    }
    return true
}

const remarkCheck = ({value}) => {
    if (value && value.trim().length === 0) {
        return '请输入有效字符'
    }
    return true
}

const blankCheck = ({value}) => {
    //空格校验
    if (/\s/.test(value)) {
        return `不能包含空格`
    }
    return true
}

const phoneCheck = ({value}) => {
    const pass = /^\d{7,8}$/.test(value)
    if (!pass) {
        return '请输入正确的电话号码'
    }
    return true
}

const numberCheck = ({value}) => {
    const hasNonNumber = /\D+/.test(value)
    if (hasNonNumber) {
        return '只能输入数字'
    }
    return true
}

const mobileCheck = ({value}) => {
    if (value) {
        value = value.trim() || ''
    }
    // const pass =
    //   /^(((13[0-9])|(147)|(15[0-3])|(15[5-9])|(166)|(17[0-3])|(17[5-8])|(18[0-9])|(19[8-9])|(191))+\d{8})$/.test(
    //     value
    //   );
    // 原来会验证手机的号码段，现在只验证11位数字
    const pass = /^\d{11}$/.test(value)
    if (!pass) {
        return '请输入正确的手机号码'
    }
    return true
}

const emailCheck = ({value}) => {
    const pass = validator.isEmail(value || '')
    if (!pass) {
        return '请输入正确的邮箱号码'
    }
    return true
}

const urlCheck = ({value = '', order, website}) => {
    const hasProtocol = /https?:\/\//.test(value)
    if (hasProtocol) {
        return '地址不能包含http(s)'
    }
    const urls = value.split(';')
    const domains = website['Domain'].map(d => "www." + d.Domain)
    for (let i = 0, l = urls.length; i < l; i++) {
        //必须以www开头
        const pass =
            /^(www.)(?:[\w\u4e00-\u9fa5]+[\w\u4e00-\u9fa5-]*\.)+(?:[\w\u4e00-\u9fa5]{2,7}){1}(\/.*)*(\?{1}.*)*$/.test(
                urls[i]
            )
        if (!pass) {
            return '请输入正确的url地址'
        }
        if (domains.indexOf(urls[i]) === -1) {
            return '首页地址后缀应包含在域名列表内'
        }
    }
    return true
}

const idCheck = ({value, key, order}) => {
    // let organizerLicenseTypes = [2, 41] //身份证，港澳台居民居住证
    const result = IdentityCodeValid(value)
    if (!result.pass) {
        return result.tip
    }
    return result.pass
}

// 身份证有效期校验
const idDateVaildCheck = ({value: inputDate}) => {
    // 定义正则表达式
    var regex = /^\d{4}\.\d{2}\.\d{2}$/

    dateArr = inputDate.split('-')
    const startDate = dateArr[0] || ''
    const endDate = dateArr[1] || ''

    // 使用正则表达式进行匹配
    if (
        !regex.test(startDate) ||
        (!regex.test(endDate) && endDate !== '长期')
    ) {
        return '身份证有效期格式不符合要求'
    }
    const currentDate = new Date()
    const startDateObj = new Date(startDate)
    const endDateObj = new Date(endDate)
    // 判断开始时间是否有效
    if (currentDate < startDateObj) {
        return '身份证尚未生效'
    }

    // 判断开始日期和结束日期是否有效
    if (
        startDateObj.toString() === 'Invalid Date' ||
        (endDateObj.toString() === 'Invalid Date' && endDate !== '长期')
    ) {
        return '日期格式无效'
    }

    // 判断结束日期是否为长期
    if (endDate === '长期') {
        return true
    }

    // 判断结束日期是否早于开始日期
    if (endDateObj < startDateObj) {
        return '结束日期早于开始日期'
    }

    return true
}

const ipCheckNew = (value = '') => {
    // var ipv4 = /^((25[0-5]|2[0-4]\d|1?\d?\d)\.){3}(25[0-5]|2[0-4]\d|1?\d?\d)$/;
    // var ipv6 = /^([\da-fA-F]{1,4}:){7}[\da-fA-F]{1,4}$/
    if (validator.isIP(value, 4) || validator.isIP(value, 6)) {
        return true
    } else {
        return '请输入正确的ip地址'
    }
}

const ipCheck = ({value: ips = []}) => {
    for (let i = 0, l = ips.length; i < l; i++) {
        // const pass = /^((25[0-5]|2[0-4]\d|1?\d?\d)\.){3}(25[0-5]|2[0-4]\d|1?\d?\d)$/.test(ips[i]);
        if (ipCheckNew(ips[i]) !== true) {
            return '请按说明填写正确的IP格式'
        }
    }
    return true
}

const creditCodeCheck = ({value}) => {
    const check = new Tyshyxdm()
    if (!check.verify(value)) {
        return '请输入正确的主办单位证件号码'
    }
    return true
}
// 营业执照证件号校验
const organizerLicenseIdCheck = ({value}) => {
    if (value && value.length > 15) {
        return creditCodeCheck({value})
    } else {
        let businessLicenseNumberCheck = new Regex_BusinessLicenseNumber()
        return businessLicenseNumberCheck.isBusinesslicense(value)
    }
}

const requiredCheckForWebsite = (key) => (value) => {
    return isNotEmpty(value)
        ? true
        : `${global.CONFIG.WebColMap[key]}不能为空！`
}

const typeCheckForWebsite = (key, type) => ({value}) => {
    if (!_[`is${type}`](value)) {
        return `${global.CONFIG.WebColMap[key]}必须为${type}`
    }
    return true
}

const hasLastOneMainDomain = ({value: domains}) => {
    const mainDomain = _.filter(domains, (domain) => domain.IsMain)
    if (mainDomain.length !== 1) {
        return '必须且只能包含一个主域名'
    }
    return true
}

const personalWebNameCheck = ({value}) => {
    if (
        /公司|论坛|交流|视频|游戏|官网|影视|医疗|教育|新闻|资讯|购物|金融|股票|基金|资讯|投资|交易|支付|出版/.test(
          value
        )
      ) {
        return "您提交的网站名称已超过个人备案范畴";
      }
      return true;
}

const appPlatInfoDomainCheck = ({value: AppPlatformInformationList, order, website}) => {
    let Domains = website.Domain.map(d => d.Domain)
    let platDomain = new Set()
    AppPlatformInformationList.forEach(platInfoList => {
        platInfoList.AppPlatformDomains?.forEach(d => platDomain.add(d))
    })
    platDomain = Array.from(platDomain)
    console.log(platDomain, Domains, 'appPlatInfoDomainCheck111')
    for (let domain of Domains) {
        if (!platDomain.includes(domain)) {
            return `域名 ${domain} 未指定属于哪个平台`
        }
    }
    for (let domain of platDomain) {
        if (!Domains.includes(domain)) {
            return `域名 ${domain} 不在APP全部域名范围`
        }
    }
    return true
}
// 判断证件号是否属于某个地区
const cardIdBeloneAreaCheck = ({})

/**
 * @description: 检查域名是否符合标准
 * @param {*} domains
 * @param {*} isRequiredPicture
 * @return {*}  true | string 如果返回true，表示检查通过，否则返回错误信息
 */
const domainFormatCheck = ({value: domains = [], order, website, ICPConfig}) => {
    for (let i = 0, l = domains.length; i < l; i++) {
        const domain = domains[i]
        if (!_.isObject(domain)) {
            return '域名格式错误'
        }
        const domainCheckResult = domainCheck(domain.Domain, ICPConfig.EffectiveDomain)

        if (domainCheckResult !== true) {
            return domainCheckResult
        }
    }
    return true
}
/**
 * @description: 检查APP全部域名是否符合三级域名标准
 * @param {*} domains
 * @param {*} isRequiredPicture
 * @return {*}  true | string 如果返回true，表示检查通过，否则返回错误信息
 */
const thirdDomainCheck = ({value: domains = [], order, website, ICPConfig}) => {
    for (let i = 0, l = domains.length; i < l; i++) {
        const domain = domains[i].Domain
        let mainDomain = pickMainDomain(domain, ICPConfig.EffectiveDomain)
        let subDomain = domain.slice(0, -mainDomain.length)
        // 为空说明传的是主域名
        if (subDomain === "") {
            return true
        }
        let subDomainStrArray = subDomain.split('.')
        if (subDomainStrArray.length >3 ) {
            return "APP域名仅支持备案到三级域名。例：example.cn为一级域名（主域名），a.example.cn为二级域名，a.b.example.cn为三级域名"
        }
    }
    return true
}
/**
 * @description: 检查域名实名信息截图是否合法
 * @param {*} domains
 * @param {*} isRequiredPicture
 * @return {*}  true | string 如果返回true，表示检查通过，否则返回错误信息
 */
const domainAuthenticationCheck = async ({value: domains = [], order, website, ICPConfig}) => {
    // 获取公司id
    let companyId
    if (order.CompanyId && (order.CompanyId !== 0 || order.CompanyId !== -1)) {
        companyId = order.company_id
    } else {
        companyId = await getCompanyIdByOrderNo(order.OrderNo)
    }
    logger.getLogger("access").info("domainAuthenticationCheck the company id is:",companyId)
    if (companyId === 34278) {
        return true
    }

    const nowTimeStamp = Math.floor(Date.now() / 1000);
    for (let i = 0, l = domains.length; i < l; i++) {
        const domain = domains[i].Domain
        let mainDomain = pickMainDomain(domain, ICPConfig.EffectiveDomain)
        // 请求获取对应的域名信息，这里不报错，获取不到就让他上传，获取到了就正常判断
        try {
            let domainData = await getDomainInfo(mainDomain)
            let domainInfo = domainData.Data
            if (domainInfo.length === 0) {
                // 如果不存在，判断有没有上传截图
                if (!domains[i].domainAuthenctionPicture || domains[i].domainAuthenctionPicture.length === 0 || !await checkPictureIsInDataBase(domains[i].domainAuthenctionPicture)) {
                    return `域名:${domain}未在我司注册，请上传其顶级域名实名认证信息截图，需要包含域名、域名所有者、实名证件号码`
                }
            } else {
                if (domainInfo[0].Uname1 === order.OrganizerName && domainInfo[0].OrganCode === order.OrganizerLicenseId && judgeDomainTypeIsRight(domainInfo[0].IdType, order.OrganizerLicenseType)) {
                    return true
                } else if (domainInfo[0].Uname1 === order.PICMainName && domainInfo[0].OrganCode === order.PICMainLicenseId && judgeDomainTypeIsRight(domainInfo[0].IdType, order.PICMainLicenseType)) {
                    return true
                } else if (domainInfo[0].EndTime < nowTimeStamp){
                    return `域名:${domain}已过期，过期域名不可备案，请您续费域名或更换域名重新提交`
                } else {
                    return `域名:${domain}实名信息与您提交的备案主体或主体负责人信息不一致，请核实或域名过户后重新提交`
                }
            }
        } catch (e) {
            logger.getLogger('error').error(`Domain authentication failed, the err is:${e}`)
            return "域名实名截图核验出错，请联系管理员"
        }
    }
    return true;
}
/**
 * @description: 检查主体负责人手机号和网站负责人手机号
 * @param {*} domains
 * @param {*} isRequiredPicture
 * @return {*}  true | string 如果返回true，表示检查通过，否则返回错误信息
 */
const picAndWebPhoneCheck = ({value, order, website, ICPConfig}) => {
    // 主体负责人手机号和应急电话不能相同
    // 网站负责人手机号码和应急电话不能相同
    // 主体负责人和网站负责人是同一人
    //   如果是同一人两者的手机号必须相同
    //   网站负责人手机号不能和主体负责人应急电话相同
    //   网站负责人应急电话不能和主体负责人手机号相同
    //   网站负责人应急电话可以和主体负责人应急电话相同
    //   网站负责人手机号不能和网站负责人应急电话相同
    // 主体负责人和网站负责人不是同一人
    //   网站负责人手机号不能和主体负责人手机号相同
    //   网站负责人手机号不能和主体负责人应急电话相同
    //   网站负责人应急电话不能和主体负责人手机号相同
    //   网站负责人应急电话可以和主体负责人应急电话相同
    const checkResultPrefix = 'picAndWebPhoneCheck'
    let PICMainMobile = order.PICMainMobile || ''
    let PICMainName = order.PICMainName || ''
    let EmergencyPhone = order.EmergencyPhone || ''
    logger.getLogger("access").info(`picAndWebPhoneCheck the PICMainName: ${PICMainName}, PICMainMobile: ${PICMainMobile}, EmergencyPhone: ${EmergencyPhone}`)
    if (PICMainMobile === EmergencyPhone) {
        return checkResultPrefix + `主体负责人${PICMainName}的应急电话不能与手机号相同`
    }
    // 获取网站负责人手机号，网站负责人名称
    for (let webInfo of website) {
        logger.getLogger("access").info(`picAndWebPhoneCheck the web PICName:${webInfo.PICName}, Mobile:${webInfo.Mobile}, EmergencyPhone:${webInfo.EmergencyPhone}`)
        if (webInfo.EmergencyPhone === webInfo.Mobile) {
            return checkResultPrefix + `网站${webInfo.Name}负责人${webInfo.PICName}的应急电话不能与手机号相同`
        }
        // 主体负责人和网站负责人是同一人，手机号必须相同，但两者的应急电话不能与手机号相同
        if (webInfo.PICName === PICMainName) {
            if (webInfo.Mobile !== PICMainMobile) {
                return checkResultPrefix + `网站${webInfo.Name}负责人${webInfo.PICName}的手机号必须与主体负责人${PICMainName}的手机号一致`
            }
            if (webInfo.EmergencyPhone === PICMainMobile) {
                return checkResultPrefix + `网站${webInfo.Name}负责人${webInfo.PICName}的应急电话不能与主体负责人${PICMainName}的手机号相同`
            }
        } else {
            // 主体负责人和网站负责人不是同一人主体负责人和网站负责人手机号不能相同，应急电话不可以与主体负责人和网站负责人手机号相同，但是两者的应急电话可以相同
            if (webInfo.Mobile === PICMainMobile) {
                return checkResultPrefix + `网站${webInfo.Name}负责人${webInfo.PICName}的手机号不能与主体负责人${PICMainName}的手机号相同`
            }
            if (webInfo.EmergencyPhone === PICMainMobile) {
                return checkResultPrefix + `网站${webInfo.Name}负责人${webInfo.PICName}的应急电话不能与主体负责人${PICMainName}的手机号相同`
            }
            if (webInfo.Mobile === EmergencyPhone) {
                return checkResultPrefix + `网站${webInfo.Name}负责人${webInfo.PICName}的手机号不能与主体负责人的应急电话相同`
            }
        }
    }
    return true
}
/**
 * @description: 检查域名的结构中有没有域名证书图片，进入这个步骤的前提是触发了相关的配置
 * @param {*} domains
 * @return {*} true | string 如果返回true，表示检查通过，否则返回错误信息
 */
const checkCerificationPicture = (domains = []) => {
    for (let i = 0, l = domains.length; i < l; i++) {
        const pictureCheckResult = requiredCheckForWebsite(
            'CerificationPicture'
        )(domains[i].CerificationPicture)

        if (pictureCheckResult !== true) {
            return '请上传域名证书图片'
        }
    }
    return true
}

/**
 * @description: 网站备案号与主体备案号检查，防止凡科上传的备案号没有关联
 * @param {string} value  网站备案号的值
 * @param {string} key  “ICPWebNo"
 * @param {object} order  整个订单
 * @return {boolean}
 */
const icpWebNoCheck = ({value, key, website, order}) => {
    // 备案类型不为3与7时，直接返回true;既只检查3与7的备案号
    if (order.Type != 3 && order.Type != 7) {
        return true
    }
    if (order.ICPMainNo) {
        if (order.ICPMainNo === value) {
            return '网站备案号与主体备案号不能一致'
        } else if (new RegExp(order.ICPMainNo).test(value)) {
            return true
        } else {
            return '网站备案号与主体备案号不匹配'
        }
    } else {
        return '请先填写主体备案号'
    }
}
// 网站手机号复杂性校验，网站负责人不同时，手机号不能相同，相同时，手机号必须相同 此处的value为order的website数组
const mobileComplicatedVerfiy = ({value, key, website, order}) => {
    if (value.length === 1) {
        return true
    }
    let pass = true
    for (let i = 0; i < value.length - 1; i++) {
        for (let j = i + 1; j < value.length; j++) {
            if (value[i].LicenseId === value[j].LicenseId && value[i].Mobile !== value[j].Mobile) {
                pass = `网站"${value[i].Name}" 与 网站"${value[j].Name}" 负责人相同时，负责人手机号必须一致`
            } else if (value[i].LicenseId !== value[j].LicenseId && value[i].Mobile === value[j].Mobile) {
                pass = `网站"${value[i].Name}" 与 网站"${value[j].Name}" 负责人不同时，不能填写同一手机号`
            }
            if (pass !== true) {
                break
            }
        }
        if (pass !== true) {
            break
        }
    }
    return pass
}
module.exports = {
    alwaysPass: true,
    isNotEmpty,
    domainCheck,
    ipCheckNew,
    icpWebNoCheck,
    ipCheck,
    charCheck,
    hasLastOneMainDomain,
    remarkCheck,
    blankCheck,
    numberCheck,
    mobileCheck,
    emailCheck,
    checkCerificationPicture,
    domainFormatCheck,
    urlCheck,
    idCheck,
    organizerLicenseIdCheck,
    idDateVaildCheck,
    mobileComplicatedVerfiy,
    personalWebNameCheck,
    appPlatInfoDomainCheck,
    thirdDomainCheck,
    domainAuthenticationCheck,
    picAndWebPhoneCheck,
}
