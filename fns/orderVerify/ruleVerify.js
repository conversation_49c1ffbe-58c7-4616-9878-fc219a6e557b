/**
 * 备案订单规则校验解析func
 * rule为类sequelize写法的校验
 * 包含了 Condition，Condition为前提条件，若满足条件时，对Value校验
 * 规则格式为
{
	"main": {
		"key1": [{
			"Condition": {}, // 条件
			"ValueRequest": {} // 值校验
		}],
		"key2": [{
		}]
	},
	"web": {
		"key1": [{
		}],
		"key2": [{
		}]
	}
}
    key1、key2代表要验证的字段名称
    Condition代表触发验证的条件
    解释：
    Condition 为 {} 表示没有条件，直接校验
    1.单个条件， Condition: { "xxx": {"ccc": "vvv"}}
    xxx格式为 ICP.AAA Web.BBB（当校验字段为网站字段时才有此条件） 指定当ICP的AAA字段怎样时，或者当该网站的字段怎样时
    ccc为枚举可选为 ["equal", "notEqual", "in", "notIn"]
    vvv的格式为 vvv类型可以为 number / string / array ,当ccc为 equal 或者 notEqual时，vvv可以为变量变量首字母使用$表示
    a.当值为array时，array中的值只能为常量，array值的范围为 xxx的可选值（如，xxx为ICP.Type时， vvv的可选值只能为各种备案类型的值）
    b.当vvv的类型为string时，vvv的值可以是 （xxx的可选值、某个变量、或者自定义值）（例如： 常量 "1",1, "aaa", 变量："$ICP.Phone", "$ICP.Email", "$Web.XXX"）
    2.多个条件时， 
    a. 多个条件都满足 Condition: {"xxx1": {"ccc1": "vvv1"}, "xxx2":{"ccc2": "vvv2" }}
    b. 只满足xxx1 或者xxx2其中一个条件 Condition: { "or": {"xxx1": {"ccc1": "vvv1"}, "xxx2": {"ccc2": "vvv2" }} }
    c. 满足 xxx1 或者xxx2其中一个条件 并且满足xxx3条件 Condition: { "or": {"xxx1": {"ccc1": "vvv1"}, "xxx2": {"ccc2": "vvv2" }}, "xxx3": {"ccc3": "vvv3"} }
    xxx和vvv的格式 以及值的表达 和单个条件时一致

    ValueRequest代表对value的校验
    ValueRequest: [{ 
        "key": "xxx"
        "value": "vvv",
        "tips": "提示信息"
    }]
    xxx可以为 equal(等于) notequal(不等于) in(在value内) notin(不在value内) func(使用func校验) length(长度等于)lengthMoreThan(长度大于) lengthLessThan（长度小于）
    vvv 可以为 string array
    触发条件，则执行Value检查， 不触发条件不检查
 * }
 */
/**
 *
 * @param {*} value 当前检查的值， order当前订单，包含网站字段，website，若检查网站的字段，website为当前网站obj, rule为当前检查字段设置的rule
 * @returns
 */
const checkFunc = require('./baseCheckFunc')

module.exports = async function ({ value, key, order, website, rules }) {
    let Pass = false // 是否通过检验
    for (let rule of rules) {
        let CondTrigger = false // 是否触发条件标记
        if (!rule.Condition || JSON.stringify(rule.Condition) === '{}') {
            // 无条件，直接校验
            CondTrigger = true
        } else {
            let conds = Object.keys(rule.Condition)
            for (let i = 0; i < conds.length; i++) {
                if (
                    conds[i].toLowerCase() === 'or' ||
                    conds[i].toLowerCase() === 'and'
                ) {
                    let cond = rule.Condition[conds[i]]
                    for (let key of cond) {
                        CondTrigger = parseMsg(key, cond[key], order, website)
                        if (!CondTrigger && conds[i].toLowerCase() === 'and') {
                            break // 任一个条件没触发 就没触发条件
                        }
                        if (CondTrigger && conds[i].toLowerCase() === 'or') {
                            break // 任一个条件触发 就触发条件
                        }
                    }
                } else {
                    CondTrigger = parseMsg(
                        conds[i],
                        rule.Condition[conds[i]],
                        order,
                        website
                    )
                    if (CondTrigger === false) {
                        break // 最外层是并行判断， 一个为false，就没触发到条件，可以不校验了
                    }
                }
            }
        }
        console.log(CondTrigger, 'CondTrigger')
        if (CondTrigger) {
            // 触发条件，解析要验证的内容，验证
            for (let verifyItem of rule['ValueRequest']) {
                Pass = false // 是否通过检验,初始为不通过
                let key = verifyItem['key']
                let verifyVal = verifyItem['value']
                let tips = verifyItem['tips']
                // 判断要对比的值的类型
                switch (key) {
                    case 'equal':
                        if (typeof verifyVal === 'number') {
                            // int 类型
                            if (value === verifyVal) Pass = true
                        } else {
                            // string
                            if (
                                verifyVal.length > 5 &&
                                verifyVal.substring(0, 5) ===
                                    '$ICP.'
                            ) {
                                // 检查对比的对象为变量,ICP中的值
                                let col = verifyVal.substring(5)
                                if (value === order[col]) Pass = true
                            } else if (
                                verifyVal.length > 5 &&
                                verifyVal.substring(0, 5) ===
                                    '$Web.'
                            ) {
                                // 检查对比的对象为变量,Web中的值
                                let col = verifyVal.substring(5)
                                if (value === website[col]) Pass = true
                            } else {
                                //  检查对比的对象为常量
                                if (value === verifyVal)
                                    Pass = true
                            }
                        }
                        break
                    case 'notequal':
                        if (typeof verifyVal === 'number') {
                            // int 类型
                            if (value !== verifyVal) Pass = true
                        } else {
                            if (
                                verifyVal.length > 5 &&
                                verifyVal.substring(0, 5) ===
                                    '$ICP.'
                            ) {
                                // 检查对比的对象为变量,ICP中的值
                                let col = verifyVal.substring(5)
                                if (value !== order[col]) Pass = true
                            } else if (
                                verifyVal.length > 5 &&
                                verifyVal.substring(0, 5) ===
                                    '$Web.'
                            ) {
                                // 检查对比的对象为变量,Web中的值
                                let col = verifyVal.substring(5)
                                if (value !== website[col]) Pass = true
                            } else {
                                //  检查对比的对象为常量
                                if (value !== verifyVal)
                                    Pass = true
                            }
                        }
                        break
                    case 'in':
                        if (verifyVal.includes(value))
                            Pass = true
                        break
                    case 'notin':
                        if (Array.isArray(verifyVal)) {
                            // 常量直接对比
                            if (!verifyVal.includes(value))
                                Pass = true
                        } else {
                            Pass = true
                        }
                        break
                    case 'length':
                        if (
                            Array.isArray(value) &&
                            value.length === verifyVal
                        ) {
                            Pass = true
                        }
                        break
                    case 'lengthMoreThan':
                        if (
                            Array.isArray(value) &&
                            value.length > verifyVal
                        ) {
                            Pass = true
                        }
                        break
                    case 'lengthLessThan':
                        if (
                            Array.isArray(value) &&
                            value.length < verifyVal
                        ) {
                            Pass = true
                        }
                        break
                    case 'func':
                        // 一些通用的func校验。根据func名字，执行检查
                        let funcName = verifyVal
                        console.log(funcName, 'funcName')
                        if (checkFunc[funcName]) {
                            Pass = checkFunc[funcName]({
                                value,
                                order,
                                website,
                                ICPConfig: global.CONFIG.ICPConfig,
                            })
                        } else {
                            // func不存在的情况直接通过
                            Pass = true
                        }
                        break
                    case 'awaitFunc':
                        // 一些通用的func校验。根据func名字，执行检查
                        let awaitFuncName = verifyVal
                        console.log(awaitFuncName, 'awaitFuncName')
                        if (checkFunc[awaitFuncName]) {
                            Pass = await checkFunc[awaitFuncName]({
                                value,
                                order,
                                website,
                                ICPConfig: global.CONFIG.ICPConfig,
                            })
                        } else {
                            // func不存在的情况直接通过
                            Pass = true
                        }
                        break
                    case 'default':
                        // 没有匹配到任何，默认通过
                        Pass = true
                        break
                }
                // 此处返回具体某个校验的tips
                if (Pass !== true) {
                    // 有一个规则，没有通过，就结束校验
                    return Pass || tips || '' //有一个不通过 就跳出循环
                }
            }
        } else {
            Pass = true // 验证通过
        }
    }
    if (Pass === true) {
        return true
    }
}
// 根据条件的的key value 解析出
// Target(ICP)
function parseMsg(key, value, order, website) {
    let CondTrigger = false
    let Target =
        key.substring(0, 4) === 'ICP.'
            ? 'ICP'
            : key.substring(0, 4) === 'Web.'
            ? 'Web'
            : ''
    let Field = '',
        OriginValue = ''
    switch (Target) {
        case 'ICP':
            Field = key.substring(4)
            if (Field === 'AreaId') {
                OriginValue =
                    parseInt(order[Field].toString().substring(0, 2) || 0) *
                    10000 // 提取Value到省
            } else {
                OriginValue = order[Field]
            }
            break
        case 'Web':
            Field = key.substring(4)
            OriginValue = website[Field]
            break
        default:
            Field = key
            OriginValue = website[Field]
    }
    for (let judge in value) {
        let condStr = value[judge]
        switch (judge) {
            // 只有等于和不等于能使用变量，其他都必须为常量
            case 'equal':
                // 等于
                if (typeof value[judge] !== 'number') {
                    condStr =
                        value[judge].indexOf('$ICP.') !== -1
                            ? order[value[judge].split('$ICP.')[1]]
                            : value[judge].indexOf('$Web.') !== -1
                            ? website[value[judge].split('$Web.')[1]]
                            : value[judge]
                }
                console.log(Field, OriginValue, condStr, 'equal')
                console.log(
                    typeof value[judge],
                    typeof OriginValue,
                    'typeof value[judge] equal'
                )
                console.log(
                    value[judge] == OriginValue,
                    'value[judge] === OriginValue equal'
                )
                console.log(
                    condStr == OriginValue,
                    'condStr === OriginValue equal'
                )
                if (condStr === OriginValue) {
                    CondTrigger = true
                }
                break
            case 'notEqual':
                // 不等于
                if (typeof value[judge] === 'number') {
                    condStr = value[judge]
                } else {
                    condStr =
                        value[judge].indexOf('$ICP.') !== -1
                            ? order[value[judge].split('$ICP.')[1]]
                            : value[judge].indexOf('$Web.') !== -1
                            ? website[value[judge].split('$Web.')[1]]
                            : value[judge]
                }
                if (condStr !== OriginValue) {
                    CondTrigger = true
                }
                break
            case 'in':
                // 包含
                if (condStr.includes(OriginValue)) {
                    CondTrigger = true //只要有一个满足 就触发条件，验证
                }
                break
            case 'notIn':
                // 不包含
                if (!condStr.includes(OriginValue)) {
                    CondTrigger = true //只要有一个满足 就触发条件，验证
                }
                break
        }
    }
    return CondTrigger
}
