/**
 * @file funs 审核侧，订单更新类操作
 * <AUTHOR>
 * @return
 */
const { Op } = require('sequelize')
const _ = require('lodash')
const { parseJSON } = require('../kits')
const { ICPWebPlatInfoModel } = require('../../mongoModels/icp')
const { LogModel } = require("../../models")
const logger = require('../../libs/logger')

function cancelRecordSuccess(order, icpModel, webModel) {
    // 取消类备案，成功后的操作
    if (order.Type === 4) {
        // 注销主体，将此主体与网站统一执行注销
        return Promise.all([
            icpModel.update(
                { Status: 1 },
                {
                    where: { Id: order.ICPId },
                }
            ),
            webModel.update(
                { Status: 1 },
                {
                    where: { MainId: order.ICPId },
                }
            ),
        ])
    } else {
        // 注销网站与接入
        let mainId
        return (
            Promise.all([
                // 执行此备案网站的注销
                webModel.update(
                    { Status: 1 },
                    {
                        where: { Id: order.ICPWebId },
                    }
                ),
                webModel.findAll({
                    attributes: ['MainId'],
                    where: { Id: order.ICPWebId },
                }),
            ])
                // 下述操作作用：如果此网站为最后一条有效网站，则取消主体状态
                .then(([updateResult, webInfo]) => {
                    if (webInfo.length === 0) {
                        throw new Error('no this web record')
                    }
                    mainId = webInfo[0].MainId
                    // 确定当前主体，有没有未注销的网站
                    return webModel.count({
                        where: {
                            MainId: mainId,
                            Status: { [Op.notIn]: [1, 2] },
                        },
                    })
                })
                .then((webCountInThisICP) => {
                    if (webCountInThisICP === 0) {
                        return icpModel.update(
                            { Status: 1 },
                            {
                                where: { Id: mainId },
                            }
                        )
                    }
                })
        )
    }
}

// 执行对订单的审核状态更新
function updateOrderNextStatus(
    { OrderNo, nextStatus, remoteUser },
    orderModel
) {
    // 取消类备案，成功后的操作
    console.log({ OrderNo, nextStatus, remoteUser })
    return orderModel.update(
        {
            Status: nextStatus,
            // 删除错误信息
            Error: {},
            Auditor: remoteUser,
        },
        {
            where: { OrderNo },
        }
    )
}

// 变更类管局审核成功
async function changeRecordSuccess(order, icpModel, webModel, orderModel) {
    // 分三合一、主体、网站、接入（接入与网站，操作相同）
    // 功能描述，主体与网站的图片部分在此做同步，
    // 数据部分的同步，置空主体与网站的name后,由icp-hengan的订单任务，完成单项的同步

    // 三和一，都要更新，状态与图片与信息
    // 变更主体只需要更新主体图片，主体状态 要看是否还存在 新增的同时进行变更主体的订单 若有则不动主体的变更状态
    // 变更网站只需要更新对应网站图片
    // 变更接入无需更新

    // 暂时使用cron方式，todo,更新为MQ

    let icpUpdateJSON = {},
        webUpdateJSON = [],
        platInfoUpdateArr = []

    // 主体信息需要更新：三合一，主体变更
    if (order.Type === 7 || order.Type === 8) {
        // 获取图片
        // 调整状态与主办单位名置空
        icpUpdateJSON = _.pick(order, [
            'OrganizerLicensePicture',
            'ICPId',
            'OrganizerResidencePermitPicture',
            'PICMainLicensePicture',
            'OtherPicture',
        ])

        icpUpdateJSON.Status = 0
    }

    // 网站信息需要更新
    if (order.Type === 7 || order.Type === 9) {
        // CWebsiteId相对可靠谱，如此值不对，更新类操作是无法到管局的
        order.Website.forEach((website) => {
            let webJSON = _.pick(website, [
                'CWebsiteId',
                'ICPWebNo',
                'LicensePicture',
                'CurtainPicture',
                'AuthVerificationPicture',
                'OtherPicture',
                'InternetServiceType',
                'OperatingPlatformType',
                'ProvideSdkService',
                'UseThirdPartySdkServices',
                'UseThirdPartySdkServiceDetails',
                'AppIcon',
                'AppPlatformInformationList',
            ])
            webJSON.Status = 0
            webUpdateJSON.push(webJSON)
            if (website.InternetServiceType == 6) {
                platInfoUpdateArr.push(
                    webModel
                        .findOne({
                            where: {
                                CWebsiteId: website.CWebsiteId,
                                ICPWebNo: website.ICPWebNo,
                            }
                        })
                        .then(async (webInfo) => {
                            webInfo = parseJSON(webInfo)
                            await ICPWebPlatInfoModel.findOneAndUpdate(
                                { ICPWebId: webInfo.Id },
                                {
                                    $set: {
                                        AppPlatformInformationList:
                                            website.AppPlatformInformationList,
                                    },
                                },
                                { upsert: true }
                            )
                        })
                )
            }
        })
    }

    // 接入变更，更新网站内容与状态，其它不变
    if (order.Type === 10) {
        webUpdateJSON.push({
            CWebsiteId: order.Website[0].CWebsiteId,
            ICPWebNo: order.Website[0].ICPWebNo,
            Status: 0,
        })
    }

    // 如果是 变更主体（因为变更主体 允许新增+变更主体） 查看是否还存在 新增时变更了主体的流程中订单
    if (order.Type === 8) {
        let orderExist = await orderModel.count({
            where: {
                Type: {
                    [Op.in]: [2, 3],
                },
                Status: {
                    [Op.ne]: 12,
                },
                ICPId: order.ICPId,
                NeedModifyIcp: 1,
                IsDeleted: 0,
            },
        })
        if (orderExist != 0) {
            // 不更新 主体状态 只更新其他信息就好了
            delete icpUpdateJSON.Status
        }
    }

    let promise = []

    if (JSON.stringify(icpUpdateJSON) !== '{}') {
        let ICPId = icpUpdateJSON.ICPId
        promise.push(
            icpModel.update(icpUpdateJSON, {
                where: { Id: ICPId },
            })
        )
    }

    if (webUpdateJSON.length !== 0) {
        webUpdateJSON.forEach((element) => {
            let CWebsiteId = element.CWebsiteId,
                ICPWebNo = element.ICPWebNo
            promise.push(
                webModel.update(element, {
                    where: { CWebsiteId, ICPWebNo },
                })
            )
        })
    }
    return Promise.all(promise.concat(platInfoUpdateArr))
}

// 新增类成功
async function addRecordSuccess(order, params, icpModel, webModel, orderModel) {
    // 从订单拉取全部数据全，执行次同步

    // 主体部分
    let icp = _.pick(order, [
        'AreaId',
        'OrganizerType',
        'OrganizerName',
        'OrganizerAddress',
        'OrganizerLicenseType',
        'OrganizerLicenseId',
        'OrganizerLicensePicture',
        'OrganizerResidencePermitPicture',
        'PICMainOfficePhone',
        'PICMainOfficePhonePre',
        'UnitSuperior',
        'OrganizerLicenseArea',
        'PICMainName',
        'EmergencyPhone',
        'PICMainMobile',
        'PICMainEmail',
        'PICMainLicenseType',
        'PICMainLicenseId',
        'PICMainLicenseDate',
        'PICMainLicensePicture',
        'PICMainQQ',
        'Remark',
        'ICPMainNo',
        'CompanyId',
        'OtherPicture',
    ])
    icp.CMainId = order.CMainId === -1 ? order.Id : order.CMainId
    // 默认新增 主体状态为0 若后面有变更相关的 再修改该状态值
    icp.Status = 0
    // 网站部分
    let websites = order.Website.map((website) => {
        // website.CWebsiteId = website.Id 只有新增类 需要设置网站的CWebsiteId 为订单网站信息的id
        if (website.InternetServiceType == 6) {
            website.Domain = website.Domain.filter(domain => domain.BelongToUs == 'true')
        }
        return _.pick(website, [
            'Id',
            'Name',
            'IP',
            'Url',
            'ServiceType',
            'ServiceContent',
            'Phone',
            'PreAppoval',
            'Language',
            'PICName',
            'EmergencyPhone',
            'Mobile',
            'Email',
            'LicenseType',
            'LicenseId',
            'LicenseDate',
            'LicensePicture',
            'QQ',
            'ICPWebNo',
            'CurtainPicture',
            'AppCode',
            'Remark',
            'Domain',
            'AuthVerificationPicture',
            'AppCode',
            'ConnectType',
            'BaseManageprovince',
            'OtherPicture',
            'RelevantPromiseLetter',
            'InternetServiceType',
            'OperatingPlatformType',
            'ProvideSdkService',
            'UseThirdPartySdkServices',
            'UseThirdPartySdkServiceDetails',
            'AppIcon',
            'AppPlatformInformationList',
        ])
    })

    if (!_.isEmpty(params.ICPMainNo)) {
        icp.ICPMainNo = icp.ICPMainNo || params.ICPMainNo
    }

    // 如果没有网站备案号，判断数据有问题
    if (!icp.ICPMainNo) {
        throw new Error('未找到主体备案号')
    }

    // 备案号与线上不一至
    if (websites.length) {
        if (
            order.Type !== 3 &&
            (!params.Website || params.Website.length !== websites.length)
        )
            throw new Error('备案中的网站长度与订单中的不一致')
    }

    // 为订单中的网站复加备案号
    if (params.Website) {
        _.each(params.Website, (websiteInfo) => {
            let { ICPWebNo, Id } = websiteInfo
            let website = _.find(websites, (website) => website.Id === Id)
            if (!website) {
                throw new Error('未找到匹配的网站：' + Id)
            }
            website.ICPWebNo = ICPWebNo
            if (!website.ICPWebNo) {
                throw new Error('未找到匹配的网站备案号')
            }
        })
    }

    // 两个新增类 同时带有变更主体的 考虑主体状态的变化
    if ([2, 3].includes(order.Type) && order.NeedModifyIcp) {
        // 判断是否还存在 变更主体(变更主体 或者新增并变更主体)的订单 若不存在 则将主体状态置为 0，否则则不变主体状态
        let orderCount = await orderModel.count({
            where: {
                OrganizerLicenseId: order.OrganizerLicenseId,
                IsDeleted: 0,
                Id: {
                    [Op.ne]: order.Id,
                },
                Status: {
                    [Op.ne]: 12,
                },
                [Op.or]: [
                    { NeedModifyIcp: 1, Type: { [Op.in]: [2, 3] } },
                    { Type: 8 },
                ],
            },
        })
        if (orderCount != 0) {
            icp.Status = 8
        }
    }
    // 已备案记录，如果存在匹配的，则更新，如果不存在，则创建记录
    // 完成ICP的查询与创建
    return icpModel
        .findAll({
            attributes: ['Id', 'Status', 'CompanyId'],
            where: { ICPMainNo: icp.ICPMainNo },
        })
        .then(async (icps) => {
            icps = parseJSON(icps)
            if (icps.length !== 0) {
                if (icps[0].Status !== 1) {
                    delete icp.CompanyId // 备案未注销不允许更新公司id，防止凡科直接提交原来已经存在的备案
                } else { // 如果发现是注销的，那么就更新了公司id，相当于进行了迁移
                    try {
                        // 将数据写到写到迁移表里面
                        let migrateLog = {
                            Action: 'DistributionICPRecord',
                            ICPId: icps[0].Id,
                            Operator: 'system',
                            Status: 0,
                            Params: JSON.stringify(icp),
                            CompanyId: icp.CompanyId,
                            Result: '成功',
                            SourceCompanyId: icps[0].CompanyId,
                            ICPMainNo: icp.ICPMainNo,
                        }
                        await LogModel.create(migrateLog)
                    } catch (e) {
                        logger.getLogger('error').error("failed to insert migration log, the err is:", e)
                    }
                }
                return icpModel
                    .update(icp, {
                        where: { Id: icps[0].Id },
                    })
                    .then(() => Promise.resolve(icps[0].Id))
            } else {
                // 如果备案记录不存在，创建
                return icpModel
                    .create(icp)
                    .then((createInfo) =>
                        Promise.resolve(parseJSON(createInfo).Id)
                    )
            }
        })
        .then((id) => {
            let promises = []
            websites.forEach((website) => {
                // 新增类设置Cid
                if ([1, 2, 3].includes(order.Type)) {
                    website.CWebsiteId = website.Id
                }
                delete website.Id

                // 为网站附加值icpId的
                website.MainId = id

                // 查询此记录是否在t_web中存在，如果存在，则update,不存在，则create
                promises.push(
                    webModel
                        .findAll({
                            where: { ICPWebNo: website.ICPWebNo },
                        })
                        .then(async (data) => {
                            data = parseJSON(data)
                            if (data.length !== 0) {
                                // 如果是网站更新，取订单的值更新原来的记录，网站名置空，置空与新增的方式一致由Cron重新拉取数据
                                website.Status = 0
                                await webModel.update(website, {
                                    where: { Id: data[0].Id },
                                })
                                return data[0].Id
                            } else {
                                let webInfo = await webModel.create(website)
                                return parseJSON(webInfo).Id
                            }
                        })
                        .then(async (Id) => {
                            if (website.InternetServiceType == 6) {
                                await ICPWebPlatInfoModel.findOneAndUpdate(
                                    {
                                        ICPWebId: Id,
                                    },
                                    {
                                        $set: {
                                            AppPlatformInformationList:
                                                website.AppPlatformInformationList,
                                        }
                                    },
                                    { upsert: true }
                                )
                            }
                        })
                )
            })
            return Promise.all(promises)
        })
}

module.exports = {
    cancelRecordSuccess,
    updateOrderNextStatus,
    addRecordSuccess,
    changeRecordSuccess,
}
