/**
 * @file funs 审核侧，订单一下的状态取值
 * <AUTHOR>
 * @return
 */

// 获取订单的下一个状态
function getNextStatus(status, action, type, Version = 1) {
    // 通用流程
    const generalMapping = {
        2: {
            resolve: 3,
            reject: 4,
        },
        7: {
            resolve: 8,
            reject: 9,
        },
        8: {
            resolve: 11,
            reject: 9,
        },
        11: {
            resolve: 12,
            reject: 13,
        },
        15: {
            resolve: 12,
            reject: 13,
        },
    }

    // 4.6后的简化流程
    let simpleMapping = {
        2: {
            resolve: 8,
            reject: 4,
        },
        7: {
            resolve: 8,
            reject: 9,
        },
        8: {
            resolve: 11,
            reject: 4,
        },
        11: {
            resolve: 12,
            reject: 13,
        },
        15: {
            resolve: 12,
            reject: 13,
        },
    }

    if (Version == 1) {
        if ((type === 8 || type === 10) && (status === 2 || status === 8)) {
            // 非普通流程是变更接入与主体，后续不复审的流程会引入
            return simpleMapping[status][action]
        }
        if (!generalMapping[status] || !generalMapping[status][action])
            return false

        return generalMapping[status][action]
    } else if (Version == 4.6) {
        if (
            (type === 8 || type === 10 || type === 1) &&
            (status === 2 || status === 8)
        ) {
            // 非普通流程是变更接入与主体，后续不复审的流程会引入
            return simpleMapping[status][action]
        }

        if (!simpleMapping[status] || !simpleMapping[status][action])
            return false

        return simpleMapping[status][action]
    }
}

module.exports = {
    getNextStatus,
}
