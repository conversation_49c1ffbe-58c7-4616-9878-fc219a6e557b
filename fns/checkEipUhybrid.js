const { checkIsIpWhite } = require('./checkIPisMatch')
const getIpInfo = require("./getIPInfo");
const ucloudinternalapi = require("../libs/ucloudinternalapi");
const Logger = require("../libs/logger")

const zoneMap = {
    0: 'pathx通用可用区',
    1: '浙江可用区A',
    1001: '北京一可用区A',
    //3001: '香港可用区A', // 去掉香港可用区，只需要境内资源
    //3002: '香港可用区B',
    4001: '北京二可用区B',
    5001: '北京二可用区C',
    7001: '广东可用区B',
    7101: '广东二可用区A',
    8001: '上海一可用区A',
    8002: '上海一可用区B',
    8003: '上海一可用区C',
    8100: '上海二可用区A',
    8200: '上海二可用区B',
    8300: '上海二可用区C',
    8400: '上海二可用区D',
    8401: '上海三可用区A',
    8501: '上海四可用区A',
    9001: '北京二可用区D',
    9002: '北京二可用区E',
    10019: '杭州可用区A',
    10020: '嘉兴可用区A',
    10021: '泉州可用区A',
    10027: '乌兰察布可用区A',
    110100: '浪潮华北一可用区A',
    110200: '浪潮华北二可用区A',
}

async function checkEipUhybrid(IPWhiteList, company_id, ips, cb) {
    try {
        company_id = parseInt(company_id)
        // 凡科账号不检查
        if (company_id === 34278) {
            return cb(0, {
                IsBelong: true,
                DataInfo: '通过核验',
            })
        }
        //检查是否在白名单内
        let [isIpWhite, res] = await checkIsIpWhite(
            IPWhiteList,
            company_id,
            ips
        )
        if (res) {
            return cb(isIpWhite, res)
        }
        let result = ''
        let ipsInfo = await getEipUhybridIpsInfo(ips)
        let UhostAndUhybridCount = await getUhostAndUhybridCount(company_id)
        Logger.getLogger('access').info(`the ipsInfo is ${JSON.stringify(ipsInfo)}, the UhostAndUhybridCount info is ${JSON.stringify(UhostAndUhybridCount)}`)
        // 混合云网段可以不存在物理机柜，在下面判断eip是否有云主机
        if (ipsInfo.length === 0) {
            return cb(67020, {
                IsBelong: false,
                Tip: '未检测到您备案账号下的任何资源，请确认是否已购买境内云主机及EIP或相关资源。如未购买，请先购买后再提交备案申请',
                DataInfo: '解析查询公司信息失败',
            })
        }
        for (let item of ipsInfo) {
            let checkResult = ''
            let isEipOrUhybrid = false
            if (item.RetCode !== 0) {
                return cb(67020, {
                    IsBelong: false,
                    Tip: 'IP资源找不到',
                    DataInfo: '解析查询公司信息失败',
                })
            }
            if (!item.Data || !item.Data.List || item.Data.List.length === 0) {
                checkResult += `未找到与 IP：${item.Ip}相关的资源信息，请确认该 IP 是否已正确绑定资源，或联系技术支持排查问题;`
            } else {
                for (let data of item.Data.List) {
                    // 如果该资源是eip或者混合云网段
                    if (data.ResourceType && (data.ResourceType === 102 || data.ResourceType === 10)) {
                        // ipsInfo也会返回其他资源类型,有一个资源类型是eip或者混合云网段则认为改ip是eip或者混合云网段
                        isEipOrUhybrid = true
                        // 如果该资源在境内
                        if (data.RegionId && zoneMap.hasOwnProperty(data.RegionId)) {
                            // 公司id不对
                            if (data.TopOrganizationId !== company_id) {
                                checkResult += `检测到 IP：${item.Ip}的资源归属公司与备案账号不匹配，请确认资源归属是否正确;`
                            } else {
                                // 根据资源类型判断是否有对应的资源
                                // 是eip但没有境内云主机
                                if (data.ResourceType === 10 && UhostAndUhybridCount.uhostCount === 0 && UhostAndUhybridCount.ulHostCount === 0 && UhostAndUhybridCount.upHostCount === 0) {
                                    checkResult += `检测到您的 EIP 资源：${item.Ip}未绑定境内云主机或无境内云主机，请先购买并绑定境内云主机后再提交备案申请;`
                                }  else {
                                    // 返回的资源信息中有一个符合条件的即可满足
                                    checkResult = ''
                                    break
                                }
                            }
                        } else {
                            // 不在境内添加错误信息
                            checkResult += `检测到您的资源：${item.Ip}不在境内，备案申请仅支持境内资源;`
                        }
                    }
                }
            }
            // 如果没有一个资源类型是eip或者混合云网段，则直接添加错误信息
            if (!isEipOrUhybrid) {
                checkResult += `检测到您的资源：${item.Ip}不是EIP或混合云网段;`
            }
            result += checkResult
        }
        if (result) {
            return cb(0, {
                IsBelong: false,
                Tip: result,
            })
        }
        return cb(0, {
            IsBelong: true,
            DataInfo: '通过核验',
        })
    } catch (e) {
        Logger.getLogger('error').error(`checkEipUhybrid err the err is ${e.message}`)
        cb(67020, {
            IsBelong: false,
            Tip: '请填写本账号下UCloud内地外网EIP资源或混合云外网IP',
            DataInfo: e.message || '解析查询公司信息失败',
        })
    }
}

async function getEipUhybridIpsInfo(ips) {
    try {
        let ipInfos = await getIpInfo(
            {
                Token: '361d3765-836a-400a-8ecb-025216762c7c',
                Action: 'Compass.IGetResourcesAndCompanyInfosByPublicIp',
                timeout: 60 * 1000,
                method: 'POST',
            },
            ips
        )
        return ipInfos
    } catch (e) {
        Logger.getLogger('error').error(`get ips ${JSON.stringify(ips)} info error: ${e.message}`)
        return []
    }
}

// 获取云主机和混合云机柜的数量
async function getUhostAndUhybridCount(companyId) {
    try {
        let result = {
            uhostCount: 0,
            ulHostCount: 0,
            upHostCount: 0
        }
        let uresourceResult = await ucloudinternalapi({
            Backend: 'UResource',
            Action: 'IGetResourceCount',
            TopOrganizationId: companyId,
            // 增加验证云主机，需要查询云主机资源
            ResourceType: ['1', '370', '20'],
            ZoneId: Object.keys(zoneMap), //只需要查境内可用区
        })
        if (uresourceResult.RetCode !== 0) {
            throw new Error(`Get the number of uhosts and uhybrid failed, the response is ${JSON.stringify(uresourceResult)}`)
        }
        for (let info of uresourceResult.Infos) {
            if (info.ResourceType === 1) {
                result.uhostCount++
            } else if (info.ResourceType === 370) {
                result.ulHostCount++
            } else if (info.ResourceType === 20) {
                result.upHostCount++
            }
        }
        return  result
    } catch (e) {
        Logger.getLogger('error').error(`get ${companyId} uhost and uhybrid count error: ${e.message}`)
        return {
            uhostCount: 0,
            ulHostCount: 0,
            upHostCount: 0
        }
    }
}

module.exports = {
    checkEipUhybrid
}