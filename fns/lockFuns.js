function lock(redlock, res, timeout = 3 * 1000) {
    return new Promise((rs, rj) => {
        redlock.lock(res, timeout, function (error, lockInstance) {
            error ? rj(error) : rs(lockInstance)
        })
    })
}

function unLock(redlock, lockInstance) {
    return new Promise((rs, rj) => {
        redlock.unlock(lockInstance, function (error, data) {
            console.log(error)
            error ? rj(error) : rs(data)
        })
    })
}

module.exports = {
    lock,
    unLock,
}
