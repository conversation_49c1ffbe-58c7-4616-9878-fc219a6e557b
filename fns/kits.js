const _ = require('lodash')
const punycode = require('punycode')
const { Op } = require('sequelize')
const csvtojson = require('csvtojson')
const crypto = require('crypto')
const axios = require('axios')
const moment = require('moment')
const netmask = require('netmask').Netmask
const domainLicenseTypeData = require('../configs/common/domain_license_type.json')
const organizerLicenseType = require('../configs/common/organizer_license_type.json')
const {
    OrderModel,
    PictureModel
} = require('../models')
const logger = require("../libs/logger")
const {
    checkIdCardValidDate,
    validateIdNumberToAgeYear,
} = require('../fns/verifyFun')
const axiosApi = require("../libs/axiosApi");
const { create } = require('xmlbuilder2')
const { parseStringPromise } = require('xml2js')

function mongoResultCheck(result) {
    return true
}

function getTableModel(tableName, db) {
    return require('../models/' + tableName)['model']
}

function getPunyDomain(domain) {
    // 中文域名punycode处理
    var pattern = new RegExp('[\u4E00-\u9FA5]+')
    return pattern.test(domain) ? punycode.toASCII(domain) : domain
}

function getVerifyResult(result, type, params) {
    // 判断与预期的正反面是否一致
    let errorMessage = ''
    switch (type) {
        case 0:
            // 身份证OCR

            if (!result.Info) {
                // 如果没有返回信息，抛出
                result.IsMatch = false
                result.Adults = false
                result.OCRMatch = false
                result.Message = 'OCR时未读到信息'
                return result
            }

            // 有返回信息，判断是否与预期的正反面一致
            if (ReplaceFirstUper(result.Info.Side) !== params.Side) {
                result.IsMatch = false
                result.Adults = false
                result.OCRMatch = false
                result.Effective = false
                result.Message =
                    '请按照第一张人像页，第二张国徽页的顺序，依次上传身份证证件图片。'
                return result
            }

            if (result.Info && result.Info.Side === 'back') {
                result.Effective = checkIdCardValidDate(result.Info.ValidDate)
                errorMessage = !result.Effective
                    ? errorMessage + '证件有效期异常'
                    : errorMessage
                errorMessage = !result.IsLegality
                    ? errorMessage +
                      '证件合规性异常，请使用原件拍摄,禁止使用复印、扫描件'
                    : errorMessage
                result.IsMatch = result.Effective && result.IsLegality

                if (params.LicenseDate && result.Info.ValidDate != params.LicenseDate) {
                    result.IsMatch = false
                    result.OCRMatch = false
                    errorMessage = errorMessage + '证件有效期与输入不一致'
                }
            }

            if (result.Info && result.Info.Side === 'front') {
                result.Adults = validateIdNumberToAgeYear(
                    result.Info.IdCardNumber
                )
                // 如果是正面，做OCR内容值的判断
                result.OCRMatch =
                    result.Info.Name === params.Name &&
                    result.Info.IdCardNumber === params.IdCardNumber

                // errorMeasage生成
                errorMessage = !result.Adults
                    ? errorMessage + '用户未成年'
                    : errorMessage
                errorMessage = !result.IsLegality
                    ? errorMessage +
                      '证件合规性异常，请使用原件拍摄,禁止使用复印、扫描件'
                    : errorMessage
                errorMessage = !result.OCRMatch
                    ? errorMessage + 'OCR姓名与证件Id不匹配'
                    : errorMessage

                result.IsMatch =
                    result.Adults && result.IsLegality && result.OCRMatch
            }

            if (errorMessage !== '') {
                result.Message = errorMessage
            }
            return result
        default:
            return result
    }
}

function parseJSON(jsonstring) {
    return JSON.parse(JSON.stringify(jsonstring))
}

function sleep(millseconds = 3000) {
    return new Promise((rs) => {
        setTimeout(() => rs(), millseconds)
    })
}

//将base64编码后的 csv转换为json
/**
 * 解析csv文件func
 * @param {*} csvFileString base64的文件数据
 * @param {*} checkMsg 检查读取的文件中包含的中文信息，以调整编码
 * @returns {Array}
 */
function csvParse(csvFileString, checkMsg) {
    let str = Buffer.from(
        csvFileString.split('data:text/csv;base64,')[1],
        'base64'
    )

    //目前只认为上传的文件编码会是GBK或者utf-8
    //如果转码成功则认为是GBK
    //否则则认为是utf-8
    let newstr = str.toString()
    
    if (!newstr.includes(checkMsg)) {
        return []
    }
    return new Promise((resolve, reject) => {
        csvtojson()
            .fromString(newstr)
            .then((res) => resolve(res))
            .catch((err) => reject(err))
    })
}

//求差集arr1-arr2
function arrayMinus(arr1, arr2) {
    var result = []
    arr1.forEach(function (x) {
        if (arr2.indexOf(x) === -1) {
            result.push(x)
        } else {
            return
        }
    })
    return result
}

// 数组，长度
function splitArray(array, subGroupLength) {
    let index = 0
    let newArray = []
    while (index < array.length) {
        newArray.push(array.slice(index, (index += subGroupLength)))
    }
    return newArray
}

// 首字母大写
function ReplaceFirstUper(str) {
    str = str.toLowerCase()
    return str.replace(/\b(\w)|\s(\w)/g, function (m) {
        return m.toUpperCase()
    })
}

// 统计对象的总数
function objectCount(o) {
    var t = typeof o
    if (t == 'string') {
        return o.length
    } else if (t == 'object') {
        var n = 0
        for (var i in o) {
            n++
        }
        return n
    }
    return false
}
// 统计对象的总数
function getICPMainNoByICPWebNo(ICPWebNo) {
    return _.dropRight(ICPWebNo.split('-')).join('-')
}

// 得到通用的，需要查询的对象
function getQueryObject(params) {
    let queryObject = {}
    console.log(
        params,
        params.BeginTime !== undefined && params.EndTime !== undefined
    )
    // 如果存在启止时间
    if (params.BeginTime !== undefined && params.EndTime !== undefined) {
        queryObject.CreateTime = {
            [Op.between]: [params.BeginTime, params.EndTime],
        }
        delete params.BeginTime
        delete params.EndTime
    }

    _.forEach(params, function (value, key) {
        if (value !== undefined) {
            queryObject[key] = value
        }
    })
    return queryObject
}
function trimObject(params) {
    let keys = Object.keys(params)
    _.forEach(keys, (k) => {
        if (typeof params[k] === 'string') {
            params[k] = params[k].trim()
        }
    })
    return params
}
function generateOrderNo() {
    let OrderNo = moment().format('OYYYYMMDDHHmmss')
    let random = parseInt(Math.random() * 9999)
        .toString()
        .padStart(6, 0)
    // 补零，补足六位
    return OrderNo + random
}

function decodeBase64Image(dataString) {
    let response = {}

    let matches = dataString.split(';base64,')
    if (matches.length !== 2) {
        return new Error('Invalid input string')
    }

    response.type = matches[0].replace('data:', '')

    response.data = Buffer.from(matches[1], 'base64')

    return response
}
function generateFileName(pic, type) {
    let hash = crypto.createHash('sha1')
    let sha1msg = hash.update(pic, 'utf8').digest('hex')
    return (
        sha1msg +
        '.' +
        (type.split('/')[1] === 'jpeg' ? 'jpg' : type.split('/')[1])
    )
}

async function convertUS3FileToBase64(fileUrl) {
    let bufferFile = await axios.get(fileUrl, {
        responseType: 'arraybuffer',
    })
    let picBase64 = Buffer.from(bufferFile.data).toString('base64')
    return picBase64
}

function checkImgType(filename) {
    //用文件名name后缀判断文件类型
    if (!/\.(jpg|jpeg|png|GIF|JPG|PNG)$/.test(filename)) {
        return false
    } else {
        return true
    }
}
/**
 * 判断IP是否在 网段内 网段支持单个网段 和多个网段
 * @param {*} IPSegs
 * @param {*} IP
 */
function checkIPInSeg(IPSegs, IP) {
    let res = false
    if (typeof IPSegs === 'string') {
        IPSegs = [IPSegs]
    }
    for (let IPSeg of IPSegs) {
        let block = new netmask(IPSeg)
        if (block.contains(IP)) {
            res = true
            break
        }
    }
    return res
}

function pickMainDomain(domain, suffixs) {
    if (!_.isString(domain) || !domain.length) {
        // null, ''
        throw new Error('输入的域名为空或者不是字符串')
    }

    let matchingSuffixs = []
    suffixs.forEach((suffix) => {
        if (!suffix.length) {
            // 后缀名单错误处理
            return;
        }

        if (suffix === domain) {
            // 'com.cn'
            matchingSuffixs.push(suffix)
            return
        }

        let dotSuffix = '.' + suffix
        let lastIndex = domain.lastIndexOf(dotSuffix)

        // 包含后缀将匹配后缀暂存
        if (lastIndex > -1 && lastIndex + dotSuffix.length === domain.length) {
            matchingSuffixs.push(suffix)
        }
    })

    if (!matchingSuffixs.length) {
        // 无匹配后缀 'a', ''
        let splitDomains = domain.split('.')
        let rootDomain = splitDomains.slice(-2).join('.')
        return rootDomain
    }

    // 获取最长匹配后缀
    let matchingSuffix = _.maxBy(matchingSuffixs, (suffix) => suffix.length)
    if (domain.length <= matchingSuffix.length + 1) {
        // 'com.cn', '.com.cn'
        throw new Error('输入的域名长度小于或等于匹配后缀的长度')
    }

    let splitDomains = domain
        .replace(new RegExp(`\\.${matchingSuffix}$`), '')
        .split('.')
    if (
        _.some(splitDomains, (domainStr) => {
            // ..com.cn a..com.cn  .a.com.cn
            return !domainStr.length
        })
    ) {
        throw new Error('输入的域名包含空字符串')
    }

    // 获取一级域名
    let rootDomain =
        splitDomains[splitDomains.length - 1] + '.' + matchingSuffix
    return rootDomain
}

// 获取我司域名信息
async function getDomainInfo (domain) {
    const getDomainInfoConfig = {
        method: 'post',
        url: global.CONFIG.getDomainInfoUrl,
        headers: {
            'Content-Type': 'application/json',
        },
        data: {
            Action: 'GetUdnrList',
            Backend: 'UdnrV2',
            data: {
                page: 1,
                result:10,
                Dn: domain,
            },
        },
    };
    let res = await axiosApi(getDomainInfoConfig)
    return res.data
}

// 检查图片是否在我司
async function checkPictureIsInDataBase(pictures) {
    let rows = await PictureModel.findAll({
        where: {
            Url: { [Op.in]: pictures },
        },
    })
    rows = rows.map((row) => row.Url)
    for (var i in pictures) {
        // 如果有图片不在结果里面，则返回错误上传图片
        if (_.indexOf(rows, pictures[i]) === -1 && pictures[i] !== global.CONFIG.PORT_UNIQUE) {
            return false
        }
    }
    return true
}

// 根据订单号获取公司id
async function getCompanyIdByOrderNo(orderNo) {
    try {
        let orders = await OrderModel.findAll({
            where: {
                OrderNo: orderNo
            },
            attributes: ['CompanyId'],
        })
        orders = parseJSON(orders)

        if (orders.length === 0) {
            logger.getLogger("access").info("the company id is not exist, the orderNo is:", orderNo)
            return 0
        } else {
            return orders[0].CompanyId
        }
    } catch (e) {
        logger.getLogger('error').error("get company id from orderNo failed, the err is :", e)
        return 0
    }
}

function judgeDomainTypeIsRight(domainLicenseType, judgeLicenseType) {
    // 域名特有的证件类型直接返回不做匹配
    if (domainLicenseType === "CTTYDMZ" || domainLicenseType === "GWJGZJ") {
        return true
    }
    // 我们备案特有的证件类型也不做匹配直接返回
    let judgeLicenseTypeWhiteList = [35, 43, 44, 45, 46, 47, 48, 49]
    if (judgeLicenseTypeWhiteList.includes(judgeLicenseType)) {
        return true
    }

    // 只匹配双方都有的证件类型
    let judgeLicenseTypeStr =  judgeLicenseType.toString()
    logger.getLogger("access").info(`the domainLicenseType is ${domainLicenseType}, the judgeLicenseType is ${judgeLicenseTypeStr}`)
    if (domainLicenseTypeData[domainLicenseType] === organizerLicenseType[judgeLicenseTypeStr]) {
        return true
    }
    return false
}

function formatDateToYMD(data, type) {
    try {
        if (type === 'timestamp') {
            const date = new Date(data);
            const year = date.getFullYear();
            const month = date.getMonth() + 1;
            const day = date.getDate();
            return { year, month, day };
        }

        if (type === 'dateString') {
            // 正则提取：2025年01月08日（支持无前导0）
            const match = data.match(/(\d{4})年0?(\d{1,2})月0?(\d{1,2})日/);
            if (match) {
                const [, year, month, day] = match;
                return {
                    year: parseInt(year),
                    month: parseInt(month),
                    day: parseInt(day)
                };
            } else {
                throw new Error("Invalid date string format");
            }
        }

        throw new Error("Unsupported type");
    } catch (e) {
        logger.getLogger("access").error(`the formatDateToYMD error is ${e}`)
        return {
            year: 0,
            month: 0,
            day: 0
        }
    }
}
function ipToInt(ip, isIPv6 = false) {
    if (isIPv6) {
        // IPv6 转 128位整数（用BigInt数组表示高低64位）
        const parts = ip.split(':');
        let high = BigInt(0);
        let low = BigInt(0);

        // 处理简化的IPv6地址（如 ::1）
        const expanded = [];
        let expandPos = -1;
        for (let i = 0; i < parts.length; i++) {
            if (parts[i] === '') {
                expandPos = i;
                break;
            }
        }

        if (expandPos >= 0) {
            const before = parts.slice(0, expandPos);
            const after = parts.slice(expandPos + 1);
            const missing = 8 - (before.length + after.length);
            expanded.push(...before, ...Array(missing).fill('0'), ...after);
        } else {
            expanded.push(...parts);
        }

        // 填充到8部分
        while (expanded.length < 8) {
            expanded.push('0');
        }

        // 转换为BigInt
        for (let i = 0; i < 8; i++) {
            const part = expanded[i] || '0';
            const num = BigInt(`0x${part}`);
            if (i < 4) {
                high = (high << BigInt(16)) | num;
            } else {
                low = (low << BigInt(16)) | num;
            }
        }

        return { high, low };
    } else {
        // IPv4 转 32位整数
        const parts = ip.split('.').map(Number);
        return (parts[0] << 24) | (parts[1] << 16) | (parts[2] << 8) | parts[3];
    }
}

function isIPRangeOverlap(checkInputIP, checkOriginIP) {
    // 检查IP类型是否相同
    if (checkInputIP.IPType !== checkOriginIP.IPType) {
        return false;
    }

    const isIPv6 = checkInputIP.IPType === 1;

    if (!isIPv6) {
        // IPv4 处理
        const start1 = ipToInt(checkInputIP.IPStart);
        const end1 = ipToInt(checkInputIP.IPEnd);
        const start2 = ipToInt(checkOriginIP.IPStart);
        const end2 = ipToInt(checkOriginIP.IPEnd);

        return !(end1 < start2 || end2 < start1);
    } else {
        // IPv6 处理
        const range1Start = ipToInt(checkInputIP.IPStart, true);
        const range1End = ipToInt(checkInputIP.IPEnd, true);
        const range2Start = ipToInt(checkOriginIP.IPStart, true);
        const range2End = ipToInt(checkOriginIP.IPEnd, true);

        // 比较高低位
        const noOverlap =
            (range1End.high < range2Start.high ||
                (range1End.high === range2Start.high && range1End.low < range2Start.low)) ||
            (range2End.high < range1Start.high ||
                (range2End.high === range1Start.high && range2End.low < range1Start.low));

        return !noOverlap;
    }
}

// 检查IPStart是否小于IPEnd
function compareIPs(start, end, isIPv6 = false) {
    // 验证合法性
    if (isIPv6) {
        const { high: highStart, low: lowStart } = ipToInt(start, true);
        const { high: highEnd, low: lowEnd } = ipToInt(end, true);

        if (highStart < highEnd) return true;
        if (highStart === highEnd && lowStart < lowEnd) return true;
        return false;
    } else {
        const intStart = ipToInt(start);
        const intEnd = ipToInt(end);

        return intStart < intEnd;
    }
}

function pwdHash(input) {
    // MD5二进制 → Base64
    // input是口令加随机字符串
    const md5Buffer = crypto.createHash('md5').update(input).digest()

    // MD5十六进制字符串（大写） → Base64（编码文本）
    const md5HexUpper = md5Buffer.toString('hex').toUpperCase()
    const base64FromHexString = Buffer.from(md5HexUpper).toString('base64')
    return base64FromHexString
}

// 上报加密函数
function infoEncrypt(plaintext) {
    // 固定 16 字节 key 和 iv（与 Java 一致）
    const key = Buffer.from(global.CONFIG.cnnicConfig.key, 'utf8'); // 16 字节
    const iv = Buffer.from(global.CONFIG.cnnicConfig.iv, 'utf8');  // 16 字节
    const cipher = crypto.createCipheriv('aes-128-cbc', key, iv);
    let encrypted = cipher.update(plaintext, 'utf8', 'binary');
    encrypted += cipher.final('binary');

    const encryptedBytes = Buffer.from(encrypted, 'binary');

    // 拼接 IV + 密文
    const combined = Buffer.concat([iv, encryptedBytes]);
    return combined.toString('base64');
}

// 解密函数
function infoDecrypt(base64Str) {
    const combined = Buffer.from(base64Str, 'base64');
    const iv = combined.subarray(0, 16);
    const encryptedBytes = combined.subarray(16);

    const decipher = crypto.createDecipheriv('aes-128-cbc', global.CONFIG.cnnicConfig.key, iv);
    let decrypted = decipher.update(encryptedBytes, undefined, 'utf8');
    decrypted += decipher.final('utf8');
    return decrypted;
}

/**
 * 生成随机字符串
 * @param {number} length - 字符串长度，默认20
 * @returns {string} 随机生成的包含大小写英文字母的字符串
 */
function generateRandomString(length = 20) {
    const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz';
    let result = '';

    for (let i = 0; i < length; i++) {
        const randomIndex = Math.floor(Math.random() * characters.length);
        result += characters[randomIndex];
    }

    return result;
}

function serializeToXML(jsObject) {
    const doc = create().ele(jsObject);
    return doc.end({ headless: true });
}

// XML → JS对象（反序列化）
async function deserializeFromXML(xmlString) {
    const result = await parseStringPromise(xmlString, {
        explicitArray: false, // 简化结构，不包装成数组
        ignoreAttrs: false    // 保留属性
    });
    return result;
}

// 获取当前时间2025-04-01 22:16:00这种格式
function getCurrentFormattedTime() {
    const now = new Date();

    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0'); // 月份是从0开始
    const day = String(now.getDate()).padStart(2, '0');

    const hours = String(now.getHours()).padStart(2, '0');
    const minutes = String(now.getMinutes()).padStart(2, '0');
    const seconds = String(now.getSeconds()).padStart(2, '0');

    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}

module.exports = {
    csvParse,
    splitArray,
    getICPMainNoByICPWebNo,
    mongoResultCheck,
    generateOrderNo,
    trimObject,
    getPunyDomain,
    getQueryObject,
    arrayMinus,
    getVerifyResult,
    getTableModel,
    objectCount,
    parseJSON,
    sleep,
    generateFileName,
    decodeBase64Image,
    checkIPInSeg,
    isAdmin: (name) => {
        return _.findIndex(global.CONFIG.admins, (admin) => admin === name) >= 0
    },
    convertUS3FileToBase64,
    checkImgType,
    pickMainDomain,
    getDomainInfo,
    checkPictureIsInDataBase,
    getCompanyIdByOrderNo,
    judgeDomainTypeIsRight,
    formatDateToYMD,
    isIPRangeOverlap,
    compareIPs,
    pwdHash,
    infoEncrypt,
    infoDecrypt,
    generateRandomString,
    serializeToXML,
    deserializeFromXML,
    getCurrentFormattedTime
}
