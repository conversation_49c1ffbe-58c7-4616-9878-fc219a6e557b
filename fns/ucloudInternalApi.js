const uuid = require('uuid/v4')
const { promisify } = require('util')
const innerApi = require('../libs/ucloud-internal-api-request/index')
const logger = require('../libs/logger')
const defaultTimeout = 2 * 1000

function request(options, data, cb) {
    let uid = options.uid ? options.uid : uuid()

    logger
        .getLogger('api')
        .info('[' + uid + '] Options:' + JSON.stringify(options))
    logger.getLogger('api').info('[' + uid + '] Data:' + JSON.stringify(data))

    if (options.action === undefined || options.category === undefined) {
        logger
            .getLogger('error')
            .error('[' + uid + ']NO ACTION OR CATEGORY IN OPTIONS')
        return cb(new Error('NO ACTION OR CATEGORY IN OPTIONS'))
    }

    let timeout = options.timeout || defaultTimeout

    innerApi.request(
        {
            method: options.method || 'POST',
            category: options.category,
            action: options.action,
            session: {
                public_key: global.CONFIG.internalApiKeys.publicKey,
                secret_key: global.CONFIG.internalApiKeys.secretKey,
            },
            data: data,
            options: {
                timeout: timeout,
            },
            json: true,
        },
        (err, body) => {
            err
                ? logger.getLogger('api').error('[' + uid + ']', err.message)
                : logger
                      .getLogger('api')
                      .info('[' + uid + ']', JSON.stringify(body))

            cb(err, body)
        }
    )
}

module.exports = request
module.exports.innerApi = innerApi
module.exports.requestAsync = promisify(request)
