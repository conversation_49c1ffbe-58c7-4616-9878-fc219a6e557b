'use strict'
const uuid = require('uuid/v4')
const { rp: ucloudInternalApiAsync } = require('./ucloudApi')
const assert = require('assert')

/*
 * TODO: 该接口支持可以一次性发送多条邮件，同一内容，不同收件人
 *
 * @params { Object } opt
 * @params { string } opt.email     邮箱名称
 * @params { string } opt.title     邮件标题
 * @params { string } opt.content   邮件正文
 * @params { Object } AttachFiles 邮箱附件（如有）[{AttachName: xxx, AttachContent: xxxx(base64编码)}]
 * @params { string= } opt.uid      uuid 用于记录在logger文件方便标识
 * @params { integer= } opt.timeout 请求接口超时时间，默认为3000，即3秒
 * @params { string= } opt.url      请求调用的邮箱地址，一般来说不用管
 * @params { fileUrl= } opt.fileUrl 请求调用的发送附件的地址，一般来说也不用管
 * @return { Promise.<integer, Error> } id 返回邮件的标识符, 该id 可以通过checkSendingResult等函数调用的接口查询其发送状态
 */
const sendMail = async ({
    email, //邮箱
    content, //正文内容
    title, //邮件标题
    attachFiles, // 附件 [{AttachName: xxx, AttachContent: xxxx(base64编码)}]
    taskId, //发送任务Id,
    channel = 1, // 渠道
} = {}) => {
    taskId = taskId || uuid()

    let params = {
        Target:
            global.CONFIG.env === 'production'
                ? email
                : global.CONFIG.emailAndSms.email,
        SendType: 'EMAIL',
        SessionNo: taskId,
        NotifyType: 2,
        Subject: title,
        Content: content,
        ChannelId: channel,
    }

    if (attachFiles && attachFiles.length > 0) {
        params.AttachFiles = attachFiles
    }

    await pushMessage(params)

    return taskId
}

// 推送通知
const pushMessage = async (params, uid) => {
    params.AuthKey = '661154923'
    params.ChannelId = params.ChannelId || 1
    const { RetCode, Message } = await ucloudInternalApiAsync(
        {
            category: 'UMS',
            action: 'PushMessage',
            uid: uid,
        },
        { ...params }
    )
    assert(RetCode === 0, Message || 'ums push message fail')

    return true
}
/*
 * 发送短信的接口
 * TODO: 该接口支持可以一次性发送多条短信，同一内容，不同收件人
 *
 * @params { object } option
 * @params { string } option.mobile 手机号
 * @params { string } option.mobile 发送内容
 * @params { string= } option.uid uuid, 记录在日志中用
 * @params { number= } option.timeout 接口的请求超时时间，默认为3秒，单位为毫秒
 * @params { string= } option.url  请求的url
 *
 * @return { Promise.resolve<string, Error> } 发送短信得到的ID，用于查阅发送短信的状态
 */
const sendSms = async ({
    mobile,
    content,
    taskId,
    ChannelId,
    // prefix = '【优刻得】尊敬的UCloud用户，',
} = {}) => {
    console.log(ChannelId, 'sms')
    taskId = taskId || uuid()
    const params = {
        SendType: 'SMS',
        SessionNo: taskId,
        Content: content,
        ChannelId: ChannelId ? parseInt(ChannelId) : 1,
        Target:
            global.CONFIG.env === 'production'
                ? mobile
                : global.CONFIG.emailAndSms.mobile,
    }

    await pushMessage(params, taskId)

    return taskId
}

const checkSendingResult = async (taskId, messageType, target) => {
    console.log(1133, taskId, messageType, target)
    let Dict = {
        '-1': 'Failed',
        0: 'Sending',
        1: 'Success',
    }
    return Dict[await getStatus({ taskId, messageType })] || 'Sending'
}

/*
 * 通过发送短信和邮件拿到的uuid 去获取发送状态
 *
 * @params { object } option
 * @params { string } option.taskId 发送短信和邮件得到的Id
 * @params { string= } option.url 请求接口的url
 * @params { number= } option.timeout 接口的超时时间，默认为3秒
 * @params { string= } option.uid uuid, 记录在日志中用
 *
 * @return { Promise.<number, Error> } 发送状态, 目前是 0:发送中; 1:发送成功; -1:发送失败
 */

const getStatus = async ({ taskId, messageType, target }) => {
    // const sendType = MessageTypeMap[sendType]

    // assert(sendType, 'illegal messageType')

    // taskId 由 sessionNo:target 组成
    const taskIdSplit = taskId.split(':')

    const sessionNo = taskIdSplit[0]
    const params = {
        SendType: messageType,
        SessionNo: sessionNo,
    }

    const queryTarget = target || taskIdSplit[1]
    if (queryTarget) {
        params.Target = queryTarget
    }

    let data = await getUmsRecord(params)

    try {
        // 0 发送中   1 成功   -1失败

        if (data.length === 0) {
            return 0
        }
        data = data[0]
        if (data.SubmitResult === 0) {
            return 1
        } else {
            return -1
        }
        // if (data.SubmitProvider === 101) {
        //     return 1
        // }
        // if (typeof data.ReportResult === 'number') {
        //     return data.ReportResult === 0 ? 1 : -1
        // }
    } catch (error) {
        return 0
    }
}

const getUmsRecord = async (params) => {
    // if (IS_NOT_PRODUCTION) {
    // 	return []
    // }
    params.Backend = 'SNS'
    params.Action = 'SNS.GetUmsRecords'
    const { RetCode, Message, Data } = await ucloudInternalApiAsync(
        {
            category: 'SNS',
            timeout: 15 * 1000,
            action: 'GetUmsRecords',
        },
        params
    )
    console.log(222222, RetCode, Message, Data)
    assert(RetCode === 0, Message || 'ums getUmsRecord fail')

    return Data || []
}
module.exports = {
    sendMail,
    sendSms,
    checkSendingResult,
}
