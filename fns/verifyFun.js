const moment = require('moment')
const request = require('request')
const crypto = require('crypto')
const verify_type = require('../configs/common/verify_api_type.json')
const _ = require('lodash')
const ucloudinternalapi = require('../libs/ucloudinternalapi')
const { Op } = require('sequelize')
const ffmpeg = require('fluent-ffmpeg')
const { parseURI } = require('uri-parse-lib')
const { upload } = require('../libs/uploadFile')
const fs = require('fs')
const path = require('path')
const mineType = require('mime-types')
const producer = require('../libs/producer')
const logger = require('../libs/logger').getLogger('mqProducer')
const UploadFileToUS3 = require('./order/UploadFileToUS3')
const parse = require('url-parse')
const FILENAMELIST = ['Picture', 'Photo1', 'Photo2', 'Video', 'IdPhoto']

// 供调用前插入验证数据
async function insertLog(params, LogModel) {
    let insertBody = {
        DataContent: params,
        CreateTime: moment().format('X'),
        Type: params.Type,
        Remark: params.Remark,
        CompanyId: params.CompanyId,
        OrderNo: params.OrderNo,
    }
    if (params.Operator) {
        insertBody.Operator = params.Operator
    }
    if (params.UUID) {
        insertBody.UUID = params.UUID
    }
    let insertId
    insertId = await LogModel.create(insertBody)
    insertId = JSON.parse(JSON.stringify(insertId))
    return insertId.Id
}

// 传入URL获取文件名，供系统复用之前取出文件名，判断是否有重复之用
function getFileName(urlString) {
    let urlObj = parse(urlString, true)
    // 如果发现不是ICP服务自有的域名，直接返回
    if (urlObj.hostname !== 'icp.cn-bj.ufileos.com') {
        return urlString
    }

    // 如是自有域名，解析出文件名
    return urlObj.pathname
}

// 验证时，查最近是否有同类成功的查询
async function getVerifyLog(params, logId, LogModel, day = 7) {
    let likeObject = []
    let type = params.Type

    delete params.Type

    _.forEach(params, function (value, key) {
        if (_.indexOf(FILENAMELIST, key) !== -1) {
            likeObject.push({
                data_content: {
                    [Op.like]: `%${value}%`,
                },
            })
        } else if (key === 'OrderNo') {
            likeObject.push({
                OrderNo: value,
            })
        } else {
            if (value !== undefined) {
                likeObject.push({
                    data_content: {
                        [Op.like]: _.isNumber(value)
                            ? `%"${key}":${value}%`
                            : value instanceof Object
                            ? `%"${key}":${JSON.stringify(value)}%`
                            : `%"${key}":"${value}"%`,
                    },
                })
            }
        }
    })
    let log = await LogModel.findOne({
        order: [['Id', 'DESC']],
        where: {
            Type: type, //  小tips约束查询条件可提高查询速度
            [Op.and]: likeObject,
            // 不是当前自己的Id
            Id: {
                [Op.ne]: logId,
            },
            UpdateTime: {
                [Op.gt]: parseInt(moment().subtract(day, 'days').format('X')),
            },
        },
    })

    log = JSON.parse(JSON.stringify(log))
    console.log(log, 999999)
    log =
        log === null
            ? null
            : { Error: log.Result === 0 ? false : true, Response: log.Response }
    return log
}

function updateLog(params, logId, LogModel) {
    let updateBody = {
        UpdateTime: moment().format('X'),
    }
    if (params.Response) {
        updateBody.Response = params.Response
    }

    if (params.Multiplex) {
        updateBody.Multiplex = params.Multiplex
    }

    if (params.Error === true) {
        updateBody['Result'] = 1
    } else if (params.Error === false) {
        updateBody['Result'] = 0
    }
    // Result若有入参则优先级最高
    if (params.Result !== undefined) {
        updateBody['Result'] = params.Result
    }
    // 执行更新
    if (params.ReturnObject) {
        updateBody.ReturnObject = params.ReturnObject
    }

    return LogModel.update(updateBody, {
        where: {
            Id: logId,
        },
    })
}

function verifyAPI(params) {
    return new Promise((resolve) => {
        var options = {
            method: 'POST',
            url: global.CONFIG.verifyAPI.url,
            // url: 'http://localhost:8080',
            qs: {
                Action: params.Action,
                Backend: params.body?.Backend || 'IdAuth',
            },
            headers: {
                'Content-Type': 'application/json',
            },
            body: params.body,
            json: true,
        }

        // 强制标注调用来源
        options.body.Source = 'ICP'

        request(options, function (error, response, body) {
            console.log(error, body, options)
            if (error || !body) {
                return resolve({ Error: true, Response: { Error: error } })
            }
            if (body.RetCode !== 0) {
                return resolve({ Error: true, Response: body })
            }
            resolve({ Error: false, Response: body })
        })
    })
}

function ffmpegParseVideo(url) {
    let filename = parseURI(url)
    filename = filename.pathname.split('.')[0]

    // 获取像素信息
    return new Promise((resolve, reject) => {
        ffmpeg.ffprobe(url, function (err, metadata) {
            if (err) {
                return reject(err)
            }
            let metaInfo = _.find(metadata.streams, function (o) {
                return o.height !== 0
            })
            // 做动态压缩
            let rota = 750 / metaInfo.height
            return resolve(Math.ceil(rota * 100))
        })
    }).then((rota) => {
        return new Promise((resolve, reject) => {
            new ffmpeg(url)
                .screenshots({
                    timemarks: ['0%', '30%', '60%', '90%'],
                    filename: 'shotPic',
                    folder: path.join(
                        global.CONFIG.logPath,
                        'shotPic' + filename
                    ),
                    size: `${rota}%`,
                })
                .on('end', function () {
                    // resolve('/data/tmp' + filename)
                    // 执行完成，上传文件
                    let fileList = fs.readdirSync(
                        path.join(global.CONFIG.logPath, 'shotPic' + filename)
                    )

                    // 获取图片Bae64
                    let faceRange = []
                    for (const iterator of fileList) {
                        // path.join('/data/tmp', filename, iterator)
                        let filePath = path.join(
                            global.CONFIG.logPath,
                            'shotPic' + filename,
                            iterator
                        )
                        faceRange.push(filePath)
                        // var imageData = fs.readFileSync(filePath)
                        // // mineType.lookup(filePath)
                        // fileListBase64.push('data:' + mineType.lookup(filePath) + ';base64,' + imageData.toString("base64"))
                    }

                    // 取第一张脸
                    // let firstFace = faceRange[0]

                    resolve(faceRange)
                })
                .on('error', function (err, stdout, stderr) {
                    reject(err)
                })
        }).then((faceRange) => {
            let imageData = fs.readFileSync(faceRange[0])
            let face1Base64 =
                'data:' +
                mineType.lookup(faceRange[0]) +
                ';base64,' +
                imageData.toString('base64')
            // 发送到消息队列处理 图片 ---如果不需要存储  直接注释下面的 try cath 内容
            try {
                const videoName =
                    filename.split('/').length > 1
                        ? filename.split('/')[1]
                        : filename.split('/')[0]
                // producer.send({
                //     type: 'icp',
                //     topic: 'uploadFileToUS3',
                //     data: {
                //         faceRange, //图片数组
                //         baseName: videoName, //视频名称
                //         directory: global.CONFIG.tmpUS3PictureDir,
                //     },
                // })
                UploadFileToUS3({
                    faceRange, //图片数组
                    baseName: videoName, //视频名称
                    directory: global.CONFIG.tmpUS3PictureDir,
                })
            } catch (err) {
                logger.error('uploadFileToUS3 mq send error,', err)
            }
            return Promise.all([verifyManyFace(faceRange), upload(face1Base64)])
        })
    })
}

/*
 * 取3张脸与视频第一帧对比得到结果
 * 入参为第一帧与其它的帧
 *
 * @params face1 face2 face4 face 5
 * @return checkRes boolean
 *
 */
function verifyManyFace(faceRange) {
    var options = {
        method: 'POST',
        url: 'https://api.megvii.com/faceid/v2/verify',
        headers: {},
        json: true,
        formData: {
            api_key: 'tTXC5shV1jKB5lMUPGfDo1yWpKHjBzTN',
            api_secret: 'NVeU_YBMHf5iJNjZKhdIMzggumDJGkPv',
            comparison_type: '0',
            face_image_type: 'raw_image',
            fail_when_multiple_faces: '1',
            fail_when_ref_multiple_faces: '1',
            uuid: '3019',
            face_quality_threshold: '0',
            multi_oriented_detection: '1',
        },
    }

    // 上传文件
    ;[0, 1, 2, 3].forEach((element) => {
        if (element === 0) {
            options.formData.image = {
                value: fs.createReadStream(faceRange[element]),
                options: {
                    filename: faceRange[element],
                    contentType: null,
                },
            }
        } else {
            options.formData['image_ref' + element] = {
                value: fs.createReadStream(faceRange[element]),
                options: {
                    filename: faceRange[element],
                    contentType: null,
                },
            }
        }
    })

    return new Promise((resolve) => {
        request(options, function (error, response, body) {
            if (error || !body) {
                return resolve({ Error: true, Response: { Message: error } })
            }

            return resolve({ Error: false, Response: body })
        })
    })
}

// 文件名得文件在ufile的url
function getUrl(Url, Name, Quality, Convert) {
    if (Url) {
        return Url
    }

    if (!Name) {
        throw new Error('文件名不能为空')
    }

    // 如果传入参数则做压缩
    if (Quality && _.isInteger(Quality)) {
        // 是否使用US3的压缩
        return (
            global.CONFIG.ufile.target +
            '/' +
            Name +
            signature(Name, Math.floor(Date.now() / 1000) + 600) +
            (Quality ? `&iopcmd=convert&q=${Quality}&dst=jpg` : '')
        )
    } else if (Convert) {
        // 是否强制格式转换
        return (
            global.CONFIG.ufile.target +
            '/' +
            Name +
            signature(Name, Math.floor(Date.now() / 1000) + 600) +
            '&iopcmd=convert&dst=jpg'
        )
    } else {
        return (
            global.CONFIG.ufile.target +
            '/' +
            Name +
            signature(Name, Math.floor(Date.now() / 1000) + 600)
        )
    }
}

function signature(filename, expires) {
    // let auth = crypto
    // 	.createHmac('sha1', global.CONFIG.ufile.privateKey)
    // 	.update('GET' + '\n\n\n' + expires + '\n' + '/icp/' + filename)
    // 	.digest()
    // 	.toString('base64')
    // icp 是bucket名字哦，不是目录哦！！！！！！
    return (
        '?UCloudPublicKey=' +
        encodeURIComponent(global.CONFIG.ufile.publicKey) +
        '&Signature=' +
        encodeURIComponent(
            crypto
                .createHmac('sha1', global.CONFIG.ufile.privateKey)
                .update('GET' + '\n\n\n' + expires + '\n' + '/icp/' + filename)
                .digest()
                .toString('base64')
        ) + // sha1 Hmac + base64
        '&Expires=' +
        expires
    )
}

//对需要返回的日志数据进行处理
function parseLog(log) {
    // 下面的写法不太好
    let row = log[log.length - 1]
    let info = {}
    //对于不同的Type做不同的数据处理
    if (row.Type === 0) {
        if (
            row.Result === 1 ||
            row.Response === '' ||
            !row.Response.Info ||
            row.Response.RetCode !== 0
        ) {
            info['Name'] = ''
            info['Code'] = ''
        } else {
            info['Name'] = row.Response.Info.Name
            info['Code'] = row.Response.Info.IdCardNumber
        }
        return { key: 'OCRInfo', value: info }
    } else if (row.Type === 1) {
        if (
            row.Result === 1 ||
            row.Response === '' ||
            row.Response.RetCode !== 0
        ) {
            info['Name'] = ''
            info['Code'] = ''
        } else {
            info['Name'] = row.Response.Name
            info['Code'] = row.Response.Code
        }
        return { key: 'OCRInfo', value: info }
    } else if (row.Type === 6) {
        if (
            Object.keys(row.Response).length === 0 ||
            (row.Response.RetCode !== undefined && row.Response.RetCode !== 0)
        ) {
            return { key: 'UploadFile', value: {} }
        }
        let files = row.Response.Files
        let uploadFiles = []
        //数据存在一维数组和二维数组两种形式，因此分情况进行处理
        if (files[0][0].length === 1) {
            for (let i = 0; i < files.length; i++) {
                let body = {
                    Name: '',
                    Url: '',
                }
                body.Name = files[i]
                if (!files[i]) {
                    throw new Error('文件名不能为空')
                }

                body.Url =
                    global.CONFIG.ufile.target +
                    '/' +
                    files[i] +
                    signature(files[i], Math.floor(Date.now() / 1000) + 600)
                uploadFiles.push(body)
            }
        } else {
            for (let j = 0; j < files.length; j++) {
                let arr = []
                for (let t = 0; t < files[j].length; t++) {
                    let body = {
                        Name: '',
                        Url: '',
                    }
                    body.Name = files[j][t]
                    if (!files[j][t]) {
                        throw new Error('文件名不能为空')
                    }
                    body.Url =
                        global.CONFIG.ufile.target +
                        '/' +
                        files[j][t] +
                        signature(
                            files[j][t],
                            Math.floor(Date.now() / 1000) + 600
                        )
                    arr.push(body)
                }
                uploadFiles.push(arr)
            }
        }
        return { key: 'UploadFile', value: uploadFiles }
    } else if (row.Type === 4) {
        if (
            Object.keys(row.Response).length === 0 ||
            (row.Response.RetCode !== undefined && row.Response.RetCode !== 0)
        ) {
            return { key: 'UploadFile', value: {} }
        }
        let files = row.Response.FileName
        let uploadFiles = []
        let body = {
            Name: '',
            Url: '',
        }
        body.Name = files
        if (!files) {
            throw new Error('files不能为空')
        }
        body.Url =
            global.CONFIG.ufile.target +
            '/' +
            files +
            signature(files, Math.floor(Date.now() / 1000) + 600)
        uploadFiles.push(body)
        return { key: 'UploadFile', value: uploadFiles }
    } else {
        let funName
        for (let key of Object.keys(verify_type)) {
            if (verify_type[key] === row.Type) {
                funName = key
                break
            }
        }
        return { key: funName, value: row.Response }
    }
}

function EpicEntPersonRelationAPI(params) {
    return new Promise((resolve) => {
        var options = {
            method: 'POST',
            url: global.CONFIG.verifyAPI.epicEntPersonRelationUrl,
            qs: {
                Action: 'EpicEntPersonRelation',
            },
            headers: {
                'Content-Type': 'application/json',
            },
            body: {
                CompanyName: params.CompanyName,
                CompanyCode: params.CompanyCode,
                LegalEntityName: params.LegalEntityName,
                LegalEntityId: params.LegalEntityId,
            },
            json: true,
        }
        request(options, function (error, response, body) {
            if (error || !body) {
                return resolve({ Error: true, Response: { Message: error } })
            }
            if (body.RetCode !== 0) {
                return resolve({ Error: true, Response: body })
            }
            return resolve({ Error: false, Response: body })
        })
    })
}

async function SendCaptcha(params) {
    // 与账户的数据调用,发送短信验证码
    let baseOptions = {
        Backend: 'UVerificationCode',
        Action: 'SendPhoneVerificationCode',
        UserPhone: '(86)' + params.UserPhone,
        VerifyType: 'RECORD_AUDIT',
        SendType: 'SMS',
        Channel: parseInt(params.channel),
    }

    let responseData = await ucloudinternalapi(baseOptions)
    return responseData
}

async function CheckCaptcha(params) {
    // 与账户的数据调用,核验短信验证码
    let baseOptions = {
        Backend: 'UVerificationCode',
        Action: 'CheckVerificationCode',
        Identity: '(86)' + params.UserPhone,
        VerifyType: 'RECORD_AUDIT',
        Code: params.Code,
        Verify: 1,
    }

    let responseData = await ucloudinternalapi(baseOptions)

    return responseData
}

/*
 * 判断身份证有效期是否过期或不正确
 * 在有效期内返回 true，否则返回 false
 *
 * @params validDate string
 * @return checkRes boolean
 *
 */
function checkIdCardValidDate(validDate) {
    let reg = /[\u4e00-\u9FA5]+/ // 匹配一个或以上汉字

    // 长期身份证，有汉字就通过
    if (reg.test(validDate)) {
        return true
    }

    let now
    let endTime

    // 将身份证的到期时间转成unix
    try {
        now = parseInt(moment().format('X'))
        endTime = parseInt(new Date(validDate.split('-')[1]).getTime() / 1000)
    } catch (e) {
        return false
    }

    return endTime > now ? true : false
}

function validateIdNumberToAgeYear(str) {
    let date = new Date()
    let currentYear = date.getFullYear()
    let currentMonth = date.getMonth() + 1
    let currentDate = date.getDate()

    // let idxSexStart = str.length == 18 ? 16 : 14
    let birthYearSpan = str.length == 18 ? 4 : 2

    let year
    let month
    let day
    // let sex
    // let birthday
    let age

    // let idxSex = 1 - str.substr(idxSexStart, 1) % 2
    // sex = idxSex == '1' ? '女' : '男'
    year = (birthYearSpan == 2 ? '19' : '') + str.substr(6, birthYearSpan)
    month = str.substr(6 + birthYearSpan, 2)
    day = str.substr(8 + birthYearSpan, 2)
    // birthday = year + '-' + month + '-' + day
    let monthFloor =
        currentMonth < parseInt(month, 10) ||
        (currentMonth == parseInt(month, 10) && currentDate < parseInt(day, 10))
            ? 1
            : 0
    age = currentYear - parseInt(year, 10) - monthFloor

    if (age >= 18) {
        return true
    }

    return false
}

module.exports = {
    ffmpegParseVideo,
    insertLog,
    getVerifyLog,
    updateLog,
    verifyAPI,
    getFileName,
    getUrl,
    parseLog,
    EpicEntPersonRelationAPI,
    checkIdCardValidDate,
    SendCaptcha,
    CheckCaptcha,
    validateIdNumberToAgeYear,
}
