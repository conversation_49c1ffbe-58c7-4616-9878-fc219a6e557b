/**
 * @file funs 获取时代互联全部域名
 * <AUTHOR>
 * @params Data string
 * @return  Sign string
 */

const moment = require('moment')
const { CONNECTSYNCVALUE, CONNECTSYNCCOMMENT } = require('../../configs/')
const { v4: uuidv4 } = require('uuid')
const CryptoJS = require('crypto-js')
const qs = require('qs')
const urlencode = require('urlencode')
const axiosApi = require('../../libs/axiosApi')

// 获取时代互联的全部域名
async function getAllDomainInNOWCN() {
    // 时间戳需要二次urlencode
    let timestamp = moment().utc().format()

    let baseObject = {
        AccessKeyID: global.CONFIG['now.cn'].AccessKeyID,
        SignatureMethod: 'HMAC-SHA1',
        SignatureNonce: uuidv4(),
        Timestamp: urlencode(timestamp, 'utf-8'),
    }
    baseObject.Signature = getCryptoText(baseObject)
    baseObject.Timestamp = timestamp

    let res
    try {
        res = await axiosApi({
            method: 'get',
            timeout: 10 * 1000,
            url:
                global.CONFIG['now.cn'].DescribeICPDataAllURL +
                qs.stringify(baseObject),
        })
    } catch (error) {
        return Promise.reject(error)
    }
    if (res.data && res.data.Data && res.data.Data.length !== 0) {
        return Promise.resolve(res.data.Data)
    } else {
        // 出错控制
        return Promise.reject('no data')
    }
}

// 获取时代互联签名
function getCryptoText(signObject) {
    let baseString = []
    for (let key of Object.keys(signObject).sort()) {
        baseString.push(key + '=' + signObject[key])
    }

    baseString = urlencode(baseString.join('&'), 'utf-8')
    baseString = 'GET&%2F&' + baseString

    let hash = CryptoJS.HmacSHA1(
        baseString,
        global.CONFIG['now.cn'].secret + '&'
    ).toString()
    hash = new Buffer(hash).toString('base64')

    return hash
}

module.exports = {
    getAllDomainInNOWCN,
}
