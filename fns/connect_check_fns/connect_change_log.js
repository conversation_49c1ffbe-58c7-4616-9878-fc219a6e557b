// 该函数已废弃，代码中无引用处
const ObjectId = require('mongodb').ObjectId
const moment = require('moment')
const { CONNECTSYNCVALUE } = require('../../configs/')

// 更新接入信息变化的日志
function updateConnectSyncLog(
    logCollection,
    insertedId,
    { metaInfo, status, needFocus }
) {
    // 记录外层
    let dateSet = {
        Action: status,
    }

    if (needFocus) {
        dateSet.NeedFocus = needFocus
    }

    // 过程中产生的日志
    let logSet = {
        Time: parseInt(moment().format('X')),
        Action: status,
        MetaInfo: metaInfo,
    }

    return logCollection.updateOne(
        { _id: ObjectId(insertedId) },
        {
            $set: dateSet,
            $push: {
                Logs: {
                    $each: [logSet],
                    $slice: -100000,  // 限制大小10w条数据
                },
            },
        }
    )
}
module.exports = {
    updateConnectSyncLog,
}
