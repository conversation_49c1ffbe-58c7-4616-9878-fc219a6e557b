/*
 * @Date: 2022-09-05 11:16:55
 * @LastEditors: william.qian <EMAIL>
 * @LastEditTime: 2023-03-06 17:09:21
 * @FilePath: /newicp/fns/connect_check_fns/xinNet.js
 */
const axiosApi = require('../../libs/axiosApi')
const qs = require('qs')
const moment = require('moment')

/**
 * @file funs 获取域名在新网的备案情况
 * <AUTHOR>
 * @params Data string
 * @return  Sign string
 */
// 通过单个域名确定是否备案在新网
async function getDomainICPInfoInXinnet(domain) {
    let baseObject = {
        domain: domain,
        localRecord: true,
        preRecord: true,
    }

    baseObject = qs.stringify(baseObject)

    // 签名部分
    const { createHmac } = await import('crypto')
    const hmac = createHmac('sha256', global.CONFIG.xinnet.sign)
    hmac.update(baseObject)
    let sign = hmac.digest('hex')

    let res
    try {
        res = await Promise.all([
            axiosApi({
                method: 'get',
                url: global.CONFIG['xinnet'].getDomainICPInfoURL + baseObject,
                headers: {
                    key: global.CONFIG['xinnet'].key,
                    timestamp: moment().format('x'),
                    sign: sign,
                },
                timeout: 30 * 1000,
            }),
            axiosApi({
                method: 'get',
                url:
                    global.CONFIG['xinnet'].getDomainICPInfoURLForCeBoos +
                    baseObject,
                headers: {
                    key: global.CONFIG['xinnet'].key,
                    timestamp: moment().format('x'),
                    sign: sign,
                },
                timeout: 30 * 1000,
            }),
        ])
    } catch (error) {
        return Promise.reject(error)
    }

    // res[0]不判断结果值，直接取最终信息。找不到直接res[1]的结果就好

    if (res[0]?.data?.data?.info?.localRecord === true) {
        return Promise.resolve(res[0].data.data.info)
    } else if (
        res[1].data &&
        res[1].data.code &&
        res[1].data.code === 'success' &&
        res[1].data.data &&
        res[1].data.data.info
    ) {
        return Promise.resolve(res[1].data.data.info)
    } else {
        // 出错控制
        return Promise.reject('no data')
    }
}

module.exports = {
    getDomainICPInfoInXinnet,
}
