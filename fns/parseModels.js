/*
 * @Author: william.qian <EMAIL>
 * @Date: 2022-06-01 10:29:02
 * @LastEditors: william.qian <EMAIL>
 * @LastEditTime: 2023-04-13 16:30:55
 * @FilePath: /newicp/fns/parseModels.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
const _ = require('lodash')
const uppercamelcase = require('uppercamelcase')

/**
 * @file 处理Models
 * <AUTHOR>
 * 功能:
 * 1: 完成大小写转换
 * 2:
 */

function parseModels(models) {
    let parseModel = {}
    _.forEach(models, function (value, key) {
        value.field = key

        parseModel[upperCamelcase(key)] = value
    })
    return parseModel
}

function upperCamelcase(v) {
    v = uppercamelcase(v)
    // 特殊处理PIC Person in charge
    if (v.substr(0, 3) === 'Pic') v = 'PIC' + v.substr(3, v.length - 1)

    // 特殊处理ICP
    if (v.substr(0, 3) === 'Icp') v = 'ICP' + v.substr(3, v.length - 1)
    // 特殊处理URL
    // if (v.substr(0, 3) === 'Url') v = 'URL' + v.substr(3, v.length - 1)
    // 特殊处理 QQ
    if (v.substr(-2) === 'Qq') v = v.substr(0, v.length - 2) + 'QQ'
    // 特殊处理 uuid
    if (v.substr(-4) === 'Uuid') v = v.substr(0, v.length - 4) + 'UUID'
    // 特殊处理 ucloud
    if (v.substr(0, 6) === 'Ucloud') v = 'UCloud' + v.substr(6, v.length - 1)
    // 特殊处理 IP
    if (v === 'Ip') v = 'IP'

    if (v === 'Bu') v = 'BU'

    return v
}

const convertObjectKeysToCamel = (obj) => {
    return Object.keys(obj).reduce((prev, now) => {
        prev[upperCamelcase(now)] = obj[now]
        return prev
    }, {})
}

// 数组类型，为空的处理方式
function parseNullArr(value) {
    if (!value || value.length === 0) {
        return []
    } else {
        return value.split('|')
    }
}

// 对象类型，为空的处理方式
function parseNullObject(value, defaultValue) {
    if (
        !value ||
        value === '' ||
        value === '""' ||
        value === '{}' ||
        value === "''"
    ) {
        return defaultValue
    }

    try {
        if (typeof value === 'object') {
            return value
        } else {
            return JSON.parse(value)
        }
    } catch (error) {
        return defaultValue
    }
}

module.exports = {
    parseModels,
    upperCamelcase,
    parseNullArr,
    parseNullObject,
    convertObjectKeysToCamel,
}
