/*
 * @Date: 2022-09-05 11:16:55
 * @LastEditors: l<PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2023-06-13 18:22:45
 * @FilePath: /newicp/fns/mongoFuns.js
 */
// const logger = require('../libs/logger')
const uuid = require('uuid/v4')
const { promisify } = require('util')

// 考虑MongoDB日志是否落库

// insertMany
function insertMany(collection, Data, cb) {
    collection.insertMany(Data, function (err, result) {
        cb(err, result)
    })
}

// find

function find(collection, Selector, Option, cb) {
    collection
        .find(Selector)
        // .setReadPreference('secondaryPreferred')
        .sort(Option.Sort || { _id: -1 })
        .limit(Option.Limit || 20)
        .skip(Option.Offset || 0)
        .toArray(function (err, docs) {
            cb(err, docs)
        })
}
// count
function count(collection, Selector, cb) {
    collection
        .find(Selector)
        // .setReadPreference('secondaryPreferred')
        .count(function (err, docs) {
            cb(err, docs)
        })
}

function updateMany(collection, Selector, Data, cb) {
    collection.updateMany(
        Selector,
        Data
    )(function (err, docs) {
        cb(err, docs)
    })
}

module.exports = {
    insertMany,
    find,
    count,
    updateMany,
    insertManyAsync: promisify(insertMany),
    findAsync: promisify(find),
    countAsync: promisify(count),
    updateManyAsync: promisify(updateMany),
}
