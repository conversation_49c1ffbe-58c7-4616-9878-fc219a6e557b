const uuid = require('uuid/v4')

const innerApi = require('../libs/ucloud-api-request/index')
const logger = require('../libs/logger')
const { promisify } = require('util')
const request = require('./request')
const defaultTimeout = 2 * 1000

function requestICP(options, cb) {
    options.url = 'http://10.182.44.184:5000'
    options.timeout = 10 * 1000
    request(options, function (error, body) {
        cb(error, body)
    })
}
module.exports = requestICP
module.exports.rp = promisify(requestICP)
