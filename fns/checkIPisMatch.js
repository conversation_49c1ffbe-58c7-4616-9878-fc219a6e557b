const _ = require('lodash')
const getIpInfo = require('./getIPInfo')
const { parseJSON } = require('./kits')

async function checkIPisMatch(IPWhiteList, company_id, ips, cb) {
    try {
        company_id = parseInt(company_id)
        //检查是否在白名单内
        let [isIpWhite, res] = await checkIsIpWhite(
            IPWhiteList,
            company_id,
            ips
        )

        if (res) {
            return cb(isIpWhite, res)
        }
        //不在白名单，则校验是否是我司境内资源，且在该账号下
        let result = ''
        let ipInfo = await getIpInfo(
            {
                Token: '361d3765-836a-400a-8ecb-025216762c7c',
                Action: 'Compass.IGetCompanyByEip',
                action: 'Compass.IGetCompanyByEip',
                timeout: 60 * 1000,
                method: 'POST',
                TopOrganizationId: company_id,
            },
            ips
        )

        ipInfo.forEach((item) => {
            if (item.RetCode !== 0) {
                return cb(67020, {
                    IsBelong: false,
                    Tip: 'EIP资源找不到',
                    DataInfo: '解析查询公司信息失败',
                })
            }
            if (!item.Data) {
                result += `${item.Ip}：EIP资源找不到；`
            } else if (
                item.Data.AzGroupEn &&
                item.Data.AzGroupEn.substr(0, 3) !== 'cn-'
            ) {
                result += `${item.Ip}：EIP资源不在境内；`
            } else if (item.Data.TopOrganizationId !== company_id) {
                result += `${item.Ip}：EIP资源不属于该账号；`
            }
        })
        if (result) {
            return cb(0, {
                IsBelong: false,
                Tip: result,
            })
        }
        return cb(0, {
            IsBelong: true,
            DataInfo: '通过核验',
        })
    } catch (e) {
        console.log(e, 'err')
        cb(67020, {
            IsBelong: false,
            Tip: '请填写本账号下UCloud内地外网EIP资源',
            DataInfo: e.message || '解析查询公司信息失败',
        })
    }

    // return new Promise((resolve) => {
    // 	ipwhiteModel
    // 		.get({
    // 			CompanyId: company_id
    // 		})
    // 		.then(datas => {
    // 			let isAllMatch = 1
    // 			//传入的iplist是不能完全匹配上白名单如果是则为1，有一个不是，则为0
    // 			_.forEach(iplist, function (value) {

    // 				if (value === 'all') {
    // 					// all 为数据库的特殊规则,下次时间够可以换成子网形式
    // 					cb(0, {
    // 						IsBelong: false,
    // 						DataInfo: '无效IP'
    // 					})
    // 				}

    // 				if (_.findIndex(datas, ['IP', value]) === -1) {
    // 					isAllMatch = 0
    // 				}
    // 			})
    // 			if (_.findIndex(datas, ['IP', 'all']) !== -1 || isAllMatch === 1) {
    // 				//如果是白名单的中all的直接通过,如果全部匹配也直接通过
    // 				cb(0, {
    // 					IsBelong: true,
    // 					DataInfo: '通过核验'
    // 				})
    // 			} else {
    // 				resolve(0)
    // 			}
    // 		})
    // })
    // .then(() => {
    // 	//组织Id List
    // 	const options = {
    // 		action: 'Account.GetOrganizationInfo',
    // 		category: 'Account',
    // 		timeout: 10 * 1000
    // 	}

    // 	const data = {
    // 		CompanyId: [parseInt(company_id)],
    // 		Offset:0,
    // 		Limit:999,
    // 		Backend: 'Account',
    // 		Action: 'Account.GetOrganizationInfo'
    // 	}

    // 	return new Promise((resolve) => {
    // 		ucloudapi(options, data, (err, body) => {
    // 			//不报错，出错的记录置为3，返回结果中做提示
    // 			if (err || body.RetCode !== 0)
    // 				cb(67020, {
    // 					IsBelong: false,
    // 					Tip: '解析查询公司信息失败,请重试',
    // 					DataInfo: '解析查询公司信息失败'
    // 				})
    // 			resolve(_.map(body.OrganizationInfo, 'OrganizationId'))
    // 		})

    // 	})
    // })
    // .then(res => {
    // 	let promisesList = []
    // 	_.forEach(iplist, function (value) {
    // 		promisesList.push(
    // 			// 单个IP可能会对应多个托管云的公司，能匹配上一个既可
    // 			new Promise((rs) => {
    // 				var options = {
    // 					method: 'GET',
    // 					url: 'http://*************:5065/',
    // 					qs: {
    // 						Action: 'fetchCompanyIdsByIps',
    // 						tag: 'chinaland'
    // 					},
    // 					headers: {
    // 						'Content-Type': 'application/json'
    // 					},
    // 					body: {
    // 						ips: [value],
    // 						tag: 'chinaland',
    // 						needSource: true
    // 					},
    // 					json: true
    // 				}

    // 				request(options, function (error, response, body) {
    // 					// 错误处理
    // 					if (error || body.data === undefined || body.data.found === undefined || body.data.found.length <= 0) return rs(-1)

    // 					let isMatch = -1
    // 					// 只要查到的IP,有一个能查到公司既可
    // 					_.forEach(body.data.found, function (value) {
    // 						if (_.indexOf(res, value.orgId) !== -1) {
    // 							isMatch = 1
    // 						}
    // 					})
    // 					rs(isMatch)
    // 				})
    // 			})
    // 		)
    // 	})
    // 	return Promise.all(promisesList)
    // })
    // .then(checkResults => {
    // 	//查询上述步骤有没有不匹配的情况，有一个失败就是失败
    // 	let isError = _.indexOf(checkResults, -1)
    // 	if (isError === -1) {
    // 		cb(0,{
    // 			IsBelong: true})
    // 	} else {
    // 		cb(0, {
    // 			IsBelong: false,
    // 			Tip: '请填写本账号下UCloud内地外网EIP资源',
    // 		})
    // 	}
    // })
    // .catch(e => {
    // 	cb(67020, {
    // 		IsBelong: false,
    // 		Tip: '请填写本账号下UCloud内地外网EIP资源',
    // 		DataInfo: e.message
    // 	})
    // })
}

function checkIsIpWhite(IPWhiteList, company_id, iplist) {
    return new Promise((resolve, reject) => {
        IPWhiteList.findAll({
            where: {
                CompanyId: company_id,
            },
        })
            .then((datas) => {
                // datas = datas.field
                datas = parseJSON(datas)

                let isAllMatch = 1,
                    ipInWhiteList = []
                //传入的iplist是不能完全匹配上白名单如果是则为1，有一个不是，则为0
                _.forEach(iplist, function (value) {
                    if (value === 'all') {
                        // all 为数据库的特殊规则,下次时间够可以换成子网形式
                        return resolve([
                            0,
                            {
                                IsBelong: false,
                                DataInfo: '无效IP',
                            },
                        ])
                    }

                    // 将查到的datas中的IP字段取出来，组成一个数组，然后判断传入的iplist中的IP是否在这个数组中，如果不在则为0
                    ipInWhiteList = _.map(datas, 'IP')
                    ipInWhiteList = _.flattenDeep(ipInWhiteList)
                    // 有一个不匹配就是0

                    if (ipInWhiteList.indexOf(value) === -1) {
                        isAllMatch = 0
                    }
                })
                if (ipInWhiteList.indexOf('all') !== -1 || isAllMatch === 1) {
                    //如果是白名单的中all的直接通过,如果全部匹配也直接通过
                    return resolve([
                        0,
                        {
                            IsBelong: true,
                            DataInfo: '通过核验',
                        },
                    ])
                } else {
                    return resolve([0])
                }
            })
            .catch((e) => {
                return reject(new Error(e.message) || e)
            })
    })
}

module.exports = {
    checkIPisMatch,
    checkIsIpWhite,
}
