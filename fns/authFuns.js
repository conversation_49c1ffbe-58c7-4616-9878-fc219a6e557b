const ucloudinternalapi = require('../libs/ucloudinternalapi')
const _ = require('lodash')
const getUserChannelInfo = require('../fns/auth/getUserChannelInfo')
// 获取实名信息，然后整理
async function getAuthInfo(companyId, channel = 1) {
    // 获取渠道信息
    let isDedicatedCloud = await getUserChannelInfo(channel, 'isDedicatedCloud')
    
    if (!isDedicatedCloud) {
        // ucloud渠道，默认ucloud渠道value为0
        let authInfo = await ucloudinternalapi({
            Backend: 'UAccount',
            Action: 'IGetUserAuthInfoList',
            CompanyId: companyId,
        })
        return authProcessingPlant(authInfo)
    } else {
        // 获取专属云实名认证信息
        let authInfo = await ucloudinternalapi({
            Backend: 'virtualseller-realname',
            Action: 'GetUserAuthInfo',
            Channel: parseInt(channel),
            _returnCompleteInfo: true,
            _originSystem: 'DML_CRD',
            company_id: companyId,
            headers: { 
                'x-company-id': +companyId,
                'x-user-id': 0,
                'x-channel-id': +channel,
            },
        })
        if (authInfo.RetCode !== 0 || authInfo.AuthState !== 'CERTIFIED') {
            return {
                AuthType: 'NO_AUTH',
            }
        } else {
            // 判断是企业还是个人 0企业 1个人
            if (authInfo.AuthType !== 0) {
                return {
                    AuthType: '个人认证',
                    CertificateType: authInfo.CertificateType,
                    Name: authInfo.UserName || authInfo.FormData?.UserName,
                    CertificateNo: authInfo.IdentityNo,
                }
            } else {
                return {
                    AuthType: '企业认证',
                    CertificateType: authInfo.CertificateType || '',
                    Name: authInfo.CompanyName,
                    CertificateNo: authInfo.CreditCode,
                }
            }
        }
    }
}

// 整理实名信息到需要的格式
function authProcessingPlant(authInfo) {
    // 根据实名类型取出对应信息
    // 检查返回结果
    let processedInfo = {}

    if (
        authInfo['AuthInfo'] === undefined ||
        authInfo.AuthInfo.length === 0 ||
        _.findIndex(authInfo.AuthInfo, ['AuditState', '已认证']) === -1
    ) {
        processedInfo = {
            AuthType: 'NO_AUTH',
        }
        return processedInfo
    }

    // 可能同时存在2条记录，如果存在，取企业的实名信息，保证进入下个流程的数组长度为1
    if (authInfo.AuthInfo.length === 2) {
        let authIndex = _.findIndex(authInfo.AuthInfo, ['AuthType', '企业认证'])
        authInfo.AuthInfo = authInfo.AuthInfo[authIndex]
    }

    if (authInfo.AuthInfo[0].AuthType === '个人认证') {
        processedInfo = {
            AuthType: authInfo.AuthInfo[0].AuthType,
            CertificateType: authInfo.AuthInfo[0].CertificateType,
            Name: authInfo.AuthInfo[0].UserName,
            CertificateNo: authInfo.AuthInfo[0].IdentityNo,
        }
    } else {
        processedInfo = {
            AuthType: authInfo.AuthInfo[0].AuthType,
            CertificateType: authInfo.AuthInfo[0].CertificateType,
            Name: authInfo.AuthInfo[0].CompanyName,
            CertificateNo: authInfo.AuthInfo[0].CreditCode,
        }
    }

    return processedInfo
}

module.exports = {
    getAuthInfo,
    authProcessingPlant,
}
