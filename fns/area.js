import area from '../configs/common/area.json'
import _ from 'underscore'

export function splitAreaId(areaId) {
    if (areaId == null) {
        return {
            district: null,
            city: null,
            province: null,
        }
    }
    let district = areaId
    let city = Math.floor(areaId / 100) * 100
    let province = Math.floor(areaId / 10000) * 10000
    if (district === city) {
        district = null
    }
    if (city === province) {
        city = null
    }
    return {
        district,
        city,
        province,
    }
}

export function getPhonePre(addressId) {
    const province = Math.floor(addressId / 10000) * 10000
    let provinceInfo =
        area &&
        province &&
        _.find(area, (provinceInfo) => provinceInfo.Id === province)
    return provinceInfo && provinceInfo.PhonePre
}

export function getProvinceByRefer(refer) {
    const provinceInfo =
        area && _.find(area, (provinceInfo) => provinceInfo.Refer === refer)
    return provinceInfo
}

export function getShotProvinceId(areaId) {
    let { province } = splitAreaId(areaId)
    return province ? province / 10000 : null
}

export const findProvinces = (provinceId, provinces) => {
    return _.findIndex(provinces, (province) => province === provinceId) > -1
}

export const getProvinceId = (order) => {
    let provinceId
    switch (+order.Type) {
        case 1:
        case 7:
            provinceId = order.AreaId
                ? splitAreaId(order.AreaId).province
                : null
            break
        case 2:
            if (order.ICPMainNo) {
                let icpFirstLetter = order.ICPMainNo.substr(0, 1)
                let provinceInfo = getProvinceByRefer(icpFirstLetter)

                provinceId = provinceInfo ? provinceInfo.Id : null
            } else {
                provinceId = null
            }
            break
        case 3:
        case 10:
            if (
                order.Website &&
                order.Website[0] &&
                order.Website[0].ICPWebNo
            ) {
                let icpFirstLetter = order.Website[0].ICPWebNo.substr(0, 1)
                let provinceInfo = getProvinceByRefer(icpFirstLetter)

                provinceId = provinceInfo ? provinceInfo.Id : null
            } else {
                provinceId = null
            }
            break
        default:
            break
    }
    return provinceId
}

export const findArea = (order = {}, provinces) => {
    let shotProvinceId = getShotProvinceId(getProvinceId(order))
    return findProvinces(shotProvinceId, provinces)
}