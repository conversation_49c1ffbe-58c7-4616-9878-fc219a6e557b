const uuid = require('uuid/v4')
const innerApi = require('../libs/ucloud-api-request/index')
const logger = require('../libs/logger')

const innerRequest = function ({
    category,
    action,
    data,
    public_key,
    secret_key,
    method = 'POST',
    timeout = 2000,
}) {
    return new Promise((rs) => {
        innerApi.request(
            {
                method,
                category,
                action,
                session: {
                    public_key,
                    secret_key,
                },
                data,
                options: {
                    timeout,
                },
                json: true,
            },
            (err, body) => rs([body, err])
        )
    })
}

const sleep = (millseconds = 3000) => {
    return new Promise((rs) => {
        setTimeout(() => rs(), millseconds)
    })
}

module.exports = async function request({
    options,
    data,
    uid = uuid(),
    retryTimes = 0,
    sleepTime = 300,
    timeout = 3000,
    public_key = global.CONFIG.internalApiKeys.publicKey,
    secret_key = global.CONFIG.internalApiKeys.secretKey,
} = {}) {
    if (options.action === undefined || options.category === undefined) {
        logger
            .getLogger('error')
            .error('[' + uid + ']NO ACTION OR CATEGORY IN OPTIONS')
        return Promise.reject(Error('NO ACTION OR CATEGORY IN OPTIONS'))
    }
    logger
        .getLogger('api')
        .info('[' + uid + '] Options:' + JSON.stringify(options))
    logger.getLogger('api').info('[' + uid + '] Data:' + JSON.stringify(data))

    let params = Object.assign({}, options, {
        data,
        public_key,
        secret_key,
        timeout,
    })

    let err
    while (retryTimes >= 0) {
        const [body, error] = await innerRequest(params)
        if (!error) {
            logger.getLogger('api').info('[' + uid + ']', JSON.stringify(body))
            return body
        }
        await sleep(sleepTime)

        retryTimes--
        err = error
    }

    logger.getLogger('api').error('[' + uid + ']', err.message)
    return Promise.reject(err)
}
