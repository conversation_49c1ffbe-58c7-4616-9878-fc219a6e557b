'use strict'
const axiosApi = require('../../libs/axiosApi')
/**
 * 通过渠道ID获取渠道信息
 * @param {*} channelId 
 * @param {*} target enum { 'language', 'isDedicatedCloud' }
 * @returns 
 */
module.exports = async (channelId, target) => {
    let res = await axiosApi({
        url: global.CONFIG.ucloudInternalApi,
        method: 'POST',
        data: {
            Action: 'IGetChannelInfo',
            Backend: 'UAccountManager',
            ChannelId: channelId,
        },
    })

    switch (target) {
        case 'language':
            return res.data?.Data[0]?.ExtendData?.MainLang
        case 'isDedicatedCloud':
            return res.data?.Data[0]?.ExtendData?.IsDedicatedCloud
        default:
            return res.data
    }
}
