/*获取公司下所有认证的信息的接口，是目前账户的最新接口，建议使用此接口
 * @Date: 2022-07-25 16:11:02
 * @LastEditors: l<PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2022-07-25 18:00:27
 * @FilePath: /newicp/fns/auth/getUserAuthInfo.js
 */
'use strict'

const ucloudInternalApi = require('../ucloudInternalApi')
const _ = require('lodash')

const CERTIFICATETYPE = [
    { id: 1, value: '身份证' },
    { id: 2, value: '回乡证/台胞证' },
    { id: 3, value: '护照' },
    { id: 4, value: '台湾通行证复合证照' },
    { id: 5, value: '护照复合证照' },
    { id: 6, value: '港澳通行证复合证照' },
]
const REGIONTYPE = [
    { id: 1, value: '大陆' },
    { id: 2, value: '港澳台' },
    { id: 3, value: '海外' },
]
const AUTHMETHOD = [
    { id: 1, value: '人工审核' },
    { id: 2, value: '自动审核' },
    { id: 3, value: '活体认证' },
]

const AUDITMAP = {
    1: { 0: '未认证' },
    2: { 0: '待审核', 1: '法务不通过', 2: '企业自动审核' },
    3: {
        0: '法务通过/财务待审核',
        1: '财务待认证',
        2: '财务已驳回',
        3: '财务自动打款',
    },
    4: { 1: '已认证' },
}
const FaceAUDITMAP = {
    10: '待活体视频认证',
    11: '活体视频认证失败',
    20: '活体视频认证成功',
    21: '证件照上传失败',
    30: '证件照上传成功',
    31: '实名信息验证失败',
    40: '实名信息验证成功',
}

const UNIVERISTYAUDITMAP = {
    3: '高校认证信息IP验证失败',
    4: '高校认证信息验证成功',
    2: '高校认证成功',
}

// 处理各类值的翻译
function parseTypeToCn(authObject) {
    // 证件类型
    if (typeof authObject.CertificateType === 'number') {
        authObject.CertificateType = getMapValue(
            CERTIFICATETYPE,
            authObject.CertificateType
        )
    }
    // 地区(1:大陆，2:港澳台，3:海外)
    authObject.RegionType = getMapValue(REGIONTYPE, authObject.RegionType)

    // AuthMethod(1:人工审核, 2:自动审核, 3:活体认证)
    authObject.AuthMethod = getMapValue(AUTHMETHOD, authObject.AuthMethod)

    // 账号类型(1:银行账号，2:支付宝账号)
    if (authObject.AccountType) {
        authObject.AccountType =
            authObject.AccountType == 1 ? '银行账号' : '支付宝账号'
    }

    // authObject.AuditState = authObject.AuditStatus == 0 ? '待审核' : '已认证'
    if (authObject.AuthType === '个人认证-活体') {
        authObject.AuditState = FaceAUDITMAP[authObject.AuditStatus]
    } else if (authObject.AuthType === '个人认证-机构') {
        authObject.AuditState = UNIVERISTYAUDITMAP[authObject.AuditStatus]
    } else {
        authObject.AuditState =
            AUDITMAP[authObject.AuditPhrase + ''][authObject.AuditStatus + '']
    }
    return authObject
}
// 直接取值会有判空的问题
function getMapValue(map, value) {
    let valueIndex = _.findIndex(map, ['id', value])
    if (valueIndex !== -1) {
        return map[valueIndex].value
    } else {
        return value
    }
}
/**
 * 获取用户认证信息
 * @param {*} companyId  公司id
 * @param {*} authType 认证类型 0为企业认证，1为个人认证，无传参则为均获取
 * @returns
 */
async function getAuthInfoByCompanyId(companyId, authType) {
    // 使用Promise.all的方式

    let authInfoOther = await getUserAuthInfoPromiseV2(companyId)
    if (authInfoOther && authInfoOther.ERR)
        return Promise.reject(authInfoOther.ERR)

    let returnResult = []
    if (
        authInfoOther &&
        authInfoOther.BusinessAuth &&
        authInfoOther.BusinessAuth.CompanyId !== 0
    ) {
        authInfoOther.BusinessAuth.AuthType = '企业认证'
        returnResult.push(parseTypeToCn(authInfoOther.BusinessAuth))
    }
    if (
        authInfoOther &&
        authInfoOther.FaceAuth &&
        authInfoOther.FaceAuth.CompanyId !== 0
    ) {
        authInfoOther.FaceAuth.AuthType = '个人认证-活体'

        returnResult.push(parseTypeToCn(authInfoOther.FaceAuth))
    }
    if (
        authInfoOther &&
        authInfoOther.UniversityAuth &&
        authInfoOther.UniversityAuth.CompanyId !== 0
    ) {
        authInfoOther.UniversityAuth.AuthType = '个人认证-机构'
        returnResult.push(parseTypeToCn(authInfoOther.UniversityAuth))
    }
    if (
        authInfoOther &&
        authInfoOther.BankAuth &&
        authInfoOther.BankAuth.CompanyId !== 0
    ) {
        authInfoOther.BankAuth.AuthType = '个人认证'
        returnResult.push(parseTypeToCn(authInfoOther.BankAuth))
    }

    if (authType !== undefined && authType == 0) {
        returnResult = _.filter(returnResult, { AuthType: '企业认证' })
    }
    if (authType && authType == 1) {
        returnResult = _.filter(
            returnResult,
            (res) => res.AuthType !== '企业认证'
        )
    }

    return returnResult
}

function getUserAuthInfoPromiseV2(companyId) {
    return new Promise((rs, rj) => {
        // 使用四合一的接口。如果确定公司Id的情况下。查询活体与学生认证的数据
        // AuthType 接口方默认为0
        // 0 查企业
        // 1 查个人
        ucloudInternalApi(
            {
                category: 'UAccount',
                method: 'POST',
                action: 'IGetCompanyAuthInfo',
            },
            {
                Action: 'IGetCompanyAuthInfo',
                Backend: 'UAccount',
                CompanyId: companyId,
            },
            (err, body) => {
                if (err) return rj(err)

                if (body.RetCode !== 0) return rj(new Error(body.Message || ''))
                rs(body.AuthInfo)
            }
        )
    })
}

module.exports = getAuthInfoByCompanyId
