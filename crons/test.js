/**
 * @file test
 * <AUTHOR>
 */

const Cron = require('../libs/cron')
const moment = require('moment')
const _ = require('lodash')

module.exports = class CheckCompanyInfoByTYC extends Cron {
    constructor() {
        super()
        // 30秒查一次
        this.frequency = '1 * * * * *'
        this.active = 0
    }
    async main() {
        let now = moment().format('X')

        try {
            // 获取token
            return Promise.reject(
                e instanceof Error ? { err: '11111', code: 31901 } : e
            )

            return 0
        } catch (e) {
            // 发送错误，更新记录为发送失败状态
            return Promise.reject(
                e instanceof Error ? { err: e, code: 31901 } : e
            )
        }
    }
    async exec(sid, logger, cb) {
        this.main()
            .then(cb)
            .catch((err) => {
                // 正常结束，日志中说明原因
                if (err.code === 0) {
                    logger.info(err.err)
                    return cb()
                }

                // 错误处理
                cb(
                    new this.Err(
                        err.err instanceof Error ? err.err : Error(err.err),
                        err.code
                    )
                )
            })
    }
}

async function getNoRecordDomain(token) {
    // 获取未备案
    let timeRange = []
    // 结束时间为今天9点

    if (moment().format('d') === '1') {
        // 确定今天周几，如果是周一，拉取上周六9点至周一9点的记录（接口只能请求1天的数据）
        for (const key in [0, 1, 2]) {
            // 得前3天的时间范围
            // 艹，这玩意花了我1小时
            console.log(key, 333)
            timeRange.push({
                startTime: moment()
                    .startOf('day')
                    .add(9, 'hours')
                    .subtract(1, 'days')
                    .subtract(key, 'days')
                    .format('YYYY-MM-DD HH:MM:SS'),
                endTime: moment()
                    .startOf('day')
                    .add(9, 'hours')
                    .subtract(key, 'days')
                    .format('YYYY-MM-DD HH:MM:SS'),
            })
        }
    } else {
        timeRange.push({
            startTime: moment()
                .startOf('day')
                .add(9, 'hours')
                .subtract(1, 'days')
                .format('YYYY-MM-DD HH:MM:SS'),
            endTime: moment()
                .startOf('day')
                .add(9, 'hours')
                .format('YYYY-MM-DD HH:MM:SS'),
        })
    }
    console.log(timeRange)
    let recordList = []
    // 发起请求
    for (const key in timeRange) {
        if (timeRange.hasOwnProperty(key)) {
            const element = timeRange[key]
            let { code, data, msg } = await getNoRecordDomainInAodun(
                this,
                element.startTime,
                element.endTime,
                token
            )
            console.log(code, data, msg, token)
            if (code === 0) {
                recordList.push(...data)
            }
        }
    }
    console.log(33, recordList, 44)
    return recordList
}
