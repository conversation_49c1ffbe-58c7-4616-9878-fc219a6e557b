/*
 * @Date: 2022-09-05 11:16:55
 * @LastEditors: william.qian <EMAIL>
 * @LastEditTime: 2023-10-12 14:07:56
 * @FilePath: /newicp/crons/AutoSyncICPInfoFromHengan.js
 */
/**
 *同步恒安的备案信息 和我们redis缓存保持一致
 */
const moment = require('moment')
const Cron = require('../libs/cron')
const redis = require('../libs/redis')
const uuid = require('uuid/v4')
const CronLogger = require('../libs/cronLogger')
const producer = require('../libs/producer')
const henganApi = require('../libs/henganApiPromise')
const _ = require('lodash')
const axiosAPI = require('../libs/axiosApi')
const name = 'AutoSyncICPInfoFromHengan'

Cron.setCron(name, {
    cron: '1 */10 * * * *', // 每10分钟查询一次
    startDate: new Date(),
})

//设置定时任务的消费
Cron.process(name, async function main() {
    let sid = uuid()
    let cronLogger = new CronLogger('AutoSyncICPInfoFromHengan', sid)
    try {
        const redisClient = redis.get()
        cronLogger.info('start')
        let allHenganRes = await henganApi('SelectICPInfoAction', {
            KeyWordType: 6,
            Keyword: `2006-01-12 13:44:51~${moment().format(
                'YYYY-MM-DD HH:mm:ss'
            )}`,
            QueryType: 2,
            timeout: 60 * 1000,
        })

        let henganInfo = {}
        let henganDomains = []
        allHenganRes.ICPInfos?.forEach((info) => {
            henganDomains.push(info.topdomain)
            if (henganInfo[info.topdomain] === undefined) {
                henganInfo[info.topdomain] = info.phylicnum
            }
        })
        // 没有域名的话 数据不正确
        if (henganDomains.length > 0) {
            let allBeianInfo = await redisClient.hgetall(
                'domain_icp_in_ucloud_map'
            )

            let localDomainRes = Object.keys(allBeianInfo)

            let { needAdd, needDel } = await step1(
                henganDomains,
                localDomainRes
            )
            if (needDel?.length > 0) {
                for (let domain of needDel) {
                    await producer.send({
                        type: 'icp',
                        topic: 'DelDomainFromCache',
                        data: {
                            Domain: domain,
                        },
                    })
                }
            }
            if (needAdd?.length > 0) {
                for (let domain of needAdd) {
                    await producer.send({
                        type: 'icp',
                        topic: 'AddDomainToCache',
                        data: {
                            Domain: domain,
                            ICPWebNo: henganInfo[domain],
                        },
                    })
                }
            }
            let { needChange } = await step2(henganInfo, allBeianInfo)
            if (needChange?.length !== 0) {
                for (let domain of needChange) {
                    await producer.send({
                        type: 'icp',
                        topic: 'ChangeDomainToCache',
                        data: {
                            Domain: domain,
                            NewICPWebNo: henganInfo[domain],
                            OldICPWebNo: allBeianInfo[domain],
                        },
                    })
                }
            }
        } else {
            cronLogger.info('Finish no data error')
        }
        cronLogger.info('Finish successfully')
    } catch (e) {
        let err = new Error(`备案信息同步至redis定时任务出错,${e}`)
        cronLogger.error(err)
        cronLogger.info('Finish with error')
    }
})
/**
 * 对比本地和恒安的 需要新增 和删除的数据
 * @param {*} henganDomains
 * @param {*} localDomainRes
 */
async function step1(henganDomains, localDomainRes) {
    // 获取 本地有的 恒安没有的 local -hengan
    let awaitDeleteLocal = _.difference(localDomainRes, henganDomains)

    // 获取 恒安有 本地没有的  hengan - local
    console.log(henganDomains.length, localDomainRes.length, '------')

    let awaitInsertLocal = _.difference(henganDomains, localDomainRes)
    //将要添加和删除的推送入 对应处理mq
    return { needAdd: awaitInsertLocal, needDel: awaitDeleteLocal }
}
/**
 * 对比本地和恒安的 需要变更的数据
 * @param {*} henganDomains
 * @param {*} localDomainRes
 */
async function step2(hegnanInfo, allBeianInfo) {
    // 获取交集
    let step2Res = _.intersection(
        Object.keys(hegnanInfo),
        Object.keys(allBeianInfo)
    )
    // 循环所有的域名
    // 记录本地的域名 和恒安不一致的备案号的信息
    let theDiffrences = []
    for (let domain of step2Res) {
        if (allBeianInfo[domain] !== hegnanInfo[domain]) {
            theDiffrences.push(domain)
        }
    }
    return { needChange: theDiffrences }
}
