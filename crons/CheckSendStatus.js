'use strict'
/**
 * 检查发送状态, job中data是设置延迟任务时 传入的批次Id
 */
const Cron = require('../libs/cron')
const {
    model: NotifyModel,
    SendStatusEnum,
    NotifyTypeEnum,
} = require('../models/t_notify')
const {
    model: RecordModel,
    RecordStatusEnum,
} = require('../models/t_notify_companyInfo')
const {
    model: BatchModel,
    BatchStatusEnum,
} = require('../models/t_notify_batch')
const { Op } = require('sequelize')
const _ = require('lodash')
const { parseJSON } = require('../fns/kits')
const uuid = require('uuid/v4')
const CronLogger = require('../libs/cronLogger')
const name = 'CheckSendStatus'

Cron.process(name, async function main({ data }) {
    let sid = uuid()
    let cronLogger = new CronLogger(name, sid)
    cronLogger.info('start')
    let BatchId = data

    try {
        let [batchInfo, records] = await Promise.all([
            BatchModel.findOne({
                attributes: ['Id', 'Status'],
                where: {
                    Id: BatchId,
                    Status: BatchStatusEnum.Sending,
                },
            }),
            RecordModel.findAll({
                attributes: ['Id', 'Status'],
                where: {
                    BatchId,
                    IsError: false,
                    Status: RecordStatusEnum.Sending,
                },
            }),
        ])
        batchInfo = parseJSON(batchInfo)
        records = parseJSON(records)

        if (!batchInfo || records.length === 0) {
            //没有发现该批次，或者批次中无记录
            cronLogger.info(
                `Do not meet the query conditions, BatchId: ${BatchId}`
            )
            return
        }
        // 获取批次下所有的通知记录
        let notifys = await NotifyModel.findAll({
            attributes: ['Id', 'SendStatus'],
            where: {
                CompanyNotifyId: {
                    [Op.in]: _.map(records, 'Id'),
                },
            },
        })
        notifys = parseJSON(notifys)
        // 过滤出还在发送中的
        let unsendRecords = _.filter(notifys, (notify) => {
            return [
                SendStatusEnum.New,
                SendStatusEnum.Sending,
                SendStatusEnum.Trytosending,
            ].includes(notify.SendStatus)
        })
        let promiseArray = []
        if (unsendRecords.length === 0) {
            //没有还在发送中的，说明 所有都发送完成， 更新批次状态为发送完成
            promiseArray.push(
                BatchModel.update(
                    { Status: BatchStatusEnum.SendFinish },
                    {
                        where: {
                            Id: BatchId,
                            Status: BatchStatusEnum.Sending,
                        },
                    }
                )
            )
            // 批量处理记录的发送结果
            let sendFaildArray = []
            let timeoutArray = []
            let sendSuccessArray = []
            records.forEach((record) => {
                const tmpNotify = _.filter(
                    notifys,
                    (notify) => notify.CompanyNotifyId === record.Id
                )
                const noSuccess = _.filter(
                    tmpNotify,
                    (notify) => SendStatusEnum.Sendfinish !== notify.SendStatus
                )

                const sendFailed = _.findIndex(tmpNotify, (notify) =>
                    [
                        SendStatusEnum.Sendfailed,
                        SendStatusEnum.Recivefailed,
                    ].includes(notify.SendStatus)
                )

                const timeout = _.findIndex(
                    tmpNotify,
                    (notify) => SendStatusEnum.Timeout === notify.SendStatus
                )
                if (noSuccess.length === 0) {
                    // 没有不成功的 就是发送全部成功
                    sendSuccessArray.push(record.Id)
                } else if (sendFailed !== -1) {
                    sendFaildArray.push(record.Id)
                } else if (timeout.length !== -1) {
                    timeoutArray.push(record.Id)
                }
            })
            if (sendSuccessArray.length > 0) {
                promiseArray.push(
                    RecordModel.update(
                        {
                            Status: RecordStatusEnum.Sendfinish,
                        },
                        {
                            where: {
                                Id: {
                                    [Op.in]: sendSuccessArray,
                                },
                                Status: RecordStatusEnum.Sending,
                            },
                        }
                    )
                )
            }
            if (timeoutArray.length > 0) {
                promiseArray.push(
                    RecordModel.update(
                        { Status: RecordStatusEnum.Timeout },
                        {
                            where: {
                                Id: {
                                    [Op.in]: timeoutArray,
                                },
                                Status: RecordStatusEnum.Sending,
                            },
                        }
                    )
                )
            }
            if (sendFaildArray.length > 0) {
                promiseArray.push(
                    RecordModel.update(
                        { Status: RecordStatusEnum.Sendfailed },
                        {
                            where: {
                                Id: {
                                    [Op.in]: sendFaildArray,
                                },
                                Status: RecordStatusEnum.Sending,
                            },
                        }
                    )
                )
            }
            // 批量处理状态
            if (promiseArray.length > 0) {
                await Promise.all(promiseArray)
                cronLogger.info(`finished, BatchId: ${BatchId}`)
                return
            }
        } else {
            // 判断是否已经通知超过12小时 若是 则重新推送mq查询状态，弥补mq丢失
            //晚5分钟后再查
            cronLogger.info(
                `Unfinished， 5 minutes retry check, BatchId: ${BatchId}`
            )
            return Cron.setCron('CheckSendStatus', {
                data: BatchId,
                delay: 5 * 60 * 1000, //单位毫秒
            })
        }
    } catch (err) {
        cronLogger.error(err)
    }
})
