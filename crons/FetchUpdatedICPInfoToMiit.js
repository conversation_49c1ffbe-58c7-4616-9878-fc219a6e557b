/*
 * @Date: 2022-09-05 11:16:55
 * @LastEditors: william.qian <EMAIL>
 * @LastEditTime: 2023-08-28 17:33:53
 * @FilePath: /newicp/crons/AutoClearOrder.js
 */
/**
 * 每天凌晨1点获取两天内有更新的ICP记录，将备案号保存到Redis中，然后将备案号推送到GetICPInfoAndSendToMIITConsumer
 */
const moment = require('moment')
const Cron = require('../libs/cron')
const redis = require('../libs/redis').get()
const uuid = require('uuid/v4')
const CronLogger = require('../libs/cronLogger')
const producer = require('../libs/producer')
const _ = require('lodash')
const { Op } = require('sequelize')
const { ICPModel } = require('../models')
const { parseJSON } = require('../fns/kits')
const name = 'FetchUpdatedICPInfoToMiit'

Cron.setCron(name, {
    cron: '1 1 1 * * *', // 每天执行一次
    startDate: new Date(),
})

//设置定时任务的消费
Cron.process(name, async function main() {
    console.log('start 33')
    let sid = uuid()
    let cronLogger = new CronLogger(name, sid)
    try {
        // 获取两天前的时间戳
        let twoDaysAgo = moment().subtract(2, 'days').format('X')
        // 从数据库中查找近两天内有更新的ICP记录
        let icpRecords = await ICPModel.findAll({
            where: {
                UpdateTime: {
                    [Op.gte]: parseInt(twoDaysAgo),
                },
            },
            attributes: ['ICPMainNo'],
        })

        // 取出icpRecords中的备案号，保存到Redis中,key的名字为sync_icp_to_miit_日期。sadd的方式增加
        let redisKey = `sync_icp_to_miit_${moment().format('YYYYMMDD')}`
        let icpMainNos = icpRecords.map((icpRecord) => icpRecord.ICPMainNo)

        await redis.sadd(redisKey, icpMainNos)

        // 将查找到的ICP记录推送到GetICPInfoAndSendToMIITConsumer
        await producer.send({
            type: 'icp',
            topic: 'GetICPInfoAndSendToMIIT',
            data: { BatchName: redisKey },
        })

        cronLogger.info('Finish successfully')
    } catch (e) {
        let err = new Error(`备案信息同步至redis定时任务出错,${e}`)
        cronLogger.error(err)
        cronLogger.info('Finish with error')
    }
})
