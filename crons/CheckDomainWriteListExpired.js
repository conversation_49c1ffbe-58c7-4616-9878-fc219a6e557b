/*
 * @Date: 2022-09-05 11:16:55
 * @LastEditors: l<PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2023-03-29 10:21:37
 * @FilePath: /newicp/crons/CheckDomainWriteListExpired.js
 */
/**
 *检查域名白名单是否过期
 */
const moment = require('moment')
const { sleep, parseJSON } = require('../fns/kits')
const { getTableModel } = require('../fns/kits')
const { getToken, delTrustDomain } = require('../fns/aodun/DomainService')
const Cron = require('../libs/cron')
const { Op } = require('sequelize')
const uuid = require('uuid/v4')
const CronLogger = require('../libs/cronLogger')
const { DomainWhiteListModel } = require('../models')

const name = 'CheckDomainWriteListExpired'

Cron.setCron(name, {
    cron: '1 1 * * * *',
    startDate: new Date(),
})

//设置定时任务的消费
Cron.process(name, async function main() {
    let sid = uuid()
    let cronLogger = new CronLogger('CheckDomainWriteListExpired', sid)
    try {
        await sleep()
        cronLogger.info('start')

        const date = moment().format('X')
        // 查找 未接入 未删除 并且过期时间大于当前时间的域名
        let writeList = await DomainWhiteListModel.findAll({
            where: {
                Connected: 0,
                IsDeleted: 0,
                ExpiredTime: {
                    [Op.gt]: date,
                },
            },
        })
        writeList = parseJSON(writeList)
        if (writeList.length > 0) {
            //整理所有的 要删除的域名
            const domainList = []
            const IdList = []
            writeList.forEach((list) => {
                domainList.push(list.Domain)
                IdList.push(list.Id)
            })
            // 将失效的域名 从傲顿中删除
            const token = await getToken()

            await delTrustDomain(token, domainList)

            // 更新本地记录状态 为失效
            await DomainWhiteListModel.update(
                {
                    Status: 1,
                },
                {
                    where: {
                        Id: { [Op.in]: IdList },
                    },
                }
            )
        }
        cronLogger.info('Finish successfully')
    } catch (e) {
        let err = new Error(`检查白名单列表过期出错,${e}`)
        cronLogger.error(err)
        cronLogger.info('Finish with error')
    }
})
