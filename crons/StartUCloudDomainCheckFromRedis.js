/**
 * @file StartUCloudDomainCheckFromRedis,检查Redis中目前UCloud的已接入域名记录是否还在
 * <AUTHOR>
 * 定时执行，从Redis中获取全量的域名
 * 确定目前为任务状态，拉取指定备案数据，数据保存在Redis,推到MQ启动执行
 * 使用Redis游标功能，当游标为-1时说明请求完成
 * 2024-02-23 傲顿对于在其他接入商的 1.迁移至我司，2.备案注销 均会回调我司实时操作，按理说不需要再执行批量的每日check操作，不过可以保留，防止回调中异常情况
 */

const moment = require('moment')
const _ = require('lodash')
const Cron = require('../libs/cron')
const { objectCount } = require('../fns/kits')
const uuid = require('uuid/v4')
const { CONNECTSYNCVALUE, CONNECTSYNCCOMMENT } = require('../configs/')
const { sleep } = require('../fns/kits')
const CronLogger = require('../libs/cronLogger')
const producer = require('../libs/producer')
const mongoConf = require('../libs/mongo')
const redis = require('../libs/redis').get()

const MAXPARALLEL = 8

const name = 'StartUCloudDomainCheckFromRedis'

Cron.setCron(name, {
    cron: '1 45 23 * * *',
    startDate: new Date(),
})

Cron.process(name, async function main() {
    await sleep()
    let sid = uuid()
    let cronLogger = new CronLogger(name, sid)


    cronLogger.info('start')



    let now = parseInt(moment().format('X'))

    let allDomain,
        InsertId,
        checkBatchName = moment().format('YYYY-MM-DD') + 'DomainCheck'
    try {
        // 获取Redis中的记录
        try {
            allDomain = await redis.hgetall('domain_icp_in_ucloud_map')
            // 创建检查集
            await redis.hmset(checkBatchName, allDomain)
            await redis.expire(checkBatchName, 3 * 24 * 60 * 60) // 2天后自动过期
        } catch (error) {
            throw '检查Redis中结果出错' + error
        }

        // 启动任务
        for (var Cursor of _.fill(new Array(MAXPARALLEL), 1)) {
            Cursor = Cursor + _.random(0, 20)
            await producer.send({
                type: 'icp',
                topic: 'UCloudDomainReCheckFromRedis',
                data: { BatchName: checkBatchName, Cursor },
            })
        }
        cronLogger.info('Finish successfully')

        return 0
    } catch (e) {
        // 发送错误，更新记录为发送失败状态
        cronLogger.error(e)
        cronLogger.info('Finish with error')
    }
})
