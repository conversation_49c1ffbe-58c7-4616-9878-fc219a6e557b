/**
 *  提前取管局审核率保存，每天取前一日的数据做保存
 */
const moment = require('moment')
const { sleep, parseJSON } = require('../fns/kits')
const { getTableModel } = require('../fns/kits')
const _ = require('lodash')
const {
    DashAuditModel: dashAuditModel,
    OrderHistoryModel: historyModel,
    OrderModel: orderModel,
} = require('../models')
const Cron = require('../libs/cron')
const { fn, col, Op } = require('sequelize')
const uuid = require('uuid/v4')
const CronLogger = require('../libs/cronLogger')
const redis = require('../libs/redis')

const name = 'GetThisDayAuditCount'

Cron.setCron(name, {
    cron: '1 45 2 * * *',
    startDate: new Date(),
})

//设置定时任务的消费
Cron.process(name, async function main() {
    let sid = uuid()
    let cronLogger = new CronLogger('GetThisDayAuditCount', sid)
    try {
        await sleep()
        // 先从redis里面读取这个key的值
        const redisClient = redis.get()
        let lastId = await redisClient.get(global.CONFIG.getThisDayAuditCountKey)

        if (!lastId) {
            lastId = 0
        } else {
            lastId = parseInt(lastId)
        }

        // 获取昨天的时间范围
        let yesterday = moment().subtract(1, 'day')
        // 构造一下数据
        let startTime = yesterday.clone().startOf('day').toDate()
        let endTime = yesterday.clone().endOf('day').toDate()

        cronLogger.info(`开始统计 ${yesterday.format('YYYY-MM-DD')} 的审核数据`)

        // 1.查询t_order_history 表，查询昨天一天管局审核中的数据, status = 12 的数据，根据operator字段为唯一标识，resolve_count为查找出来的数量
        let submissionRecords = await historyModel.findAll({
            attributes: ['Id', 'OrderNo', 'Operator', 'OperationTime'],
            where: {
                Id: {
                    [Op.gt]: lastId
                },
                Status: 11, // 管局审核中
                OperationTime: {
                    [Op.between]: [startTime, endTime],
                },
                Operator: {
                    [Op.like]: '%.%' // 包含人名
                }
            },
            order: [['OperationTime', 'ASC']],
        })
        submissionRecords = parseJSON(submissionRecords)

        cronLogger.info(`找到 ${submissionRecords.length} 条管局审核记录`)
        // 查询t_order表，过滤注销类的
        let orderNos = _.map(submissionRecords, 'OrderNo')
        let orderList = await orderModel.findAll({
            attributes: ['OrderNo'],
            where: {
                OrderNo: {
                    [Op.in]: orderNos,
                },
                Type: {
                    [Op.in]: [4, 5, 6],
                },
            },
        })
        // submissionRecords过滤掉orderList相同的数据
        submissionRecords = _.filter(submissionRecords, (submission) => {
            return !_.find(orderList, { OrderNo: submission.OrderNo })
        })

        // 2.查询t_order_history 表，查询昨天一天管局退回的数据 status = 15
        let rejectRecords = await historyModel.findAll({
            attributes: ['Id', 'OrderNo', 'Info', 'OperationTime'],
            where: {
                Id: {
                    [Op.gt]: lastId
                },
                Status: 15, // 管局退回
                OperationTime: {
                    [Op.between]: [startTime, endTime],
                },
                Info: {
                    [Op.ne]: null, // 排除空值
                    [Op.notLike]: '管局系统退回%' // 排除系统退回，只统计人工退回
                }
            },
            order: [['OperationTime', 'ASC']],
        })

        rejectRecords = parseJSON(rejectRecords)
        cronLogger.info(`找到 ${rejectRecords.length} 条管局人工退回记录`)

        // 查询管局退回后再更新Id
        if (submissionRecords.length > 0) {
            lastId = submissionRecords[submissionRecords.length - 1].Id || lastId
        }

        // 初始化统计结果
        let operatorStats = {}

        // 统计每个审核员的提交数量（resolve_count）
        submissionRecords.forEach((submission) => {
            if (!operatorStats[submission.Operator]) {
                operatorStats[submission.Operator] = {
                    resolve_count: 0,
                    reject_count: 0,
                }
            }
            operatorStats[submission.Operator].resolve_count++
        })

        // 3.查询第二步 status = 15的数据，上一个提交人是谁，也就是status = 12 的operator，将其标记为reject_count
        for (let reject of rejectRecords) {
            // 查找该订单最近一次status=12的提交记录
            let lastSubmission = await historyModel.findOne({
                attributes: ['Operator'],
                where: {
                    OrderNo: reject.OrderNo,
                    Status: 11,
                    OperationTime: {
                        [Op.lt]: reject.OperationTime // 在退回时间之前
                    }
                },
                order: [['OperationTime', 'DESC']], // 按时间倒序，取最近的一次
            })

            if (lastSubmission) {
                let operator = lastSubmission.Operator
                // 检查一下这个operator是不是真正的人名，避免误判
                if (!operator.includes('.')) {
                    continue
                }
                if (!operatorStats[operator]) {
                    operatorStats[operator] = {
                        resolve_count: 0,
                        reject_count: 0,
                    }
                }
                operatorStats[operator].reject_count++
            }
        }

        // 准备批量插入的数据
        let resultList = []

        Object.keys(operatorStats).forEach(operator => {
            let stats = operatorStats[operator]
            if (stats.resolve_count > 0 || stats.reject_count > 0) {
                resultList.push({
                    Operator: operator,
                    ResolveCount: stats.resolve_count,
                    RejectCount: stats.reject_count,
                    Date: startTime,
                })
            }
        })

        cronLogger.info(`准备插入 ${resultList.length} 条统计记录`)

        // 6.将结果写入到数据库中dashAuditModel中
        if (resultList.length > 0) {
            await dashAuditModel.bulkCreate(resultList)
            cronLogger.info('统计数据插入成功')
        }

        await redisClient.set(global.CONFIG.getThisDayAuditCountKey,lastId)

        cronLogger.info('Finish successfully')
    } catch (e) {
        let err = new Error(`获取今日审核数据出错,${e}`)
        cronLogger.error(err)
        cronLogger.info('Finish with error')
    }
})
