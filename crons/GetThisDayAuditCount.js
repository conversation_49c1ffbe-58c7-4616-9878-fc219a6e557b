/**
 *  提前取管局审核率保存，每天取前一日的数据做保存
 */
const moment = require('moment')
const { sleep, parseJSON } = require('../fns/kits')
const { getTableModel } = require('../fns/kits')
const _ = require('lodash')
const {
    DashAuditModel: dashAuditModel,
    OrderHistoryModel: historyModel,
    OrderModel: orderModel,
} = require('../models')
const Cron = require('../libs/cron')
const { fn, col, Op } = require('sequelize')
const uuid = require('uuid/v4')
const CronLogger = require('../libs/cronLogger')

const name = 'GetThisDayAuditCount'

Cron.setCron(name, {
    cron: '1 45 2 * * *',
    startDate: new Date(),
})

//设置定时任务的消费
Cron.process(name, async function main() {
    let sid = uuid()
    let cronLogger = new CronLogger('GetThisDayAuditCount', sid)
    try {
        await sleep()

        // 获取昨天的时间范围
        let yesterday = moment().subtract(1, 'day')
        let startTime = yesterday.startOf('day').toDate()
        let endTime = yesterday.endOf('day').toDate()

        cronLogger.info(`开始统计 ${yesterday.format('YYYY-MM-DD')} 的审核数据`)

        // 1.查询t_order_history 表，查询昨天一天管局审核中的数据, status = 12 的数据，根据operator字段为唯一标识，resolve_count为查找出来的数量
        let submissionRecords = await historyModel.findAll({
            attributes: ['Id', 'OrderNo', 'Operator', 'OperationTime'],
            where: {
                Status: 12, // 管局审核中
                OperationTime: {
                    [Op.between]: [startTime, endTime],
                },
            },
            order: [['OperationTime', 'ASC']],
        })

        submissionRecords = parseJSON(submissionRecords)
        cronLogger.info(`找到 ${submissionRecords.length} 条管局审核记录`)

        // 2.查询t_order_history 表，查询昨天一天管局退回的数据 status = 15
        let rejectRecords = await historyModel.findAll({
            attributes: ['Id', 'OrderNo', 'Info', 'OperationTime'],
            where: {
                Status: 15, // 管局退回
                OperationTime: {
                    [Op.between]: [startTime, endTime],
                },
                Info: {
                    [Op.not]: {
                        [Op.like]: '管局系统退回%' // 排除系统退回，只统计人工退回
                    }
                }
            },
            order: [['OperationTime', 'ASC']],
        })

        rejectRecords = parseJSON(rejectRecords)
        cronLogger.info(`找到 ${rejectRecords.length} 条管局人工退回记录`)

        // 初始化统计结果
        let operatorStats = {}

        // 统计每个审核员的提交数量（resolve_count）
        submissionRecords.forEach((submission) => {
            if (!operatorStats[submission.Operator]) {
                operatorStats[submission.Operator] = {
                    resolve_count: 0,
                    reject_count: 0,
                }
            }
            operatorStats[submission.Operator].resolve_count++
        })

        // 3.查询第二步 status = 15的数据，上一个提交人是谁，也就是status = 12 的operator，将其标记为reject_count
        for (let reject of rejectRecords) {
            // 查找该订单最近一次status=12的提交记录
            let lastSubmission = await historyModel.findOne({
                attributes: ['Operator'],
                where: {
                    OrderNo: reject.OrderNo,
                    Status: 12,
                    OperationTime: {
                        [Op.lt]: reject.OperationTime // 在退回时间之前
                    }
                },
                order: [['OperationTime', 'DESC']], // 按时间倒序，取最近的一次
            })

            if (lastSubmission) {
                let operator = lastSubmission.Operator
                if (!operatorStats[operator]) {
                    operatorStats[operator] = {
                        resolve_count: 0,
                        reject_count: 0,
                    }
                }
                // 从resolve_count中减去1，加到reject_count中
                if (operatorStats[operator].resolve_count > 0) {
                    operatorStats[operator].resolve_count--
                }
                operatorStats[operator].reject_count++
            }
        }

        // 准备批量插入的数据
        let resultList = []
        let statisticDate = yesterday.toDate()

        Object.keys(operatorStats).forEach(operator => {
            let stats = operatorStats[operator]
            if (stats.resolve_count > 0 || stats.reject_count > 0) {
                resultList.push({
                    operator: operator,
                    resolve_count: stats.resolve_count,
                    reject_count: stats.reject_count,
                    date: statisticDate,
                })
            }
        })

        cronLogger.info(`准备插入 ${resultList.length} 条统计记录`)

        // 6.将结果写入到数据库中dashAuditModel中
        if (resultList.length > 0) {
            await dashAuditModel.bulkCreate(resultList)
            cronLogger.info('统计数据插入成功')
        }

        cronLogger.info('Finish successfully')
    } catch (e) {
        let err = new Error(`获取今日审核数据出错,${e}`)
        cronLogger.error(err)
        cronLogger.info('Finish with error')
    }
})
