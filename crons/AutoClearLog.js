/*
 * @Date: 2022-09-05 11:16:55
 * @LastEditors: l<PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2023-05-30 17:45:28
 * @FilePath: /newicp/crons/AutoClearOrder.js
 */
/**
 * 每天清理30天前的日志，保持日志只保留最近30天
 */
const moment = require('moment')
const Cron = require('../libs/cron')
const uuid = require('uuid/v4')
const CronLogger = require('../libs/cronLogger')
const fs = require('fs');
const path = require('path');
const name = 'AutoClearLog'

Cron.setCron(name, {
    cron: '1 1 1 * * *', // 每天 1时1分1秒执行
    startDate: new Date(),
})

//设置定时任务的消费
Cron.process(name, async function main() {
    let sid = uuid()
    let cronLogger = new CronLogger('AutoClearLog', sid)
    try {
        cronLogger.info('start')
        
        const files = fs.readdirSync(global.CONFIG.logPath);
        const thirtyDaysAgo = moment().subtract(30, 'days');

        files.forEach(file => {
            const match = file.match(/^(access|api|error|mq|cron)\.log\.(\d{4}-\d{2}-\d{2})$/);
            if (match) {
                /** match 格式
                 * [
                    'access.log.2024-05-27',
                    'access',
                    '2024-05-27',
                    index: 0,
                    input: 'access.log.2024-05-27',
                    groups: undefined
                    ]
                 */
                const fileDate = moment(match[2], 'YYYY-MM-DD');
                if (fileDate.isBefore(thirtyDaysAgo)) {
                    fs.unlinkSync(path.join(global.CONFIG.logPath, file));
                    cronLogger.info(`Deleted file: ${file}`);
                }
            }
        });

        cronLogger.info('Finish successfully')
    } catch (e) {
        let err = new Error(`清理日志出错,${e}`)
        cronLogger.error(err)
        cronLogger.info('Finish with error')
    }
})
