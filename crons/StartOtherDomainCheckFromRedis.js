/**
 * @file StartOtherDomainCheckFromRedis,检查Redis中目前在供应商接入的备案记录
 * <AUTHOR>
 * 定时执行，从Redis中获取全量的域名
 * 与时代互联对比，未通过的记录与新网对比
 * 2021年10月22日16:10:11  todo 等新网提供接口
 * 确定目前为任务状态，拉取指定备案数据，到MQ
 */
const moment = require('moment')
const _ = require('lodash')
const Cron = require('../libs/cron')
const { getAllDomainInNOWCN } = require('../fns/connect_check_fns/nowCN')
const {
    getAllDomainInProxyWhite,
} = require('../fns/connect_check_fns/get_porxy_domain_white')
const { sleep, parseJSON, getTableModel } = require('../fns/kits')
const MAXPARALLEL = 8
const { CONNECTSYNCVALUE, CONNECTSYNCCOMMENT } = require('../configs/')
const uuid = require('uuid/v4')
const CronLogger = require('../libs/cronLogger')
const producer = require('../libs/producer')
const mongoConf = require('../libs/mongo')
const { ProxyCompanyWhiteDomainModel: whiteList} = require('../models')
const redis = require('../libs/redis').get()
const name = 'StartOtherDomainCheckFromRedis'

Cron.setCron(name, {
    cron: '1 45 1 * * *',
    startDate: new Date(),
})

//设置定时任务的消费
Cron.process(name, async function main() {
    let sid = uuid()
    let cronLogger = new CronLogger('StartOtherDomainCheckFromRedis', sid)

    cronLogger.info('start')



    let now = parseInt(moment().format('X'))

    let allDomainInNowCN = {}, // 时代互联的全部域名
        allOtherDomainInRedis, // 目前线上Redis全部其它域名
        allDomainInPorxyCompanyList,
        checkBatchName = moment().format('YYYY-MM-DD') + 'OtherDomainCheck'

    try {
        // 获取2处记录
        try {
            let nowCnRowData
            ;[
                allOtherDomainInRedis,
                nowCnRowData,
                allDomainInPorxyCompanyList,
            ] = await Promise.all([
                redis.hgetall('domain_icp_in_other_map'),
                getAllDomainInNOWCN(),
                getAllDomainInProxyWhite(whiteList),
            ])
            allDomainInPorxyCompanyList = parseJSON(allDomainInPorxyCompanyList)

            // 把新网的域名处理成需要的格式
            nowCnRowData.forEach((element) => {
                _.forEach(element, function (value, key) {
                    allDomainInNowCN[key] = value
                })
            })
        } catch (error) {
            throw '检查Redis中结果出错' + error
        }

        // 取出Redis有，时代没有的记录推到Mq
        // todo查询🆕网的数据   'gdlxny.com': '粤ICP备11085572号-1',
        // 取出全局有，但时代互联无的Domain

        // 取出域名列表
        let allWhiteDomain = _.map(allDomainInPorxyCompanyList, 'Domain')

        let allOtherDomain = _.keys(allOtherDomainInRedis)
        let allNowCnDomain = _.keys(allDomainInNowCN)
        let domainNeedCheck = _.difference(allOtherDomain, allNowCnDomain)
        domainNeedCheck = _.difference(domainNeedCheck, allWhiteDomain)

        // 得出需要检查的Map

        // 获取Redis中的记录
        // console.log(_.pick(allOtherDomainInRedis, domainNeedCheck))

        try {
            await Promise.all([
                // 创建检查集
                redis.sadd(checkBatchName, domainNeedCheck),
                redis.expire(checkBatchName, 3 * 24 * 60 * 60),
                redis.hmset('domain_icp_in_other_map', allDomainInNowCN), // 更新最新取到的
            ]) // 2天后自动过期)
        } catch (error) {
            throw '插入Redis中结果出错' + error
        }

        for (let index = 0; index < 9; index++) {
            await producer.send({
                type: 'icp',
                topic: 'XinNetDomainReCheckFromRedis',
                data: {
                    BatchName: checkBatchName,
                },
            })
        }

        cronLogger.info('Finish successfully')

        return 0
    } catch (e) {
        cronLogger.error(e)
        cronLogger.info('Finish with error')
    }
})
