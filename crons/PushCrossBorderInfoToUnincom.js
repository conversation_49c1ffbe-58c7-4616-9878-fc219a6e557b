'use strict'
/**
 * 检查昨日是否有新增的已完成的跨境申请，若有 推送给联通
 */
const Cron = require('../libs/cron')
const uuid = require('uuid/v4')
const CronLogger = require('../libs/cronLogger')
const errorCode = require('../configs/error')
const { CrossBorderApplyModel, ApplyStatusEnum } = require('../models')
const { Op } = require('sequelize')
const moment = require('moment')
const { parseJSON } = require('../fns/kits')
const name = 'PushCrossBorderInfoToUnincom'
const ucloudinternalapi = require('../libs/ucloudinternalapi')
const { sendMail } = require('../fns/umsMsgSend')
const { convertUS3FileToBase64 } = require('../fns/kits')
// 每天早上8点推送
Cron.setCron(name, {
    cron: '0 */3 * * * *',
    startDate: new Date(),
})

Cron.process(name, async function main() {
    let sid = uuid()
    let cronLogger = new CronLogger(name, sid)
    try {
        cronLogger.info('start')
        // 昨天凌晨的时间戳
        let yesterday = moment().subtract(1, 'days').startOf('day').format('X')
        let ApplyInfoList = await CrossBorderApplyModel.findAll({
            where: {
                Status: ApplyStatusEnum.AduitPass,
                CompanyId: 66439826,
                // UpdateTime: {
                //     [Op.gt]: yesterday,
                // },
            },
        })
        ApplyInfoList = parseJSON(ApplyInfoList)
        if (ApplyInfoList.length > 0) {
            for (let ApplyInfo of ApplyInfoList) {
                // 推送给联通
                await getAndPushCrossBorderInfo(ApplyInfo)
            }
        } else {
            // 昨日无数据
            cronLogger.info('昨日无数据 Finish successfully ')
        }
        cronLogger.info('Finish successfully')
    } catch (err) {
        cronLogger.error(err)
        cronLogger.info('Finish with error')
    }
})

async function getAndPushCrossBorderInfo({
    CompanyName,
    CompanyCode,
    LegalEntityName,
    BusinessPlace,
    LicenseIssuingAgency,
    PostalCode,
    BusinessLicenseBeginTime,
    BusinessLicenseEndTime,
    ManagerName,
    ManagerLicenseId,
    ManagerAddress,
    ManagerPhone,
    ManagerEmail,
    BusinessLicensePic,
    ManagerLicensePic,
    PromiseLetterPic,
    ProxyLetterPic,
    ServiceProtocolPic,
    UCloudProxyLetterPic,
}) {
    // 获取协议url
    let [
        BusinessLicensePicUrl,
        ManagerLicensePicUrl,
        PromiseLetterPicUrl,
        ProxyLetterPicUrl,
        ServiceProtocolPicUrl,
        UCloudProxyLetterPicUrl,
    ] = await Promise.all([
        BusinessLicensePic?.trim()
            ? ucloudinternalapi({
                  Backend: 'hegui-backend',
                  Action: 'GetPictureUrl',
                  FileName: BusinessLicensePic,
                  staff_en: 'crossborder',
              })
            : Promise.resolve({ URL: '' }),
            ManagerLicensePic?.trim()
            ? ucloudinternalapi({
                  Backend: 'hegui-backend',
                  Action: 'GetPictureUrl',
                  FileName: ManagerLicensePic,
                  staff_en: 'crossborder',
              })
            : Promise.resolve({ URL: '' }),
            PromiseLetterPic?.trim() ? ucloudinternalapi({
            Backend: 'hegui-backend',
            Action: 'GetPictureUrl',
            FileName: PromiseLetterPic,
            staff_en: 'crossborder',
        }): Promise.resolve({URL: ''}),
        ProxyLetterPic?.trim() ? ucloudinternalapi({
            Backend: 'hegui-backend',
            Action: 'GetPictureUrl',
            FileName: ProxyLetterPic,
            staff_en: 'crossborder',
        }): Promise.resolve({URL: ''}),
        ServiceProtocolPic?.trim() ? ucloudinternalapi({
            Backend: 'hegui-backend',
            Action: 'GetPictureUrl',
            FileName: ServiceProtocolPic,
            staff_en: 'crossborder',
        }): Promise.resolve({URL: ''}),
        UCloudProxyLetterPic?.trim() ? ucloudinternalapi({
            Backend: 'hegui-backend',
            Action: 'GetPictureUrl',
            FileName: UCloudProxyLetterPic,
            staff_en: 'crossborder',
        }): Promise.resolve({URL: ''}),
    ])

    let companyInfo = {
        公司名称: CompanyName,
        公司信用代码: CompanyCode,
        法人姓名: LegalEntityName,
        营业场所: BusinessPlace,
        营业执照签发机关: LicenseIssuingAgency,
        邮政编码: PostalCode,
        经办人姓名: ManagerName,
        经办人身份证号: ManagerLicenseId,
        经办人地址: ManagerAddress,
        经办人电话: ManagerPhone,
        经办人邮箱: ManagerEmail,
    }
    // 将获取到的URL 转换为Base64
    let [
        BusinessLicenseBase64,
        ManagerLicenseBase64,
        PromiseLetterBase64,
        ProxyLetterBase64,
        ServiceProtocolBase64,
        UCloudProxyLetterBase64,
    ] = await Promise.all([
        BusinessLicensePicUrl.URL ? convertUS3FileToBase64(BusinessLicensePicUrl.URL) : '',
        ManagerLicensePicUrl.URL ? convertUS3FileToBase64(ManagerLicensePicUrl.URL) : '',
        PromiseLetterPicUrl.URL ? convertUS3FileToBase64(PromiseLetterPicUrl.URL) : '',
        ProxyLetterPicUrl.URL ? convertUS3FileToBase64(ProxyLetterPicUrl.URL) : '',
        ServiceProtocolPicUrl.URL ? convertUS3FileToBase64(ServiceProtocolPicUrl.URL) : '',
        UCloudProxyLetterPicUrl.URL ? convertUS3FileToBase64(UCloudProxyLetterPicUrl.URL) : '',
    ])
    await sendMail({
        email: global.CONFIG.crosssendemail,
        title: `${CompanyName} 跨境申请信息`,
        content: `${CompanyName} 跨境申请信息`,
        attachFiles: [
            {
                AttachName: `跨境申请信息.json`,
                AttachContent: Buffer.from(
                    JSON.stringify(companyInfo)
                ).toString('base64'),
            },
            {
                AttachName: `营业执照.png`,
                AttachContent: BusinessLicenseBase64,
            },
            {
                AttachName: `经办人身份证.png`,
                AttachContent: ManagerLicenseBase64,
            },
            {
                AttachName: `承诺书`,
                AttachContent: PromiseLetterBase64,
            },
            {
                AttachName: `委托书`,
                AttachContent: ProxyLetterBase64,
            },
            {
                AttachName: `服务协议`,
                AttachContent: ServiceProtocolBase64,
            },
            {
                AttachName: `授优刻得委托书`,
                AttachContent: UCloudProxyLetterBase64,
            },
        ],
    })
}
