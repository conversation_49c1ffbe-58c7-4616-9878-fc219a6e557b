/*
 * @Date: 2022-09-05 11:16:55
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2023-03-24 10:30:49
 * @FilePath: /newicp/crons/AutoSyncICPToDBFromHengan.js
 */
/**
 *同步恒安的备案信息 到数据库
 *redis只能规避掉备案号变更时的信息同步，以及备案注销的同步 若有其他信息有差异无法同步，
 *故需要此任务 对近期更新的数据同步
 *此任务主要 针对 在7天内数据有更新的一个同步
 */
const moment = require('moment')
const Cron = require('../libs/cron')
const redis = require('../libs/redis')
const uuid = require('uuid/v4')
const CronLogger = require('../libs/cronLogger')
const producer = require('../libs/producer')
const henganApi = require('../libs/henganApiPromise')
const _ = require('lodash')
const axiosAPI = require('../libs/axiosApi')
const name = 'AutoSyncICPToDBFromHengan'

Cron.setCron(name, {
    cron: '0 0 0 * * 2', // 每周一 凌晨查询一次
    startDate: new Date(),
})

//设置定时任务的消费
Cron.process(name, async function main() {
    let sid = uuid()
    let cronLogger = new CronLogger(name, sid)
    try {
        const redisClient = redis.get()
        cronLogger.info('start')
        // 查找到7天内更新了的数据
        let [mainHenganRes, webHenganRes] = await Promise.all([
            henganApi('SelectICPInfoAction', {
                KeyWordType: 2,
                Keyword: `${moment()
                    .subtract(7, 'days')
                    .format('YYYY-MM-DD HH:mm:ss')}~${moment().format(
                    'YYYY-MM-DD HH:mm:ss'
                )}`,
                QueryType: 2,
                timeout: 60 * 1000,
            }),
            henganApi('SelectICPInfoAction', {
                KeyWordType: 7,
                Keyword: `${moment()
                    .subtract(7, 'days')
                    .format('YYYY-MM-DD HH:mm:ss')}~${moment().format(
                    'YYYY-MM-DD HH:mm:ss'
                )}`,
                QueryType: 2,
                timeout: 60 * 1000,
            }),
        ])
        let ICPMainNos = new Set()
        mainHenganRes.ICPInfos?.forEach((info) => {
            ICPMainNos.add(info.mainphylicnum)
        })
        webHenganRes.ICPInfos?.forEach((info) => {
            ICPMainNos.add(info.mainphylicnum)
        })
        ICPMainNos = Array.from(ICPMainNos)
        // 没有域名的话 数据不正确
        if (ICPMainNos.length > 0) {
            await redisClient.sadd(
                `SyncICPToDB-${moment().format('YYYY-MM-DD')}`,
                ICPMainNos
            )
            // 推送延迟任务 去执行同步 1min 执行一次同步
            await producer.send({
                type: 'icp-delay',
                topic: 'SyncLocalICPDBFromHengan',
                data: {
                    Key: `SyncICPToDB-${moment().format('YYYY-MM-DD')}`,
                },
                opts: {
                    headers: { 'x-delay': 1 * 60 * 1000 }, // 延迟1分钟检查时间
                },
            })
        } else {
            cronLogger.info('Finish no data error')
        }
        cronLogger.info('Finish successfully')
    } catch (e) {
        let err = new Error(`备案信息同步至数据库，定时任务出错,${e}`)
        cronLogger.error(err)
        cronLogger.info('Finish with error')
    }
})
