/*
 * @Date: 2022-09-05 11:16:55
 * @LastEditors: l<PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2023-05-30 17:45:28
 * @FilePath: /newicp/crons/AutoClearOrder.js
 */
/**
 * 每月一号生成过期半年的订单
 */
const moment = require('moment')
const Cron = require('../libs/cron')
const redis = require('../libs/redis')
const uuid = require('uuid/v4')
const CronLogger = require('../libs/cronLogger')
const producer = require('../libs/producer')
const _ = require('lodash')
const { Op } = require('sequelize')
const {
    OrderModel,
    OrderStatusEnum,
    ClearOrderBatchModel,
    ClearOrderRecordModel,
} = require('../models')
const { parseJSON } = require('../fns/kits')
const name = 'AutoClearOrder'

Cron.setCron(name, {
    cron: '30 30 1 1 * *', // 每月1号的1点30分30秒生成
    startDate: new Date(),
})

//设置定时任务的消费
Cron.process(name, async function main() {
    let sid = uuid()
    let cronLogger = new CronLogger('AutoClearOrder', sid)
    try {
        let limitDate = moment().subtract('6', 'months').format('X')

        cronLogger.info('start')
        let batchInfo = await ClearOrderBatchModel.create({
            Remark: `${moment().format('YYYY-MM-DD')} 自动生成`,
            Operator: `System.Auto`,
        })
        let batchId = parseJSON(batchInfo).Id
        let orders = await OrderModel.findAll({
            where: {
                UpdateTime: {
                    [Op.lte]: limitDate,
                },
                Status: {
                    [Op.notIn]: [
                        OrderStatusEnum.Auditing,
                        OrderStatusEnum.GovAuditing,
                        OrderStatusEnum.GovAuditPass,
                    ],
                },
                IsDeleted: 0,
            },
            attributes: ['OrderNo', 'CompanyId', 'UpdateTime'],
        })
        orders = parseJSON(orders)
        let records = []
        if (orders.length > 0) {
            // 生成记录
            records = orders.map((order) => {
                return {
                    BatchId: batchId,
                    OrderNo: order.OrderNo,
                    CompanyId: order.CompanyId,
                    OrderUpdateTime: order.UpdateTime,
                }
            })
            await ClearOrderRecordModel.bulkCreate(records)
        }
        cronLogger.info('Finish successfully')
    } catch (e) {
        let err = new Error(`备案信息同步至redis定时任务出错,${e}`)
        cronLogger.error(err)
        cronLogger.info('Finish with error')
    }
})
