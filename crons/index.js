/*
 * @Date: 2022-07-14 18:21:57
 * @LastEditors: li<PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2022-09-01 17:06:40
 * @FilePath: /newicp/crons/index.js
 */
'use strict'
const Cron = require('../libs/cron')
const CronLogger = require('../libs/cronLogger')
const Redis = require('../libs/redis')
const uuid = require('uuid/v4')
let redisObj = Redis.get()

const main = async () => {
    let sid = uuid()
    let cronLogger = new CronLogger('CronManager', sid)
    try {
        // 每次启动 先删除原所有的重复定时任务
        if (!(await redisObj.setnx('sechdule', 1))) {
            return
        } 
        await redisObj.expire('sechdule', 5)
        
        cronLogger.info('delete All')

        await Cron.removeAll()

        cronLogger.info('init')

        require('./CheckDomainWriteListExpired')
        require('./StartOtherDomainCheckFromRedis')
        // require('./StartUCloudDomainCheckFromRedis')
        // require('./AutoSyncICPInfoFromHengan')
        require('./CheckSendStatus')
        require('./AutoGenerateUnresourceBatch')
        require('./GetThisDayAuditCount')
        require('./CreateICPPaidForVIP')
        require('./CreateICPQueryPaidForFK')
        require('./CreateDomainQueryPaidForZR')
        require('./AutoCheckCrossBorderExpired')
        require('./CreateICPPaidForNormal')
        // require('./CreateNoAccessNotifyBatch')
        require('./AutoClearOrder')
        require('./AutoClearLog')
        require('./FetchUpdatedICPInfoToMiit.js')
        require('./AutoSyncConfigType.js')
        // require('./PushCrossBorderInfoToUnincom.js')
    } catch (err) {
        console.log(err)
        cronLogger.error(err)
    }
}

main()
