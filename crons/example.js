/*
 * @Author: william.qian <EMAIL>
 * @Date: 2022-02-16 11:11:48
 * @LastEditors: william.qian <EMAIL>
 * @LastEditTime: 2023-10-08 11:00:51
 * @FilePath: /newicp/crons/example.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
'use strict'

const Cron = require('../libs/cron')
const uuid = require('uuid/v4')
const CronLogger = require('../libs/cronLogger')
const redis = require('../libs/redis')

const name = 'YourCronJobName'

Cron.setCron(name, {
    cron: '1 1 1 1 * *', // 你的cron表达式
    startDate: new Date(),
})

Cron.process(name, async function main({ data }) {
    let sid = uuid()
    let cronLogger = new CronLogger(name, sid)
    try {
        cronLogger.info(name + ' start')

        // 你的代码在这里

        cronLogger.info('Finish successfully')
    } catch (e) {
        let err = new Error(`出错,${e}`)
        cronLogger.error(err)
        cronLogger.info('Finish with error')
    }
})
