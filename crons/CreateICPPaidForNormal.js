'use strict'
/**备案订单 计费
 * 每月1日执行，为大客户创建备案后付费订单，提交到管局阶段、新增类、之前未计费的icp订单
 * 从t_order_history取出提交到管局的订单，后与t_order对比，取出订单数据
 * 与mongo中的t_bill_details中做数据查询，确定订单是否计费过了
 * 未计费过的订单，提交
 */
const Cron = require('../libs/cron')
const { parseJSON, getTableModel } = require('../fns/kits')
const { Op } = require('sequelize')
const billMap = require('../configs/common/bill_map.json')
const {
    OrderHistoryModel: historyModel,
    OrderModel: orderModel,
} = require('../models')
const producer = require('../libs/producer')
const { NotifyTypeModel } = require('../mongoModels/icp')
const _ = require('lodash')
const uuid = require('uuid/v4')
const CronLogger = require('../libs/cronLogger')
const moment = require('moment')
const mongoConf = require('../libs/mongo')

const name = 'CreateICPPaidForNormal'
const billInfo = _.find(billMap, { ProductSmallClass: 'ICPOrderForNormal' })

// 收费公司列表
let chargeCompanyIds = {
    62935358: {
        Emails: ['<EMAIL>', '<EMAIL>', '<EMAIL>'],
    },
}

Cron.setCron(name, {
    cron: '1 40 3 1 * *', // 每月执行一次 和凡科订单处理时间分开
    startDate: new Date(),
})
Cron.process(name, async function main({ data }) {
    let sid = uuid()
    let mongo = mongoConf.get('icp')
    let cronLogger = new CronLogger(name, sid)
    cronLogger.info(name + 'start')
    const billListCollection = mongo.collection('t_bill_list')
    const billDetailsCollection = mongo.collection('t_bill_details')

    try {
        let lastMonthStartTime = moment().subtract(1, 'month').startOf('month')
        let lastMonthEndTime = parseInt(
            moment().subtract(1, 'month').endOf('month').format('X')
        )
        let billMonth = lastMonthStartTime.format('YYYY-MM')
        lastMonthStartTime = parseInt(lastMonthStartTime.format('X'))

        // 还是分开查询，先查日志
        let orderMap = await historyModel.findAll({
            attributes: ['OrderNo', 'OperationTime'],
            group: 'OrderNo',
            where: {
                status: 11, // 提交到管局阶段
                operation_time: {
                    [Op.between]: [
                        lastMonthStartTime * 1000,
                        lastMonthEndTime * 1000,
                    ],
                },
            },
        })
        orderMap = parseJSON(orderMap)
        let orderNos = _.map(orderMap, 'OrderNo')
        orderNos = _.uniq(orderNos)
        // orderMap中，用orderList过滤出有效的订单信息
        for (let companyId in chargeCompanyIds) {
            // 后确定类型,过滤出需要计费的类型
            let orderList = await orderModel.findAll({
                attributes: ['OrderNo'],
                where: {
                    Type: {
                        [Op.in]: [1, 2, 3],
                    },
                    CompanyId: companyId,
                    OrderNo: {
                        [Op.in]: orderNos,
                    },
                },
            })
            orderList = parseJSON(orderList)
            orderList = _.map(orderList, 'OrderNo')
            let orders = _.filter(orderMap, (o) => orderList.indexOf(o.OrderNo) !== -1)
            // 批次情况
            if (orders.length === 0) {
                return 0
            }
            let billInsertInfo = await billListCollection.insertOne({
                BillType: billInfo.ProductSmallClassId,
                BillNameCN: billInfo.ProductSmallClassCN + billMonth,
                Type: billInfo.Type,
                CreateTime: parseInt(moment().format('X')),
                UpdateTime: parseInt(moment().format('X')),
                BillName: 'hegui-' + billInfo.ProductSmallClass + '-' + billMonth,
                CompanyId: +companyId,
                OrganizationId: +companyId,
                TotalAmount: billInfo.Price * orders.length,
                Count: orders.length,
                StartTime: lastMonthStartTime,
                EndTime: lastMonthEndTime,
                WhetherToPush: 0,
            })
    
            // 处理账单详情
            orders = orders.map((row) => {
                row.BillId = billInsertInfo.insertedId
                row.BillName = billInfo.ProductSmallClassCN + billMonth
                row.CompanyId = +companyId
                row.Count = 1
                row.TriggerTime = parseInt(moment(row.OperationTime).format('X'))
                row.UId = row.OrderNo
                delete row.OrderNo
                delete row.OperationTime
                return row
            })
    
            await billDetailsCollection.insertMany(orders)
            // {计费标记}
            // 推送通知
            // 获取通知类型
            let notifyTypeInfo = await NotifyTypeModel.findOne({ Name: '订单计费' })
            if (notifyTypeInfo?.Type) {
                // 推送通知
                await producer.send({
                    type: 'icp',
                    topic: 'CreateBillNotifyBatch',
                    data: {
                        BillInfos: [
                            {
                                CompanyId: companyId,
                                Notify: { Emails: chargeCompanyIds[companyId].Emails },
                                BillId: billInsertInfo.insertedId,
                            },
                        ],
                        Type: notifyTypeInfo?.Type,
                        BillType: '备案订单',
                    },
                })
            } else {
                cronLogger.info(name + '没有发现该通知类型')
            }
        }
    } catch (e) {
        console.log(e)
        let err = new Error(`生成订单出错,${e}`)
        cronLogger.error(err)
        cronLogger.info('Finish with error')
    }
})
