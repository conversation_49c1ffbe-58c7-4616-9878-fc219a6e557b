'use strict'
/**
 * 每月1日执行，数据库确定中瑞的域名接口查询情况
 * 查询每天次数，按日天统计
 */
const Cron = require('../libs/cron')
const billMap = require('../configs/common/bill_map.json')
const icpDatabase = require('../libs/mysql').get('icp')
const bill_notify_map = require('../configs/common/bill_notify_map.json')
const _ = require('lodash')
const { sleep, toLine, getTableModel, parseJSON } = require('../fns/kits')
const { NotifyTypeModel } = require('../mongoModels/icp')
const uuid = require('uuid/v4')
const moment = require('moment')
const mongoConf = require('../libs/mongo')
const producer = require('../libs/producer')
const name = 'CreateDomainQueryPaidForZR'
const billInfo = _.find(billMap, { ProductSmallClass: 'DomainInfoCheck' })
const companyIdZR = 65979125
const organizationIdZR = 63992878
const CronLogger = require('../libs/cronLogger')
const { fn, col, Op } = require('sequelize')
const verify_type = require('../configs/common/verify_api_type.json')

Cron.setCron(name, {
    cron: '30 20 3 1 * *',
    startDate: new Date(),
})
Cron.process(name, async function main({ data }) {
    let sid = uuid()
    let mongo = mongoConf.get('icp')
    let cronLogger = new CronLogger(name, sid)
    let startDay = moment().subtract(1, 'month').startOf('month')
    let endDay = moment().subtract(1, 'month').endOf('month')

    let billMonth = startDay.format('YYYY-MM')

    cronLogger.info(name + 'start')
    try {
        let LogModel = getTableModel('t_verify_log', icpDatabase)
        const billListCollection = mongo.collection('t_bill_list')
        const billDetailsCollection = mongo.collection('t_bill_details')
        // 取范围内的订单

        let actionRow = await LogModel.findAll({
            attributes: [
                [fn('from_unixtime', col(`create_time`), '%Y-%m-%d'), 'Date'],
                [fn('COUNT', col(`Id`)), 'Count'],
                'CreateTime',
                'Type',
            ],
            group: [[fn('DAY', col(`Date`))], 'Type'],
            where: {
                CreateTime: {
                    [Op.between]: [startDay.format('X'), endDay.format('X')],
                },
                CompanyId: companyIdZR,
                Result: 0, // 只取成功的
                Type: [
                    verify_type['GetDomainRegisterMsg'],
                    verify_type['CheckDomainRegisterMsg'],
                ], //  只记这2种类型的订单
            },
        })

        actionRow = parseJSON(actionRow)

        // 执行查询得到结构体[{Date,Count,Type}]
        let result = [],
            sumCount = _.sumBy(actionRow, function (o) {
                return o.Count
            })

        // 保存账单信息
        // 批次情况
        let billInsertInfo = await billListCollection.insertOne({
            BillType: billInfo.ProductSmallClassId,
            BillNameCN: billInfo.ProductSmallClassCN + billMonth,
            BillName: 'hegui-' + billInfo.ProductSmallClass + '-' + billMonth,
            Type: billInfo.Type,
            CompanyId: companyIdZR,
            OrganizationId: organizationIdZR, //因为是凡科，限定都是34278,
            Count: sumCount,
            StartTime: parseInt(startDay.format('X')),
            EndTime: parseInt(startDay.endOf('month').format('X')),
            CreateTime: parseInt(moment().format('X')),
            UpdateTime: parseInt(moment().format('X')),
            TotalAmount: Math.ceil(billInfo.Price * sumCount * 100) / 100,
            WhetherToPush: 0,
        })

        for (const actionRowElement of actionRow) {
            result.push({
                Count: actionRowElement.Count,
                UId: actionRowElement.Date,
                TriggerTime: actionRowElement.CreateTime,
                BillId: billInsertInfo.insertedId,
                BillName: billInfo.ProductSmallClassCN + billMonth,
                CompanyId: companyIdZR,
            })
        }

        await billDetailsCollection.insertMany(result)
        // 获取通知类型
        let notifyTypeInfo = await NotifyTypeModel.findOne({ Name: '订单计费' })
        if (notifyTypeInfo?.Type) {
            // 推送通知
            await producer.send({
                type: 'icp',
                topic: 'CreateBillNotifyBatch',
                data: {
                    BillInfos: [
                        {
                            CompanyId: companyIdZR,
                            Notify: { Emails: bill_notify_map.ZR.Emails },
                            BillId: billInsertInfo.insertedId,
                        },
                    ],
                    Type: notifyTypeInfo?.Type,
                    BillType: '域名信息查询',
                },
            })
        } else {
            cronLogger.info(name + '没有发现该通知类型')
        }
        // // 账单详情
    } catch (e) {
        console.log(e)
        let err = new Error(`生成订单出错,${e}`)
        cronLogger.error(err)
        cronLogger.info(name + 'Finish with error')
    }
})
