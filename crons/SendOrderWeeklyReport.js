'use strict'
/**
 * 定时任务：发送周报邮件（订单统计）
 * 统计周期同 GetOrderUCWeeklyStatistics：上周五 00:00:00 ~ 本周四 23:59:59
 * 每周五 08:10 自动执行
 */
const Cron = require('../libs/cron')
const uuid = require('uuid/v4')
const CronLogger = require('../libs/cronLogger')
const { sendMail } = require('../fns/umsMsgSend')
const WeeklyStatMethod = require('../methods/admin/Dashboard/GetOrderUCWeeklyStatistics')
const moment = require('moment')
const path = require('path')
const fs = require('fs')

const name = 'SendOrderWeeklyReport'

// 收件人
const recipientStr = '<EMAIL>;<EMAIL>'

// 服务器已设置东八区时间，每周五 08:10 触发
Cron.setCron(name, {
    cron: '0 10 8 * * 5', // 秒 分 时 日 月 周
    startDate: new Date(),
})

console.log('[SendOrderWeeklyReport] cron registered: 0 10 8 * * 5 (Asia/Shanghai)')


Cron.process(name, async function main() {
    const sid = uuid()
    const logger = new CronLogger(name, sid)
    logger.info("start")

    try {
        // 调用统计方法
        const statResult = await new Promise((resolve, reject) => {
            const method = new WeeklyStatMethod((code, res) => {
                if (code === 0) return resolve(res.Data)
                return reject(new Error(`stat fail, code=${code}`))
            })
            method.exec()
        })

        // 生成邮件内容
        const {
            period,
            newOrderCount,
            newOrderWoW,
            newOrderCountFanke,
            newOrderWoWFanke,
            newOrderCountDirect,
            newOrderWoWDirect,
            changeOrderCount,
            changeOrderWoW,
            changeOrderCountFanke,
            changeOrderWoWFanke,
            changeOrderCountDirect,
            changeOrderWoWDirect,
            ucloudAuditCount,
            ucloudAuditWoW,
        } = statResult

        // 计算上一期区间
        const [startStr, endStr] = period.split(' ~ ')
        const lastPeriod = `${moment(startStr).subtract(7, 'days').format('YYYY-MM-DD')} ~ ${moment(endStr).subtract(7, 'days').format('YYYY-MM-DD')}`

        // 使用内置 HTML 模板
        let md = `
        <h2>备案审核周报（{{period}}）</h2>
        <p>对比周期：<strong>{{lastPeriod}}</strong></p>

        <h3>新增订单统计</h3>
        <table border="1" cellpadding="6" cellspacing="0" style="border-collapse:collapse;font-size:14px;">
          <tr><th>指标</th><th>当前值</th><th>环比</th></tr>
          <tr><td>新增订单总量</td><td>{{newOrderCount}}</td><td>{{newOrderWoW}}</td></tr>
          <tr><td>新增订单量-凡科</td><td>{{newOrderCountFanke}}</td><td>{{newOrderWoWFanke}}</td></tr>
          <tr><td>新增订单量-直客</td><td>{{newOrderCountDirect}}</td><td>{{newOrderWoWDirect}}</td></tr>
        </table>

        <h3>变更订单统计</h3>
        <table border="1" cellpadding="6" cellspacing="0" style="border-collapse:collapse;font-size:14px;">
          <tr><th>指标</th><th>当前值</th><th>环比</th></tr>
          <tr><td>变更订单总量</td><td>{{changeOrderCount}}</td><td>{{changeOrderWoW}}</td></tr>
          <tr><td>变更订单量-凡科</td><td>{{changeOrderCountFanke}}</td><td>{{changeOrderWoWFanke}}</td></tr>
          <tr><td>变更订单量-直客</td><td>{{changeOrderCountDirect}}</td><td>{{changeOrderWoWDirect}}</td></tr>
        </table>

        <h3>UCloud 审核统计</h3>
        <table border="1" cellpadding="6" cellspacing="0" style="border-collapse:collapse;font-size:14px;">
          <tr><th>指标</th><th>当前值</th><th>环比</th></tr>
          <tr><td>UCloud 审核总量</td><td>{{ucloudAuditCount}}</td><td>{{ucloudAuditWoW}}</td></tr>
        </table>
        <p style="font-size:12px;color:#666;margin-top:20px;">本邮件由系统自动发送，请勿回复。</p>`;

        // 替换占位符
        const percent = (v)=> (v===null||v===undefined?'-': `${(v*100).toFixed(2)}%`)
        const replaceMap = {
            '{{period}}': period,
            '{{lastPeriod}}': lastPeriod,
            '{{newOrderCount}}': newOrderCount,
            '{{newOrderWoW}}': percent(newOrderWoW),
            '{{newOrderCountFanke}}': newOrderCountFanke,
            '{{newOrderWoWFanke}}': percent(newOrderWoWFanke),
            '{{newOrderCountDirect}}': newOrderCountDirect,
            '{{newOrderWoWDirect}}': percent(newOrderWoWDirect),
            '{{changeOrderCount}}': changeOrderCount,
            '{{changeOrderWoW}}': percent(changeOrderWoW),
            '{{changeOrderCountFanke}}': changeOrderCountFanke,
            '{{changeOrderWoWFanke}}': percent(changeOrderWoWFanke),
            '{{changeOrderCountDirect}}': changeOrderCountDirect,
            '{{changeOrderWoWDirect}}': percent(changeOrderWoWDirect),
            '{{ucloudAuditCount}}': ucloudAuditCount,
            '{{ucloudAuditWoW}}': percent(ucloudAuditWoW),
        }
        for (const [k, v] of Object.entries(replaceMap)) {
            md = md.replace(new RegExp(k, 'g'), String(v))
        }

        // 邮件标题与内容
        const title = `订单周报 (${period})`
        const content = md

        logger.info("ciallo debug content", content)
        // 发送邮件
        await sendMail({
            email: recipientStr,
            title,
            content,
        })

        logger.info('Finish successfully')
    } catch (err) {
        logger.error(err)
        logger.info('Finish with error')
    }
}) 