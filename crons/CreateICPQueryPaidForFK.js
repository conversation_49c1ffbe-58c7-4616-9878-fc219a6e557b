'use strict'
/**域名备案查询 计费
 * 每月1日执行，通过ELK查询凡科对接口的调用情况
 * 查询每天次数，按日天统计，
 */
const Cron = require('../libs/cron')
const billMap = require('../configs/common/bill_map.json')
const _ = require('lodash')
const bill_notify_map = require('../configs/common/bill_notify_map.json')
const { NotifyTypeModel } = require('../mongoModels/icp')
const { sleep, toLine } = require('../fns/kits')
const producer = require('../libs/producer')
const uuid = require('uuid/v4')
const moment = require('moment')
const mongoConf = require('../libs/mongo')
const axiosApi = require('../libs/axiosApi')
const name = 'CreateICPQueryPaidForFK'
const billInfo = _.find(billMap, { ProductSmallClass: 'ICPQuery' })
const companyIdFK = 34278
const organizationIdFK = 34278
const CronLogger = require('../libs/cronLogger')

Cron.setCron(name, {
    cron: '1 1 1 1 * *',
    startDate: new Date(),
})
Cron.process(name, async function main({ data }) {
    let sid = uuid()
    let mongo = mongoConf.get('icp')
    let cronLogger = new CronLogger(name, sid)
    let startDay = moment().subtract(1, 'month').startOf('month')
    let billMonth = startDay.format('YYYY-MM')
    cronLogger.info(name + 'start')
    try {
        const billListCollection = mongo.collection('t_bill_list')
        const billDetailsCollection = mongo.collection('t_bill_details')
        // 取范围内的订单
        // 获取上个月全部的日期做为ELK的查询Index
        let dayList = []
        dayList.push(startDay.format('YYYY-MM-DD'))
        for (let i = 0; i < startDay.daysInMonth() - 1; i++) {
            dayList.push(startDay.add(1, 'day').format('YYYY-MM-DD'))
        }

        console.log(dayList.data)
        // 执行查询得到结构体{day,count}
        let result = [],
            sumCount = 0

        for (let i = 0; i < dayList.length; i++) {
            let everyDayDetail = await getEveryDayCount(dayList[i])
            if (everyDayDetail !== 0) {
                result.push({
                    Count: everyDayDetail.count,
                    CompanyId: companyIdFK,
                    UId: dayList[i],
                    TriggerTime: parseInt(moment(dayList[i]).format('X')),
                })
                sumCount = sumCount + everyDayDetail.count
            }
        }

        // 保存账单信息
        // 批次情况
        let billInsertInfo = await billListCollection.insertOne({
            BillType: billInfo.ProductSmallClassId,
            BillNameCN: billInfo.ProductSmallClassCN + billMonth,
            BillName: 'hegui-' + billInfo.ProductSmallClass + '-' + billMonth,
            Type: billInfo.Type,
            CompanyId: companyIdFK,
            OrganizationId: organizationIdFK, //因为是凡科，限定都是34278,
            Count: sumCount,
            StartTime: parseInt(startDay.format('X')),
            EndTime: parseInt(startDay.endOf('month').format('X')),
            CreateTime: parseInt(moment().format('X')),
            UpdateTime: parseInt(moment().format('X')),
            TotalAmount: Math.ceil(billInfo.Price * sumCount * 100) / 100,
            WhetherToPush: 0,
        })
        result = result.map((row) => {
            row.BillId = billInsertInfo.insertedId
            row.BillName = billInfo.ProductSmallClassCN + billMonth
            return row
        })

        await billDetailsCollection.insertMany(result)

        // 推送通知
        // 获取通知类型
        let notifyTypeInfo = await NotifyTypeModel.findOne({ Name: '订单计费' })
        if (notifyTypeInfo?.Type) {
            // 推送通知
            await producer.send({
                type: 'icp',
                topic: 'CreateBillNotifyBatch',
                data: {
                    BillInfos: [
                        {
                            CompanyId: companyIdFK,
                            Notify: { Emails: bill_notify_map.FK.Emails },
                            BillId: billInsertInfo.insertedId,
                        },
                    ],
                    Type: notifyTypeInfo?.Type,
                    BillType: '域名备案查询',
                },
            })
        } else {
            cronLogger.info(name + '没有发现该通知类型')
        }
    } catch (e) {
        console.log(e)
        let err = new Error(`生成订单出错,${e}`)
        cronLogger.error(err)
        cronLogger.info('Finish with error')
    }
})

async function getEveryDayCount(indexName) {
    // 防止请求挂，加个超时
    await sleep(500)
    return axiosApi({
        method: 'get',
        url: global.CONFIG.uxiaoELK + `api-log-${indexName}/doc/_count`,
        headers: {
            'Content-Type': 'application/json',
        },
        timeout: 30 * 1000,
        data: JSON.stringify({
            query: {
                bool: {
                    must: [
                        {
                            match: {
                                company_id: '34278',
                            },
                        },
                        {
                            match: {
                                Action: 'ICPQueryDomainStatus',
                            },
                        },
                        {
                            match: {
                                RetCode: '0',
                            },
                        },
                    ],
                },
            },
        }),
    })
        .then((response) => {
            return Promise.resolve(response.data)
        })
        .catch((err) => {
            return Promise.resolve(0)
        })
}
