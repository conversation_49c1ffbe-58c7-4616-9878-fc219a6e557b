'use strict'
/**
 * 检查发送状态, job中data是设置延迟任务时 传入的批次Id
 */
const Cron = require('../libs/cron')
const GenerateUnresourceBatch = require('../methods/admin/UnResourcesNotify/GenerateUnresourceBatch')
const uuid = require('uuid/v4')
const CronLogger = require('../libs/cronLogger')
const errorCode = require('../configs/error')

const name = 'AutoGenerateUnresourceBatch'

// 每周6的早上 1小时1分1秒开始执行 此处的周是6代表周六
Cron.setCron(name, {
    cron: '1 1 1 * * 6',
    startDate: new Date(),
})

Cron.process(name, async function main() {
    let sid = uuid()
    let cronLogger = new CronLogger(name, sid)
    cronLogger.info('start')
    // 调用方法
    let method = new GenerateUnresourceBatch((retCode, data) => {
        // 不需要返回数据
        data.RetCode = retCode
        // 拼凑 ErrorMessage
        if (retCode !== 0 && !data.Message) {
            data.Message = errorCode[retCode] || 'Internal Error'
            cronLogger.error(JSON.stringify(data))
        } else {
            cronLogger.info(`finished`)
        }
    })
    await method.exec()
})
