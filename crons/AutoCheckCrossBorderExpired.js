/*
 * @Date: 2022-09-01 17:06:02
 * @LastEditors: l<PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2022-09-19 16:06:59
 * @FilePath: /newicp/crons/AutoCheckCrossBorderExpired.js
 */
'use strict'
/**
 * 检查发送状态, job中data是设置延迟任务时 传入的批次Id
 */
const Cron = require('../libs/cron')
const { CrossBorderApplyModel, ApplyStatusEnum } = require('../models')
const { Op } = require('sequelize')
const _ = require('lodash')
const { parseJSON } = require('../fns/kits')
const uuid = require('uuid/v4')
const moment = require('moment')
const CronLogger = require('../libs/cronLogger')
const name = 'CheckCrossBorderExpired'

Cron.setCron(name, {
    cron: '1 1 1 * * *', // 每天的1点1分1秒执行
    startDate: new Date(),
})

Cron.process(name, async function main() {
    let sid = uuid()
    let cronLogger = new CronLogger(name, sid)
    cronLogger.info('start')
    const date = moment().format('X')
    try {
        // 获取所有审核完成的记录
        let applys = await CrossBorderApplyModel.findAll({
            attributes: ['Id', 'Status', 'BusinessLicenseEndTime'],
            where: {
                Status: ApplyStatusEnum.AduitPass,
            },
        })
        applys = parseJSON(applys)
        // 获取所有已过期的数据
        applys = _.filter(applys, (a) => a.BusinessLicenseEndTime < date)

        if (applys.length !== 0) {
            let ids = applys.map((a) => a.Id)
            await CrossBorderApplyModel.update(
                {
                    Status: ApplyStatusEnum.Expired,
                },
                {
                    where: {
                        Id: {
                            [Op.in]: ids,
                        },
                    },
                }
            )
        }
        cronLogger.info('finished success')
    } catch (err) {
        cronLogger.error(err)
    }
})
