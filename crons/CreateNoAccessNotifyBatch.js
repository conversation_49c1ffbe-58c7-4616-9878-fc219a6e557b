/**
 * 每天定时创建已备案未接入通知批次，先从傲盾接口拉取数据获取ip，domain，然后创建通知批次解析数据
 */
'use strict'
const Cron = require('../libs/cron')
const uuid = require('uuid/v4')
const CronLogger = require('../libs/cronLogger')
const name = 'CreateNoAccessNotifyBatch'
const sid = uuid()
let cronLogger = new CronLogger(name, sid)
const producer = require('../libs/producer')
// '16 33 * * * *'
// 秒 分 时 日 月 周
Cron.setCron(name, {
    cron: '0 15 5 * * 1',
    startDate: new Date(),
})

//设置定时任务的消费
Cron.process(name, async function main() {
    try {
        cronLogger.info(sid + '-' + name + '-' + 'start')
        producer.send({
            type: 'icp-delay',
            topic: 'NoAccessNotifyAutoCreate',
            data: {
                time: 10,
            },
            opts: {
                headers: { 'x-delay': 1 * 1000 }, // 延迟1分钟检查时间
            },
        })
        cronLogger.info('Finish successfully')
    } catch (e) {
        let err = new Error(`自动创建已备案未接入批次出错,${e}`)
        cronLogger.error(err)
        cronLogger.info(sid + 'Finish with error')
    }
})
