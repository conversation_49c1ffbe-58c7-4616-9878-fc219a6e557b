/*
 * @Author: william.qian <EMAIL>
 * @Date: 2022-02-16 11:11:48
 * @LastEditors: william.qian <EMAIL>
 * @LastEditTime: 2023-10-12 11:52:15
 * @FilePath: /newicp/crons/example.js
 * @Description: 主要作用，同步DescribeICPConfig接口中的各项参数
 * 以下参数无法自动同步，需要手动修改OrderType、OrderStatus、ICPStatus、CurtainStatus、BaseManageprovince、PreAppovalType
 * 剩下的数据，作为动态数据，需要从傲盾中获取
 */
'use strict'

const Cron = require('../libs/cron')
const uuid = require('uuid/v4')
const CronLogger = require('../libs/cronLogger')
const redis = require('../libs/redis')
const moment = require('moment')
const henganApiPromise = require('../libs/henganApiPromise')
const { sleep } = require('../fns/kits')
const mongoConf = require('../libs/mongo')
const name = 'AutoSyncConfigType'

Cron.setCron(name, {
    cron: '1 1 9 * * *', // 你的cron表达式
    startDate: new Date(),
})

Cron.process(name, async function main() {
    console.log('start')
    let sid = uuid()
    let mongo = mongoConf.get('icp-config')
    let cronLogger = new CronLogger(name, sid)
    try {
        cronLogger.info(name + ' start')
        const redisClient = redis.get()
        const commonConfigCollection = mongo.collection('common_config')

        let rawConfig = await redisClient.get('DescribeICPConfig')
        console.log(rawConfig, 1)
        let newConfig
        try {
            rawConfig = JSON.parse(rawConfig)
            await commonConfigCollection.insertOne({
                Config: rawConfig,
                Name: moment().format('YYYY-MM-DD') + '自动保存',
                UnixTime: moment().unix(),
            })
        } catch (e) {
            cronLogger.error(`解析Redis或者保存到Mongo过程失败, ${e}`)
            return
        }
        // 以下参数无法自动同步，需要手动修改
        newConfig = {
            OrderType: rawConfig.OrderType,
            OrderStatus: rawConfig.OrderStatus,
            ICPStatus: rawConfig.ICPStatus,
            CurtainStatus: rawConfig.CurtainStatus,
            ConnectType:rawConfig.ConnectType,
            BaseManageprovince: rawConfig.BaseManageprovince,
            ServiceContent: rawConfig.ServiceContent,
            PreAppovalType: rawConfig.PreAppovalType,
            OrganizerTypeLicenseMapping: rawConfig.OrganizerTypeLicenseMapping,
            EffectiveDomain: rawConfig.EffectiveDomain,
        }

        // 以下参数，从傲盾中查询原始数据，开始处理
        try {
            // 主办单位类型
            await Promise.all([
                getInternetWebsiteServiceTypes(),
                getAPPServiceTypes(),
                getOrganizerType(),
                getArea(),
                //getConnectType(),
                getServiceType(),
                getSDKServiceProviders(),
                getSDKServiceType(),
                getLanguage(),
                henganApiPromise('SelectBaseInterfaceAction', { Type: '6' }),
            ]).then(
                ([
                    internetWebsiteServiceTypes,
                    APPServiceTypes,
                    organizerType,
                    area,
                    //connectType,
                    serviceType,
                    sdkServiceProviders,
                    sdkServiceType,
                    language,
                    licenseList,
                ]) => {
                    newConfig.InternetWebsiteServiceTypes =
                        internetWebsiteServiceTypes
                    newConfig.APPServiceTypes = APPServiceTypes
                    newConfig.OrganizerType = organizerType
                    newConfig.Area = area
                    //newConfig.ConnectType = connectType
                    newConfig.ServiceType = serviceType
                    newConfig.SDKServiceProviders = sdkServiceProviders
                    newConfig.SDKServiceType = sdkServiceType
                    newConfig.Language = language
                    // 得到证件类型，根据规则得到需要的数据集

                    newConfig.OrganizerLicenseType = licenseList.Data.map(
                        (item) => ({
                            Key: item.id,
                            Value: item.certificatetypename,
                        })
                    )

                    //  按Key排序
                    newConfig.OrganizerLicenseType =
                        newConfig.OrganizerLicenseType.sort(
                            (a, b) => a.Key - b.Key
                        )

                    // 获取PIC证件类型，根据规则得到需要的数据集
                    let PICLicenseList = licenseList.Data.filter(
                        (item) => item.unitpropertyid === 5
                    )

                    newConfig.PICLicenseType = PICLicenseList.map((item) => ({
                        Key: item.id,
                        Value: item.certificatetypename,
                    }))

                    // 按Key排序
                    newConfig.PICLicenseType = newConfig.PICLicenseType.sort(
                        (a, b) => a.Key - b.Key
                    )
                }
            )
        } catch (e) {
            // 只要是出错，就不保存
            cronLogger.error(`拉取动态数据失败, ${e}`)
            return
        }

        await redisClient.set('DescribeICPConfig', JSON.stringify(newConfig))
        cronLogger.info('Finish successfully')
    } catch (e) {
        let err = new Error(`出错,${e}`)
        cronLogger.error(err)
        cronLogger.info('Finish with error')
    }
})

/**
 * 获取主办单位类型
 * @async
 * @function getOrganizerType
 * @returns {Promise<Array>} 返回主办单位类型的数组
 * @throws {Error} 如果获取主办单位类型失败，抛出错误
 */
async function getOrganizerType() {
    let result = await henganApiPromise('SelectBaseInterfaceAction', {
        Type: '1',
    })
    if (result.RetCode !== 0) {
        throw new Error('获取主办单位类型失败')
    }
    return result.Data.map((item) => ({
        Key: item.id,
        Value: item.unitpropertyname,
    }))
}

/**
 * 获取连接类型
 * @async
 * @function getConnectType
 * @returns {Promise<Array>} 返回连接类型的数组
 * @throws {Error} 如果获取连接类型失败，抛出错误
 */
async function getConnectType() {
    let result = await henganApiPromise('SelectBaseInterfaceAction', {
        Type: '9',
    })
    if (result.RetCode !== 0) {
        throw new Error('获取连接类型失败')
    }
    return result.Data
}

/**
 * 获取服务类型
 * @async
 * @function getServiceType
 * @returns {Promise<Array>} 返回服务类型的数组
 * @throws {Error} 如果获取服务类型失败，抛出错误
 */
async function getServiceType() {
    let result = await henganApiPromise('SelectBaseInterfaceAction', {
        Type: '8',
    })
    if (result.RetCode !== 0) {
        throw new Error('获取服务类型失败')
    }
    return result.Data.filter((item) => item.effectiveyn === 1).map((item) => ({
        Key: item.computeid,
        Value: item.content,
    }))
}

/**
 * 获取SDK服务提供商
 * @async
 * @function getSDKServiceProviders
 * @returns {Promise<Array>} 返回SDK服务提供商的数组
 * @throws {Error} 如果获取SDK服务提供商失败，抛出错误
 */
async function getSDKServiceProviders() {
    let result = await henganApiPromise('SelectBaseInterfaceAction', {
        Type: '19',
    })
    if (result.RetCode !== 0) {
        throw new Error('获取SDK服务提供商失败')
    }
    return result.Data
}

/**
 * 获取SDK服务类型
 * @async
 * @function getSDKServiceType
 * @returns {Promise<Array>} 返回SDK服务类型的数组
 * @throws {Error} 如果获取SDK服务类型失败，抛出错误
 */
async function getSDKServiceType() {
    let result = await henganApiPromise('SelectBaseInterfaceAction', {
        Type: '20',
    })
    if (result.RetCode !== 0) {
        throw new Error('获取SDK服务类型失败')
    }
    return result.Data
}
/**
 * 获取APP服务类型
 * @async
 * @function getAPPServiceTypes
 * @returns {Promise<Array>} 返回APP服务类型的数组
 * @throws {Error} 如果获取APP服务类型失败，抛出错误
 */
async function getAPPServiceTypes() {
    let result = await henganApiPromise('SelectBaseInterfaceAction', {
        Type: '18',
    })
    if (result.RetCode !== 0) {
        throw new Error('获取APP服务类型失败')
    }
    return buildTree(result.Data)
}

/**
 * 获取语言
 * @async
 * @function getLanguage
 * @returns {Promise<Array>} 返回语言的数组
 * @throws {Error} 如果获取语言失败，抛出错误
 */
async function getLanguage() {
    let result = await henganApiPromise('SelectBaseInterfaceAction', {
        Type: '14',
    })
    if (result.RetCode !== 0) {
        throw new Error('获取语言失败')
    }
    return result.Data.map((item) => ({
        Key: item.id,
        Value: item.name,
    }))
}

/**
 * 获取互联网服务类型
 * @async
 * @function getInternetWebsiteServiceTypes
 * @returns {Promise<Array>} 返回互联网服务类型的数组
 * @throws {Error} 如果获取互联网服务类型失败，抛出错误
 */
async function getInternetWebsiteServiceTypes() {
    let result = await henganApiPromise('SelectBaseInterfaceAction', {
        Type: '16',
    })
    if (result.RetCode !== 0) {
        throw new Error('获取互联网服务类型失败')
    }

    return result.Data.map((item) => ({
        Key: item.id,
        Value: item.name,
    }))
}

/**
 * 获取地区配置
 * @async
 * @function getArea
 * @returns {Promise<Array>} 返回地区配置的数组
 * @throws {Error} 如果获取地区配置失败，抛出错误
 */
async function getArea() {
    let result = await henganApiPromise('GetAreaAndUpdate', { IsUpdate: false })
    if (result.RetCode !== 0) {
        throw new Error('获取地区配置失败' + result.Message)
    }
    return result.body
}

function buildTree(arr, pid = 0) {
    let result = []
    for (let item of arr) {
        if (item.pid === pid) {
            let children = buildTree(arr, item.id)
            if (children.length) {
                item.children = children
            }
            result.push(item)
        }
    }
    return result
}
