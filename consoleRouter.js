const express = require('express')
const router = express.Router()
const requireAll = require('require-all')
const _ = require('lodash')
const uuid = require('uuid/v4')
const transferParams = require('./libs/transferParams')
const methods = requireAll(__dirname + '/methods/console')
// 跨境报备服务
const cMethods = requireAll(__dirname + '/methods/crossBorder')
// 多个服务通用接口
const coMethods = requireAll(__dirname + '/methods/common')
// 审核侧和客户侧共用的接口放置在admin/Common中 主要是接口名称不一致导致的需要转换的
const { trimObject } = require('./fns/kits')
const errorCode = require('./configs/error')
const logger = require('./libs/logger')
const moment = require('moment')
// const { context, trace } = require('@opentelemetry/api')
const SwaggerAndSchema = require('swagger-and-schema')

var ss = new SwaggerAndSchema({ apisDirPath: `${__dirname}/configs/apis` })
const getIpSegment = require('./libs/ipSegment')

// 引入MQ 消息处理（customer）方法
const limitRequest = require('./libs/limitRequest')

if (global.CONFIG.env === 'production') {
    require('./mq')
}

const apiJsonPath = '/console/api-docs.json'
// 初始化内部网段
getIpSegment.getUcloudIPSegment()
// 生成 json 文件的路径
// 由于express目前无法提供port，所以需要手动传入port
router.get(apiJsonPath, ss.genApiJsonRouter(global.CONFIG.port))
// 生成swaggerUI文件的路径
router.get('/console/api-docs', ss.genSwaggerRouter(apiJsonPath))

router.all('/metrics',(req, res) => {
	res.json({ RetCode: 0, Message: 'OK'})
})
router.use(limitRequest)

router.all('*', (req, res) => {
    let params = {}
    let reqdate = new Date()
    _.extend(params, req.query)
    _.extend(params, req.body)
    _.extend(params, req.params)
    params = transferParams(params)
    params = trimObject(params)
    let uid = uuid()
    let resp = {
        RetCode: 0,
        Message: 'OK',
    }
    logger.getLogger('access').info('[' + uid + ']', params)
    res.on('finish', function () {
        // const curspan = trace.getSpan(context.active())
        // curspan.setAttributes({ ...resp, UUID: uid })
        logger
            .getLogger('access')
            .info(
                '[' + uid + '][Record]',
                `Action[${params.Action}][Time-Consume]`,
                `${moment().diff(reqdate, 'ms')}ms`
            )
    })
    // 控制台不需要白名单

    if (
        (global.CONFIG.env === 'development' || global.CONFIG.env === 'test') &&
        !req.query.top_organization_id
    ) {
        req.query.top_organization_id = 455
    }

    params.CompanyId = params.company_id || params.top_organization_id
    params.client_ip = params.client_ip || req.connection.remoteAddress
    params.Channel = params.channel || 1
    if (
        !methods[params.Action] &&
        !cMethods[params.Action] &&
        !coMethods[params.Action]
    ) {
        resp = {
            RetCode: 10001,
            Message: `No Such Method, ${params.Action}`,
        }

        logger.getLogger('error').error('[' + uid + ']', resp)
        return res.json(resp)
    } else if (coMethods[params.Action]) {
        methods[params.Action] = coMethods[params.Action]
    } else if (cMethods[params.Action]) {
        methods[params.Action] = cMethods[params.Action]
    }

    const error = ss.validateWithHook(params, null, null, convertParams)

    if (error) {
        logger.getLogger('error').error('[' + uid + ']', JSON.stringify(error))
        resp = {
            RetCode: 10003,
            Message: JSON.stringify(error),
        }
        return res.json(resp)
    }

    // 调用方法
    let method = new methods[params.Action]((retCode, data) => {
        // 不需要返回数据
        if (!data) {
            data = {}
        }

        // 如果 data 不是对象
        if (!_.isObject(data)) {
            resp = {
                RetCode: 10002,
                Message: 'Internel Error',
            }
            logger.getLogger('error').error('[' + uid + ']', data)
            return res.json(resp)
        }

        if (params.Action) {
            data.Action = `${params.Action}Response`
        }
        data.RetCode = retCode

        // 拼凑 ErrorMessage
        if (retCode !== 0 && !data.Message) {
            data.Message = errorCode[retCode] || 'Internal Error'
        }
        resp = {
            RetCode: data.RetCode,
            Message: data.Message,
        }
        logger.getLogger('access').info('[' + uid + ']', JSON.stringify(data))
        res.json(data)
    })

    method.exec(params)
})

function convertParams(params, rules) {
    if (!rules) return params
    // if (rules.type !== "object") {
    //   return params
    // }
    console.log('first====', rules, params)
    Object.keys(rules).forEach((key) => {
        switch (rules[key].type) {
            case 'integer':
                console.log('======1', key)
                if (typeof params[key] === 'string') {
                    params[key] = parseInt(params[key])
                    console.log('======', key, params[key])
                }
                break
            case 'array':
                console.log('===2', params[key])
                if (Array.isArray(params[key])) {
                    params[key] = params[key].map((param) =>
                        convertParams(
                            param,
                            rules[key].items
                                ? rules[key].items.properties ||
                                      rules[key].items
                                : rules[key].items
                        )
                    )
                }
                break
            case 'object':
                console.log('======3', key)
                if (params[key]) {
                    params[key] = convertParams(
                        params[key],
                        rules[key].properties
                    )
                }
                break
            case 'boolean':
                console.log('======4', key)
                if (params[key]) {
                    params[key] =
                        params[key] === 'true' ||
                        params[key] === true ||
                        params[key] === '1' ||
                        params[key] === 1
                }
                break
            case 'string':
                console.log('======5', key)
                if (params[key]) {
                    params[key] = params[key].toString().trim()
                }
                break
            default:
                break
        }
    })
    console.log(params, 'aaaaaaaaaaaa')
    return params
}

module.exports = router
