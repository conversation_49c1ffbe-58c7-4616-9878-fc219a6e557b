const express = require('express')
const router = express.Router()
const requireAll = require('require-all')
const _ = require('lodash')
const uuid = require('uuid/v4')

const methods = require('./methods/admin')
const cMethods = requireAll(__dirname + '/methods/common')
const coMethods = requireAll(__dirname + '/methods/crossBorder')
const errorCode = require('./configs/error')
// const whiteList = require('./configs/whiteList')
const logger = require('./libs/logger')

// const { context, trace } = require('@opentelemetry/api')
const SwaggerAndSchema = require('swagger-and-schema')
const moment = require('moment')
var ss = new SwaggerAndSchema({ apisDirPath: `${__dirname}/configs/apis` })
const getIpSegment = require('./libs/ipSegment')

// 引入MQ 消息处理（customer）方法
if (global.CONFIG.env === 'production') {
    // require('./mq/index')
    require('./crons')
}

const apiJsonPath = '/admin/api-docs.json'
// 生成 json 文件的路径
// 由于express目前无法提供port，所以需要手动传入port
router.get(apiJsonPath, ss.genApiJsonRouter(global.CONFIG.port))
getIpSegment.getUcloudIPSegment()

// 生成swaggerUI文件的路径
router.get('/api-docs', ss.genSwaggerRouter(apiJsonPath))

router.all('/metrics',(req, res) => {
	res.json({ RetCode: 0, Message: 'OK'})
})

router.all('*', (req, res) => {
    let reqdate = new Date()
    let params = {}
    _.extend(params, req.query)
    _.extend(params, req.body)
    _.extend(params, req.params)
    let uid = uuid()
    logger.getLogger('access').info('[' + uid + ']', 'headers:', JSON.stringify(req.headers) , 'params:',JSON.stringify(params))
    let resp = {
        RetCode: 0,
        Message: 'OK',
    }
    res.on('finish', function () {
        // const curspan = trace.getSpan(context.active())
        // curspan.setAttributes({ ...resp, UUID: uid })
        logger
            .getLogger('access')
            .info(
                '[' + uid + '][Record]',
                `Action[${params.Action}][Time-Consume]`,
                `${moment().diff(reqdate, 'ms')}ms`
            )
    })
    // 控制台不需要白名单
    params['__ssoUser'] =
        params.staff_name_en || req.headers.remote_user || 'local_test'

    // 部分接口不需要鉴权
    if (
        global.CONFIG.env === 'production' &&
        !params['__ssoUser'] &&
        params.Action !== 'GetPicture' &&
        params.Action !== 'GetICPList' &&
        params.Action !== 'GetChatAuxiliaryAnswer' &&
        params.Action !== 'GetWebPageData'
    ) {
        resp = {
            RetCode: 10002,
            Message: '没有经过sso认证',
        }
        logger.getLogger('error').error('[' + uid + ']', resp)
        return res.json(resp)
    }

    if (
        !methods[params.Action] &&
        !cMethods[params.Action] &&
        !coMethods[params.Action]
    ) {
        resp = {
            RetCode: 10001,
            Message: 'No Such Method',
        }

        logger.getLogger('error').error('[' + uid + ']', resp)
        return res.json(resp)
    } else if (cMethods[params.Action]) {
        methods[params.Action] = cMethods[params.Action]
    } else if (coMethods[params.Action]) {
        methods[params.Action] = coMethods[params.Action]
    }

    // const error = ss.validate(params)
    const error = ss.validateWithHook(params, null, null, convertParams)

    if (error) {
        logger.getLogger('error').error('[' + uid + ']', JSON.stringify(error))
        resp = {
            RetCode: 10003,
            Message: JSON.stringify(error),
        }
        return res.json(resp)
    }
    // 调用方法
    let method = new methods[params.Action]((retCode, data) => {
        // 不需要返回数据
        if (!data) {
            data = {}
        }

        // 如果 data 不是对象
        if (!_.isObject(data)) {
            logger.getLogger('error').error('[' + uid + ']', data)
            resp = {
                RetCode: 10002,
                Message: 'Internel Error',
            }
            return res.json(resp)
        }

        if (params.Action) {
            data.Action = `${params.Action}Response`
        }
        data.RetCode = retCode

        // 拼凑 ErrorMessage
        if (retCode !== 0 && !data.Message) {
            data.Message = errorCode[retCode] || 'Internal Error'
        }
        resp = {
            RetCode: data.RetCode,
            Message: data.Message,
        }
        logger.getLogger('access').info('[' + uid + ']', JSON.stringify(data))

        res.json(data)
    })

    method.exec(params)
})

function convertParams(params, rules) {
    if (!rules) return params
    // if (rules.type !== "object") {
    //   return params
    // }
    Object.keys(rules).forEach((key) => {
        switch (rules[key].type) {
            case 'integer':
                if (typeof params[key] === 'string') {
                    params[key] = parseInt(params[key])
                }
                break
            case 'array':
                if (Array.isArray(params[key])) {
                    params[key] = params[key].map((param) =>
                        convertParams(
                            param,
                            rules[key].items
                                ? rules[key].items.properties ||
                                      rules[key].items
                                : rules[key].items
                        )
                    )
                }
                break
            case 'object':
                if (params[key]) {
                    params[key] = convertParams(
                        params[key],
                        rules[key].properties
                    )
                }
                break
            case 'boolean':
                console.log('======4', key)
                if (params[key]) {
                    params[key] =
                        params[key] === 'true' ||
                        params[key] === true ||
                        params[key] === '1' ||
                        params[key] === 1
                }
                break
            case 'string':
                console.log('======5', key)
                if (params[key]) {
                    params[key] = params[key].toString().trim()
                }
                break
            default:
                break
        }
    })
    return params
}

module.exports = router
