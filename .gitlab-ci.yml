# 全局变量
variables:
  IMAGE_NAME: "hub.ucloudadmin.com/tpl_crd_icp/newicp-backend"

  # 下面这些变量针对 KUN 真共享 Runner 设置
  # 通过定义 KUN_NAMESPACE 这个变量，job runner 将运行在指定的 Namespace 中，这样就能使用这个 NS 下的权限、Secret 以及所有其他资源
  KUN_NAMESPACE: prj-icp
  # KUN_USER 和 KUN_PASSWORD 两个变量用于指定 KUN 的账号，改账号需要拥有上面 Namespace 的权限，由于我们已经将这两个变量设置成 Gitlab CI/CD Variables，所以这里无需设置。
  KUN_USER: yuewen.li
  KUN_PASSWORD: vdg22q79abx45v513y72danr1ld80jeg
    # 通过 KUN_IMAGE_PULL_SECRET_NAMES 变量指定 image pull secrets，以英文逗号隔开
  KUN_IMAGE_PULL_SECRET_NAMES: "crd"
  # 通过 KUN_IMAGE_PUSH_SECRET 指定 Push 镜像所需要的 Secret，如果不需要 build 镜像，不设置或设置为空即可
  KUN_IMAGE_PUSH_SECRET: "crd"
  # 下面 4 个变量可以修改默认的 Runner 资源配置, 这些变量也可以针对每个 Job 单独设置
  KUBERNETES_CPU_REQUEST: "1000m"
  KUBERNETES_CPU_LIMIT: "2000m"
  KUBERNETES_MEMORY_REQUEST: "1024Mi"
  KUBERNETES_MEMORY_LIMIT: "4Gi"

# stages 顺序运行, 同一个 stage 的所有 job 并行
stages:
  - BuildImage
  - PreDeploy
  - ProductionDeploy

docker-image:
  # 通过 uaek 的标签来使用 KUN 真共享 Runner
  tags:
    - uaek-c1
  stage: BuildImage
  # 请使用 UAEK Kaniko 镜像，该 Image 在官方镜像基础上，添加了 busybox，这样才能和 gitlab 联动起来
  # image: hub.ucloudadmin.com/public/uaek-kaniko-executor:latest
  image: hub.ucloudadmin.com/public/uaek-kaniko-executor:v1.3.0
  script:
    # 首先我们需要为我们的镜像生成一个 tag，规则是：如果有 git tag，就使用 git tag，如果没有的话，就使用 git commit sha
    - IMAGE_TAG=$CI_COMMIT_SHA && ADMIN_SIGN="" && if [[ -n "$CI_COMMIT_TAG" ]]; then IMAGE_TAG=$CI_COMMIT_TAG; fi
    - if [[ -n "$CI_COMMIT_TAG" ]] && [[ $(echo $CI_COMMIT_TAG | grep admin) ]]; then ADMIN_SIGN=$(echo $CI_COMMIT_TAG | grep admin); fi
    # 用于区分 客户侧和审核侧的发布 若提交至审核侧分支 或者tag中包含审核侧admin标志， 则修改镜像文件 审核侧镜像
    - if [[ "$CI_COMMIT_BRANCH" == admin-dev ]] || [[  "$ADMIN_SIGN" != "" ]]; then sed -i "s/console/admin/g" Dockerfile && sed -i "s/6060/6161/g" Dockerfile ; fi
    - if [[ "$CI_COMMIT_BRANCH" == admin-dev ]]; then IMAGE_TAG="${IMAGE_TAG}-admin"; fi
    # - /kaniko/executor -c $CI_PROJECT_DIR -f build/Dockerfile --cache=true -d $IMAGE_NAME:$IMAGE_TAG --cache-repo ${IMAGE_NAME}-cache
    - /kaniko/executor -c $CI_PROJECT_DIR -f Dockerfile -d $IMAGE_NAME:$IMAGE_TAG
  # 使用 only 来限制这个 job 什么情况下会运行，下面的设置标识只有在 dev 分支发生变化，或者新的 tag 被创建时才会运行。
  only:
    - tags
    - dev
    - admin-dev
 
 # 灰度发布，将部署在 指定 集群的 prj-icp 项目下，资源集为 newicp-console-backend
pre-deploy:
  # 通过 uaek 的标签来使用 KUN 真共享 Runner
  tags:
    - uaek-c1
  stage: PreDeploy
  # 我们使用 uaek-ciclient 工具镜像，在 UAEK 的部署系统中创建一个版本，资源集 newicp-backend
  image: hub.ucloudadmin.com/uaek/uaek-ciclient:latest
  variables:
    CD_PROJECT: prj-icp
    CD_RESOURCESET: newicp-backend
    CD_FILE: scripts/deploy/output/pre.yaml
    CD_USERNAME: $KUN_USER              # 用户名（邮箱前缀）
    CD_PASSWORD: $KUN_PASSWORD          # UAEK 授权码
    CD_CREATE_JOB: "true"  # 设置为 false，部署任务不会被自动创建，用户需要在 KUN 控制台上手工创建
    CD_CLUSTER: uae-c1 # 指定部署的集群
  script:
    - IMAGE_TAG=$CI_COMMIT_SHA && if [[ -n "$CI_COMMIT_TAG" ]]; then IMAGE_TAG=$CI_COMMIT_TAG ; fi
    # 用于区分 客户侧和审核侧的发布 若提交至审核侧分支 或者tag中包含审核侧标志， 则修改镜像文件 审核侧镜像
    - YML_DIR=dev && if [[ "$CI_COMMIT_BRANCH" == admin-dev ]] ; then YML_DIR=admin-dev && sed -i "s/console.js/admin.js/g" Dockerfile ; fi
    # 修改tag用于区分是审核侧还是客户侧的灰度镜像
    - if [[ "$CI_COMMIT_BRANCH" == admin-dev ]]; then IMAGE_TAG="${IMAGE_TAG}-admin"; fi
    - cd $CI_PROJECT_DIR/scripts/deploy/$YML_DIR && kustomize edit set image $IMAGE_NAME=$IMAGE_NAME:$IMAGE_TAG
    - cd $CI_PROJECT_DIR && mkdir scripts/deploy/output && kustomize build scripts/deploy/$YML_DIR > ${CD_FILE}
    - cd $CI_PROJECT_DIR && /root/ciclient -version=$IMAGE_TAG
  # argifacts will be uploaded to gitlab, you can download them from browser
  artifacts:
    paths:
      - scripts/deploy/output
  # run this job only when new tag is created
  only:
    - dev
    - admin-dev

 # 线上发布，将部署在 指定 集群的 prj-icp 项目下，资源集为 newicp-backend-online
production-deploy:
  # 通过 uaek 的标签来使用 KUN 真共享 Runner
  tags:
    - uaek-c1
  stage: ProductionDeploy
  # 我们使用 uaek-ciclient 工具镜像，在 UAEK 的部署系统中创建一个版本，该版本属于资源集 uaek-cicd-demo-test
  image: hub.ucloudadmin.com/uaek/uaek-ciclient:latest
  variables:
    CD_PROJECT: prj-icp
    CD_RESOURCESET: newicp-backend-online
    CD_FILE: scripts/deploy/output/prod.yaml
    CD_USERNAME: $KUN_USER              # 用户名（邮箱前缀）
    CD_PASSWORD: $KUN_PASSWORD          # UAEK 授权码
    CD_CREATE_JOB: "true"  # 设置为 false，部署任务不会被自动创建，用户需要在 KUN 控制台上手工创建
    CD_CLUSTER: uae-c1 # 指定部署的集群
  script:
    - IMAGE_TAG=$CI_COMMIT_SHA && if [[ -n "$CI_COMMIT_TAG" ]]; then IMAGE_TAG=$CI_COMMIT_TAG ; fi
    # 如果tag中包含admin 则发布admin
    - YML_DIR=prod && if [[ $CI_COMMIT_TAG =~ admin ]]; then YML_DIR=admin-prod && echo "test ${YML_DIR}"; fi
    - cd $CI_PROJECT_DIR/scripts/deploy/$YML_DIR && kustomize edit set image $IMAGE_NAME=$IMAGE_NAME:$IMAGE_TAG
    - cd $CI_PROJECT_DIR && mkdir scripts/deploy/output && kustomize build scripts/deploy/$YML_DIR > ${CD_FILE}
    - cd $CI_PROJECT_DIR && /root/ciclient -version=$IMAGE_TAG
  # argifacts will be uploaded to gitlab, you can download them from browser
  artifacts:
    paths:
      - scripts/deploy/output
  # 我们不希望正式部署全自动运行，所以这里指定了 when: manual，只有当人工在页面上点击了继续之后，这个 job 才会运行
  when: manual
  # allow_failure 规定了这个 job 是否允许失败，也就是说当这个 job 失败时，下面的 job 是否运行，默认值为 false
  # 但是当指定了 when: manual 后，allow_failure 会变成 true，所以我们显式地加上 allow_failure: false
  # allow_failure: false
  # run this job only when new tag is created
  only:
    - tags


