'use strict'
process.env.UV_THREADPOOL_SIZE = 128

let ENV = require('./configs/env')

if (process.env.SERVER_ENV) {
    ENV = process.env.SERVER_ENV
}
process.env.NODE_TLS_REJECT_UNAUTHORIZED = '0'

global.CONFIG = require('./configs/' + ENV + '/config')

if (process.env.APPPath) {
    global.CONFIG.appPath = process.env.APPPath
}
// 增加追踪
// if (global.CONFIG.env === 'production') {
//     require('./libs/tracer')('console')
// }
const Logger = require('./libs/logger')
const express = require('express')
const http = require('http')
const cookieParser = require('cookie-parser')
const bodyParser = require('body-parser')
const os = require('os')
const _ = require('lodash')
const uppercamelcase = require('uppercamelcase')
const rateLimit = require('express-rate-limit')
const RedisStore = require('rate-limit-redis')
let app = express()
const redis = require('./libs/redis').get()
const initChannelInfo = require('./libs/initChannelInfo')
app.use(bodyParser.json({ limit: '50mb' }))
app.use(
    bodyParser.urlencoded({
        limit: '50mb',
        extended: false,
    })
)
app.use(cookieParser())

initChannelInfo()

const limiter = rateLimit({
    // Rate limiter configuration
    windowMs: 1 * 1000, // 单位为毫秒
    max: 15, // Limit each IP to 10 requests per `window` (here, per 1s)
    standardHeaders: true, // Return rate limit info in the `RateLimit-*` headers
    legacyHeaders: false, // Disable the `X-RateLimit-*` headers
    // 先使用网关携带的x-real-ip客户端的真实ip, 若网关无或者未通过网关访问，则使用x-forwarded-for的最左侧的ip（客户端ip） 否则再使用默认的req.ip
    keyGenerator: (req, res) => req.headers['x-real-ip'] || req.headers['x-forwarded-for']?.split(',')[0] || req.ip,
    // Redis store configuration
    store: new RedisStore({
        sendCommand: (...args) => redis.call(...args),
    }),
})
app.use(limiter)

app.use('/', require('./consoleRouter'))

const getLocalIP = function () {
    let IPv4 = '0.0.0.0'
    const interfaces = os.networkInterfaces() || {}
    if (interfaces.eth0) {
        IPv4 = interfaces.eth0[0].address
    }
    return IPv4
}

this.uppercamelcase = function (v) {
    // 特殊处理PIC Person in charge
    return v.substr(0, 3) === 'pic'
        ? 'PIC' + uppercamelcase(v.substr(3, v.length - 1))
        : uppercamelcase(v)
}
let localIP = getLocalIP()

http.createServer(app).listen(global.CONFIG.consolePort, localIP, () => {
    console.log(
        `Listen to  ${localIP}  ${
            global.CONFIG.consolePort
        } at ${new Date()} Env ${ENV}`
    )
})
app.on('error', function (err) {
    Logger.getLogger('error').error(
        `[${new Date()}], system: server error occurred: ${err.message}`
    )
})

process.on('uncaughtException', (err) =>
    Logger.getLogger('error').error(
        `[${new Date()}] system: uncaughtException`,
        err.message
    )
)
