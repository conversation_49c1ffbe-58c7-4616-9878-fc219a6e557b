const { DataTypes } = require('sequelize')
const {
    parseModels,
    upperCamelcase,
    parseNullObject,
} = require('../fns/parseModels')
const DB = require('../libs/mysql')
const sequelize = DB.get('icp')

const VerifyLogModel = {}

const VerifyLogTypeEnum = {
    GetIdenityOCRInfo: 0,
    GetBusinessLicenseOCRInfo: 1,
    VerifyTwoFaceMatch: 2,
    GetRandomNumber: 3,
    ValidateVideo: 4,
    ValidatePersonalInfo: 5,
    UploadFile: 6,
    CheckEpicEntPersonRelation: 7,
    SendCaptcha: 8,
    CheckCaptcha: 9,
    CheckVideoAndGetBestPicture: 10,
    GetDomainRegisterMsg: 11,
    CheckDomainRegisterMsg: 12,
    ValidatePersonalTwo: 13,
    ValidateBusinessLicenseInfo: 14,
}

const VerifyLogTypeCnEnum = {
    0: '身份证OCR',
    1: '营业执照OCR',
    2: '双脸匹配',
    3: '获取活体随机数',
    4: '验证视频',
    5: '验证个人信息（三要素）',
    6: '上传文件',
    7: '检查企业法人四要素信息',
    8: '发送验证码',
    9: '检查验证码',
    10: '视频换脸与光线过暗',
    11: '获取域名注册信息',
    12: '检查域名注册信息',
    13: '检查个人信息（二要素）',
    14: '检查企业三要素',
}
VerifyLogModel.VerifyLogTypeCnEnum = VerifyLogTypeCnEnum
VerifyLogModel.VerifyLogTypeEnum = VerifyLogTypeEnum
VerifyLogModel.model = sequelize.define(
    't_verify_log',
    parseModels({
        id: {
            type: DataTypes.INTEGER(11).UNSIGNED,
            allowNull: false,
            primaryKey: true,
            autoIncrement: true,
        },
        uuid: {
            type: DataTypes.STRING(255),
            allowNull: true,
        },
        data_content: {
            type: DataTypes.STRING(1024),
            allowNull: false,
            defaultValue: '',
            set(val) {
                this.setDataValue(
                    upperCamelcase('data_content'),
                    JSON.stringify(val)
                )
            },
            get() {
                const rawValue = this.getDataValue(
                    upperCamelcase('data_content')
                )
                return parseNullObject(rawValue, {})
            },
        },
        create_time: {
            type: DataTypes.INTEGER(16),
            allowNull: false,
            defaultValue: '0',
        },
        update_time: {
            type: DataTypes.INTEGER(16),
            allowNull: false,
            defaultValue: '0',
        },
        result: {
            type: DataTypes.INTEGER(4),
            allowNull: false,
            defaultValue: '0',
        },
        type: {
            type: DataTypes.INTEGER(4),
            allowNull: false,
            defaultValue: '0',
        },
        multiplex: {
            type: DataTypes.INTEGER(4),
            allowNull: false,
            defaultValue: '0',
        },
        response: {
            type: DataTypes.STRING(8048),
            allowNull: false,
            defaultValue: '',
            set(val) {
                this.setDataValue(
                    upperCamelcase('response'),
                    JSON.stringify(val)
                )
            },
            get() {
                const rawValue = this.getDataValue(upperCamelcase('response'))
                return parseNullObject(rawValue, {})
            },
        },
        return_object: {
            type: DataTypes.STRING(8048),
            allowNull: false,
            defaultValue: '',
            set(val) {
                this.setDataValue(
                    upperCamelcase('return_object'),
                    JSON.stringify(val)
                )
            },
            get() {
                const rawValue = this.getDataValue(
                    upperCamelcase('return_object')
                )
                return parseNullObject(rawValue, {})
            },
        },
        order_no: {
            type: DataTypes.STRING(255),
            allowNull: false,
            defaultValue: '',
            unique: true,
        },
        remark: {
            type: DataTypes.STRING(255),
            allowNull: false,
            defaultValue: 'user',
        },
        operator: {
            type: DataTypes.STRING(255),
            allowNull: false,
            defaultValue: 'user',
        },
        company_id: {
            type: DataTypes.INTEGER(50),
            allowNull: true,
        },
    }),
    {
        tableName: 't_verify_log',
    }
)
module.exports = VerifyLogModel
