const {
    parseModels,
    upperCamelcase,
    parseNullArr,
    parseNullObject,
} = require('../fns/parseModels')
const { DataTypes } = require('sequelize')
const DB = require('../libs/mysql')
const sequelize = DB.get('icp')

const OrderWebModel = {}

OrderWebModel.model = sequelize.define(
    't_order_web',
    parseModels({
        id: {
            type: DataTypes.INTEGER(11),
            allowNull: false,
            primaryKey: true,
            autoIncrement: true,
        },
        order_no: {
            type: DataTypes.STRING(255),
            allowNull: false,
            defaultValue: '',
        },
        icp_web_no: {
            type: DataTypes.STRING(255),
            allowNull: false,
            defaultValue: '',
        },
        name: {
            type: DataTypes.STRING(255),
            allowNull: false,
            defaultValue: '',
        },
        domain: {
            type: DataTypes.TEXT('long'),
            allowNull: false,
            defaultValue: '',
            comment:
                '网站下的域名列表，包含域名，域名证书，域名是否在我司接入，域名是否为主域名',
            set(val) {
                this.setDataValue(upperCamelcase('domain'), JSON.stringify(val))
            },
            get() {
                const rawValue = this.getDataValue(upperCamelcase('domain'))
                return parseNullObject(rawValue, [])
            },
        },
        fetch_info: {
            type: DataTypes.STRING(1024),
            allowNull: false,
            defaultValue: '',
            set(val) {
                this.setDataValue(
                    upperCamelcase('fetch_info'),
                    JSON.stringify(val)
                )
            },
            get() {
                const rawValue = this.getDataValue(upperCamelcase('fetch_info'))
                return parseNullObject(rawValue, {})
            },
        },
        ip: {
            type: DataTypes.STRING(1024),
            allowNull: false,
            defaultValue: '',
            set(val) {
                this.setDataValue(
                    upperCamelcase('ip'),
                    typeof val === 'object' ? val.join('|') : ''
                )
            },
            get() {
                const rawValue = this.getDataValue(upperCamelcase('ip'))
                return parseNullArr(rawValue)
            },
        },
        url: {
            type: DataTypes.STRING(1024),
            allowNull: false,
            defaultValue: '',
        },
        language: {
            type: DataTypes.STRING(255),
            allowNull: false,
            defaultValue: '',
            set(val) {
                this.setDataValue(
                    upperCamelcase('language'),
                    typeof val === 'object' ? val.join('|') : ''
                )
            },
            get() {
                const rawValue = this.getDataValue(upperCamelcase('language'))
                return parseNullArr(rawValue)
            },
        },
        need_modify_web: {
            type: DataTypes.TINYINT(1),
            allowNull: false,
            defaultValue: 0,
        },
        service_type: {
            type: DataTypes.STRING(255),
            allowNull: false,
            defaultValue: '',
            set(val) {
                this.setDataValue(
                    upperCamelcase('service_type'),
                    typeof val === 'object' ? val.join('|') : ''
                )
            },
            get() {
                const rawValue = this.getDataValue(
                    upperCamelcase('service_type')
                )
                return parseNullArr(rawValue)
            },
        },
        pre_appoval: {
            type: DataTypes.STRING(10240),
            allowNull: false,
            defaultValue: '[]',
            set(val) {
                this.setDataValue(
                    upperCamelcase('pre_appoval'),
                    !val || val === '' ? '[]' : JSON.stringify(val)
                )
            },
            get() {
                const rawValue = this.getDataValue(
                    upperCamelcase('pre_appoval')
                )
                return parseNullObject(rawValue, [])
            },
        },
        pic_name: {
            type: DataTypes.STRING(255),
            allowNull: false,
            defaultValue: '',
        },
        phone: {
            type: DataTypes.STRING(16),
            allowNull: false,
            defaultValue: '',
        },
        phone_pre: {
            type: DataTypes.STRING(16),
            allowNull: true,
            defaultValue: '',
        },
        mobile: {
            type: DataTypes.STRING(255),
            allowNull: false,
            defaultValue: '',
        },
        email: {
            type: DataTypes.STRING(255),
            allowNull: false,
            defaultValue: '',
        },
        qq: {
            type: DataTypes.STRING(255),
            allowNull: false,
            defaultValue: '',
        },
        license_type: {
            type: DataTypes.INTEGER(4),
            allowNull: false,
            defaultValue: '0',
        },
        license_id: {
            type: DataTypes.STRING(255),
            allowNull: false,
            defaultValue: '',
        },
        license_date: {
            type: DataTypes.STRING(128),
            allowNull: false,
            defaultValue: '',
            comment: '网站负责人证件有效期',
        },
        license_picture: {
            type: DataTypes.STRING(1024),
            allowNull: false,
            defaultValue: '',
            set(val) {
                this.setDataValue(
                    upperCamelcase('license_picture'),
                    typeof val === 'object' ? val.join('|') : ''
                )
            },
            get() {
                const rawValue = this.getDataValue(
                    upperCamelcase('license_picture')
                )
                return parseNullArr(rawValue)
            },
        },
        curtain_picture: {
            type: DataTypes.STRING(1024),
            allowNull: false,
            defaultValue: '',
            set(val) {
                this.setDataValue(
                    upperCamelcase('curtain_picture'),
                    typeof val === 'object' ? val.join('|') : ''
                )
            },
            get() {
                const rawValue = this.getDataValue(
                    upperCamelcase('curtain_picture')
                )
                return parseNullArr(rawValue)
            },
        },
        relevant_promise_letter: {
            type: DataTypes.STRING(1024),
            allowNull: false,
            defaultValue: '',
            comment: '有关承诺书',
            set(val) {
                this.setDataValue(
                    upperCamelcase('relevant_promise_letter'),
                    typeof val === 'object' ? val.join('|') : ''
                )
            },
            get() {
                const rawValue = this.getDataValue(
                    upperCamelcase('relevant_promise_letter')
                )
                return parseNullArr(rawValue)
            },
        },
        auth_verification_picture: {
            type: DataTypes.STRING(1024),
            allowNull: false,
            defaultValue: '',
            set(val) {
                this.setDataValue(
                    upperCamelcase('auth_verification_picture'),
                    typeof val === 'object' ? val.join('|') : ''
                )
            },
            get() {
                const rawValue = this.getDataValue(
                    upperCamelcase('auth_verification_picture')
                )
                return parseNullArr(rawValue)
            },
        },
        other_picture: {
            type: DataTypes.STRING(1024),
            allowNull: false,
            defaultValue: '',
            set(val) {
                this.setDataValue(
                    upperCamelcase('other_picture'),
                    typeof val === 'object' ? val.join('|') : ''
                )
            },
            get() {
                const rawValue = this.getDataValue(
                    upperCamelcase('other_picture')
                )
                return parseNullArr(rawValue)
            },
        },
        remark: {
            type: DataTypes.STRING(1024),
            allowNull: true,
            defaultValue: '',
        },
        create_time: {
            type: DataTypes.INTEGER(11),
            allowNull: true,
            defaultValue: '0',
        },
        update_time: {
            type: DataTypes.INTEGER(11),
            allowNull: true,
            defaultValue: '0',
        },
        license_picture_for_auth: {
            type: DataTypes.STRING(1024),
            allowNull: false,
            defaultValue: '',
            set(val) {
                this.setDataValue(
                    upperCamelcase('license_picture_for_auth'),
                    typeof val === 'object' ? val.join('|') : ''
                )
            },
            get() {
                const rawValue = this.getDataValue(
                    upperCamelcase('license_picture_for_auth')
                )
                return parseNullArr(rawValue)
            },
        },
        curtain_picture_for_auth: {
            type: DataTypes.STRING(1024),
            allowNull: false,
            defaultValue: '',
            set(val) {
                this.setDataValue(
                    upperCamelcase('curtain_picture_for_auth'),
                    typeof val === 'object' ? val.join('|') : ''
                )
            },
            get() {
                const rawValue = this.getDataValue(
                    upperCamelcase('curtain_picture_for_auth')
                )
                return parseNullArr(rawValue)
            },
        },
        auth_verification_picture_for_auth: {
            type: DataTypes.STRING(1024),
            allowNull: false,
            defaultValue: '',
            set(val) {
                this.setDataValue(
                    upperCamelcase('auth_verification_picture_for_auth'),
                    typeof val === 'object' ? val.join('|') : ''
                )
            },
            get() {
                const rawValue = this.getDataValue(
                    upperCamelcase('auth_verification_picture_for_auth')
                )
                return parseNullArr(rawValue)
            },
        },
        emergency_phone: {
            type: DataTypes.STRING(256),
            allowNull: true,
        },
        service_content: {
            type: DataTypes.INTEGER(11),
            allowNull: false,
            defaultValue: 1,
        },
        authentication_code: {
            type: DataTypes.STRING(256),
            allowNull: true,
            set(val) {
                this.setDataValue(
                    upperCamelcase('authentication_code'),
                    typeof val === 'object' ? val.join('|') : ''
                )
            },
            get() {
                const rawValue = this.getDataValue(
                    upperCamelcase('authentication_code')
                )
                return parseNullArr(rawValue)
            },
        },
        app_code: {
            type: DataTypes.STRING(255),
            allowNull: true,
        },
        c_website_id: {
            type: DataTypes.INTEGER(11),
            allowNull: false,
            defaultValue: '-1',
        },
        connect_type: {
            type: DataTypes.INTEGER(11),
            allowNull: false,
            defaultValue: '5',
        },
        base_manageprovince: {
            type: DataTypes.STRING(2),
            allowNull: false,
            defaultValue: '5',
            comment: '服务器放置地',
        },
        uuid: {
            type: DataTypes.STRING(255),
            allowNull: true,
        },
        curtain_uuid: {
            type: DataTypes.STRING(255),
            allowNull: true,
        },
        authoriztion_picture: {
            type: DataTypes.STRING(255),
            allowNull: true,
            set(val) {
                this.setDataValue(
                    upperCamelcase('authoriztion_picture'),
                    typeof val === 'object' ? val.join('|') : ''
                )
            },
            get() {
                const rawValue = this.getDataValue(
                    upperCamelcase('authoriztion_picture')
                )
                return parseNullArr(rawValue)
            },
        },
        front_error: {
            type: DataTypes.TEXT,
            allowNull: false,
            defaultValue: '{}',
            set(val) {
                this.setDataValue(
                    upperCamelcase('front_error'),
                    JSON.stringify(val)
                )
            },
            get() {
                const rawValue = this.getDataValue(
                    upperCamelcase('front_error')
                )
                return parseNullObject(rawValue, {})
            },
        },
        reuse_website: {
            type: DataTypes.INTEGER(11),
            allowNull: true,
        },
        internet_service_type: {
            type: DataTypes.INTEGER(11),
            allowNull: false,
            defaultValue: '1',
        },
        is_deleted: {
            type: DataTypes.INTEGER(1),
            allowNull: false,
            defaultValue: '0',
        },
        curtain_status: {
            type: DataTypes.INTEGER(1),
            allowNull: false,
            defaultValue: '0',
        },
        operating_platform_type: {
            type: DataTypes.STRING(20),
            allowNull: false,
            defaultValue: '',
            comment: '运行平台类型',
            set(val) {
                this.setDataValue(
                    upperCamelcase('operating_platform_type'),
                    typeof val === 'object' ? val.join('|') : ''
                )
            },
            get() {
                const rawValue = this.getDataValue(
                    upperCamelcase('operating_platform_type')
                )
                return parseNullArr(rawValue)
            },
        },
        provide_sdk_service: {
            type: DataTypes.TINYINT(1),
            allowNull: false,
            defaultValue: 0,
            comment: '是否提供SDK服务',
        },
        use_third_party_sdk_services: {
            type: DataTypes.TINYINT(1),
            allowNull: false,
            defaultValue: 0,
            comment: '是否使用第三方SDK服务',
        },
        use_third_party_sdk_service_details: {
            type: DataTypes.TEXT,
            allowNull: false,
            defaultValue: '',
            comment: '使用第三方SDK服务',
            set(val) {
                this.setDataValue(
                    upperCamelcase('use_third_party_sdk_service_details'),
                    JSON.stringify(val)
                )
            },
            get() {
                const rawValue = this.getDataValue(
                    upperCamelcase('use_third_party_sdk_service_details')
                )
                return parseNullObject(rawValue, [])
            },
        },
        app_icon: {
            type: DataTypes.STRING(50),
            default: '',
            comment: 'APP图标名称',
        },
    }),
    {
        tableName: 't_order_web',
    }
)
module.exports = OrderWebModel
