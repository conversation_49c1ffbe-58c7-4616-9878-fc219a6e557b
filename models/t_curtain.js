const { parseModels, parseNullArr } = require('../fns/parseModels')
const { DataTypes } = require('sequelize')
const DB = require('../libs/mysql')
const sequelize = DB.get('icp')

const CurtainModel = {}

CurtainModel.model = sequelize.define(
    't_curtain',
    parseModels({
        id: {
            type: DataTypes.INTEGER(11),
            allowNull: false,
            primaryKey: true,
            autoIncrement: true,
        },
        order_no: {
            type: DataTypes.STRING(255),
            allowNull: false,
            defaultValue: '',
            unique: true,
        },
        recipient: {
            type: DataTypes.STRING(255),
            allowNull: false,
            defaultValue: '',
        },
        recipient_mobile: {
            type: DataTypes.STRING(16),
            allowNull: false,
            defaultValue: '',
        },
        recipient_address: {
            type: DataTypes.STRING(1024),
            allowNull: false,
            defaultValue: '',
        },
        express_company: {
            type: DataTypes.STRING(255),
            allowNull: false,
            defaultValue: '',
        },
        express_no: {
            type: DataTypes.STRING(255),
            allowNull: false,
            defaultValue: '',
        },
        send_time: {
            type: DataTypes.INTEGER(11),
            allowNull: false,
            defaultValue: '0',
        },
        create_time: {
            type: DataTypes.INTEGER(11),
            allowNull: false,
            defaultValue: '0',
        },
    }),
    {
        tableName: 't_curtain',
    }
)
module.exports = CurtainModel
