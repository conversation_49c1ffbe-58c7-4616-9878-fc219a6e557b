/*
 * @Author: william.qian <EMAIL>
 * @Date: 2022-10-31 16:45:35
 * @LastEditors: william.qian <EMAIL>
 * @LastEditTime: 2022-12-28 17:20:29
 * @FilePath: /newicp/models/t_bulk_block_domain_batch.js
 * @Description: 批量封禁的记录表
 */
const { parseModels } = require('../fns/parseModels')
const { DataTypes } = require('sequelize')
const DB = require('../libs/mysql')
const sequelize = DB.get('icp')

const BulkBlockRecordModel = {}

BulkBlockRecordModel.BlockRecordStatusEnum = {
    Init: 0, //已导入，
    Blocking: 1, //封禁中，
    Success: 2, //封禁成功，
    Failed: 3, //封禁失败，
    InWhite: 4, //白名单中，
    Accessed: 5, //已接入，
}
BulkBlockRecordModel.model = sequelize.define(
    't_bulk_block_domain_record',
    parseModels({
        id: {
            type: DataTypes.INTEGER(11),
            primaryKey: true,
            autoIncrement: true,
            comment: '记录Id',
        },
        batch_id: {
            type: DataTypes.INTEGER(11),
            comment: '批次Id',
        },
        domain: {
            type: DataTypes.STRING(255),
            allowNull: false,
            defaultValue: '',
            comment: '域名',
        },
        remark: {
            type: DataTypes.STRING(1024),
            allowNull: false,
            defaultValue: '',
            comment: '封禁失败的备注',
        },
        status: {
            type: DataTypes.INTEGER(4),
            allowNull: false,
            defaultValue: '0',
        },
        create_time: {
            type: DataTypes.INTEGER(11),
            allowNull: true,
            defaultValue: sequelize.NOW,
        },
        update_time: {
            type: DataTypes.INTEGER(11),
            allowNull: true,
            defaultValue: sequelize.NOW,
        },
    }),
    {
        tableName: 't_bulk_block_domain_record',
    }
)

module.exports = BulkBlockRecordModel
