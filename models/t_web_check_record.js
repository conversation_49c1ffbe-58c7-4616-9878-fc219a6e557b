/*
 * @Author: william.qian <EMAIL>
 * @Date: 2023-03-20 18:53:39
 * @LastEditors: william.qian <EMAIL>
 * @LastEditTime: 2023-05-16 10:50:20
 * @FilePath: /newicp/models/t_web_check_record.js
 * @Description: 网站备案信息检查
 */
const { parseModels } = require('../fns/parseModels')
const { DataTypes } = require('sequelize')
const DB = require('../libs/mysql')
const sequelize = DB.get('icp')

const WebCheckRecordModel = {}

WebCheckRecordModel.model = sequelize.define(
    't_web_check_record',
    parseModels({
        id: {
            type: DataTypes.INTEGER(11),
            allowNull: false,
            primaryKey: true,
            autoIncrement: true,
        },
        batch_id: {
            type: DataTypes.INTEGER(11),
            allowNull: false,
            comment: '批次Id',
        },
        icp_web_no: {
            type: DataTypes.STRING(50),
            allowNull: false,
            defaultValue: '',
            comment: '网站备案号',
        },
        icp_main_no: {
            type: DataTypes.STRING(50),
            allowNull: false,
            defaultValue: '',
            comment: '网站备案号',
        },
        web_name: {
            type: DataTypes.STRING(255),
            allowNull: false,
            defaultValue: '',
            comment: '网站名',
        },
        company_id: {
            type: DataTypes.INTEGER(11),
            allowNull: true,
            comment: '公司Id',
        },
        domain: {
            type: DataTypes.STRING(255),
            allowNull: false,
            defaultValue: '',
            comment: '域名',
        },
        icp_main_no_true: {
            type: DataTypes.BOOLEAN,
            allowNull: false,
            defaultValue: false,
            comment: '有正确的备案号',
        },
        url_true: {
            type: DataTypes.BOOLEAN,
            allowNull: false,
            defaultValue: false,
            comment: '链接到正确的备案号',
        },
        title_true: {
            type: DataTypes.BOOLEAN,
            allowNull: false,
            defaultValue: false,
            comment: '有正确的网站title',
        },
        have_resolver: {
            type: DataTypes.BOOLEAN,
            allowNull: false,
            defaultValue: false,
            comment: '是否解析到我司',
        },
        is_error: {
            type: DataTypes.BOOLEAN,
            allowNull: false,
            defaultValue: false,
            comment: '请求过程是否有错误',
        },
        error_message: {
            type: DataTypes.STRING(255),
            allowNull: false,
            defaultValue: '',
            comment: '有正确的备案号',
        },
        create_time: {
            type: DataTypes.INTEGER(11),
            allowNull: true,
            defaultValue: sequelize.NOW,
        },
        update_time: {
            type: DataTypes.INTEGER(11),
            allowNull: true,
            defaultValue: sequelize.NOW,
        },
    }),
    {
        tableName: 't_web_check_record',
    }
)
module.exports = WebCheckRecordModel
