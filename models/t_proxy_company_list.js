const { parseModels, parseNullArr } = require('../fns/parseModels')
const { DataTypes } = require('sequelize')
const DB = require('../libs/mysql')
const sequelize = DB.get('icp')

const ProxyCompanyListModel = {}

ProxyCompanyListModel.model = sequelize.define(
    't_proxy_company_list',
    parseModels({
        id: {
            type: DataTypes.INTEGER(11),
            allowNull: false,
            primaryKey: true,
            autoIncrement: true,
        },
        company_id: {
            type: DataTypes.INTEGER(11),
            allowNull: false,
            unique: true,
        },
        company_name: {
            type: DataTypes.STRING(256),
            allowNull: false,
            defaultValue: '',
        },
        operator: {
            type: DataTypes.STRING(255),
            allowNull: false,
            defaultValue: '',
        },
        create_time: {
            type: DataTypes.INTEGER(11),
            allowNull: true,
        },
        update_time: {
            type: DataTypes.INTEGER(11),
            allowNull: true,
        },
        note: {
            type: DataTypes.TEXT,
            allowNull: true,
        },
        quota: {
            type: DataTypes.INTEGER(11),
            allowNull: true,
            defaultValue: null,
        },
        website_quota: {
            type: DataTypes.INTEGER(11),
            allowNull: true,
            defaultValue: null,
        },
        company_type: {
            type: DataTypes.INTEGER(11),
            allowNull: true,
        },
        one_click_release: {
            type: DataTypes.INTEGER(4),
            allowNull: false,
            defaultValue: 0,
        },
    }),
    {
        tableName: 't_proxy_company_list',
    }
)
module.exports = ProxyCompanyListModel
