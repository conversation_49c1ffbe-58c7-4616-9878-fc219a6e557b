/*
 * @Date: 2023-03-08 14:00:56
 * @LastEditors: li<PERSON><PERSON>en <EMAIL>
 * @LastEditTime: 2023-03-20 11:30:49
 * @FilePath: /newicp/models/t_order_web_threeline.js
 */
const {
    parseModels,
    upperCamelcase,
    parseNullArr,
} = require('../fns/parseModels')
const { DataTypes } = require('sequelize')
const DB = require('../libs/mysql')
const sequelize = DB.get('icp')

const OrderWebTwoLineModel = {}

OrderWebTwoLineModel.model = sequelize.define(
    't_order_web_twoline',
    parseModels({
        id: {
            type: DataTypes.INTEGER(11),
            allowNull: false,
            primaryKey: true,
            autoIncrement: true,
        },
        order_web_id: {
            type: DataTypes.STRING(255),
            allowNull: false,
        },
        ip: {
            type: DataTypes.STRING(1024),
            allowNull: false,
            defaultValue: '',
            set(val) {
                this.setDataValue(
                    upperCamelcase('ip'),
                    typeof val === 'object' ? val.join('|') : ''
                )
            },
            get() {
                const rawValue = this.getDataValue(upperCamelcase('ip'))
                return parseNullArr(rawValue)
            },
        },
    }),
    {
        indexes: [
            {
                unique: true,
                fields: ['order_web_id'],
            },
        ],
        tableName: 't_order_web_twoline',
    }
)
module.exports = OrderWebTwoLineModel
