const { DataTypes } = require('sequelize')
const { parseModels } = require('../fns/parseModels')
const DB = require('../libs/mysql')
const sequelize = DB.get('icp')

const OrderNoteModel = {}

OrderNoteModel.model = sequelize.define(
    't_order_note',
    parseModels({
        id: {
            type: DataTypes.INTEGER(11),
            allowNull: false,
            primaryKey: true,
            autoIncrement: true,
            field: 'id',
        },
        company_id: {
            type: DataTypes.INTEGER(16),
            allowNull: false,
            defaultValue: '0',
        },
        order_no: {
            type: DataTypes.STRING(255),
            allowNull: false,
            defaultValue: '',
            unique: true,
        },
        note: {
            type: DataTypes.STRING(255),
            allowNull: false,
            defaultValue: '',
        },
        update_time: {
            type: DataTypes.INTEGER(11),
            allowNull: true,
            defaultValue: 0,
        },
    }),
    {
        tableName: 't_order_note',
    }
)

module.exports = OrderNoteModel
