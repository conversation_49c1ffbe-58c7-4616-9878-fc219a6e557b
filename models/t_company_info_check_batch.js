const { DataTypes } = require('sequelize')
const { parseModels } = require('../fns/parseModels')
const DB = require('../libs/mysql')
const sequelize = DB.get('icp')

const CompanyInfoCheckBatchModel = {}

CompanyInfoCheckBatchModel.model = sequelize.define(
    't_company_info_check_batch',
    parseModels({
        id: {
            autoIncrement: true,
            type: DataTypes.INTEGER.UNSIGNED,

            allowNull: false,
            primaryKey: true,
        },
        remark: {
            type: DataTypes.STRING(40),
            allowNull: false,
            defaultValue: '',
        },
        file_name: {
            type: DataTypes.STRING(256),
            allowNull: false,
            defaultValue: '',
        },
        create_time: {
            type: DataTypes.INTEGER,
            allowNull: true,
        },
        update_time: {
            type: DataTypes.INTEGER,
            allowNull: true,
        },
        source_type: {
            type: DataTypes.INTEGER,
            allowNull: false,
        },
        status: {
            type: DataTypes.INTEGER,
            allowNull: false,
        },
        creator: {
            type: DataTypes.INTEGER,
            allowNull: false,
        },
    }),
    {
        sequelize,
        tableName: 't_company_info_check_batch',
        timestamps: false,
        indexes: [
            {
                name: 'PRIMARY',
                unique: true,
                using: 'BTREE',
                fields: [{ name: 'id' }],
            },
        ],
    }
)
module.exports = CompanyInfoCheckBatchModel
