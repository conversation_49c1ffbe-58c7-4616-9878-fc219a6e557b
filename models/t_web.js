const { DataTypes } = require('sequelize')
const {
    parseModels,
    upperCamelcase,
    parseNullArr,
    parseNullObject,
} = require('../fns/parseModels')
const DB = require('../libs/mysql')
const sequelize = DB.get('icp')

const ICPWebModel = {}

const WebStatusEnum = {
    Normal: 0, //网站接入正常',
    Weblogout: 1, //: '网站已注销',
    Connlogout: 2, //: '接入已注销',
    Weblogouting: 5, //: '网站注销中',
    Connlogouting: 6, // '接入注销中',
    Icpchanging: 7, //: '修改网站信息',
    Webchanging: 9, //: '修改网站信息',
    Connchanging: 10, // '修改接入信息',
    WebAndConnchanging: 19, //'修改网站与接入信息',
}
ICPWebModel.WebStatusEnum = WebStatusEnum

ICPWebModel.model = sequelize.define(
    't_web',
    parseModels({
        id: {
            type: DataTypes.INTEGER(11),
            allowNull: false,
            primaryKey: true,
            autoIncrement: true,
        },
        main_id: {
            type: DataTypes.INTEGER(11),
            allowNull: false,
            defaultValue: '0',
        },
        c_website_id: {
            type: DataTypes.INTEGER(11),
            allowNull: false,
            defaultValue: '-1',
        },
        icp_web_no: {
            type: DataTypes.STRING(255),
            allowNull: false,
            defaultValue: '',
            unique: true,
        },
        status: {
            type: DataTypes.INTEGER(11),
            allowNull: false,
            defaultValue: '0',
        },
        name: {
            type: DataTypes.STRING(255),
            allowNull: false,
            defaultValue: '',
        },
        domain: {
            type: DataTypes.TEXT('long'),
            allowNull: false,
            defaultValue: '',
            comment:
                '网站下的域名列表，包含域名，域名证书，域名是否在我司接入，域名是否为主域名',
            set(val) {
                this.setDataValue(upperCamelcase('domain'), JSON.stringify(val))
            },
            get() {
                const rawValue = this.getDataValue(upperCamelcase('domain'))
                return parseNullObject(rawValue, [])
            },
        },
        ip: {
            type: DataTypes.STRING(1024),
            allowNull: false,
            defaultValue: '',
            set(val) {
                this.setDataValue(
                    upperCamelcase('ip'),
                    typeof val === 'object' ? val.join('|') : ''
                )
            },
            get() {
                const rawValue = this.getDataValue(upperCamelcase('ip'))
                return parseNullArr(rawValue)
            },
        },
        url: {
            type: DataTypes.STRING(1024),
            allowNull: false,
            defaultValue: '',
        },
        language: {
            type: DataTypes.STRING(255),
            allowNull: true,
            defaultValue: '1',
            set(val) {
                this.setDataValue(
                    upperCamelcase('language'),
                    typeof val === 'object' ? val.join('|') : ''
                )
            },
            get() {
                const rawValue = this.getDataValue(upperCamelcase('language'))
                return parseNullArr(rawValue)
            },
        },
        service_type: {
            type: DataTypes.STRING(255),
            allowNull: false,
            defaultValue: '',
            set(val) {
                this.setDataValue(
                    upperCamelcase('service_type'),
                    typeof val === 'object' ? val.join('|') : ''
                )
            },
            get() {
                const rawValue = this.getDataValue(
                    upperCamelcase('service_type')
                )
                return parseNullArr(rawValue)
            },
        },
        service_content: {
            type: DataTypes.INTEGER(11),
            allowNull: true,
            default: 0
        },
        pre_appoval: {
            type: DataTypes.STRING(2048),
            allowNull: false,
            defaultValue: '',
            set(val) {
                this.setDataValue(
                    upperCamelcase('pre_appoval'),
                    JSON.stringify(val)
                )
            },
            get() {
                const rawValue = this.getDataValue(
                    upperCamelcase('pre_appoval')
                )
                return parseNullObject(rawValue, [])
            },
        },
        pic_name: {
            type: DataTypes.STRING(255),
            allowNull: false,
            defaultValue: '',
        },
        phone: {
            type: DataTypes.STRING(16),
            allowNull: false,
            defaultValue: '',
        },
        phone_pre: {
            type: DataTypes.STRING(16),
            allowNull: true,
            defaultValue: '',
        },
        mobile: {
            type: DataTypes.STRING(255),
            allowNull: false,
            defaultValue: '',
        },
        email: {
            type: DataTypes.STRING(255),
            allowNull: false,
            defaultValue: '',
        },
        qq: {
            type: DataTypes.STRING(255),
            allowNull: false,
            defaultValue: '',
        },
        license_type: {
            type: DataTypes.INTEGER(4),
            allowNull: false,
            defaultValue: '0',
        },
        license_id: {
            type: DataTypes.STRING(255),
            allowNull: false,
            defaultValue: '',
        },
        license_date: {
            type: DataTypes.STRING(128),
            allowNull: false,
            defaultValue: '',
            comment: '网站负责人证件有效期',
        },
        license_picture: {
            type: DataTypes.STRING(1024),
            allowNull: false,
            defaultValue: '',
            set(val) {
                this.setDataValue(
                    upperCamelcase('license_picture'),
                    typeof val === 'object' ? val.join('|') : ''
                )
            },
            get() {
                const rawValue = this.getDataValue(
                    upperCamelcase('license_picture')
                )
                return parseNullArr(rawValue)
            },
        },
        curtain_picture: {
            type: DataTypes.STRING(1024),
            allowNull: false,
            defaultValue: '',
            set(val) {
                this.setDataValue(
                    upperCamelcase('curtain_picture'),
                    typeof val === 'object' ? val.join('|') : ''
                )
            },
            get() {
                const rawValue = this.getDataValue(
                    upperCamelcase('curtain_picture')
                )
                return parseNullArr(rawValue)
            },
        },
        relevant_promise_letter: {
            type: DataTypes.STRING(1024),
            allowNull: false,
            defaultValue: '',
            comment: '有关承诺书',
            set(val) {
                this.setDataValue(
                    upperCamelcase('relevant_promise_letter'),
                    typeof val === 'object' ? val.join('|') : ''
                )
            },
            get() {
                const rawValue = this.getDataValue(
                    upperCamelcase('relevant_promise_letter')
                )
                return parseNullArr(rawValue)
            },
        },
        auth_verification_picture: {
            type: DataTypes.STRING(1024),
            allowNull: true,
            defaultValue: '',
            set(val) {
                this.setDataValue(
                    upperCamelcase('auth_verification_picture'),
                    typeof val === 'object' ? val.join('|') : ''
                )
            },
            get() {
                const rawValue = this.getDataValue(
                    upperCamelcase('auth_verification_picture')
                )
                return parseNullArr(rawValue)
            },
        },
        other_picture: {
            type: DataTypes.STRING(1024),
            allowNull: false,
            defaultValue: '',
            set(val) {
                this.setDataValue(
                    upperCamelcase('other_picture'),
                    typeof val === 'object' ? val.join('|') : ''
                )
            },
            get() {
                const rawValue = this.getDataValue(
                    upperCamelcase('other_picture')
                )
                return parseNullArr(rawValue)
            },
        },
        remark: {
            type: DataTypes.STRING(1024),
            allowNull: true,
            defaultValue: '',
        },
        create_time: {
            type: DataTypes.INTEGER(11),
            allowNull: false,
            defaultValue: '0',
        },
        update_time: {
            type: DataTypes.INTEGER(11),
            allowNull: false,
            defaultValue: '0',
        },
        emergency_phone: {
            type: DataTypes.STRING(256),
            allowNull: true,
            default: ''
        },
        app_code: {
            type: DataTypes.STRING(255),
            allowNull: true,
            default: ''
        },
        connect_type: {
            type: DataTypes.INTEGER(11),
            allowNull: true,
            defaultValue: 5,
        },
        base_manageprovince: {
            type: DataTypes.STRING(2),
            allowNull: true,
            defaultValue: '5',
            comment: '服务器放置地',
        },
        is_synced_cid: {
            type: DataTypes.INTEGER(11),
            allowNull: false,
            defaultValue: '0',
        },
        authoriztion_picture: {
            type: DataTypes.STRING(255),
            allowNull: true,
            default: '',
            set(val) {
                this.setDataValue(
                    upperCamelcase('authoriztion_picture'),
                    typeof val === 'object' ? val.join('|') : ''
                )
            },
            get() {
                const rawValue = this.getDataValue(
                    upperCamelcase('authoriztion_picture')
                )
                return parseNullArr(rawValue)
            },
        },
        internet_service_type: {
            type: DataTypes.INTEGER(11),
            allowNull: false,
            defaultValue: '1',
        },
        is_deleted: {
            type: DataTypes.INTEGER(1),
            allowNull: false,
            defaultValue: '0',
        },
        operating_platform_type: {
            type: DataTypes.STRING(20),
            allowNull: false,
            defaultValue: '',
            comment: '运行平台类型',
            set(val) {
                this.setDataValue(
                    upperCamelcase('operating_platform_type'),
                    typeof val === 'object' ? val.join('|') : ''
                )
            },
            get() {
                const rawValue = this.getDataValue(
                    upperCamelcase('operating_platform_type')
                )
                return parseNullArr(rawValue)
            },
        },
        provide_sdk_service: {
            type: DataTypes.TINYINT(1),
            allowNull: false,
            defaultValue: 0,
            comment: '是否提供SDK服务',
        },
        use_third_party_sdk_services: {
            type: DataTypes.TINYINT(1),
            allowNull: false,
            defaultValue: 0,
            comment: '是否使用第三方SDK服务',
        },
        use_third_party_sdk_service_details: {
            type: DataTypes.TEXT,
            allowNull: false,
            defaultValue: '',
            comment: '使用第三方SDK服务',
            set(val) {
                this.setDataValue(
                    upperCamelcase('use_third_party_sdk_service_details'),
                    JSON.stringify(val)
                )
            },
            get() {
                const rawValue = this.getDataValue(
                    upperCamelcase('use_third_party_sdk_service_details')
                )
                return parseNullObject(rawValue, [])
            },
        },
        app_icon: {
            type: DataTypes.STRING(50),
            allowNull: true,
            default: '',
            comment: 'APP图标名称',
        },
    }),
    {
        tableName: 't_web',
    }
)

ICPWebModel.WebMap = {
    ICPWebNo: '网站备案号',
    Name: '网站名称',
    AppIcon: 'App图标',
    Domain: '域名',
    FetchInfo: '补充字段，各省份的特殊要求',
    IP: 'IP',
    Url: '网站IP',
    Language: '网站语言',
    ServiceType: '服务类型',
    PreAppoval: '前置审批',
    PICName: '网站负责人名称',
    Phone: '网站负责人座机',
    PhonePre: '网站负责人座机前缀',
    Mobile: '网站负责人手机',
    Email: '网站负责人邮箱',
    QQ: '网站负责人QQ',
    LicenseType: '网站负责人证件类型',
    LicenseId: '网站负责人证件号',
    LicenseDate: '网站负责人证件有效期',
    LicensePicture: '网站负责人证件照片',
    CurtainPicture: '幕布照片',
    CounterCheatingPromiseLetter: '反诈承诺书',
    AuthVerificationPicture: '真实性核验单',
    OtherPicture: '其他图片',
    Remark: '备注',
    EmergencyPhone: '紧急联系人电话',
    ServiceContent: '服务内容',
    AuthenticationCode: '',
    AppCode: 'APP认证的代码，已废弃',
    ConnectType: '接入类型',
    BaseManageprovince: '服务器放置地',
    AuthoriztionPicture: '授权图片，已废弃',
    ReuseWebsite: '复用来源，已废弃',
    InternetServiceType: '互联网服务类型',
    CurtainStatus: '幕布照片状态',
    RelevantPromiseLetter: '有关承诺书',
    OperatingPlatformType: '运行平台类型',
    ProvideSdkService: '是否提供SDK服务',
    UseThirdPartySdkServices: '是否使用第三方SDK服务',
    UseThirdPartySdkServiceDetails: '第三方SDK服务详情',
}

module.exports = ICPWebModel
