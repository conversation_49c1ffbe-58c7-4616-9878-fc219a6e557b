/*
 * @Date: 2023-05-16 17:06:46
 * @LastEditors: l<PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2023-05-18 16:37:42
 * @FilePath: /newicp/models/t_clear_order_batch.js
 */
const { DataTypes } = require('sequelize')
const { parseModels } = require('../fns/parseModels')
const DB = require('../libs/mysql')
const sequelize = DB.get('icp')

const ClearBatchStatusEnum = {
    awaitClear: 0, // 待清理
    notified: 1, // 已通知
    clearing: 2, // 清理中
    cleared: 3, // 已清理
}

const ClearOrderBatchModel = {}
ClearOrderBatchModel.ClearBatchStatusEnum = ClearBatchStatusEnum
ClearOrderBatchModel.model = sequelize.define(
    't_clear_order_batch',
    parseModels({
        id: {
            type: DataTypes.INTEGER(11),
            allowNull: false,
            primaryKey: true,
            autoIncrement: true,
        },
        status: {
            type: DataTypes.INTEGER(11),
            allowNull: false,
            defaultValue: ClearBatchStatusEnum.awaitClear,
        },
        operator: {
            type: DataTypes.STRING(255),
            allowNull: false,
            defaultValue: '',
        },
        create_time: {
            type: DataTypes.INTEGER(11),
            allowNull: true,
            defaultValue: 0,
        },
        update_time: {
            type: DataTypes.INTEGER(11),
            allowNull: true,
            defaultValue: 0,
        },
        remark: {
            type: DataTypes.STRING(255),
            allowNull: true,
        },
    }),
    {
        tableName: 't_clear_order_batch',
    }
)
module.exports = ClearOrderBatchModel
