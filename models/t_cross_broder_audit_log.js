/*
 * @Date: 2022-07-26 16:24:30
 * @LastEditors: l<PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2022-08-01 11:53:29
 * @FilePath: /newicp/models/t_cross_broder_audit_log.js
 */
const { parseModels } = require('../fns/parseModels')
const { DataTypes } = require('sequelize')
const DB = require('../libs/mysql')
const sequelize = DB.get('icp')
const CrossBorderAuditLogModel = {}

CrossBorderAuditLogModel.model = sequelize.define(
    't_cross_border_audit_log',
    parseModels({
        id: {
            type: DataTypes.INTEGER(11),
            allowNull: false,
            primaryKey: true,
            autoIncrement: true,
        },
        company_id: {
            type: DataTypes.INTEGER(11),
            allowNull: false,
            comment: '申请id',
        },
        operator: {
            type: DataTypes.STRING(255),
            allowNull: false,
            defaultValue: '',
            comment: '操作人',
        },
        remark: {
            type: DataTypes.STRING(1024),
            allowNull: false,
            defaultValue: '',
            comment: '操作内容',
        },
        create_time: {
            type: DataTypes.INTEGER(11),
            allowNull: false,
            defaultValue: '0',
        },
    }),
    {
        tableName: 't_cross_border_audit_log',
        indexes: [{ fields: ['company_id'] }],
    }
)

module.exports = CrossBorderAuditLogModel
