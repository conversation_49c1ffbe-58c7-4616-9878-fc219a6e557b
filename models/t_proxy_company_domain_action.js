const { parseModels } = require('../fns/parseModels')
const { DataTypes } = require('sequelize')
const DB = require('../libs/mysql')
const sequelize = DB.get('icp')

const ProxyCompanyDomainActionModel = {}

ProxyCompanyDomainActionModel.model = sequelize.define(
    't_proxy_company_domain_action',
    parseModels({
        id: {
            type: DataTypes.INTEGER(11),
            allowNull: false,
            primaryKey: true,
            autoIncrement: true,
        },
        company_id: {
            type: DataTypes.INTEGER(50),
            allowNull: false,
        },
        operator: {
            type: DataTypes.STRING(255),
            allowNull: false,
            defaultValue: '',
        },
        company_name: {
            type: DataTypes.STRING(255),
            allowNull: false,
            defaultValue: '',
        },
        domain: {
            type: DataTypes.STRING(255),
            allowNull: false,
            defaultValue: '',
        },
        create_time: {
            type: DataTypes.INTEGER(11),
            allowNull: false,
        },
        update_time: {
            type: DataTypes.INTEGER(11),
            allowNull: false,
        },
        action_name: {
            type: DataTypes.STRING(255),
            allowNull: true,
        },
        remark: {
            type: DataTypes.STRING(255),
            allowNull: true,
        },
    }),
    {
        tableName: 't_proxy_company_domain_action',
    }
)
module.exports = ProxyCompanyDomainActionModel
