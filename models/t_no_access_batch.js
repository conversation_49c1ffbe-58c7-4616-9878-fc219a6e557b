/*
 * @Author: william.qian <EMAIL>
 * @Date: 2022-11-03 18:12:45
 * @LastEditors: william.qian <EMAIL>
 * @LastEditTime: 2022-11-03 18:14:41
 * @FilePath: /newicp/models/t_no_access_batch.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
/**
 *已备案未接入批次表
 */
const { parseModels } = require('../fns/parseModels')
const { DataTypes } = require('sequelize')
const DB = require('../libs/mysql')
const sequelize = DB.get('icp')

const UninsertBatchModel = {}
const BatchStatusEnum = {
    Pull: 0, //数据获取中，
    Parsing: 1, //数据解析中，
    Parsed: 2, //已解析，
    Sending: 3, //发送中，
    SendFinish: 4, //发送完成，
    Finish: 5, //已完成
}
UninsertBatchModel.BatchStatusEnum = BatchStatusEnum
UninsertBatchModel.model = sequelize.define(
    't_no_access_batch',
    parseModels({
        id: {
            type: DataTypes.INTEGER(11),
            primaryKey: true,
            autoIncrement: true,
        },
        name: {
            type: DataTypes.STRING(255),
            allowNull: true,
            defaultValue: '',
        },
        status: {
            type: DataTypes.INTEGER(4),
            allowNull: false,
            defaultValue: 0,
        },
        create_time: {
            type: DataTypes.INTEGER(11),
            allowNull: true,
            defaultValue: sequelize.NOW,
        },
        update_time: {
            type: DataTypes.INTEGER(11),
            allowNull: true,
            defaultValue: sequelize.NOW,
        },
        operater: {
            type: DataTypes.STRING(255),
            allowNull: false,
            defaultValue: 'System.Auto',
        },
    }),
    {
        tableName: 't_no_access_batch',
    }
)

module.exports = UninsertBatchModel
