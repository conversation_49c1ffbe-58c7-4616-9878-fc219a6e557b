const { DataTypes } = require('sequelize')
const {
    parseModels,
    upperCamelcase,
    parseNullArr,
} = require('../fns/parseModels')
const DB = require('../libs/mysql')
const sequelize = DB.get('icp')

const ICPModel = {}

const ICPStatusEnum = {
    Normal: 0, // 主体备案正常,
    Logout: 1, // 已注销,
    Logouting: 4, // 注销主体中,
    Icpchanging: 7, // 主体变更中,
    Mainchanging: 8, //主体变更中,
    Isdeleted: 20, //主体已删除"
}

ICPModel.ICPStatusEnum = ICPStatusEnum

ICPModel.model = sequelize.define(
    't_icp',
    parseModels({
        id: {
            type: DataTypes.INTEGER(11),
            allowNull: false,
            primaryKey: true,
            autoIncrement: true,
            field: 'id',
        },
        status: {
            type: DataTypes.INTEGER(11),
            allowNull: false,
            defaultValue: '0',
        },
        c_main_id: {
            type: DataTypes.INTEGER(11),
            allowNull: false,
            defaultValue: '-1',
        },
        company_id: {
            type: DataTypes.INTEGER(16),
            allowNull: false,
            defaultValue: '0',
        },
        channel: {
            type: DataTypes.INTEGER(11),
            allowNull: false,
            defaultValue: 1,
        },
        icp_main_no: {
            type: DataTypes.STRING(255),
            allowNull: false,
            defaultValue: '',
            unique: true,
        },
        area_id: {
            type: DataTypes.INTEGER(11),
            allowNull: true,
            defaultValue: '0',
        },
        organizer_type: {
            type: DataTypes.INTEGER(4),
            allowNull: false,
            defaultValue: '0',
        },
        organizer_name: {
            type: DataTypes.STRING(255),
            allowNull: false,
            defaultValue: '',
        },
        organizer_address: {
            type: DataTypes.STRING(255),
            allowNull: false,
            defaultValue: '',
        },
        organizer_license_type: {
            type: DataTypes.INTEGER(4),
            allowNull: false,
            defaultValue: '0',
        },
        organizer_license_id: {
            type: DataTypes.STRING(255),
            allowNull: false,
            defaultValue: '',
        },
        organizer_license_area: {
            type: DataTypes.STRING(255),
            allowNull: false,
            defaultValue: '',
        },
        pic_main_name: {
            type: DataTypes.STRING(255),
            allowNull: false,
            defaultValue: '',
        },
        pic_main_office_phone: {
            type: DataTypes.STRING(16),
            allowNull: true,
            defaultValue: '',
            comment: '主体负责人办公电话',
        },
        pic_main_office_phone_pre: {
            type: DataTypes.STRING(16),
            allowNull: true,
            defaultValue: '',
            comment: '主体负责人办公电话区号',
        },
        pic_main_mobile: {
            type: DataTypes.STRING(255),
            allowNull: false,
            defaultValue: '',
        },
        pic_main_email: {
            type: DataTypes.STRING(255),
            allowNull: false,
            defaultValue: '',
        },
        pic_main_qq: {
            type: DataTypes.STRING(255),
            allowNull: false,
            defaultValue: '',
        },
        pic_main_license_type: {
            type: DataTypes.INTEGER(4),
            allowNull: false,
            defaultValue: '0',
        },
        pic_main_license_id: {
            type: DataTypes.STRING(255),
            allowNull: false,
            defaultValue: '',
        },
        pic_main_license_date: {
            type: DataTypes.STRING(128),
            allowNull: false,
            defaultValue: '',
            comment: '主体负责人证件有效期',
        },
        organizer_license_picture: {
            type: DataTypes.STRING(1024),
            allowNull: false,
            defaultValue: '',
            set(val) {
                this.setDataValue(
                    upperCamelcase('organizer_license_picture'),
                    typeof val === 'object' ? val.join('|') : ''
                )
            },
            get() {
                const rawValue = this.getDataValue(
                    upperCamelcase('organizer_license_picture')
                )
                return parseNullArr(rawValue)
            },
        },
        organizer_residence_permit_picture: {
            type: DataTypes.STRING(1024),
            allowNull: false,
            defaultValue: '',
            set(val) {
                this.setDataValue(
                    upperCamelcase('organizer_residence_permit_picture'),
                    typeof val === 'object' ? val.join('|') : ''
                )
            },
            get() {
                const rawValue = this.getDataValue(
                    upperCamelcase('organizer_residence_permit_picture')
                )
                return parseNullArr(rawValue)
            },
        },
        pic_main_license_picture: {
            type: DataTypes.STRING(1024),
            allowNull: false,
            defaultValue: '',
            set(val) {
                this.setDataValue(
                    upperCamelcase('pic_main_license_picture'),
                    typeof val === 'object' ? val.join('|') : ''
                )
            },
            get() {
                const rawValue = this.getDataValue(
                    upperCamelcase('pic_main_license_picture')
                )
                return parseNullArr(rawValue)
            },
        },
        other_picture: {
            type: DataTypes.STRING(1024),
            allowNull: false,
            defaultValue: '',
            set(val) {
                this.setDataValue(
                    upperCamelcase('other_picture'),
                    typeof val === 'object' ? val.join('|') : ''
                )
            },
            get() {
                const rawValue = this.getDataValue(
                    upperCamelcase('other_picture')
                )
                return parseNullArr(rawValue)
            },
        },
        remark: {
            type: DataTypes.STRING(1024),
            allowNull: true,
            defaultValue: '',
        },
        create_time: {
            type: DataTypes.INTEGER(11),
            allowNull: true,
            defaultValue: 0,
        },
        update_time: {
            type: DataTypes.INTEGER(11),
            allowNull: true,
            defaultValue: 0,
        },
        is_deleted: {
            type: DataTypes.INTEGER(1),
            allowNull: false,
            defaultValue: 0,
        },
        unit_superior: {
            type: DataTypes.STRING(255),
            allowNull: true,
            defaultValue: '',
        },
        emergency_phone: {
            type: DataTypes.STRING(256),
            allowNull: true,
            defaultValue: '',
        },
        is_synced_cid: {
            type: DataTypes.INTEGER(11),
            allowNull: false,
            defaultValue: 0,
        },
        unit_certificate_exp_date: {
            type: DataTypes.STRING(255),
            allowNull: true,
            defaultValue: '',
        },
    }),
    {
        tableName: 't_icp',
    }
)
ICPModel.MainMap = {
    OrderNo: '订单号',
    Type: '订单类型',
    Status: '订单状态',
    CompanyId: '公司Id',
    Channel: '渠道Id',
    AreaId: '地域Id',
    ICPMainNo: '主体备案号',
    OrganizerType: '主办单位证件类型',
    OrganizerName: '主办单位名称',
    OrganizerAddress: '主办单位地址',
    OrganizerLicenseType: '主办单位证件类型',
    OrganizerLicenseId: '主办单位证件号',
    OrganizerLicensePicture: '主办单位证件图片',
    UnitCertificateExpDate: '主办单位证件有效期',
    OrganizerLicenseArea: '主办单位证件地址',
    OrganizerResidencePermitPicture: '主办单位暂住证/居住证扫描件',
    PICMainName: '主体负责人姓名',
    PICMainOfficePhone: '主体负责人座机',
    PICMainOfficePhonePre: '主体负责人座机前缀',
    PICMainMobile: '主体负责人手机号',
    PICMainEmail: '主体负责人邮箱',
    PICMainQQ: '主体负责人QQ',
    PICMainLicenseType: '主体负责人证件类型',
    PICMainLicenseId: '主体负责人证件号',
    PICMainLicensePicture: '主体负责人证件图片',
    PICMainLicenseDate: '主体负责人证件有效期',
    OtherPicture: '其他图片',
    Remark: '备注',
    ICPMainPassword: '主体备案密码，转接入或注销使用',
    SubmitReexamineType: '复审类型',
    Error: '错误字段',
    IsDeleted: '是否被删除',
    Auditor: '审核人，内部人员的邮箱',
    RejectReason: '退回原因',
    LogoutReason: '注销原因',
    Domain: '域名',
    ICPWebNo: '网站备案号',
    ICPWebPassword: '网站备案密码',
    IP: 'IP地址',
    CurtainPicture: '幕布照片',
    CurtainStatus: '幕布采集状态',
    PICWebLicensePicture: '网站负责人证件',
    AuthVerificationPicture: '真实性核验单照片',
    UnitSuperior: '投资者或上级单位主管名称',
    EmergencyPhone: '紧急联系电话',
    PromiseLetterPicture: '承诺书图片',
}

module.exports = ICPModel
