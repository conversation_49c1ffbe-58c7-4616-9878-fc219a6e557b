const {
    parseModels,
    upperCamelcase,
    parseNullArr,
} = require('../fns/parseModels')
const { DataTypes } = require('sequelize')
const DB = require('../libs/mysql')
const sequelize = DB.get('icp')

const IPWhiteListModel = {}

IPWhiteListModel.model = sequelize.define(
    't_ip_white_list',
    parseModels({
        id: {
            type: DataTypes.INTEGER(11),
            allowNull: false,
            primaryKey: true,
            autoIncrement: true,
        },
        company_id: {
            type: DataTypes.INTEGER(255),
            allowNull: false,
            unique: true,
        },
        ip: {
            type: DataTypes.STRING(1024),
            allowNull: false,
            defaultValue: '',
            set(val) {
                this.setDataValue(
                    upperCamelcase('ip'),
                    typeof val === 'object' ? val.join('|') : ''
                )
            },
            get() {
                const rawValue = this.getDataValue(upperCamelcase('ip'))
                return parseNullArr(rawValue)
            },
        },
        operator: {
            type: DataTypes.STRING(255),
            allowNull: false,
            defaultValue: '',
        },
        create_time: {
            type: DataTypes.INTEGER(11),
            allowNull: true,
            defaultValue: sequelize.NOW,
        },
        update_time: {
            type: DataTypes.INTEGER(11),
            allowNull: true,
            defaultValue: sequelize.NOW,
        },
    }),
    {
        tableName: 't_ip_white_list',
    }
)
module.exports = IPWhiteListModel
