/*
 * @Author: william.qian <EMAIL>
 * @Date: 2022-08-22 16:16:03
 * @LastEditors: william.qian <EMAIL>
 * @LastEditTime: 2023-05-18 16:47:18
 * @FilePath: /newicp/models/t_company_block_list.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
const { DataTypes } = require('sequelize')
const { parseModels } = require('../fns/parseModels')
const DB = require('../libs/mysql')
const sequelize = DB.get('icp')

const CompanyBlockListModel = {}

CompanyBlockListModel.model = sequelize.define(
    't_company_block_list',
    parseModels({
        id: {
            type: DataTypes.INTEGER(11),
            allowNull: false,
            primaryKey: true,
            autoIncrement: true,
        },
        company_id: {
            type: DataTypes.STRING(255),
            allowNull: false,
            defaultValue: '',
            unique: true,
        },
        operator: {
            type: DataTypes.STRING(255),
            allowNull: false,
            defaultValue: '',
        },
        create_time: {
            type: DataTypes.INTEGER(11),
            allowNull: true,
            defaultValue: sequelize.NOW,
        },
        update_time: {
            type: DataTypes.INTEGER(11),
            allowNull: true,
            defaultValue: sequelize.NOW,
        },
        remark: {
            type: DataTypes.STRING(255),
            allowNull: true,
        },
    }),
    {
        tableName: 't_company_block_list',
    }
)
module.exports = CompanyBlockListModel
