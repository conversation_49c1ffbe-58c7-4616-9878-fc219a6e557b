const { DataTypes } = require('sequelize')
const {
    parseModels,
    upperCamelcase,
    parseNullObject,
} = require('../fns/parseModels')
const DB = require('../libs/mysql')
const sequelize = DB.get('icp')

const UpdateLogModel = {}

const UpdateLogTypeEnum = {
    CRON_AUTSYNC: 1, // 由Cron AutoSync 全量自动数据拉取功能更新
    CRON_DOWNLOADMAINPICTUREFROMHENGAN: 2, //由Cron DownloadMainPictureFromHeangan 主体图片下载功能更新更新
    API_ICPSYNCFROMHENGAN: 3, //由API全量文字差异项功能更新
    API_ICPPICTURESYNCFROMHENGAN: 4, //由API全量图片下载功能更新
    CRON_DOWNLOADWEBPICTUREFROMHENGAN: 5, //由Cron DownloadWebPictureFromHeangan 网站图片下载功能更新更新
}
UpdateLogModel.UpdateLogTypeEnum = UpdateLogTypeEnum
UpdateLogModel.model = sequelize.define(
    't_update_log',
    parseModels({
        id: {
            type: DataTypes.INTEGER(11),
            allowNull: false,
            primaryKey: true,
            autoIncrement: true,
        },
        main_id: {
            type: DataTypes.INTEGER(16),
            allowNull: false,
            defaultValue: '0',
        },
        update_content: {
            type: DataTypes.TEXT,
            allowNull: false,
            set(val) {
                this.setDataValue(
                    upperCamelcase('update_content'),
                    JSON.stringify(val)
                )
            },
            get() {
                const rawValue = this.getDataValue(
                    upperCamelcase('update_content')
                )
                return parseNullObject(rawValue, {})
            },
        },
        error_info: {
            type: DataTypes.TEXT,
            allowNull: false,
            set(val) {
                this.setDataValue(
                    upperCamelcase('error_info'),
                    JSON.stringify(val)
                )
            },
            get() {
                const rawValue = this.getDataValue(upperCamelcase('error_info'))
                return parseNullObject(rawValue, {})
            },
        },
        create_time: {
            type: DataTypes.INTEGER(11),
            allowNull: false,
            defaultValue: '0',
        },
        operator: {
            type: DataTypes.STRING(255),
            allowNull: true,
            defaultValue: 'admin',
        },
        type: {
            type: DataTypes.INTEGER(11),
            allowNull: true,
        },
    }),
    {
        tableName: 't_update_log',
    }
)
module.exports = UpdateLogModel
