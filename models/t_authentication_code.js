/*
 * @Author: william.qian <EMAIL>
 * @Date: 2022-08-22 16:16:03
 * @LastEditors: william.qian <EMAIL>
 * @LastEditTime: 2023-05-18 16:47:40
 * @FilePath: /newicp/models/t_authentication_code.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
const { DataTypes } = require('sequelize')
const { parseModels, parseNullArr } = require('../fns/parseModels')
const DB = require('../libs/mysql')
const sequelize = DB.get('icp')

const AuthenticationCodeModel = {}

AuthenticationCodeModel.model = sequelize.define(
    't_authentication_code',
    parseModels({
        id: {
            type: DataTypes.INTEGER(11),
            allowNull: false,
            primaryKey: true,
            autoIncrement: true,
        },
        company_id: {
            type: DataTypes.INTEGER(255),
            allowNull: false,
        },
        ip: {
            type: DataTypes.STRING(40),
            allowNull: false,
            defaultValue: '',
        },
        operator: {
            type: DataTypes.STRING(255),
            allowNull: true,
            defaultValue: '',
        },
        code: {
            type: DataTypes.STRING(255),
            allowNull: false,
            defaultValue: '',
        },
        target_email: {
            type: DataTypes.STRING(255),
            allowNull: false,
            defaultValue: '',
        },
        target_company_id: {
            type: DataTypes.INTEGER(11),
            allowNull: true,
            defaultValue: sequelize.NOW,
        },
        create_time: {
            type: DataTypes.INTEGER(11),
            allowNull: true,
            defaultValue: sequelize.NOW,
        },
        update_time: {
            type: DataTypes.INTEGER(11),
            allowNull: false,
        },
        is_block: {
            type: DataTypes.INTEGER(11),
            allowNull: false,
            defaultValue: '0',
        },
    }),
    {
        tableName: 't_authentication_code',
    }
)
module.exports = AuthenticationCodeModel
