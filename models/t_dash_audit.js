const { parseModels } = require('../fns/parseModels')
const { DataTypes, literal } = require('sequelize')
const DB = require('../libs/mysql')
const sequelize = DB.get('icp')

const DashAuditModel = {}

DashAuditModel.model = sequelize.define(
    't_dash_audit',
    parseModels({
        id: {
            type: DataTypes.INTEGER(11),
            primaryKey: true,
            autoIncrement: true,
            comment: '公司记录Id',
        },
        operator: {
            type: DataTypes.STRING(255),
            allowNull: false,
            comment: '操作者,正常的审核员与总计ALL',
        },
        reject_count: {
            type: DataTypes.INTEGER(11),
            allowNull: false,
            comment: '驳回次数',
        },
        resolve_count: {
            type: DataTypes.INTEGER(11),
            allowNull: false,
            comment: '通过次数',
        },
        date: {
            type: DataTypes.DATE,
            allowNull: false,
            defaultValue: literal('CURRENT_TIMESTAMP'),
        },
    }),
    {
        tableName: 't_dash_audit',
    }
)

module.exports = DashAuditModel
