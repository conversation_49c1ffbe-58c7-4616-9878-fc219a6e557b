const {
    parseModels,
    upperCamelcase,
    parseNullArr,
    parseNullObject,
} = require('../fns/parseModels')
const { DataTypes } = require('sequelize')
const DB = require('../libs/mysql')
const sequelize = DB.get('icp')

const CompanyInfoChecRecordModel = {}

CompanyInfoChecRecordModel.model = sequelize.define(
    't_company_info_check_record',
    parseModels({
        id: {
            autoIncrement: true,
            type: DataTypes.INTEGER.UNSIGNED,
            allowNull: false,
            primaryKey: true,
        },
        batch_id: {
            type: DataTypes.INTEGER,
            allowNull: false,
        },
        organizer_type: {
            type: DataTypes.INTEGER,
            allowNull: false,
        },
        organizer_name: {
            type: DataTypes.STRING(256),
            allowNull: true,
        },
        organizer_license_area: {
            type: DataTypes.STRING(1024),
            allowNull: false,
            defaultValue: '',
        },
        pic_main_name: {
            type: DataTypes.STRING(256),
            allowNull: true,
        },
        organizer_license_id: {
            type: DataTypes.STRING(256),
            allowNull: false,
            defaultValue: '',
        },
        pic_main_license_id: {
            type: DataTypes.STRING(256),
            allowNull: false,
            defaultValue: '',
        },
        create_time: {
            type: DataTypes.INTEGER,
            allowNull: false,
        },
        update_time: {
            type: DataTypes.INTEGER,
            allowNull: false,
        },
        status: {
            type: DataTypes.INTEGER,
            allowNull: false,
            defaultValue: 0,
        },
        check_result: {
            type: DataTypes.INTEGER,
            allowNull: false,
            defaultValue: 0,
        },
        message: {
            type: DataTypes.TEXT,
            allowNull: true,
        },
        remark: {
            type: DataTypes.STRING(1024),
            allowNull: true,
        },
        return_object: {
            type: DataTypes.STRING(8048),
            allowNull: true,
            set(val) {
                this.setDataValue(
                    upperCamelcase('return_object'),
                    JSON.stringify(val)
                )
            },
            get() {
                const rawValue = this.getDataValue(
                    upperCamelcase('return_object')
                )
                return parseNullObject(rawValue, [])
            },
        },
    }),
    {
        tableName: 't_company_info_check_record',
    }
)
module.exports = CompanyInfoChecRecordModel
