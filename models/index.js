'use strict'
/**
 * 1. 提前加载所有model
 * 2. 建立依赖关系
 * 3. 暴露model  外部可直接 引用models模块后 解构出来 需要的model
 * 例如： const { ICPModel } = require('../../models')
 * 4.新增model 需要在此处引用后 并暴露出去
 * 5.此处可以将枚举也暴露出去，后续 也只需要通过 const { ICPStatusEnum } = require('../../models') 获取即可
 */
//配置
const { model: ConfigModel } = require('./t_config')
// 备案
const { model: ICPModel, ICPStatusEnum } = require('./t_icp')
const { model: ICPWebModel, WebStatusEnum } = require('./t_web')
const { model: ICPWebTwoLineModel } = require('./t_web_twoline')
// 备案订单
const {
    model: OrderModel,
    OrderTypeEnum,
    OrderStatusEnum,
    CurtainStatusEnum,
} = require('./t_order')
const { model: OrderWebModel } = require('./t_order_web')
const { model: OrderWebTwoLineModel } = require('./t_order_web_twoline')
const { model: OrderHistoryModel } = require('./t_order_history')
const { model: OrderNoteModel } = require('./t_order_note')
const { model: OrderPictureModel } = require('./t_order_picture')
// 备案过程中信息
const { model: PictureModel } = require('./t_picture')
const { model: CurtainModel } = require('./t_curtain')
const { model: LogModel } = require('./t_log')

const { model: AuthenticationCodeModel } = require('./t_authentication_code')
const { model: CompanyBlockListModel } = require('./t_company_block_list')
const {
    model: CompanyInfoCheckBatchModel,
} = require('./t_company_info_check_batch')
// 控制面板审核
const { model: DashAuditModel } = require('./t_dash_audit')
// 白名单
const { model: CompanyWhiteListModel } = require('./t_company_white_list')
const { model: DomainWhiteListModel } = require('./t_domain_white_list')
const { model: IPWhiteListModel } = require('./t_ip_white_list')

//通知
const { model: EmailNoticeModel } = require('./t_email_notice')
const { model: NotifyBatchModel } = require('./t_notify_batch')
const { model: CompanyNotifyModel } = require('./t_notify_companyInfo')
const { model: NotifyModel } = require('./t_notify')
//已备案未接入通知
const { model: UninsertBatchModel, BatchStatusEnum: UninsertBatchStatusEnum } = require('./t_no_access_batch')
const { model: NoAccessNotifyModel } = require('./t_no_access_notify')
const { model: NoAccessBUNotifyModel } = require('./t_no_access_bu_notify')
const { model: UninsertBatchRecordModel } = require('./t_no_access_record')
const {
    model: ProxyCompanyDomainActionModel,
} = require('./t_proxy_company_domain_action')
const { model: ProxyCompanyListModel } = require('./t_proxy_company_list')
const {
    model: ProxyCompanyWhiteDomainModel,
} = require('./t_proxy_company_white_domain')
const { model: TokenModel } = require('./t_token')
const { model: UpdateLogModel, UpdateLogTypeEnum } = require('./t_update_log')
const {
    model: VerifyLogModel,
    VerifyLogTypeEnum,
    VerifyLogTypeCnEnum,
} = require('./t_verify_log')

// 封禁功能
const {
    model: BulkBlockBatchModel,
    BatchStatusEnum,
} = require('./t_bulk_block_domain_batch')
const {
    model: BulkBlockRecordModel,
    BlockRecordStatusEnum,
} = require('./t_bulk_block_domain_record')

// 跨境报备
const {
    model: CrossBorderApplyModel,
    ApplyStatusEnum,
} = require('./t_cross_border_apply')
const {
    model: CrossBorderAuditLogModel,
} = require('./t_cross_broder_audit_log')
const {
    model: ClearOrderBatchModel,
    ClearBatchStatusEnum,
} = require('../models/t_clear_order_batch')

const {
    model: ClearOrderRecordModel,
    ClearRecordStatusEnum,
} = require('../models/t_clear_order_record')

ICPModel.hasMany(ICPWebModel, {
    foreignKey: 'MainId',
    as: 'Website',
    sourceKey: 'Id',
})
ICPWebModel.belongsTo(ICPModel, {
    foreignKey: 'MainId',
    as: 'ICP',
    targetKey: 'Id',
})

OrderModel.hasMany(OrderWebModel, {
    foreignKey: 'OrderNo',
    as: 'Website',
    sourceKey: 'OrderNo',
})

OrderWebModel.belongsTo(OrderModel, {
    foreignKey: 'OrderNo',
    as: 'Order',
    targetKey: 'OrderNo',
})

NotifyBatchModel.hasMany(CompanyNotifyModel, {
    foreignKey: 'BatchId',
    as: 'Record',
    sourceKey: 'Id',
})

CompanyNotifyModel.hasMany(NotifyModel, {
    foreignKey: 'CompanyNotifyId',
    as: 'Notify',
    sourceKey: 'Id',
})

// 网站信息检查
const {
    model: WebCheckBatchModel,
    WebCheckBatchStatusEnum,
} = require('./t_web_check_batch')
const { model: WebCheckRecordModel } = require('./t_web_check_record')

// orderWebTwoLine 表 和OrderWeb表的映射关系
OrderWebTwoLineModel.belongsTo(OrderWebModel, {
    foreignKey: 'OrderWebId',
    as: 'OrderWeb',
    targetKey: 'Id',
})
OrderWebModel.hasOne(OrderWebTwoLineModel, {
    foreignKey: 'OrderWebId',
    as: 'TwoLineIPs',
    sourceKey: 'Id',
})

// icpWebTwoLine 表 与 icpWeb表的映射关系
ICPWebTwoLineModel.belongsTo(ICPWebModel, {
    foreignKey: 'ICPWebId',
    as: 'ICPWeb',
    targetKey: 'Id',
})

ICPWebModel.hasOne(ICPWebTwoLineModel, {
    foreignKey: 'ICPWebId',
    as: 'TwoLineIPs',
    sourceKey: 'Id',
})

// CrossBorderApplyModel.sync({ force: false, alter: true})

module.exports = {
    ICPModel,
    ICPStatusEnum,
    ICPWebModel,
    ICPWebTwoLineModel,
    BulkBlockBatchModel,
    BatchStatusEnum,
    BulkBlockRecordModel,
    BlockRecordStatusEnum,
    WebStatusEnum,
    OrderModel,
    CurtainStatusEnum,
    OrderTypeEnum,
    OrderStatusEnum,
    OrderWebModel,
    OrderWebTwoLineModel,
    OrderHistoryModel,
    OrderNoteModel,
    OrderPictureModel,
    PictureModel,
    CurtainModel,
    LogModel,
    UpdateLogModel,
    UpdateLogTypeEnum,
    VerifyLogModel,
    VerifyLogTypeEnum,
    VerifyLogTypeCnEnum,
    AuthenticationCodeModel,
    CompanyInfoCheckBatchModel,
    ConfigModel,
    CompanyBlockListModel,
    CompanyWhiteListModel,
    DashAuditModel,
    EmailNoticeModel,
    DomainWhiteListModel,
    IPWhiteListModel,
    NotifyBatchModel,
    CompanyNotifyModel,
    NotifyModel,
    ProxyCompanyDomainActionModel,
    ProxyCompanyListModel,
    ProxyCompanyWhiteDomainModel,
    TokenModel,
    CrossBorderApplyModel,
    ApplyStatusEnum,
    CrossBorderAuditLogModel,
    UninsertBatchModel,
    UninsertBatchStatusEnum,
    NoAccessNotifyModel,
    NoAccessBUNotifyModel,
    UninsertBatchRecordModel,
    WebCheckBatchModel,
    WebCheckBatchStatusEnum,
    WebCheckRecordModel,
    ClearOrderBatchModel,
    ClearBatchStatusEnum,
    ClearOrderRecordModel,
    ClearRecordStatusEnum,
}
