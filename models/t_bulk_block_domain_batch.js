/*
 * @Author: william.qian <EMAIL>
 * @Date: 2022-10-31 16:45:35
 * @LastEditors: william.qian <EMAIL>
 * @LastEditTime: 2022-11-10 16:43:39
 * @FilePath: /newicp/models/t_bulk_block_domain_batch.js
 * @Description: 批量封禁的批次表
 */
const { parseModels } = require('../fns/parseModels')
const { DataTypes } = require('sequelize')
const DB = require('../libs/mysql')
const sequelize = DB.get('icp')

const BulkBlockBatchModel = {}

const BatchStatusEnum = {
    Init: 0, // 上传完成
    Blocking: 1, // 封禁中
    Finish: 2, //已完成
}

BulkBlockBatchModel.BatchStatusEnum = BatchStatusEnum
BulkBlockBatchModel.model = sequelize.define(
    't_bulk_block_domain_batch',
    parseModels({
        id: {
            type: DataTypes.INTEGER(11),
            primaryKey: true,
            autoIncrement: true,
            comment: '批次Id',
        },
        description: {
            type: DataTypes.STRING(255),
            allowNull: false,
            defaultValue: '',
            comment: '批次描述：《XXX.XX的封禁批次》',
        },
        status: {
            type: DataTypes.INTEGER(4),
            allowNull: false,
            values: Object.values(BatchStatusEnum),
            defaultValue: '0',
            comment: '批次状态',
        },
        create_time: {
            type: DataTypes.INTEGER(11),
            allowNull: true,
            defaultValue: sequelize.NOW,
        },
        update_time: {
            type: DataTypes.INTEGER(11),
            allowNull: true,
            defaultValue: sequelize.NOW,
        },
    }),
    {
        tableName: 't_bulk_block_domain_batch',
    }
)

module.exports = BulkBlockBatchModel
