const { parseModels } = require('../fns/parseModels')
const { DataTypes } = require('sequelize')
const DB = require('../libs/mysql')
const sequelize = DB.get('icp')

const ProxyCompanyWhiteDomainModel = {}

ProxyCompanyWhiteDomainModel.model = sequelize.define(
    't_proxy_company_white_domain',
    parseModels({
        id: {
            type: DataTypes.INTEGER(11),
            allowNull: false,
            primaryKey: true,
            autoIncrement: true,
        },
        company_id: {
            type: DataTypes.INTEGER(50),
            allowNull: false,
        },
        operator: {
            type: DataTypes.STRING(255),
            allowNull: false,
            defaultValue: '',
        },
        company_name: {
            type: DataTypes.STRING(255),
            allowNull: false,
            defaultValue: '',
        },
        domain: {
            type: DataTypes.STRING(255),
            allowNull: false,
            unique: true,
            defaultValue: '',
        },
        create_time: {
            type: DataTypes.INTEGER(11),
            allowNull: false,
        },
        update_time: {
            type: DataTypes.INTEGER(11),
            allowNull: false,
        },
        remark: {
            type: DataTypes.STRING(255),
            allowNull: true,
        },
        icp_web_no: {
            type: DataTypes.STRING(255),
            allowNull: true,
        },
    }),
    {
        tableName: 't_proxy_company_white_domain',
    }
)
module.exports = ProxyCompanyWhiteDomainModel
