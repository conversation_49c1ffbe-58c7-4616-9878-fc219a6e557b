const {
    parseModels,
    upperCamelcase,
    parseNullArr,
} = require('../fns/parseModels')
const { DataTypes } = require('sequelize')
const DB = require('../libs/mysql')
const sequelize = DB.get('icp')

const OrderPictureModel = {}

OrderPictureModel.model = sequelize.define(
    't_order_picture',
    parseModels({
        Id: {
            type: DataTypes.INTEGER(11),
            allowNull: false,
            primaryKey: true,
            autoIncrement: true,
        },
        company_id: {
            type: DataTypes.INTEGER(11),
            allowNull: false,
            defaultValue: '0',
        },
        license_id: {
            type: DataTypes.STRING(255),
            allowNull: false,
            defaultValue: '',
        },
        license_type: {
            type: DataTypes.STRING(1024),
            allowNull: false,
            defaultValue: '',
        },
        order_no: {
            type: DataTypes.STRING(255),
            allowNull: false,
            defaultValue: '',
            unique: true,
        },
        liveness_uuid: {
            type: DataTypes.STRING(1024),
            allowNull: false,
            defaultValue: '',
        },
        picture_name: {
            type: DataTypes.TEXT,
            allowNull: false,
            defaultValue: '',
            set(val) {
                this.setDataValue(
                    upperCamelcase('picture_name'),
                    typeof val === 'object' ? val.join('|') : ''
                )
            },
            get() {
                const rawValue = this.getDataValue(
                    upperCamelcase('picture_name')
                )
                return parseNullArr(rawValue)
            },
        },
        license_picture: {
            type: DataTypes.TEXT,
            allowNull: false,
            defaultValue: '',
            set(val) {
                this.setDataValue(
                    upperCamelcase('license_picture'),
                    typeof val === 'object' ? val.join('|') : ''
                )
            },
            get() {
                const rawValue = this.getDataValue(
                    upperCamelcase('license_picture')
                )
                return parseNullArr(rawValue)
            },
        },
        uuid: {
            type: DataTypes.STRING(255),
            allowNull: true,
        },
        create_time: {
            type: DataTypes.INTEGER(11),
            allowNull: false,
            defaultValue: '0',
        },
    }),
    {
        tableName: 't_order_picture',
    }
)
module.exports = OrderPictureModel
