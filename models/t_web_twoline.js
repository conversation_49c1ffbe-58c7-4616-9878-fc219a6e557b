/*
 * @Date: 2023-03-08 14:00:56
 * @LastEditors: li<PERSON><PERSON>en <EMAIL>
 * @LastEditTime: 2023-03-20 16:44:10
 * @FilePath: /newicp/models/t_web_twoline.js
 */
const {
    parseModels,
    upperCamelcase,
    parseNullArr,
} = require('../fns/parseModels')
const { DataTypes } = require('sequelize')
const DB = require('../libs/mysql')
const sequelize = DB.get('icp')

const WebTwoLineModel = {}

WebTwoLineModel.model = sequelize.define(
    't_web_twoline',
    parseModels({
        id: {
            type: DataTypes.INTEGER(11),
            allowNull: false,
            primaryKey: true,
            autoIncrement: true,
        },
        icp_web_id: {
            type: DataTypes.STRING(255),
            allowNull: false,
        },
        ip: {
            type: DataTypes.STRING(1024),
            allowNull: false,
            defaultValue: '',
            set(val) {
                this.setDataValue(
                    upperCamelcase('ip'),
                    typeof val === 'object' ? val.join('|') : ''
                )
            },
            get() {
                const rawValue = this.getDataValue(upperCamelcase('ip'))
                return parseNullArr(rawValue)
            },
        },
    }),
    {
        indexes: [
            {
                unique: true,
                fields: ['icp_web_id'],
            },
        ],
        tableName: 't_web_twoline',
    }
)
module.exports = WebTwoLineModel
