/*
 * @Author: william.qian <EMAIL>
 * @Date: 2022-11-03 18:12:45
 * @LastEditors: william.qian <EMAIL>
 * @LastEditTime: 2022-11-03 18:51:08
 * @FilePath: /newicp/models/t_no_access_notify.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
/**
 *已备案未接入通知信息表
 */
const { parseModels } = require('../fns/parseModels')
const { DataTypes } = require('sequelize')
const DB = require('../libs/mysql')
const Sequelize = DB.get('icp')

const SendStatusEnum = {
    New: 0, //待发送
    Sending: 1, //发送中
    Sendfinish: 2, //已经接收
    Sendfailed: 3, //发送失败
    Recivefailed: 4, //接收失败
    Timeout: 5, //超时未响应
    Trytosending: 6, //尝试发送中
    Forbidden: 7, //禁止发送
}

const NotifyTypeEnum = {
    Phone: 0, //短信
    Email: 1, //邮件
}

const NoAccessNotifyModel = {}
NoAccessNotifyModel.SendStatusEnum = SendStatusEnum
NoAccessNotifyModel.NotifyTypeEnum = NotifyTypeEnum
NoAccessNotifyModel.model = Sequelize.define(
    't_no_access_notify',
    parseModels({
        id: {
            type: DataTypes.INTEGER(11),
            primaryKey: true,
            autoIncrement: true,
            comment: '通知记录Id',
        },
        batch_org_id: {
            type: DataTypes.INTEGER(11),
            allowNull: false,
            defaultValue: 0,
            comment: '此通知人归属的批次项目Id',
        },
        mobile: {
            type: DataTypes.STRING(20),
            allowNull: true,
            comment: '手机号,',
        },
        email: {
            type: DataTypes.STRING(256),
            allowNull: true,
            comment: '邮箱',
        },
        sms_status: {
            type: DataTypes.INTEGER(2),
            allowNull: true,
            defaultValue: SendStatusEnum.NEW,
            values: Object.values(SendStatusEnum),
            comment:
                '短信发送状态,0:待发送，1:发送中，2:发送成功, 3:发送失败，4:接收失败，5:尝试发送,6:禁止发送，7:超时未响应',
        },
        email_status: {
            type: DataTypes.INTEGER(2),
            allowNull: true,
            defaultValue: SendStatusEnum.NEW,
            values: Object.values(SendStatusEnum),
            comment:
                '邮件发送状态,0:待发送，1:发送中，2:发送成功, 3:发送失败，4:接收失败，5:尝试发送,6:禁止发送，7:超时未响应',
        },
        sms_task_id: {
            type: DataTypes.STRING(64),
            allowNull: true,
            defaultValue: '',
            comment: '短信发送任务id',
        },
        email_task_id: {
            type: DataTypes.STRING(64),
            allowNull: true,
            defaultValue: '',
            comment: '邮件发送任务id',
        },
        sms_retry_time: {
            type: DataTypes.INTEGER(11),
            allowNull: true,
            defaultValue: 0,
            comment: '短信发送重试次数',
        },
        email_retry_time: {
            type: DataTypes.INTEGER(11),
            allowNull: true,
            defaultValue: 0,
            comment: '邮件发送重试次数',
        },
        create_time: {
            type: DataTypes.INTEGER(11),
            allowNull: true,
            defaultValue: Sequelize.NOW,
        },
        update_time: {
            type: DataTypes.INTEGER(11),
            allowNull: true,
            defaultValue: Sequelize.NOW,
        },
    }),
    {
        tableName: 't_no_access_notify',
    }
)

module.exports = NoAccessNotifyModel
