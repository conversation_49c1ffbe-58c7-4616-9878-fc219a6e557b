const { parseModels } = require('../fns/parseModels')
const { DataTypes } = require('sequelize')
const DB = require('../libs/mysql')
const sequelize = DB.get('icp')

const NotifyBatchModel = {}

//初始化包含4中类型
const TypeEnum = {
    Expired: 0, // '过期域名通知',
    CompanyInfo: 1, // '企业四要素不一致通知', // 准备激活
    BeianInfo: 2, //'域名实名与备案不一致通知', // 已激活
    Unresource: 3, // 无资源有接入通知,
}

const BatchStatusEnum = {
    PUll: 0, //数据获取中，
    Parsing: 1, //数据解析中，
    Parsed: 2, //已解析，
    Sending: 3, //发送中，
    SendFinish: 4, //发送完成，
    Finish: 5, //已完成
}
NotifyBatchModel.BatchStatusEnum = BatchStatusEnum
NotifyBatchModel.TypeEnum = TypeEnum
NotifyBatchModel.model = sequelize.define(
    't_notify_batch',
    parseModels({
        id: {
            type: DataTypes.INTEGER(11),
            primaryKey: true,
            autoIncrement: true,
        },
        type: {
            type: DataTypes.INTEGER(4),
            allowNull: false,
            defaultValue: TypeEnum.Unresource,
        },
        remark: {
            type: DataTypes.STRING(255),
            allowNull: false,
            defaultValue: '',
            unique: true,
        },
        status: {
            type: DataTypes.INTEGER(4),
            allowNull: false,
            defaultValue: '0',
        },
        notify_day: {
            type: DataTypes.INTEGER(4),
            allowNull: false,
            defaultValue: '0',
            comment: '通知的截止日',
        },
        create_time: {
            type: DataTypes.INTEGER(11),
            allowNull: true,
            defaultValue: sequelize.NOW,
        },
        update_time: {
            type: DataTypes.INTEGER(11),
            allowNull: true,
            defaultValue: sequelize.NOW,
        },
    }),
    {
        tableName: 't_notify_batch',
    }
)

module.exports = NotifyBatchModel
