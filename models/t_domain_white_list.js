/*
 * @Date: 2022-10-17 15:59:08
 * @LastEditors: l<PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2022-11-01 17:43:43
 * @FilePath: /newicp/models/t_domain_white_list.js
 */
const { DataTypes } = require('sequelize')
const {
    parseModels,
    upperCamelcase,
    parseNullArr,
} = require('../fns/parseModels')
const DB = require('../libs/mysql')
const sequelize = DB.get('icp')

const DomainWhiteListModel = {}

DomainWhiteListModel.model = sequelize.define(
    't_domain_white_list',
    parseModels({
        id: {
            type: DataTypes.INTEGER(11),
            allowNull: false,
            primaryKey: true,
            autoIncrement: true,
            field: 'id',
        },
        domain: {
            type: DataTypes.STRING(1024),
            allowNull: false,
            defaultValue: '',
            unique: true,
            comment: '域名',
        },
        operator: {
            type: DataTypes.STRING(16),
            allowNull: true,
            defaultValue: '',
            comment: '操作人',
        },
        is_deleted: {
            type: DataTypes.INTEGER(1),
            allowNull: false,
            defaultValue: 0,
            comment: '是否被删除',
        },
        sealed: {
            type: DataTypes.INTEGER(1),
            allowNull: false,
            defaultValue: 0,
            comment: '是否封禁',
        },
        unseal_times: {
            type: DataTypes.INTEGER(11),
            allowNull: false,
            defaultValue: 0,
            comment: '解封次数',
        },
        connected: {
            type: DataTypes.INTEGER(1),
            allowNull: false,
            defaultValue: 0,
            comment: '是否接入',
        },
        remark: {
            type: DataTypes.STRING(255),
            allowNull: true,
            defaultValue: '',
            comment: '备注',
        },
        expired_time: {
            type: DataTypes.INTEGER(11),
            allowNull: false,
            defaultValue: '0',
            comment: '过期时间',
        },
        create_time: {
            type: DataTypes.INTEGER(11),
            allowNull: true,
            defaultValue: '0',
            comment: '创建时间',
        },
        update_time: {
            type: DataTypes.INTEGER(11),
            allowNull: true,
            defaultValue: '0',
            comment: '修改时间',
        },
    }),
    {
        tableName: 't_domain_white_list',
    }
)
module.exports = DomainWhiteListModel
