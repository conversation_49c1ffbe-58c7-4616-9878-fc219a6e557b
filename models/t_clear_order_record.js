/*
 * @Date: 2023-05-16 17:06:46
 * @LastEditors: l<PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2023-05-29 14:03:28
 * @FilePath: /newicp/models/t_clear_order_record.js
 */
const { DataTypes } = require('sequelize')
const { parseModels } = require('../fns/parseModels')
const DB = require('../libs/mysql')
const sequelize = DB.get('icp')

const ClearRecordStatusEnum = {
    awaitClear: 0, // 待清理
    systemDeleted: 1, // 系统已删除
    deleted: 2, // 订单已删除
    updated: 3, // 用户已更新
}

const ClearOrderRecordModel = {}
ClearOrderRecordModel.ClearRecordStatusEnum = ClearRecordStatusEnum
ClearOrderRecordModel.model = sequelize.define(
    't_clear_order_record',
    parseModels({
        id: {
            type: DataTypes.INTEGER(11),
            allowNull: false,
            primaryKey: true,
            autoIncrement: true,
        },
        batch_id: {
            type: DataTypes.INTEGER(11),
            allowNull: false,
        },
        company_id: {
            type: DataTypes.INTEGER(11),
            allowNull: false,
            defaultValue: 0,
        },
        order_no: {
            type: DataTypes.STRING(255),
            allowNull: false,
            defaultValue: '',
        },
        status: {
            type: DataTypes.INTEGER(1),
            allowNull: false,
            defaultValue: ClearRecordStatusEnum.awaitClear,
        },
        order_update_time: {
            type: DataTypes.INTEGER(11),
            allowNull: false,
        },
        create_time: {
            type: DataTypes.INTEGER(11),
            allowNull: true,
            defaultValue: 0,
        },
        update_time: {
            type: DataTypes.INTEGER(11),
            allowNull: true,
            defaultValue: 0,
        },
    }),
    {
        indexes: [{ unique: true, fields: ['order_no', 'batch_id'] }],
        tableName: 't_clear_order_record',
    }
)
module.exports = ClearOrderRecordModel
