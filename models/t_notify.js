const { parseModels } = require('../fns/parseModels')
const { DataTypes } = require('sequelize')
const DB = require('../libs/mysql')
const Sequelize = DB.get('icp')

const SendStatusEnum = {
    New: 0, //待发送
    Sending: 1, //发送中
    Sendfinish: 2, //已经接收
    Sendfailed: 3, //发送失败
    Recivefailed: 4, //接收失败
    Timeout: 5, //超时未响应
    Trytosending: 6, //尝试发送中
    Forbidden: 7, //禁止发送
}

const NotifyTypeEnum = {
    Phone: 0, //短信
    Email: 1, //邮件
}

const NotifyModel = {}
NotifyModel.SendStatusEnum = SendStatusEnum
NotifyModel.NotifyTypeEnum = NotifyTypeEnum
NotifyModel.model = Sequelize.define(
    't_notify',
    parseModels({
        id: {
            type: DataTypes.INTEGER(11),
            primaryKey: true,
            autoIncrement: true,
            comment: '通知记录Id',
        },
        company_notify_Id: {
            type: DataTypes.INTEGER(11),
            allowNull: false,
            comment: '公司通知Id',
        },
        contact: {
            type: DataTypes.STRING(256),
            allowNull: false,
            comment: '手机号,或邮箱',
        },
        send_status: {
            type: DataTypes.INTEGER(2),
            allowNull: false,
            defaultValue: SendStatusEnum.NEW,
            values: Object.values(SendStatusEnum),
            comment:
                '短信/邮件发送状态,0:待发送，1:发送中，2:发送成功, 3:发送失败，4:接收失败，5:尝试发送,6:禁止发送，7:超时未响应',
        },
        type: {
            type: DataTypes.INTEGER(2),
            allowNull: false,
            values: Object.values(NotifyTypeEnum),
            comment: '通知类型',
        },
        task_id: {
            type: DataTypes.STRING(64),
            allowNull: false,
            defaultValue: '',
            comment: '发送任务id',
        },
        retry_time: {
            type: DataTypes.INTEGER(11),
            allowNull: false,
            defaultValue: 0,
            comment: '发送重试次数',
        },
        history_send_status: {
            type: DataTypes.INTEGER(2),
            allowNull: true,
            values: Object.values(SendStatusEnum),
            comment:
                '短信/邮件发送状态,0:待发送，1:发送中，2:发送成功, 3:发送失败，4:接收失败，5:尝试发送,6:禁止发送，7:超时未响应',
        },
        history_send_time: {
            type: DataTypes.INTEGER(11),
            allowNull: true,
            defaultValue: 0,
        },
        create_time: {
            type: DataTypes.INTEGER(11),
            allowNull: true,
            defaultValue: Sequelize.NOW,
        },
        update_time: {
            type: DataTypes.INTEGER(11),
            allowNull: true,
            defaultValue: Sequelize.NOW,
        },
    }),
    {
        tableName: 't_notify',
    }
)

module.exports = NotifyModel
