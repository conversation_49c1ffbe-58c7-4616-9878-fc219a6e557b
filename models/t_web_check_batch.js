/*
 * @Author: william.qian <EMAIL>
 * @Date: 2023-03-20 18:53:39
 * @LastEditors: william.qian <EMAIL>
 * @LastEditTime: 2023-05-11 16:32:59
 * @FilePath: /newicp/models/t_web_check_batch.js
 * @Description: 网站备案信息检查
 */
const { parseModels } = require('../fns/parseModels')
const { DataTypes } = require('sequelize')
const DB = require('../libs/mysql')
const sequelize = DB.get('icp')

const WebCheckBatchModel = {}
const WebCheckBatchStatusEnum = {
    CheckIng: 0, // 检查中,
    // ResolveIng: 2, // 解析域名，确定解析是否在我司
    Finish: 1, // 已完成,
}
WebCheckBatchModel.WebCheckBatchStatusEnum = WebCheckBatchStatusEnum

WebCheckBatchModel.model = sequelize.define(
    't_web_check_batch',
    parseModels({
        id: {
            type: DataTypes.INTEGER(11),
            allowNull: false,
            primaryKey: true,
            autoIncrement: true,
        },
        province_refer: {
            type: DataTypes.STRING(64),
            allowNull: false,
            comment: '省份简称',
        },
        batch_name: {
            type: DataTypes.STRING(255),
            allowNull: false,
            unique: true,
            defaultValue: '',
            comment: '批次名',
        },
        operator: {
            type: DataTypes.STRING(64),
            allowNull: false,
            defaultValue: '',
            comment: '操作人',
        },
        status: {
            type: DataTypes.INTEGER(2),
            allowNull: false,
            comment: '状态',
        },
        create_time: {
            type: DataTypes.INTEGER(11),
            allowNull: true,
            defaultValue: sequelize.NOW,
        },
        update_time: {
            type: DataTypes.INTEGER(11),
            allowNull: true,
            defaultValue: sequelize.NOW,
        },
        count: {
            type: DataTypes.INTEGER(11),
            allowNull: false,
            comment: '创建时间',
        },
    }),
    {
        tableName: 't_web_check_batch',
    }
)
module.exports = WebCheckBatchModel
