const { parseModels } = require('../fns/parseModels')
const { DataTypes } = require('sequelize')
const DB = require('../libs/mysql')
const sequelize = DB.get('icp')

const CompanyWhiteListModel = {}

CompanyWhiteListModel.model = sequelize.define(
    't_company_white_list',
    parseModels({
        id: {
            type: DataTypes.INTEGER(11),
            allowNull: false,
            primaryKey: true,
            autoIncrement: true,
        },
        company_id: {
            type: DataTypes.INTEGER(50),
            allowNull: false,
            unique: true,
        },
        operator: {
            type: DataTypes.STRING(255),
            allowNull: false,
            defaultValue: '',
        },
        create_time: {
            type: DataTypes.INTEGER(11),
            allowNull: false,
        },
        update_time: {
            type: DataTypes.INTEGER(11),
            allowNull: false,
        },
        remark: {
            type: DataTypes.STRING(255),
            allowNull: true,
        },
    }),
    {
        tableName: 't_company_white_list',
    }
)
module.exports = CompanyWhiteListModel
