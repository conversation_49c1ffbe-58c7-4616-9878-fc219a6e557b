const { parseModels, parseNullArr } = require('../fns/parseModels')
const { DataTypes } = require('sequelize')
const DB = require('../libs/mysql')
const sequelize = DB.get('icp')

const ConfigModel = {}

ConfigModel.model = sequelize.define(
    't_config',
    parseModels({
        Id: {
            type: DataTypes.INTEGER(11),
            allowNull: false,
            primaryKey: true,
            autoIncrement: true,
        },
        key: {
            type: DataTypes.STRING(255),
            allowNull: false,
            defaultValue: '',
        },
        config: {
            type: DataTypes.TEXT,
            allowNull: false,
        },
    }),
    {
        tableName: 't_config',
    }
)
module.exports = ConfigModel
