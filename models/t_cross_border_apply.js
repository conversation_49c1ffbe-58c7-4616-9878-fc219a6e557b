/*
 * @Date: 2022-07-26 16:24:30
 * @LastEditors: l<PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2022-09-05 13:57:20
 * @FilePath: /newicp/models/t_cross_border_apply.js
 */
const {
    parseModels,
    upperCamelcase,
    parseNullArr,
} = require('../fns/parseModels')
const { DataTypes, Op } = require('sequelize')
const DB = require('../libs/mysql')
const sequelize = DB.get('icp')
const CrossBorderApplyModel = {}

const ApplyStatusEnum = {
    Editing: 0, //编辑中
    Aduiting: 1, //审核中
    AduitPass: 2, //审核通过
    AduitReject: 3, //审核打回
    Expired: 4, //已过期
    Invalidated: 5, //已作废
}
CrossBorderApplyModel.ApplyStatusEnum = ApplyStatusEnum
CrossBorderApplyModel.model = sequelize.define(
    't_cross_border_apply',
    parseModels({
        id: {
            type: DataTypes.INTEGER(11),
            allowNull: false,
            primaryKey: true,
            autoIncrement: true,
        },
        status: {
            type: DataTypes.INTEGER(4),
            allowNull: false,
            defaultValue: ApplyStatusEnum.Editing,
            comment: '状态',
        },
        company_id: {
            type: DataTypes.INTEGER(16),
            allowNull: false,
            comment: '公司id',
        },
        channel: {
            type: DataTypes.INTEGER(4),
            allowNull: false,
            defaultValue: 1,
            comment: '渠道',
        },
        company_name: {
            type: DataTypes.STRING(255),
            defaultValue: '',
            comment: '公司名称',
        },
        company_code: {
            type: DataTypes.STRING(255),
            defaultValue: '',
            comment: '统一社会信用代码',
        },
        legal_entity_name: {
            type: DataTypes.STRING(255),
            defaultValue: '',
            comment: '法人名称',
        },
        business_place: {
            type: DataTypes.STRING(1024),
            defaultValue: '',
            comment: '经营场所',
        },
        license_issuing_agency: {
            type: DataTypes.STRING(255),
            defaultValue: '',
            comment: '发证机构',
        },
        postal_code: {
            type: DataTypes.STRING(255),
            defaultValue: '',
            comment: '邮件编码',
        },
        business_license_begin_time: {
            type: DataTypes.INTEGER(12),
            defaultValue: '0',
            comment: '营业执照有效开始时间',
        },
        business_license_end_time: {
            type: DataTypes.INTEGER(12),
            defaultValue: '0',
            comment: '营业执照有效结束时间',
        },
        manager_name: {
            type: DataTypes.STRING(255),
            defaultValue: '',
            comment: '经办人姓名',
        },
        manager_license_id: {
            type: DataTypes.STRING(255),
            defaultValue: '',
            comment: '经办人身份证号',
        },
        manager_address: {
            type: DataTypes.STRING(1024),
            defaultValue: '',
            comment: '经办人地址',
        },
        manager_phone: {
            type: DataTypes.STRING(255),
            defaultValue: '',
            comment: '经办人联系电话',
        },
        manager_email: {
            type: DataTypes.STRING(255),
            defaultValue: '',
            comment: '经办人邮箱',
        },
        business_license_pic: {
            type: DataTypes.STRING(255),
            defaultValue: '',
            comment: '营业执照图片',
        },
        manager_license_pic: {
            type: DataTypes.STRING(255),
            defaultValue: '',
            comment: '经办人身份证照片',
        },
        promise_letter_pic: {
            type: DataTypes.STRING(255),
            defaultValue: '',
            comment: '承诺书',
        },
        proxy_letter_pic: {
            type: DataTypes.STRING(255),
            defaultValue: '',
            comment: '委托书',
        },
        service_protocol_pic: {
            type: DataTypes.STRING(255),
            defaultValue: '',
            comment: '服务协议',
        },
        ucloud_proxy_letter_pic:{
            type: DataTypes.STRING(255),
            defaultValue: '',
            comment: '授优刻得委托书',
        },
        error: {
            type: DataTypes.TEXT,
            defaultValue: '{}',
            comment: '打回的错误信息',
            set(val) {
                this.setDataValue(upperCamelcase('error'), JSON.stringify(val))
            },
            get() {
                const rawValue = this.getDataValue(upperCamelcase('error'))
                return JSON.parse(rawValue)
            },
        },
        manager: {
            type: DataTypes.STRING(255),
            defaultValue: '',
            comment: '该账号的客户经理',
        },
        is_deleted: {
            type: DataTypes.INTEGER(4),
            allowNull: false,
            defaultValue: '0',
            comment: '是否删除',
        },
        audit_time: {
            type: DataTypes.INTEGER(11),
            defaultValue: '0',
            comment: '审核时间',
        },
        submit_time: {
            type: DataTypes.INTEGER(11),
            defaultValue: '0',
            comment: '提交时间',
        },
        create_time: {
            type: DataTypes.INTEGER(11),
            allowNull: false,
            defaultValue: '0',
        },
        update_time: {
            type: DataTypes.INTEGER(11),
            allowNull: false,
            defaultValue: '0',
        },
    }),
    {
        tableName: 't_cross_border_apply',
        indexes: [{ fields: ['company_id'] }],
    }
)
module.exports = CrossBorderApplyModel
