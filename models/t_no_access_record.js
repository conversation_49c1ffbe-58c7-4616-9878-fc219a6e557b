/*
 * @Author: william.qian <EMAIL>
 * @Date: 2022-11-03 18:12:45
 * @LastEditors: william.qian <EMAIL>
 * @LastEditTime: 2023-06-12 17:33:30
 * @FilePath: /newicp/models/t_no_access_record.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
/**
 *已备案未接入通知记录导入数据表
 */
const { parseModels } = require('../fns/parseModels')
const { DataTypes } = require('sequelize')
const DB = require('../libs/mysql')
const sequelize = DB.get('icp')

const UninsertBatchRecordModel = {}
UninsertBatchRecordModel.model = sequelize.define(
    't_no_access_record',
    parseModels({
        id: {
            type: DataTypes.INTEGER(11),
            primaryKey: true,
            autoIncrement: true,
        },
        batch_id: {
            type: DataTypes.INTEGER(11),
            allowNull: false,
        },
        ip: {
            type: DataTypes.STRING(255),
            allowNull: false,
            defaultValue: '',
        },
        domain: {
            type: DataTypes.STRING(255),
            allowNull: false,
            defaultValue: '',
        },
        icp_web_no: {
            type: DataTypes.STRING(255),
            allowNull: false,
            defaultValue: '',
        },
        sub_domain: {
            type: DataTypes.STRING(255),
            allowNull: false,
            defaultValue: '',
        },
        register_status: {
            type: DataTypes.INTEGER(11),
            allowNull: true,
            defaultValue: 0,
            comment: '域名是否在白名单，在白名单1，在我司接入2，不在0',
        },
        batch_org_id: {
            type: DataTypes.INTEGER(11),
            allowNull: true,
            defaultValue: 0,
            comment: '默认0,取t_no_access_orgInfo表的id',
        },
        create_time: {
            type: DataTypes.INTEGER(11),
            allowNull: true,
            defaultValue: sequelize.NOW,
        },
    }),
    {
        tableName: 't_no_access_record',
    }
)

module.exports = UninsertBatchRecordModel
