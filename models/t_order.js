const {
    parseModels,
    upperCamelcase,
    parseNullArr,
    parseNullObject,
} = require('../fns/parseModels')
const { DataTypes } = require('sequelize')
const DB = require('../libs/mysql')
const sequelize = DB.get('icp')

const OrderModel = {}

const OrderTypeEnum = {
    AddMain: 1, // 新增主体
    AddWeb: 2, // 新增互联网信息服务
    AddConn: 3, // 新增接入
    CancelMain: 4, // 注销主体
    CancelWeb: 5, // 注销互联网信息服务
    CancelConn: 6, // 取消接入
    ChangeAll: 7, // 变更备案信息
    ChangeMain: 8, // 变更主体
    ChangeWeb: 9, // 变更互联网信息服务
    ChangeConn: 10, // 变更接入
}

OrderModel.OrderTypeEnum = OrderTypeEnum

const OrderStatusEnum = {
    Editing: 1, //编辑中
    Auditing: 2, //ucloud审核中
    AuditPass: 3, //初审通过
    AuditRefuse: 4, //ucloud审核打回
    WaitReAudit: 7, //等待复审
    ReAuditPass: 8, //复审通过
    ReAuditRefuse: 9, //复审打回
    GovAuditing: 11, //管局审核中
    GovAuditPass: 12, //管局审核通过
    GovAuditRefuse: 13, //管局审核不通过
    GovReturnCache: 15, //管局退回暂存
}
OrderModel.OrderStatusEnum = OrderStatusEnum

const CurtainStatusEnum = {
    awaitGather: 0, //"未采集",
    inGather: 1, //"采集中",
    awaitSubmit: 3, //"待提交",
    Submitted: 4, //"已提交",
    awaitChange: 5, //"退回待修改"
}

OrderModel.CurtainStatusEnum = CurtainStatusEnum

OrderModel.model = sequelize.define(
    't_order',
    parseModels({
        id: {
            type: DataTypes.INTEGER(11),
            allowNull: false,
            primaryKey: true,
            autoIncrement: true,
        },
        order_no: {
            type: DataTypes.STRING(255),
            allowNull: false,
            defaultValue: '',
            unique: true,
        },
        type: {
            type: DataTypes.INTEGER(4),
            allowNull: false,
            defaultValue: '0',
        },
        status: {
            type: DataTypes.INTEGER(4),
            allowNull: false,
            defaultValue: '0',
        },
        company_id: {
            type: DataTypes.INTEGER(16),
            allowNull: false,
            defaultValue: '0',
        },
        channel: {
            type: DataTypes.INTEGER(11),
            allowNull: false,
            defaultValue: 1,
        },
        area_id: {
            type: DataTypes.INTEGER(11),
            allowNull: false,
            defaultValue: '0',
        },
        icp_main_no: {
            type: DataTypes.STRING(255),
            allowNull: false,
            defaultValue: '',
        },
        organizer_type: {
            type: DataTypes.INTEGER(4),
            allowNull: false,
            defaultValue: '0',
        },
        organizer_name: {
            type: DataTypes.STRING(255),
            allowNull: false,
            defaultValue: '',
        },
        organizer_address: {
            type: DataTypes.STRING(255),
            allowNull: false,
            defaultValue: '',
        },
        organizer_license_type: {
            type: DataTypes.INTEGER(4),
            allowNull: false,
            defaultValue: '0',
        },
        organizer_license_id: {
            type: DataTypes.STRING(255),
            allowNull: false,
            defaultValue: '',
        },
        organizer_license_area: {
            type: DataTypes.STRING(255),
            allowNull: false,
            defaultValue: '',
        },
        pic_main_name: {
            type: DataTypes.STRING(255),
            allowNull: false,
            defaultValue: '',
        },
        pic_main_office_phone: {
            type: DataTypes.STRING(16),
            allowNull: false,
            defaultValue: '',
        },
        pic_main_office_phone_pre: {
            type: DataTypes.STRING(16),
            allowNull: true,
            defaultValue: '',
        },
        pic_main_mobile: {
            type: DataTypes.STRING(255),
            allowNull: false,
            defaultValue: '',
        },
        pic_main_email: {
            type: DataTypes.STRING(255),
            allowNull: false,
            defaultValue: '',
        },
        pic_main_qq: {
            type: DataTypes.STRING(255),
            allowNull: false,
            defaultValue: '',
        },
        pic_main_license_type: {
            type: DataTypes.INTEGER(4),
            allowNull: false,
            defaultValue: '0',
        },
        pic_main_license_id: {
            type: DataTypes.STRING(255),
            allowNull: false,
            defaultValue: '',
        },
        pic_main_license_date: {
            type: DataTypes.STRING(128),
            allowNull: false,
            defaultValue: '',
            comment: '主体负责人证件有效期',
        },
        organizer_license_picture: {
            type: DataTypes.STRING(1024),
            allowNull: true,
            defaultValue: '',
            set(val) {
                this.setDataValue(
                    upperCamelcase('organizer_license_picture'),
                    typeof val === 'object' ? val.join('|') : ''
                )
            },
            get() {
                const rawValue = this.getDataValue(
                    upperCamelcase('organizer_license_picture')
                )
                return parseNullArr(rawValue)
            },
        },
        organizer_residence_permit_picture: {
            type: DataTypes.STRING(1024),
            allowNull: false,
            defaultValue: '',
            set(val) {
                this.setDataValue(
                    upperCamelcase('organizer_residence_permit_picture'),
                    typeof val === 'object' ? val.join('|') : ''
                )
            },
            get() {
                const rawValue = this.getDataValue(
                    upperCamelcase('organizer_residence_permit_picture')
                )
                return parseNullArr(rawValue)
            },
        },
        pic_main_license_picture: {
            type: DataTypes.STRING(1024),
            allowNull: false,
            defaultValue: '',
            set(val) {
                this.setDataValue(
                    upperCamelcase('pic_main_license_picture'),
                    typeof val === 'object' ? val.join('|') : ''
                )
            },
            get() {
                const rawValue = this.getDataValue(
                    upperCamelcase('pic_main_license_picture')
                )
                return parseNullArr(rawValue)
            },
        },
        promise_letter_picture: {
            type: DataTypes.STRING(1024),
            allowNull: false,
            defaultValue: '',
            set(val) {
                this.setDataValue(
                    upperCamelcase('promise_letter_picture'),
                    typeof val === 'object' ? val.join('|') : ''
                )
            },
            get() {
                const rawValue = this.getDataValue(
                    upperCamelcase('promise_letter_picture')
                )
                return parseNullArr(rawValue)
            },
        },
        other_picture: {
            type: DataTypes.STRING(1024),
            allowNull: false,
            defaultValue: '',
            set(val) {
                this.setDataValue(
                    upperCamelcase('other_picture'),
                    typeof val === 'object' ? val.join('|') : ''
                )
            },
            get() {
                const rawValue = this.getDataValue(
                    upperCamelcase('other_picture')
                )
                return parseNullArr(rawValue)
            },
        },
        need_modify_icp: {
            type: DataTypes.TINYINT(1),
            allowNull: false,
            defaultValue: 0,
            comment: '是否需要变更主体',
        },
        remark: {
            type: DataTypes.STRING(1024),
            allowNull: false,
            defaultValue: '',
        },
        create_time: {
            type: DataTypes.INTEGER(11),
            allowNull: true,
            defaultValue: sequelize.NOW,
        },
        update_time: {
            type: DataTypes.INTEGER(11),
            allowNull: true,
            defaultValue: sequelize.NOW,
        },
        icp_main_password: {
            type: DataTypes.STRING(255),
            allowNull: false,
            defaultValue: '',
        },
        submit_reexamine_type: {
            type: DataTypes.INTEGER(11),
            allowNull: false,
            defaultValue: '0',
        },
        error: {
            type: DataTypes.TEXT,
            allowNull: false,
            defaultValue: '{}',
            set(val) {
                this.setDataValue(upperCamelcase('error'), JSON.stringify(val))
            },
            get() {
                const rawValue = this.getDataValue(upperCamelcase('error'))
                return parseNullObject(rawValue, {})
            },
        },
        front_error: {
            type: DataTypes.TEXT,
            allowNull: false,
            defaultValue: '{}',
            set(val) {
                this.setDataValue(
                    upperCamelcase('front_error'),
                    JSON.stringify(val)
                )
            },
            get() {
                const rawValue = this.getDataValue(
                    upperCamelcase('front_error')
                )
                return parseNullObject(rawValue, {})
            },
        },
        front_picture_error: {
            type: DataTypes.TEXT,
            allowNull: false,
            defaultValue: '{}',
            set(val) {
                this.setDataValue(
                    upperCamelcase('front_picture_error'),
                    JSON.stringify(val)
                )
            },
            get() {
                const rawValue = this.getDataValue(
                    upperCamelcase('front_picture_error')
                )
                return parseNullObject(rawValue, {})
            },
        },
        stash: {
            type: DataTypes.TEXT,
            allowNull: false,
            defaultValue: '{}',
            set(val) {
                this.setDataValue(upperCamelcase('stash'), JSON.stringify(val))
            },
            get() {
                const rawValue = this.getDataValue(upperCamelcase('stash'))
                return parseNullObject(rawValue, {})
            },
        },
        is_deleted: {
            type: DataTypes.INTEGER(1),
            allowNull: false,
            defaultValue: '0',
        },
        step: {
            type: DataTypes.INTEGER(1),
            allowNull: true,
            defaultValue: '1',
        },
        edit_status: {
            type: DataTypes.STRING(2),
            allowNull: true,
            defaultValue: '0',
        },
        auditor: {
            type: DataTypes.STRING(128),
            allowNull: false,
            defaultValue: '',
        },
        reject_reason: {
            type: DataTypes.STRING(1024),
            allowNull: false,
            defaultValue: '',
        },
        logout_reason: {
            type: DataTypes.STRING(255),
            allowNull: false,
            defaultValue: '',
        },
        organizer_license_picture_for_auth: {
            type: DataTypes.STRING(1024),
            allowNull: false,
            defaultValue: '',
            set(val) {
                this.setDataValue(
                    upperCamelcase('organizer_license_picture_for_auth'),
                    typeof val === 'object' ? val.join('|') : ''
                )
            },
            get() {
                const rawValue = this.getDataValue(
                    upperCamelcase('organizer_license_picture_for_auth')
                )
                return parseNullArr(rawValue)
            },
        },
        pic_main_license_picture_for_auth: {
            type: DataTypes.STRING(1024),
            allowNull: false,
            defaultValue: '',
            set(val) {
                this.setDataValue(
                    upperCamelcase('pic_main_license_picture_for_auth'),
                    typeof val === 'object' ? val.join('|') : ''
                )
            },
            get() {
                const rawValue = this.getDataValue(
                    upperCamelcase('pic_main_license_picture_for_auth')
                )
                return parseNullArr(rawValue)
            },
        },

        icp_web_no: {
            type: DataTypes.STRING(255),
            allowNull: false,
            defaultValue: '',
        },
        icp_web_password: {
            type: DataTypes.STRING(0),
            allowNull: false,
            defaultValue: '',
        },
        ip: {
            type: DataTypes.STRING(0),
            allowNull: false,
            defaultValue: '',
        },
        curtain_picture: {
            type: DataTypes.STRING(0),
            allowNull: false,
            defaultValue: '',
            set(val) {
                this.setDataValue(
                    upperCamelcase('curtain_picture'),
                    typeof val === 'object' ? val.join('|') : ''
                )
            },
            get() {
                const rawValue = this.getDataValue(
                    upperCamelcase('curtain_picture')
                )
                return parseNullArr(rawValue)
            },
        },
        curtain_picture_for_auth: {
            type: DataTypes.STRING(0),
            allowNull: false,
            defaultValue: '',
        },
        pic_web_license_picture: {
            type: DataTypes.STRING(0),
            allowNull: false,
            defaultValue: '',
        },
        pic_web_license_picture_for_auth: {
            type: DataTypes.STRING(0),
            allowNull: false,
            defaultValue: '',
        },
        auth_verification_picture: {
            type: DataTypes.STRING(0),
            allowNull: false,
            defaultValue: '',
            set(val) {
                this.setDataValue(
                    upperCamelcase('auth_verification_picture'),
                    typeof val === 'object' ? val.join('|') : ''
                )
            },
            get() {
                const rawValue = this.getDataValue(
                    upperCamelcase('auth_verification_picture')
                )
                return parseNullArr(rawValue)
            },
        },
        auth_verification_picture_for_auth: {
            type: DataTypes.STRING(0),
            allowNull: false,
            defaultValue: '',
            set(val) {
                this.setDataValue(
                    upperCamelcase('auth_verification_picture_for_auth'),
                    typeof val === 'object' ? val.join('|') : ''
                )
            },
            get() {
                const rawValue = this.getDataValue(
                    upperCamelcase('auth_verification_picture_for_auth')
                )
                return parseNullArr(rawValue)
            },
        },
        processed_pictures: {
            type: DataTypes.TEXT,
            allowNull: true,
            set(val) {
                this.setDataValue(
                    upperCamelcase('processed_pictures'),
                    JSON.stringify(val)
                )
            },
            get() {
                const rawValue = this.getDataValue(
                    upperCamelcase('processed_pictures')
                )
                return parseNullObject(rawValue, [])
            },
        },
        unit_superior: {
            type: DataTypes.STRING(255),
            allowNull: true,
        },
        emergency_phone: {
            type: DataTypes.STRING(256),
            allowNull: true,
        },
        is_extract_type: {
            type: DataTypes.INTEGER(11),
            allowNull: true,
        },
        app_code: {
            type: DataTypes.STRING(255),
            allowNull: true,
        },
        c_main_id: {
            type: DataTypes.INTEGER(11),
            allowNull: false,
            defaultValue: '-1',
        },
        c_website_id: {
            type: DataTypes.INTEGER(11),
            allowNull: false,
            defaultValue: '-1',
        },
        icp_web_id: {
            type: DataTypes.INTEGER(11),
            allowNull: false,
            defaultValue: '-1',
        },
        icp_id: {
            type: DataTypes.INTEGER(11),
            allowNull: false,
            defaultValue: '-1',
        },
        details: {
            type: DataTypes.STRING(2048),
            allowNull: true,
            defaultValue: '[]',
            set(val) {
                this.setDataValue(
                    upperCamelcase('details'),
                    JSON.stringify(val)
                )
            },
            get() {
                const rawValue = this.getDataValue(upperCamelcase('details'))
                if (!rawValue || rawValue === '') {
                    return []
                }
                return JSON.parse(rawValue) || rawValue
            },
        },
        reason_id: {
            type: DataTypes.INTEGER(11),
            allowNull: true,
        },
        select_times: {
            type: DataTypes.INTEGER(2),
            allowNull: false,
            defaultValue: '0',
        },
        update_content: {
            type: DataTypes.TEXT,
            allowNull: true,
            set(val) {
                this.setDataValue(
                    upperCamelcase('update_content'),
                    JSON.stringify(val)
                )
            },
            get() {
                const rawValue = this.getDataValue(
                    upperCamelcase('update_content')
                )
                // console.log(rawValue, rawValue, 12312312312, typeof rawValue)
                if (!rawValue || rawValue === '') {
                    return []
                }
                return JSON.parse(rawValue) || rawValue
            },
        },
        inner_display: {
            type: DataTypes.INTEGER(11),
            allowNull: false,
            defaultValue: '0',
        },
        uuid: {
            type: DataTypes.STRING(255),
            allowNull: true,
        },
        organizer_uuid: {
            type: DataTypes.STRING(255),
            allowNull: true,
        },
        version: {
            type: DataTypes.STRING(50),
            allowNull: false,
            defaultValue: 4.6,
        },
        unit_certificate_exp_date: {
            type: DataTypes.STRING(255),
            allowNull: true,
        },
        curtain_status: {
            type: DataTypes.INTEGER(1),
            allowNull: false,
            defaultValue: '0',
        },
        persons_audit_status: {
            type: DataTypes.INTEGER(1),
            allowNull: false,
            defaultValue: '0',
        },
    }),
    {
        tableName: 't_order',
    }
)
module.exports = OrderModel
