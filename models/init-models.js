var DataTypes = require('sequelize').DataTypes
var _t_company_info_check_record = require('./t_proxy_company_white_domain')

function initModels(sequelize) {
    var t_company_info_check_record = _t_company_info_check_record(
        sequelize,
        DataTypes
    )

    return {
        t_company_info_check_record,
    }
}
module.exports = initModels
module.exports.initModels = initModels
module.exports.default = initModels
