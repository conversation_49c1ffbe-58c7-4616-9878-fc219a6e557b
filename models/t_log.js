const {
    parseModels,
    parseNullArr,
    upperCamelcase,
    parseNullObject,
} = require('../fns/parseModels')
const { DataTypes } = require('sequelize')
const DB = require('../libs/mysql')
const sequelize = DB.get('icp')
const LogModel = {}

LogModel.model = sequelize.define(
    't_log',
    parseModels({
        id: {
            type: DataTypes.INTEGER(11),
            allowNull: false,
            primaryKey: true,
            autoIncrement: true,
        },
        action: {
            type: DataTypes.STRING(255),
            allowNull: false,
            defaultValue: '',
        },
        params: {
            type: DataTypes.TEXT,
            allowNull: false,
            defaultValue: '{}',
            set(val) {
                this.setDataValue(upperCamelcase('params'), JSON.stringify(val))
            },
            get() {
                const rawValue = this.getDataValue(upperCamelcase('params'))
                return parseNullObject(rawValue, {})
            },
        },
        company_id: {
            type: DataTypes.INTEGER(11),
            allowNull: false,
            defaultValue: '0',
        },
        domain: {
            type: DataTypes.STRING(1024),
            allowNull: false,
            defaultValue: '',
            set(val) {
                this.setDataValue(
                    upperCamelcase('domain'),
                    Array.isArray(val) ? val.join('|') : ''
                )
            },
            get() {
                const rawValue = this.getDataValue(upperCamelcase('domain'))
                return parseNullArr(rawValue)
            },
        },
        order_no: {
            type: DataTypes.STRING(255),
            allowNull: true,
            defaultValue: '',
        },
        operator: {
            type: DataTypes.STRING(16),
            allowNull: true,
            defaultValue: '',
        },
        expired: {
            type: DataTypes.INTEGER(11),
            allowNull: false,
            defaultValue: '0',
        },
        status: {
            type: DataTypes.INTEGER(11),
            allowNull: false,
            defaultValue: '0',
        },
        result: {
            type: DataTypes.STRING(255),
            allowNull: false,
            defaultValue: '',
        },
        update_time: {
            type: DataTypes.INTEGER(11),
            allowNull: true,
            defaultValue: sequelize.NOW,
        },
        create_time: {
            type: DataTypes.INTEGER(11),
            allowNull: true,
            defaultValue: sequelize.NOW,
        },
        icp_id: {
            type: DataTypes.INTEGER(11),
            allowNull: true,
        },
        user_email: {
            type: DataTypes.STRING(255),
            allowNull: true,
        },
        icp_main_no: {
            type: DataTypes.STRING(255),
            allowNull: false,
            defaultValue: '',
        },
        source_company_id: {
            type: DataTypes.INTEGER(11),
            allowNull: false,
            defaultValue: '-1',
        },
        remark: {
            type: DataTypes.STRING(255),
            allowNull: true,
        },
    }),
    {
        tableName: 't_log',
    }
)
module.exports = LogModel
