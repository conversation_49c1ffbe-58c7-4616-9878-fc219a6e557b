/**
 *已备案未接入公司信息表
 */
const {
    parseModels,
    upperCamelcase,
    parseNullObject,
} = require('../fns/parseModels')

const { DataTypes } = require('sequelize')
const DB = require('../libs/mysql')
const sequelize = DB.get('icp')
const RecordStatusEnum = {
    New: 0, //待发送
    Sending: 1, //发送中
    Sendfinish: 2, //发送完成
    Sendfailed: 3, //发送失败
    Timeout: 4, //超时未响应
    Forbidden: 5, //禁止发送
}

const ErrorReasonEnum = {
    GetResource: '获取资源信息失败',
    GetCompanyInfo: '获取公司信息失败',
    GetICPInfo: '获取备案信息失败',
}

const CompanyNotifyModel = {}
CompanyNotifyModel.RecordStatusEnum = RecordStatusEnum
CompanyNotifyModel.ErrorReasonEnum = ErrorReasonEnum

CompanyNotifyModel.model = sequelize.define(
    't_no_access_org_info',
    parseModels({
        id: {
            type: DataTypes.INTEGER(11),
            primaryKey: true,
            autoIncrement: true,
            comment: '公司记录Id',
        },
        batch_id: {
            type: DataTypes.INTEGER(11),
            allowNull: false,
            comment: '批次Id',
        },
        company_id: {
            type: DataTypes.INTEGER(11),
            allowNull: true,
            comment: '公司Id',
        },
        organization_id: {
            type: DataTypes.INTEGER(11),
            allowNull: true,
            comment: '组织Id',
        },
        company_name: {
            type: DataTypes.STRING(256),
            allowNull: true,
            defaultValue: '',
            comment: '公司名称',
        },
        level: {
            type: DataTypes.STRING(10),
            allowNull: true,
            defaultValue: '',
            comment: '公司等级',
        },
        manager: {
            type: DataTypes.STRING(128),
            allowNull: true,
            defaultValue: '',
            comment: '客户经理',
        },
        bu: {
            type: DataTypes.STRING(128),
            allowNull: true,
            defaultValue: '',
            comment: 'BU',
        },
        main_mail: {
            type: DataTypes.STRING(128),
            allowNull: true,
            defaultValue: '',
            comment: '主账号邮箱',
        },
        channel_id: {
            type: DataTypes.STRING(128),
            allowNull: true,
            defaultValue: '',
            comment: '所属渠道',
        },
        status: {
            type: DataTypes.INTEGER(4),
            allowNull: true,
            defaultValue: 0,
            comment:
                '记录状态，0：待发送，1：发送中，2：发送成功，3：发送失败，4：超时未响应,5:禁止发送',
        },
        reason: {
            type: DataTypes.TEXT,
            allowNull: true,
            defaultValue: '',
            comment: '获取公司资源信息、公司基础信息、备案信息失败原因',
        },
        create_time: {
            type: DataTypes.INTEGER(11),
            allowNull: true,
            defaultValue: sequelize.NOW,
        },
        update_time: {
            type: DataTypes.INTEGER(11),
            allowNull: true,
            defaultValue: sequelize.NOW,
        },
    }),
    {
        tableName: 't_no_access_org_info',
    }
)

module.exports = CompanyNotifyModel
