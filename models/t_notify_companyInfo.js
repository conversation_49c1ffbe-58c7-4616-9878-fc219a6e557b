const {
    parseModels,
    upperCamelcase,
    parseNullObject,
} = require('../fns/parseModels')

const { DataTypes } = require('sequelize')
const DB = require('../libs/mysql')
const sequelize = DB.get('icp')
const RecordStatusEnum = {
    New: 0, //待发送
    Sending: 1, //发送中
    Sendfinish: 2, //发送完成
    Sendfailed: 3, //发送失败
    Timeout: 4, //超时未响应
    Forbidden: 5, //禁止发送
}

const ErrorReasonEnum = {
    GetResource: '获取资源信息失败',
    GetCompanyInfo: '获取公司信息失败',
    GetICPInfo: '获取备案信息失败',
}

const CompanyNotifyModel = {}
CompanyNotifyModel.RecordStatusEnum = RecordStatusEnum
CompanyNotifyModel.ErrorReasonEnum = ErrorReasonEnum

CompanyNotifyModel.model = sequelize.define(
    't_notify_companyInfo',
    parseModels({
        id: {
            type: DataTypes.INTEGER(11),
            primaryKey: true,
            autoIncrement: true,
            comment: '公司记录Id',
        },
        batch_id: {
            type: DataTypes.INTEGER(11),
            allowNull: false,
            comment: '批次Id',
        },
        company_id: {
            type: DataTypes.INTEGER(11),
            allowNull: false,
            comment: '公司Id',
        },
        company_name: {
            type: DataTypes.STRING(256),
            allowNull: true,
            defaultValue: '',
            comment: '公司名称',
        },
        level: {
            type: DataTypes.STRING(10),
            allowNull: true,
            defaultValue: '',
            comment: '公司等级',
        },
        manager: {
            type: DataTypes.STRING(128),
            allowNull: true,
            defaultValue: '',
            comment: '客户经理',
        },
        bu: {
            type: DataTypes.STRING(128),
            allowNull: true,
            defaultValue: '',
            comment: 'BU',
        },
        main_mail: {
            type: DataTypes.STRING(128),
            allowNull: true,
            defaultValue: '',
            comment: '主账号邮箱',
        },
        status: {
            type: DataTypes.INTEGER(4),
            allowNull: true,
            defaultValue: '0',
            values: Object.values(RecordStatusEnum),
            comment:
                '记录状态，0：待发送，1：发送中，2：发送成功，3：发送失败，4：超时未响应',
        },
        icp_info: {
            type: DataTypes.TEXT,
            allowNull: true,
            defaultValue: '',
            comment:
                '该公司Id下在本系统备案的所有域名，备案号,主办单位名称信息',
            set(val) {
                this.setDataValue(
                    upperCamelcase('icp_info'),
                    JSON.stringify(val)
                )
            },
            get() {
                const rawValue = this.getDataValue(upperCamelcase('icp_info'))
                return parseNullObject(rawValue, [])
            },
        },
        is_error: {
            type: DataTypes.TINYINT(1),
            allowNull: false,
            defaultValue: 0,
            comment: '是否发生错误',
        },
        reason: {
            type: DataTypes.TEXT,
            allowNull: true,
            defaultValue: '',
            comment: '获取公司资源信息、公司基础信息、备案信息失败原因',
        },
        attach_files: {
            type: DataTypes.TEXT,
            allowNull: true,
            defaultValue: '',
            comment: '该公司自定义的附件',
            set(val) {
                this.setDataValue(
                    upperCamelcase('attach_files'),
                    JSON.stringify(val)
                )
            },
            get() {
                const rawValue = this.getDataValue(
                    upperCamelcase('attach_files')
                )
                return parseNullObject(rawValue, [])
            },
        },
        create_time: {
            type: DataTypes.INTEGER(11),
            allowNull: true,
            defaultValue: sequelize.NOW,
        },
        update_time: {
            type: DataTypes.INTEGER(11),
            allowNull: true,
            defaultValue: sequelize.NOW,
        },
    }),
    {
        tableName: 't_notify_companyInfo',
    }
)

module.exports = CompanyNotifyModel
