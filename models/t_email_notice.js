const { parseModels, parseNullArr } = require('../fns/parseModels')
const { DataTypes } = require('sequelize')
const DB = require('../libs/mysql')
const sequelize = DB.get('icp')

const EmailNoticeModel = {}

EmailNoticeModel.model = sequelize.define(
    't_email_notice',
    parseModels({
        id: {
            type: DataTypes.INTEGER(11).UNSIGNED,
            allowNull: false,
            primaryKey: true,
            autoIncrement: true,
        },
        send_time: {
            type: DataTypes.INTEGER(16),
            allowNull: false,
            defaultValue: '0',
        },
        company_id: {
            type: DataTypes.INTEGER(16),
            allowNull: false,
        },
        email_type: {
            type: DataTypes.STRING(64),
            allowNull: false,
            defaultValue: '',
        },
        user_email: {
            type: DataTypes.STRING(255),
            allowNull: false,
            defaultValue: '',
        },
        email_status: {
            type: DataTypes.INTEGER(4),
            allowNull: false,
            defaultValue: '0',
        },
        email_task_id: {
            type: DataTypes.STRING(64),
            allowNull: false,
            defaultValue: '',
        },
        create_time: {
            type: DataTypes.INTEGER(16),
            allowNull: false,
            defaultValue: '0',
        },
        retry_times: {
            type: DataTypes.INTEGER(1),
            allowNull: false,
            defaultValue: '0',
        },
        update_time: {
            type: DataTypes.INTEGER(16),
            allowNull: false,
            defaultValue: '0',
        },
        is_del: {
            type: DataTypes.INTEGER(1),
            allowNull: false,
            defaultValue: '0',
        },
    }),
    {
        tableName: 't_email_notice',
    }
)
module.exports = EmailNoticeModel
