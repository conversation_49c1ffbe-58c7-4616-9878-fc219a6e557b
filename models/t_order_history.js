const { parseModels, upperCamelcase } = require('../fns/parseModels')
const { DataTypes } = require('sequelize')
const DB = require('../libs/mysql')
const sequelize = DB.get('icp')

const OrderHistoryModel = {}

OrderHistoryModel.model = sequelize.define(
    't_order_history',
    parseModels({
        id: {
            type: DataTypes.INTEGER(11),
            allowNull: false,
            primaryKey: true,
            autoIncrement: true,
        },
        order_no: {
            type: DataTypes.STRING(255),
            allowNull: false,
            defaultValue: '',
        },
        status: {
            type: DataTypes.INTEGER(4),
            allowNull: false,
            defaultValue: '0',
        },
        operation_time: {
            type: DataTypes.DATE,
            allowNull: false,
            defaultValue: sequelize.literal('CURRENT_TIMESTAMP'),
        },
        operator: {
            type: DataTypes.STRING(255),
            allowNull: false,
            defaultValue: '',
        },
        action: {
            type: DataTypes.STRING(255),
            allowNull: false,
            defaultValue: '',
        },
        info: {
            type: DataTypes.TEXT,
            allowNull: true,
        },
    }),
    {
        tableName: 't_order_history',
    }
)
module.exports = OrderHistoryModel
