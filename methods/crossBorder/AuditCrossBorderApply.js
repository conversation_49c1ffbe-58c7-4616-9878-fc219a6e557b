/*
 * @Date: 2022-07-26 11:56:44
 * @LastEditors: l<PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2022-10-25 14:56:51
 * @FilePath: /newicp/methods/crossBorder/AuditCrossBorderApply.js
 */
'use strict'

const { parseJSON } = require('../../fns/kits')
const Method = require('../../libs/method')
const {
    CrossBorderApplyModel,
    ApplyStatusEnum,
    CrossBorderAuditLogModel,
} = require('../../models')
const moment = require('moment')
const _ = require('lodash')
const { Op } = require('sequelize')

module.exports = class AuditCrossBorderApply extends Method {
    constructor(cb) {
        super(cb)
    }

    async exec({ Status, Id, Error: Err, Operator } = {}) {
        let self = this
        try {
            let date = moment().format('X')
            let record = await CrossBorderApplyModel.findOne({
                where: {
                    Id,
                    Status: ApplyStatusEnum.Aduiting,
                },
            })
            record = parseJSON(record)
            if (!record) {
                let err = new Error('该申请不在待审核状态')
                err.code = 67123
                throw err
            }
            let updateJSON = {
                Status,
                AuditTime: date,
                UpdateTime: date,
            }
            if (Status === ApplyStatusEnum.AduitReject) {
                // 验证Error
                updateJSON.Error = Err
            }
            if (Status === ApplyStatusEnum.AduitPass) {
                updateJSON.Error = {}
            }
            let logJSON = {
                ApplyId: Id,
                Operator,
                CompanyId: record.CompanyId,
                Remark:
                    Status === ApplyStatusEnum.AduitPass
                        ? '审核通过'
                        : `审核拒绝，原因：${Object.values(Err).join(';')}`,
                CreateTime: moment().format('X'),
            }
            if (Status === ApplyStatusEnum.AduitPass) {
                await CrossBorderApplyModel.update(
                    {
                        Status: ApplyStatusEnum.Invalidated,
                        UpdateTime: date,
                    },
                    {
                        where: {
                            CompanyId: record.CompanyId,
                            Id: {
                                [Op.ne]: Id,
                            },
                        },
                    }
                )
            }
            await Promise.all([
                CrossBorderApplyModel.update(
                    { ...updateJSON },
                    {
                        where: {
                            Id,
                        },
                    }
                ),
                CrossBorderAuditLogModel.create(logJSON),
            ])

            this.cb(0)
        } catch (e) {
            console.log(e)
            e.code = 11003
            self.err(e)
        }
    }
}
