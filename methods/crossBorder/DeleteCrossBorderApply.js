/*
 * @Date: 2022-07-26 11:56:44
 * @LastEditors: l<PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2022-09-06 10:40:45
 * @FilePath: /newicp/methods/crossBorder/DeleteCrossBorderApply.js
 */
'use strict'

const { parseJSON } = require('../../fns/kits')
const Method = require('../../libs/method')
const {
    CrossBorderApplyModel,
    ApplyStatusEnum,
    CrossBorderAuditLogModel,
} = require('../../models')
const moment = require('moment')
const _ = require('lodash')

module.exports = class DeleteCrossBorderApply extends Method {
    constructor(cb) {
        super(cb)
    }

    async exec({ Id, CompanyId } = {}) {
        try {
            let record = await CrossBorderApplyModel.findOne({
                where: {
                    Id,
                    CompanyId,
                    Status: ApplyStatusEnum.Editing,
                    IsDeleted: 0,
                },
            })
            record = parseJSON(record)
            if (!record) {
                let err = new Error('该申请不能删除')
                err.code = 67125
                return this.err(err)
            }
            let logJSON = {
                ApplyId: Id,
                Operator: CompanyId,
                CompanyId,
                Remark: '删除订单',
                CreateTime: moment().format('X'),
            }
            await Promise.all([
                CrossBorderApplyModel.update(
                    { IsDeleted: 1 },
                    {
                        where: {
                            Id,
                        },
                    }
                ),
                CrossBorderAuditLogModel.create(logJSON),
            ])
            this.cb(0)
        } catch (e) {
            console.log(e)
            e.code = 11003
            self.err(e)
        }
    }
}
