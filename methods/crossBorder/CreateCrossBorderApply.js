/*
 * @Date: 2022-07-26 11:56:44
 * @LastEditors: li<PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2022-10-31 11:09:22
 * @FilePath: /newicp/methods/crossBorder/CreateCrossBorderApply.js
 */
'use strict'

const Method = require('../../libs/method')
const verifyAndUpdateInfo = require('../../fns/cross_border_apply/VerifyAndUpdateInfo')
const verifyIdenityTwo = require('../../fns/cross_border_apply/VerifyIdenityTwo')
const frontCreateApplyCheck = require('../../fns/cross_border_apply/FrontCreateApplyCheck')
const {
    CrossBorderApplyModel,
    ApplyStatusEnum,
    CrossBorderAuditLogModel,
} = require('../../models')
const { Op } = require('sequelize')
const { parseJSON } = require('../../fns/kits')
const moment = require('moment')
module.exports = class CreateCrossBorderApply extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec(params = {}) {
        let self = this
        try {
            let Channel = params.channel || 1
            delete params.Id
            delete params.SubmitTime
            delete params.AuditTime
            delete params.channel
            params.Channel = Channel
            let currTime = moment().format('X')
            if (params.Status !== undefined) {
                if (
                    ![
                        ApplyStatusEnum.Editing,
                        ApplyStatusEnum.Aduiting,
                    ].includes(params.Status)
                ) {
                    // 参数校验错误
                    let err = new Error('参数校验错误')
                    err.code = 10003
                    throw err
                }
            }
            let authInfos = []

            // 实名信息校验
            let verifyRes = await frontCreateApplyCheck({ CompanyId: params.CompanyId, ChannelId: Channel})
            
            if (!verifyRes) {
                let err = new Error(
                    '该用户不支持申请跨境报备，请先进行企业认证'
                )
                err.code = 67120
                return this.err(err)
            }
            // 客户经理
            params.Manager = authInfos[0]?.Manager || ''
            // 查看该公司ID下是否有 除了审核通过 和已作废之外的其他订单，若有 则不支持新建
            let orderCount = await CrossBorderApplyModel.count({
                where: {
                    CompanyId: params.CompanyId,
                    Status: {
                        [Op.notIn]: [
                            ApplyStatusEnum.AduitPass,
                            ApplyStatusEnum.Invalidated,
                            ApplyStatusEnum.Expired,
                        ],
                    },
                    IsDeleted: 0,
                },
            })
            orderCount = parseJSON(orderCount)
            if (orderCount > 0) {
                //存在订单 不支持创建
                let err = new Error('该账号存在有效申请中订单，不支持新建申请')
                err.code = 67124
                return this.err(err)
            }
            params.CreateTime = currTime
            params.UpdateTime = currTime
            // 校验状态，看状态是否是要提交，若要提交 则所有字段需要校验
            if (params.Status === ApplyStatusEnum.Aduiting) {
                // 要提交
                // 1.校验所有字段是否正常传值
                params.SubmitTime = currTime
                let verifyRes = validateInput(params)
                if (!verifyRes) {
                    let err = new Error('请检查是否有缺漏填写信息')
                    err.code = 10003
                    return this.err(err)
                }
                if (!/^[0-9A-HJ-NPQRTUWXY]{15}$|^[0-9A-HJ-NPQRTUWXY]{18}$/.test(params.CompanyCode?.trim())) {
                    let err = new Error('请输入正确的统一社会信用代码')
                    err.code = 10003
                    return this.err(err)
                }
                // 2.校验营业执照是否已过期
                if (currTime > params.BusinessLicenseEndTime) {
                    let err = new Error('该营业执照已过期，请检查')
                    err.code = 67120
                    return this.err(err)
                }
                // 先校验二要素，若二要素不一致 不可提交
                let verifyTwoRes = await verifyIdenityTwo({
                    CompanyId: params.CompanyId,
                    Name: params.ManagerName,
                    LicenseId: params.ManagerLicenseId,
                })
                if (verifyTwoRes.IsMatch === false) {
                    let err = new Error(verifyTwoRes.Message)
                    err.code = 67130
                    return this.err(err)
                }
            }
            let order = await CrossBorderApplyModel.create(params)
            order = parseJSON(order)

            let logJSON = {
                ApplyId: order.Id,
                CompanyId: params.CompanyId,
                Operator: '用户：' + params.CompanyId,
                Remark:
                    params.Status === ApplyStatusEnum.Aduiting
                        ? '创建订单,审核中'
                        : '创建订单,编辑中',
            }
            await CrossBorderAuditLogModel.create(logJSON)
            // 异步 验证 身份证和营业执照
            params.Id = order.Id
            if (params.Status === ApplyStatusEnum.Aduiting) {
                verifyAndUpdateInfo(params)
            }

            return this.cb(0, { Id: order.Id })
        } catch (e) {
            console.log(e)
            e.code = 11003
            self.err(e)
        }
    }
}

function validateInput(params) {
    const {
        CompanyName,
        CompanyCode,
        LegalEntityName,
        BusinessPlace,
        LicenseIssuingAgency,
        PostalCode,
        BusinessLicenseBeginTime,
        BusinessLicenseEndTime,
        ManagerName,
        ManagerLicenseId,
        ManagerAddress,
        ManagerPhone,
        ManagerEmail,
        BusinessLicensePic,
        ManagerLicensePic,
        PromiseLetterPic,
        ProxyLetterPic,
        ServiceProtocolPic,
        UCloudProxyLetterPic,
    } = params
    if (
        !CompanyName?.trim() ||
        !CompanyCode?.trim() ||
        !LegalEntityName?.trim() ||
        !BusinessPlace?.trim() ||
        !LicenseIssuingAgency?.trim() ||
        !PostalCode?.trim() ||
        !BusinessLicenseBeginTime ||
        !BusinessLicenseEndTime ||
        !ManagerName?.trim() ||
        !ManagerLicenseId?.trim() ||
        !ManagerAddress?.trim() ||
        !ManagerPhone?.trim() ||
        !ManagerEmail?.trim() ||
        !BusinessLicensePic?.trim() ||
        !ManagerLicensePic?.trim() ||
        !PromiseLetterPic?.trim() ||
        !ProxyLetterPic?.trim() ||
        !ServiceProtocolPic?.trim() ||
        !UCloudProxyLetterPic.trim()
    ) {
        return false
    } else {
        return true
    }
}
