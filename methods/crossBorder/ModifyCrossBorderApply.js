/*
 * @Date: 2022-07-26 11:56:44
 * @LastEditors: l<PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2022-10-26 10:28:31
 * @FilePath: /newicp/methods/crossBorder/ModifyCrossBorderApply.js
 */
'use strict'

const { parseJSON } = require('../../fns/kits')
const Method = require('../../libs/method')
const {
    CrossBorderApplyModel,
    ApplyStatusEnum,
    CrossBorderAuditLogModel,
} = require('../../models')
const verifyAndUpdateInfo = require('../../fns/cross_border_apply/VerifyAndUpdateInfo')
const verifyIdenityTwo = require('../../fns/cross_border_apply/VerifyIdenityTwo')
const moment = require('moment')
const _ = require('lodash')

module.exports = class ModifyCrossBorderApply extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec(params = {}) {
        let self = this
        let currTime = moment().format('X')
        delete params.Error
        try {
            // Id 根据Id获取当前状态 校验是否可以修改
            let order = await CrossBorderApplyModel.findAll({
                where: {
                    Id: params.Id,
                    CompanyId: params.CompanyId,
                },
            })
            order = parseJSON(order)
            if (order.length === 0) {
                //订单不存在
                let err = new Error('该申请不存在')
                err.code = 67122
                return this.err(err)
            }
            order = order[0]
            //判断当前状态是否支持编辑 只有审核拒绝 编辑中 可以编辑
            if (
                ![
                    ApplyStatusEnum.AduitReject,
                    ApplyStatusEnum.Editing,
                ].includes(order.Status)
            ) {
                let err = new Error('该申请不支持修改')
                err.code = 67123
                return this.err(err)
            }
            // 校验状态，看状态是否是要提交，若要提交 则所有字段需要校验
            // 要提交
            // 1.校验字段是否正常传值
            // 2.校验营业执照是否已过期
            let logJSON

            if (params.Status === ApplyStatusEnum.Aduiting) {
                params.SubmitTime = currTime
                let verifyRes = validateInput(params)
                if (!verifyRes) {
                    let err = new Error('请检查是否有缺漏填写信息')
                    err.code = 10003
                    return this.err(err)
                }
                if (!/^[0-9A-HJ-NPQRTUWXY]{15}$|^[0-9A-HJ-NPQRTUWXY]{18}$/.test(params.CompanyCode?.trim())) {
                    let err = new Error('请输入正确的统一社会信用代码')
                    err.code = 10003
                    return this.err(err)
                }
                // 2.校验营业执照是否已过期
                if (currTime > params.BusinessLicenseEndTime) {
                    let err = new Error('该营业执照已过期，请检查')
                    err.code = 67121
                    return this.err(err)
                }
                // 先校验二要素，若二要素不一致 不可提交
                let verifyTwoRes = await verifyIdenityTwo({
                    CompanyId: params.CompanyId,
                    Name: params.ManagerName,
                    LicenseId: params.ManagerLicenseId,
                })
                if (verifyTwoRes.IsMatch === false) {
                    let err = new Error(verifyTwoRes.Message)
                    err.code = 67130
                    return this.err(err)
                }
                logJSON = {
                    CompanyId: params.CompanyId,
                    Operator: '用户：' + params.CompanyId,
                    Remark: '提交订单,审核中',
                    CreateTime: moment().format('X'),
                }
                params.Error = {}
            }
            let updateJSON = _.omit(params, ['Id'])

            await CrossBorderApplyModel.update(updateJSON, {
                where: {
                    Id: params.Id,
                },
            })
            if (params.Status === ApplyStatusEnum.Aduiting) {
                // 写日志
                await CrossBorderAuditLogModel.create(logJSON)
                // 提交审核校验 身份证和营业执照
                verifyAndUpdateInfo(params)
            }
            return this.cb(0)
        } catch (e) {
            console.log(e)
            e.code = 11003
            self.err(e)
        }
    }
}

function validateInput(params) {
    const {
        CompanyName,
        CompanyCode,
        LegalEntityName,
        BusinessPlace,
        LicenseIssuingAgency,
        PostalCode,
        BusinessLicenseBeginTime,
        BusinessLicenseEndTime,
        ManagerName,
        ManagerLicenseId,
        ManagerAddress,
        ManagerPhone,
        ManagerEmail,
        BusinessLicensePic,
        ManagerLicensePic,
        PromiseLetterPic,
        ProxyLetterPic,
        ServiceProtocolPic,
        UCloudProxyLetterPic,
    } = params
    if (
        !CompanyName?.trim() ||
        !CompanyCode?.trim() ||
        !LegalEntityName?.trim() ||
        !BusinessPlace?.trim() ||
        !LicenseIssuingAgency?.trim() ||
        !PostalCode?.trim() ||
        !BusinessLicenseBeginTime ||
        !BusinessLicenseEndTime ||
        !ManagerName?.trim() ||
        !ManagerLicenseId?.trim() ||
        !ManagerAddress?.trim() ||
        !ManagerPhone?.trim() ||
        !ManagerEmail?.trim() ||
        !BusinessLicensePic?.trim() ||
        !ManagerLicensePic?.trim() ||
        !PromiseLetterPic?.trim() ||
        !ProxyLetterPic?.trim() ||
        !ServiceProtocolPic?.trim() ||
        !UCloudProxyLetterPic?.trim()
    ) {
        return false
    } else {
        return true
    }
}
