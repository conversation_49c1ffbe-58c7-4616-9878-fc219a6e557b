/*
 * @Date: 2022-08-03 11:57:47
 * @LastEditors: li<PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2022-09-01 17:00:33
 * @FilePath: /newicp/methods/crossBorder/GetCrossBorderApplyStatus.js
 */
/* 获取账号当前跨境报备状态
 * @Date: 2022-07-26 11:56:44
 * @LastEditors: li<PERSON><PERSON>en <EMAIL>
 * @LastEditTime: 2022-08-03 11:26:41
 * @FilePath: /newicp/methods/admin/Common/GetCrossBorderApplyList.js
 */
'use strict'

const { parseJSON } = require('../../fns/kits')
const Method = require('../../libs/method')
const { CrossBorderApplyModel, ApplyStatusEnum } = require('../../models')
const frontCreateApplyCheck = require('../../fns/cross_border_apply/FrontCreateApplyCheck')

module.exports = class GetCrossBorderApplyStatus extends Method {
    constructor(cb) {
        super(cb)
    }
    /** 如果渠道是 1 直接查账户数据 如果不是1先查专属云数据 专属云报错了 说不是她渠道的 再查账户数据 没办法 渠道太多了 维护不方便
     * @param {integer} CompanyId
     * @returns
     *"CanApply": true, 是否可以新建申请 （条件：1,含有有效的企业认证，2，不存在任何订单，删除的不算）
     *"FinishApply": true, 是否存在已完成审核的有效订单
     */
    async exec({ CompanyId, channel = 1 } = {}) {
        let self = this
        try {
            let FinishApply = false,
                CanApply = false

                 // 实名信息校验
            let verifyRes = await frontCreateApplyCheck({ CompanyId, ChannelId: channel})
            
            if (!verifyRes) {
                return this.cb(0, {
                    FinishApply,
                    CanApply,
                })
            }
           
            // 能到这里 就说明有企业认证了  下面就看是否有申请记录了
            let { count, rows } = await CrossBorderApplyModel.findAndCountAll({
                where: {
                    CompanyId,
                    IsDeleted: 0,
                },
            })
            rows = parseJSON(rows)
            if (count !== 0) {
                // 存在非删除订单，CanApply为false
                // 存在审核完成的 将FinishApply 设置为true
                let auditPass = rows.filter(
                    (r) =>
                        r.Status === ApplyStatusEnum.AduitPass ||
                        r.Status === ApplyStatusEnum.Expired
                )
                if (auditPass.length > 0) {
                    FinishApply = true
                }
            } else {
                // 没有订单 且有企业认证
                CanApply = true
            }
            return this.cb(0, {
                FinishApply,
                CanApply,
            })
        } catch (e) {
            console.log(e)
            e.code = 11003
            self.err(e)
        }
    }
}
