/*
 * @Date: 2022-07-26 11:56:44
 * @LastEditors: li<PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2022-10-26 10:22:19
 * @FilePath: /newicp/methods/crossBorder/GetCrossBorderApplyLog.js
 */
'use strict'

const { parseJSON } = require('../../fns/kits')
const Method = require('../../libs/method')
const {
    CrossBorderAuditLogModel,
    VerifyLogModel,
    VerifyLogTypeEnum,
    VerifyLogTypeCnEnum,
} = require('../../models')
const moment = require('moment')
const _ = require('lodash')
const { Op } = require('sequelize')

module.exports = class GetCrossBorderApplyList extends Method {
    constructor(cb) {
        super(cb)
    }

    async exec({ CompanyId } = {}) {
        try {
            let self = this
            let logs = []
            let [applyLog, verifyLog] = await Promise.all([
                CrossBorderAuditLogModel.findAll({
                    where: {
                        CompanyId,
                    },
                }),
                await VerifyLogModel.findAll({
                    where: {
                        OrderNo: 'CrossBorder_' + CompanyId,
                        CompanyId,
                        Type: {
                            [Op.in]: [
                                VerifyLogTypeEnum.GetIdenityOCRInfo,
                                VerifyLogTypeEnum.ValidatePersonalTwo,
                                VerifyLogTypeEnum.ValidateBusinessLicenseInfo,
                            ],
                        },
                    },
                }),
            ])
            applyLog = parseJSON(applyLog)
            verifyLog = parseJSON(verifyLog)
            verifyLog = verifyLog.map((log) => {
                let resMsg
                if (log.Result === 0 && log.ReturnObject.IsMatch) {
                    resMsg = VerifyLogTypeCnEnum[parseInt(log.Type)] + "'通过'"
                } else {
                    resMsg =
                        VerifyLogTypeCnEnum[parseInt(log.Type)] +
                            "'不通过'," +
                            '详情：' +
                            log.ReturnObject.Message || '无'
                }

                return {
                    Id: log.Id,
                    CompanyId: log.CompanyId,
                    Operator: log.Operator,
                    CreateTime: log.CreateTime,
                    Remark: resMsg,
                }
            })
            logs = applyLog.concat(verifyLog)

            logs.sort((a, b) => {
                if (a.CreateTime > b.CreateTime) {
                    return -1
                } else {
                    return 1
                }
            })

            return this.cb(0, {
                Logs: logs,
            })
        } catch (e) {
            console.log(e)
            e.code = 11003
            self.err(e)
        }
    }
}
