/*
 * @Date: 2022-09-26 12:05:47
 * @LastEditors: l<PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2023-01-30 15:11:34
 * @FilePath: /newicp/methods/crossBorder/GetCrossBorderApplyList.js
 */
/* 获取跨境报备列表
 * @Date: 2022-07-26 11:56:44
 * @LastEditors: li<PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2022-08-03 11:26:41
 * @FilePath: /newicp/methods/admin/Common/GetCrossBorderApplyList.js
 */
'use strict'

const { parseJSON } = require('../../fns/kits')
const Method = require('../../libs/method')
const { CrossBorderApplyModel, ApplyStatusEnum } = require('../../models')
const _ = require('lodash')

module.exports = class GetCrossBorderApplyList extends Method {
    constructor(cb) {
        super(cb)
    }
    /**
     * @param {Int32} Id 申请记录的Id
     * @param {Int32} Status 状态 详情见 ApplyStatusEnum
     * @param {Int32} CompanyId 用户公司Id
     * @param {String} CompanyName 公司名称
     * @param {String} CompanyCode 公司统一信用代码
     * @param {String} ManagerName 经办人名称
     * @param {Int32} ManagerLicenseId 经办人证件号
     * @param {Int32} Source 请求来源 用来区分客户侧和审核侧
     * @returns
     */
    async exec(params = {}) {
        let self = this
        try {
            let queryJson = _.pick(params, [
                'Id',
                'Status',
                'CompanyId',
                'CompanyName',
                'CompanyCode',
                'ManagerName',
                'ManagerLicenseId',
            ])
            // 若非审核侧 且无CompanyId, 则返回空
            if (params.Source !== 'Hegui' && !params.CompanyId) {
                return this.cb(0, {
                    Rows: [],
                    TotalCount: 0,
                })
            }
            queryJson.IsDeleted = 0
            // 内部网关调用时 无companyID CompanyId为undefined mysql查询报错
            if (!params.CompanyId) {
                delete queryJson.CompanyId
            }
            let conditions = { where: queryJson }

            // 判断是否要获取全量
            if (params.GetAll === true) {
                // TODO:如果是获取全量的 则不做分页和 排序
            } else {
                conditions.order = [
                    ['update_time', 'desc'],
                    ['id', 'desc'],
                ]
                conditions.offset = params.Offset || 0
                conditions.limit = params.Limit || 20
            }
            let { count, rows } = await CrossBorderApplyModel.findAndCountAll(
                conditions
            )
            rows = parseJSON(rows)
            if (count === 0) {
                return this.cb(0, {
                    Rows: [],
                    TotalCount: 0,
                })
            }
            return this.cb(0, {
                Rows: rows,
                TotalCount: count,
            })
        } catch (e) {
            console.log(e)
            e.code = 11003
            self.err(e)
        }
    }
}
