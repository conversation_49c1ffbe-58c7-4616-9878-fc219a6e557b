/*
 * @Author: william.qian <EMAIL>
 * @Date: 2023-10-16 15:59:06
 * @LastEditors: william.qian <EMAIL>
 * @LastEditTime: 2023-10-18 11:53:24
 * @FilePath: /newicp/methods/common/CheckICPCompanyIdAndNameMatch.js
 * @Description: 该接口用于验证公司信息，需要传入公司名和证件号，查询天眼查确定数据是否一致
 */
const Method = require('../../libs/method')
const { checkCompanyInfo } = require('../../fns/checkOrder')

module.exports = class CheckICPCompanyIdAndNameMatch extends Method {
    constructor(cb) {
        super(cb)
    }

    async exec({ OrganizerName, OrganizerLicenseId } = {}) {
        try {
            let redis = this.redis.get()

            let result = await checkCompanyInfo({
                OrganizerName,
                OrganizerLicenseId,
                redis: redis,
            })
            return this.cb(0, { Result: result })
        } catch (e) {
            if (e instanceof Error) {
                return this.eh({
                    err: e,
                    code: 67086,
                })
            }
            this.eh(e)
        }
    }
}
