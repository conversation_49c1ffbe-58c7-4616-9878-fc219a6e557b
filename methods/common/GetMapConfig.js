/*
 * @Date: 2023-07-04 14:35:38
 * @LastEditors: l<PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2023-07-04 15:41:02
 * @FilePath: /newicp/methods/common/GetMapConfig.js
 */
const Method = require('../../libs/method')

module.exports = class GetMapConfig extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({ Key } = {}) {
        let self = this
        let redis = this.redis.get()
        try {
            if (Key) {
                let res = await redis.hgetall(`Chinese-English-Map:${Key}`)
                return this.cb(0, res)
            } else {
                let [
                    Main,
                    Web,
                    OrderType,
                    OrderStatus,
                    ICPStatus,
                    ICPWebStatus,
                ] = await Promise.all([
                    redis.hgetall(`Chinese-English-Map:Main`),
                    redis.hgetall(`Chinese-English-Map:Web`),
                    redis.hgetall(`Chinese-English-Map:OrderType`),
                    redis.hgetall(`Chinese-English-Map:OrderStatus`),
                    redis.hgetall(`Chinese-English-Map:ICPStatus`),
                    redis.hgetall(`Chinese-English-Map:ICPWebStatus`),
                ])
                return this.cb(0, {
                    Main,
                    Web,
                    OrderType,
                    OrderStatus,
                    ICPStatus,
                    ICPWebStatus,
                })
            }
        } catch (e) {
            console.log(e)
            if (e instanceof Error) {
                return self.eh({
                    err: e,
                    code: 30103,
                })
            }
            self.eh(e)
        }
    }
}
