'use strict'

const Method = require('../../libs/method')
const callOpenAIGPT4 = require('../../libs/requestOpenAi')

module.exports = class AIAnswer extends Method {
    constructor(cb) {
        super(cb)
    }

    async exec({ UserPrompt, SystemPrompt, Source } = {}) {
        let self = this
        let aiRes = ''
        try {
            let messages = [
                {
                    role: 'system',
                    content: SystemPrompt,
                },
                {
                    role: 'user',
                    content: UserPrompt,
                },
            ]
            aiRes = await callOpenAIGPT4(messages)

            // 日志记录，来源，时间，花费token
            // 下次再整合再做
            aiRes = aiRes.choices[0]?.message?.content

            return self.cb(0, {
                AIRes: aiRes,
            })
        } catch (e) {
            if (e instanceof Error) {
                return self.eh({
                    err: e,
                    code: 36059,
                })
            }
            self.eh(e)
        }
    }
}
