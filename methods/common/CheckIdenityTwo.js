// 身份证二要素（姓名，身份证号）公安校验，信息是否正确

const Method = require('../../libs/method')
const verify_type = require('../../configs/common/verify_api_type.json')
const {
    insertLog,
    getVerifyLog,
    updateLog,
    verifyAPI,
} = require('../../fns/verifyFun')
const { getTableModel } = require('../../fns/kits')

// 验证身份证二要素
module.exports = class CheckIdenityTwo extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({
        __ssoUser,
        ForceSelect,
        UUID,
        CompanyId,
        OrderNo,
        Source, //用来区分ICP还是CrossBorderApply服务
        Name, // 姓名
        LicenseId, //身份证号
        Remark,
    } = {}) {
        let Operator, logId, result, multiplex
        // 字段是否复用
        result = {}
        multiplex = 1
        Operator = __ssoUser || 'system.auto'
        let self = this

        // 如有身份证，统一做大写转换
        if (LicenseId) {
            LicenseId = LicenseId.toUpperCase()
        }
        let icpDatabase = this.db.get('icp')
        let LogModel = getTableModel('t_verify_log', icpDatabase)
        try {
            let params = {
                Name: Name,
                Id: LicenseId,
                UUID: UUID,
                Operator,
                ActionIndex: 1,
                OrderNo: OrderNo ?? (Source ? Source + '_' + CompanyId : ''), //无OrderNo使用Source_CompanyId标记，否则为''
                Remark,
                CompanyId: CompanyId,
                Backend: 'IdAuth',
                Action: 'VerifyBankCard2',
                Source: 'ICP',
            }

            if (UUID) {
                params.UUID = UUID
            }
            params['Type'] = verify_type.ValidatePersonalTwo

            // 执行日志
            logId = await insertLog(params, LogModel)

            // 如果没有强制验证，则查询旧记录

            try {
                if (!ForceSelect) {
                    // 验证是否此参数是否验证过,Picture需要解析，从URL转到文件名getFileName

                    result = await getVerifyLog(
                        {
                            OrderNo: params.OrderNo,
                            ActionIndex: params.ActionIndex,
                            Id: params.Id,
                            Name: params.Name,
                            Type: params.Type,
                        },
                        logId,
                        LogModel
                    )
                }

                // 如果未取到，或者之前的请求为出错，重试
                if (
                    result === null ||
                    JSON.stringify(result) === '{}' ||
                    result.Error ||
                    JSON.stringify(result.Response) === '{}'
                ) {
                    result = await verifyAPI({
                        Action: 'VerifyBankCard2',
                        body: params,
                    })
                    multiplex = 0
                }
            } catch (error) {
                await Promise.reject(new self.Err(error, 31301))
            }

            // 更新结果到数据库
            result.Multiplex = multiplex
            try {
                await updateLog(result, logId, LogModel)
            } catch (error) {
                await Promise.reject(new self.Err(error, 31302))
            }

            let returnObject = {}
            let returnCode = 0
            // 结果匹配
            if (result.Error && result.Response.RetCode !== 0) {
                returnCode = 31304
                returnObject = result.Response
            } else {
                returnObject.IsMatch = result.Response.IsMatch
                returnObject.Message =
                    result.Response.Message ||
                    (returnObject.IsMatch ? '验证通过' : '姓名或者证件号不合规')
            }

            await updateLog({ ReturnObject: returnObject }, logId, LogModel)

            this.cb(returnCode, returnObject)
        } catch (e) {
            if (e instanceof Error) {
                return self.eh({
                    err: e,
                    code: 31303,
                })
            }
            self.eh(e)
        }
    }
}
