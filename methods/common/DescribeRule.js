/*
 * @Author: william.qian <EMAIL>
 * @Date: 2022-06-01 10:29:02
 * @LastEditors: william.qian <EMAIL>
 * @LastEditTime: 2023-06-09 17:18:24
 * @FilePath: /newicp/methods/common/DescribeRule.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
/**
 * @file API DescribeRule,获取Mongo中的配置列表
 * <AUTHOR>
 * @return {Object} 返回符合条件的记录,格式如下
 * {
    "rule": [{"rule": {....}],
    "RetCode": 0
	}
 * 错误码:
	"xxx": "获取Redis线上配置时网络异常", 
 */

const Method = require('../../libs/method')
const { findAsync, countAsync } = require('../../fns/mongoFuns')
const ObjectId = require('mongodb').ObjectId

module.exports = class DescribeRule extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({ Id, Enable = true } = {}) {
        let self = this
        let mongo = this.mongo.get('icp-config')
        let ruleCollection = mongo.collection('rules')

        let result, findObject, countNumber
        try {
            // 查询Redis
            findObject = {}

            findObject.Enable = Enable

            if (Id) {
                findObject['_id'] = ObjectId(Id)
            }

            console.log(findObject)

            result = await findAsync(ruleCollection, findObject, {
                sort: { UnixTime: 1 },
            })

            result.map((record) => {
                record.Id = record['_id']
                delete record['_id']
            })
            return self.cb(0, { RuleList: result })
        } catch (e) {
            if (e instanceof Error) {
                return self.eh({
                    err: e,
                    code: 30103,
                })
            }
            self.eh(e)
        }
    }
}
