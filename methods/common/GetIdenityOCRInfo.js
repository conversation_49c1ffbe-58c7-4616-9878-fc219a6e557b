'use strict'
const Method = require('../../libs/method')
const verify_type = require('../../configs/common/verify_api_type.json')
const {
    insertLog,
    getVerifyLog,
    updateLog,
    verifyAPI,
    getFileName,
    getUrl,
} = require('../../fns/verifyFun')
const { getTableModel, getVerifyResult } = require('../../fns/kits')

// 身份证OCR后做姓名，证件号的匹配。
// 用户侧客户通过consoleRouter,经Map转换后。被访问到
// 2021年07月21日16:40:23 新增对正反面的判断
/**
 * 只支持 image/jpeg,image/png
 * 备注： 若入参 无Name IdCardNumber 也可获取到Info的信息自行对比 若有入参则可参考 下面字段信息判断是否匹配
 *  "OCRMatch": false,
    "IsMatch": false,
    "Message": "OCR姓名与证件Id不匹配"
 */

module.exports = class GetIdenityOCRInfo extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({
        PictureName,
        Url,
        __ssoUser,
        ForceSelect,
        UUID,
        Remark,
        CompanyId,
        OrderNo,
        Name,
        IdCardNumber,
        LicenseDate,
        Side,
        Source = 'ICP',
    } = {}) {
        let Operator, logId, result, multiplex
        // 字段是否复用
        result = {}
        multiplex = 1
        Operator = __ssoUser || 'system.auto'
        let self = this

        Url = getUrl(Url, PictureName)
        let icpDatabase = this.db.get('icp')
        let LogModel = getTableModel('t_verify_log', icpDatabase)
        try {
            let params = {
                IdPhoto: Url || PictureName,
                CompanyId: CompanyId,
                Remark,
                Backend: 'IdAuth',
                Action: 'GetIdenityOCRInfo',
                Source,
                Side,
                OrderNo: OrderNo ?? (Source ? Source + '_' + CompanyId : ''),
                Name,
                IdCardNumber,
                LicenseDate,
                Operator,
            }

            if (UUID) {
                params.UUID = UUID
            }
            params['Type'] = verify_type.GetIdenityOCRInfo

            // 执行日志
            logId = await insertLog(params, LogModel)

            // 如果没有强制验证，则查询旧记录
            try {
                if (!ForceSelect) {
                    // 验证是否此参数是否验证过,此处实际不需要，URL会更新

                    result = await getVerifyLog(
                        {
                            OrderNo: params.OrderNo,
                            IdPhoto: getFileName(Url),
                            Type: verify_type.GetIdenityOCRInfo,
                            Side,
                        },
                        logId,
                        LogModel
                    )
                    console.log(result, 3113)
                }

                // 如果未取到，或者之前的请求为出错，重试
                if (
                    result === null ||
                    JSON.stringify(result) === '{}' ||
                    result.Error === true ||
                    JSON.stringify(result.Response) === '{}'
                ) {
                    result = await verifyAPI({
                        Action: 'GetIdenityOCRInfo',
                        body: params,
                    })
                    multiplex = 0
                }
            } catch (error) {
                await Promise.reject(new self.Err(error, 30703))
            }

            // 更新结果到数据库
            result.Multiplex = multiplex
            try {
                await updateLog(result, logId, LogModel)
            } catch (error) {
                await Promise.reject(new self.Err(error, 30702))
            }

            let returnObject = {}
            let returnCode = 0
            // 结果匹配
            if (result.Error && result.Response.RetCode !== 0) {
                returnCode = 67050
                returnObject = result.Response
            } else {
                // 如果是反面，做下是否过期的判断
                returnObject = getVerifyResult(
                    result.Response,
                    verify_type.GetIdenityOCRInfo,
                    {
                        Name,
                        IdCardNumber,
                        Side,
                        ...(LicenseDate && { LicenseDate }),
                    }
                )
            }

            await updateLog({ ReturnObject: returnObject }, logId, LogModel)

            if (returnObject.Message) {
                // 出错信息Mock成中文
                // IMAGE_FILE_TOO_LARGE	图片文件过大，不超过2M
                // INVALID_IMAGE_SIZE	图片像素过大，不超过1000*1000
                returnObject.Message = returnObject.Message.replace(
                    'IMAGE_FILE_TOO_LARGE',
                    '图片文件过大，不超过2M'
                )
                returnObject.Message = returnObject.Message.replace(
                    'INVALID_IMAGE_SIZE',
                    '图片像素过大，不超过1000*1000'
                )
            }

            this.cb(returnCode, returnObject)
        } catch (e) {
            console.log(e)
            if (e instanceof Error) {
                return self.eh({
                    err: e,
                    code: 30701,
                })
            }
            self.eh(e)
        }
    }
}
