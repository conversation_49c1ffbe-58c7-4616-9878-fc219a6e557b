const Method = require('../../libs/method')
const verify_type = require('../../configs/common/verify_api_type.json')
const {
    insertLog,
    getVerifyLog,
    updateLog,
    verifyAPI,
} = require('../../fns/verifyFun')
const { getTableModel, pickMainDomain } = require('../../fns/kits')
const fromOrganizerLicenseType = require('../../configs/common/organizer_license_type.json')
const toRegisterOrganizerLicenseType = require('../../configs/common/register_organizer_license_type.json')
const uuid = require('uuid')
const _ = require('lodash')
module.exports = class CheckDomainRegisterMsg extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({
        UUID,
        __ssoUser,
        ForceSelect,
        DomainInfo,
        Remark,
        CompanyId,
        TypeTranslate, // 备案订单中的证件类型 需要转换为 check是对应的证件类型号码
        OrderNo,
    } = {}) {
        let result = {}
        let logId, multiplex
        // multiplex代表是否使用最近的缓存，1：使用，0：未使用
        multiplex = 1
        ForceSelect = true
        let self = this
        try {
            let redis = this.redis.get()
            let ICPConfig = await redis.get('DescribeICPConfig')
            ICPConfig = JSON.parse(ICPConfig)
            let effectiveDomain = ICPConfig.EffectiveDomain
            let icpDatabase = this.db.get('icp')
            let LogModel = getTableModel('t_verify_log', icpDatabase)
            let params = {
                Action: 'CheckDomainRegisterMsg',
                Operator: __ssoUser,
                UUID: UUID || uuid.v4(),
                Remark,
                OrderNo,
                CompanyId,
                Backend: 'IdAuth',
                Source: 'ICP',
                Data: DomainInfo,
            }
            params['Type'] = verify_type.CheckDomainRegisterMsg
            

            // 如果有TypeTranslate类型转换，开始转换证件类型
            try {
                for (let index = 0; index < DomainInfo.length; index++) {
                    if (TypeTranslate) {
                        DomainInfo[index].certType = convertType(
                            DomainInfo[index].certType
                            )
                    }
                    // 主要是为了保存验证前和验证后的调整
                    let tempD = DomainInfo[index].domain
                    DomainInfo[index].subDomain = tempD
                    DomainInfo[index].domain = pickMainDomain(tempD, effectiveDomain)
                }
            } catch (error) {
                let err = new Error('域名提取转换过程出错：' + error.message)
                err.code = 34305
                throw err
            }

            // 执行日志 这里主要存储用户输入的
            logId = await insertLog(params, LogModel)
           
            console.log(DomainInfo)
            if (!ForceSelect) {
                // 验证是否此参数是否验证过,此处实际不需要，URL会更新
                result = await getVerifyLog(
                    {
                        OrderNo: OrderNo || '',
                        Data: DomainInfo,
                        Type: verify_type.CheckDomainRegisterMsg,
                    },
                    logId,
                    LogModel,
                    1 // 复用1天内的
                )
            }
            if (
                result === null ||
                JSON.stringify(result) === '{}' ||
                result.Error === true                           
            ) {
                result = await verifyAPI({
                    Action: 'CheckDomainRegisterMsg',
                    body: params,
                })
                multiplex = 0
            }

            // 更新结果到数据库
            result.Multiplex = multiplex

            let returnObject = {},
                IsMatch = true,
                returnCode = 0

            try {
                // 结果匹配
                if (result.Error && result.Response.RetCode !== 0) {
                    returnCode = 35301
                    returnObject = result.Response
                    returnObject.IsMatch = false
                } else {
                    let CompRes = [],
                        Message = ''
                    result.Response.compRes.forEach((res) => {
                        let thisContrast = {
                            registrant: false,
                            certType: false,
                            certNo: false,
                        }
                        if (res.results !== 0 && res.results !== 2) {
                            // 如果有一项的结果不为0与2，匹配失败，有Message
                            IsMatch = false
                            // 核心数据提取，具体不匹配的项,注册商
                        }
                        let thisDomainNotMatch = [] // 保存不匹配的项
                        if (
                            res.registrarContrastDetails &&
                            res.registrarContrastDetails.length !== 0
                        ) {
                            // 检查3项结果

                            for (
                                let index = 0;
                                index < res.registrarContrastDetails.length;
                                index++
                            ) {
                                const element =
                                    res.registrarContrastDetails[index]

                                if ('registrant' in element) {
                                    thisContrast.registrant =
                                        element.results === 0
                                    if (!thisContrast.registrant) {
                                        thisDomainNotMatch.push('注册名称')
                                    }
                                }
                                if ('certType' in element) {
                                    thisContrast.certType =
                                        element.results === 0
                                    if (!thisContrast.certType) {
                                        thisDomainNotMatch.push('证件类型')
                                    }
                                }
                                if ('certNo' in element) {
                                    thisContrast.certNo = element.results === 0
                                    if (!thisContrast.certNo) {
                                        thisDomainNotMatch.push('证件号码')
                                    }
                                }
                            }

                            if (thisDomainNotMatch.length !== 0) {
                                Message =
                                    Message +
                                    `${
                                        res.domain
                                    }备案信息与域名实名信息（${thisDomainNotMatch.join(
                                        ','
                                    )}）不一致; `
                            }
                        } else {
                            Message = Message + `${res.domain}:未报送; `
                        }
                        console.log(Message, 311)
                        CompRes.push({
                            Domain: res.domain,
                            ReportStatus: res.reportStatus,
                            Results: res.results,
                            Contras: thisContrast,
                            RegistrarContrastDetails:
                                res.registrarContrastDetails,
                            RegistryContrastDetails:
                                res.registryContrastDetails,
                        })
                    })
                    returnObject = {
                        CompRes,
                        IsMatch,
                    }
                    if (Message !== '') {
                        returnObject.Message = Message
                    }
                }

                result.ReturnObject = returnObject
                await updateLog(result, logId, LogModel)
            } catch (error) {
                console.log(error)
                let err = new Error('更新请求结果到数据库出错')
                err.code = 35302
                throw err
            }

            this.cb(returnCode, returnObject)
        } catch (e) {
            console.log(e)
            self.err(e)
        }
    }
}

function convertType(original) {
    // 43 44 直接对应到30的其他里面
    let cn = original
    if (!_.isNaN(parseInt(original))) {
        if (original === 43 || original === 44) {
            return 30
        }
        cn = fromOrganizerLicenseType[original]
    }
    // 再根据 相同的中文 转换为 对应的数字
    return toRegisterOrganizerLicenseType[cn]
}
