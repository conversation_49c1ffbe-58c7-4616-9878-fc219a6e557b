const Method = require('../../libs/method')
const verify_type = require('../../configs/common/verify_api_type.json')
const {
    insertLog,
    getVerifyLog,
    updateLog,
    verifyAPI,
} = require('../../fns/verifyFun')
const { getTableModel, pickMainDomain } = require('../../fns/kits')
const moment = require('moment')
const uuid = require('uuid')

/**
 * 通过域名输入
 * 获取域名的注册信息，包含
 * 域名持有者、注册商名称、注册时间、过期时间、更新时间、IANA标记、批复状态等
 */
module.exports = class GetDomainRegisterMsg extends Method {
    constructor(cb) {
        super(cb)
    }
    // ForceSelect true 强制重新获取，false || undefine 从最近的复用
    async exec({
        UUID,
        __ssoUser,
        ForceSelect,
        Domain,
        CompanyId,
        OrderNo,
        Remark,
    } = {}) {
        let result = {}
        let logId, multiplex
        // multiplex 代表是否复用 1:复用 0：未复用
        multiplex = 1
        ForceSelect = true
        let self = this

        try {
            let redis = this.redis.get()
            let ICPConfig = await redis.get('DescribeICPConfig')
            ICPConfig = JSON.parse(ICPConfig)
            let effectiveDomain = ICPConfig.EffectiveDomain
            let icpDatabase = this.db.get('icp')
            let LogModel = getTableModel('t_verify_log', icpDatabase)
            let params = {
                Action: 'GetDomainRegisterMsg',
                Operator: __ssoUser,
                UUID: UUID || uuid.v4(),
                Backend: 'IdAuth',
                Source: 'ICP',
                CompanyId,
                Domain,
                SubDomain: Domain,
                OrderNo,
                Remark,
            }
            params['Type'] = verify_type.GetDomainRegisterMsg
            let tempD = Domain
            params.SubDomain = tempD // 记录可能的二级域名
            try {
                params.Domain = pickMainDomain(tempD, effectiveDomain)
            } catch (e) {
                let err = new Error('域名提取转换过程出错：' + e.message)
                err.code = 34305
                throw err
            }
            // 执行日志  params 参数用来记录请求日志
            logId = await insertLog(params, LogModel)

            if (!ForceSelect) {
                // 验证是否此参数是否验证过,此处实际不需要，URL会更新
                result = await getVerifyLog(
                    {
                        OrderNo: OrderNo || '',
                        Domain,
                        Type: verify_type.GetDomainRegisterMsg,
                    },
                    logId,
                    LogModel,
                    1 // 复用1天内的
                )
            }
            if (
                result === null ||
                JSON.stringify(result) === '{}' ||
                result.Error === true
            ) {
                result = await verifyAPI({
                    Action: 'GetDomainRegisterMsg',
                    body: params,
                })
                multiplex = 0
            }

            // 更新结果到数据库
            result.Multiplex = multiplex

            let returnObject = {}
            let returnCode = 0

            try {
                // 结果匹配
                if (result.Error && result.Response.RetCode !== 0) {
                    returnCode = 35303
                    returnObject = result.Response
                } else {
                    let ReportStatus = result.Response.reportStatus
                    if (ReportStatus === 1) {
                        //未报送, 设置过期默认值为true
                        returnObject = {
                            ReportStatus,
                            IsExpired: true,
                            Message: Domain + ',未报送',
                        }
                    } else {
                        let IsExpired = result.Response.detailResults
                            ? result.Response.detailResults.filter(
                                  (res) =>
                                      moment().format('X') -
                                          moment(res.expirationTime).format(
                                              'X'
                                          ) >
                                      0
                              ).length !== 0
                            : true
                        returnObject = {
                            ReportStatus,
                            IsExpired,
                            DetailResults: result.Response.detailResults,
                        }
                        if (IsExpired) {
                            if (
                                moment(
                                    returnObject.DetailResults[0].expirationTime
                                )
                                    .add(45, 'days')
                                    .format('X') > moment().format('X')
                            ) {
                                // 在赎回期内
                                returnObject.Message =
                                    Domain + ',（已报送）赎回期'
                            } else {
                                //过了45天的赎回期了
                                returnObject.Message =
                                    Domain + ',（已报送）已过期'
                            }
                        } else {
                            returnObject.Message = Domain + ',（已报送）未过期'
                        }
                    }
                }
                result.ReturnObject = returnObject
                await updateLog(result, logId, LogModel)
            } catch (error) {
                let err = new Error('更新请求结果到数据库出错')
                err.code = 35304
                throw err
            }

            this.cb(returnCode, returnObject)
        } catch (e) {
            console.log(e)
            self.err(e)
        }
    }
}
