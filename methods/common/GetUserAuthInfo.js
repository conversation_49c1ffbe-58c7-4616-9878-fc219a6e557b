'use strict'
/* 通过公司ID获取认证信息
 * @Date: 2022-07-25 16:03:04
 * @LastEditors: l<PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2022-07-25 18:03:51
 * @FilePath: /newicp/methods/admin/Common/GetUserAuthInfo.js
 */

const Method = require('../../libs/method')
const getUserAuthInfo = require('../../fns/auth/getUserAuthInfo')
module.exports = class GetUserAuthInfo extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({ CompanyId, AuthType } = {}) {
        let self = this
        try {
            let AuthInfo
            let authInfos = await getUserAuthInfo(CompanyId, AuthType)
            AuthInfo = authInfos
            return this.cb(0, { AuthInfo })
        } catch (e) {
            let err = new Error('获取用户认证信息失败' + e.message)
            err.code = 67133
            self.err(err)
        }
    }
}
