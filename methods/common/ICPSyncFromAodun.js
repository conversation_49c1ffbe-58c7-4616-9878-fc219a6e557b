/* 从傲盾同步已备案的信息
 * 1.查询当前备案号的备案信息
 * 2.将数据传送至傲盾，获取差异项目，insert update delete，让傲盾中间服务来处理两边字段的转换
 * 3.数据更新 日志记录
 * 4.redis缓存更新
 * @Date: 2023-05-23 13:47:35
 * @LastEditors: li<PERSON>ew<PERSON> <EMAIL>
 * @LastEditTime: 2023-06-08 11:53:56
 * @FilePath: /newicp/methods/admin/ICPInfo/ICPSyncFromAodun.js
 */
const Method = require('../../libs/method')
const { parseJSON } = require('../../fns/kits')
const {
    ICPModel,
    ICPWebModel,
    UpdateLogModel,
    UpdateLogTypeEnum,
} = require('../../models')
const _ = require('lodash')
const { ICPWebPlatInfoModel } = require('../../mongoModels/icp')
const axiosAPI = require('../../libs/axiosApi')
const moment = require('moment')
module.exports = class ICPSyncFromAodun extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({ ICPMainNo, ResolveOrder = false, __ssoUser } = {}) {
        let self = this
        const producer = this.producer
        try {
            let ICPInfo = await ICPModel.findAll({
                where: {
                    ICPMainNo,
                },
                include: [
                    {
                        model: ICPWebModel,
                        as: 'Website',
                    },
                ],
            })
            ICPInfo = parseJSON(ICPInfo)
            if (ICPInfo.length === 0) {
                // 我司没有发现该备案
            }
            ICPInfo = ICPInfo[0]
            // 有备案, check一下是否包含APP备案 包含的话需要提取平台信息列表
            let ids = ICPInfo.Website?.map((item) => item.Id)
            if (ids.length > 0) {
                let ICPWebPlatInfos = await ICPWebPlatInfoModel.find({
                    ICPWebId: {
                        $in: ids,
                    },
                })
                ICPInfo.Website.forEach((web) => {
                    if (web.InternetServiceType === 6) {
                        web.AppPlatformInformationList =
                            _.find(ICPWebPlatInfos, { ICPWebId: web.Id })
                                ?.AppPlatformInformationList || []
                    }
                })
            }
            // 将在我司的ICP信息作为参数，向傲盾要差异
            let config = {
                url: global.CONFIG.verifyAPI.epicEntPersonRelationUrl,
                method: 'POST',
                data: {
                    Action: 'GetTheICPDifference',
                    ...ICPInfo,
                },
            }

            let ICPUpdate
            try {
                let res = await axiosAPI(config)
                ICPUpdate = res.data
                if (ICPUpdate?.RetCode !== 0) {
                    return self.cb(32911, {
                        Message:
                            ICPUpdate.Message || '请求傲盾查询备案差异出错',
                    })
                }
            } catch (e) {
                return self.cb(32911, {
                    Message: '请求傲盾查询备案差异网络错误' + e.message,
                })
            }
            let errInfo = {
                body: '',
            }
            // 根据返回的差异 执行更新，并记录更新日志
            try {
                if (JSON.stringify(ICPUpdate.MainUpdateJson) !== '{}') {
                    // 主体更新
                    await ICPModel.update(ICPUpdate.MainUpdateJson, {
                        where: {
                            Id: ICPInfo.Id,
                        },
                    })
                }
            } catch (err) {
                errInfo.body = '主体更新出错' + err.message
            }

            try {
                // 网站处理 ，增加
                if (ICPUpdate.WebUpdateJson?.insert?.length > 0) {
                    for (let web of ICPUpdate.WebUpdateJson.insert) {
                        // 查看是否是APP备案类型 若是的话需要处理平台信息
                        let insertInfo = await ICPWebModel.create(web)
                        insertInfo = parseJSON(insertInfo)
                        if (web.AppPlatformInformationList?.length > 0) {
                            await ICPWebPlatInfoModel.findOneAndUpdate(
                                { ICPWebId: insertInfo.Id },
                                {
                                    $set: {
                                        AppPlatformInformationList:
                                            web.AppPlatformInformationList,
                                    },
                                },
                                { upsert: true }
                            )
                        }
                    }
                }
            } catch (err) {
                errInfo.body += '网站插入出错' + err.message
            }
            try {
                // 网站处理 ，更新
                if (JSON.stringify(ICPUpdate.WebUpdateJson?.update) !== '{}') {
                    for (let webId in ICPUpdate.WebUpdateJson.update) {
                        await ICPWebModel.update(
                            ICPUpdate.WebUpdateJson.update[webId],
                            {
                                where: {
                                    Id: webId,
                                },
                            }
                        )
                        if (
                            ICPUpdate.WebUpdateJson.update[webId]
                                .AppPlatformInformationList?.length > 0
                        ) {
                            await ICPWebPlatInfoModel.findOneAndUpdate(
                                { ICPWebId: webId },
                                {
                                    $set: {
                                        AppPlatformInformationList:
                                            ICPUpdate.WebUpdateJson.update[
                                                webId
                                            ].AppPlatformInformationList,
                                    },
                                },
                                { upsert: true }
                            )
                        }
                    }
                }
            } catch (err) {
                errInfo.body = '网站更新出错' + err.message
            }
            // 日志记录
            await UpdateLogModel.create({
                Operator: __ssoUser,
                Type: UpdateLogTypeEnum.API_ICPSYNCFROMHENGAN,
                MainId: ICPInfo.Id,
                UpdateContent: {
                    body: ICPUpdate.MainUpdateJson,
                    website: ICPUpdate.WebUpdateJson,
                },
                CreateTime: moment().format('X'),
                ErrorInfo: errInfo,
            })
            // 标记一下 只有在第三方的更新才触发缓存处理
            // 批量处理一下 缓存的更新
            let delDomainList = [], addDomainList = []
            if (ICPUpdate.MainUpdateJson?.Status == 1) {
                // 备案注销，所有的该备案下的域名都从缓存清除，（我们不直接清除，推送到del的mq，做一次前置检查再del）
                ICPInfo.Website.forEach((web) => {
                    web.Domain?.forEach(d => {
                        delDomainList.push(d.Domain)
                    })
                })
            }
            
            if (ICPUpdate.WebUpdateJson.insert?.length > 0) {
                // 备案成功，新增的域名，添加到缓存
                for (let web of ICPUpdate.WebUpdateJson.insert) {
                    web.Domain?.forEach(d => {
                        addDomainList.push({
                            Domain: d.Domain,
                            ICPWebNo: web.ICPWebNo,
                        })
                    })
                }
            }
            if (Object.keys(ICPUpdate.WebUpdateJson.update)?.length > 0) {
                for (let webId in ICPUpdate.WebUpdateJson.update) {
                    let web = ICPInfo.Website.find(web => web.Id == webId)
                    if (ICPUpdate.WebUpdateJson.update[webId].Status == 1) {
                        // 要注销该网站
                        web.Domain?.forEach(d => {
                            delDomainList.push(d.Domain)
                        })
                    } else {
                        // 网站更新, 也放入add里面 如果存在就更新就好
                        addDomainList.push({
                            Domain: web.Domain[0].Domain,
                            ICPWebNo: ICPUpdate.WebUpdateJson.update[webId].ICPWebNo || web.ICPWebNo,
                        })
                    }
                }
            }
            // 添加 只需要一个域名就好 因为它会把备案下的所有域名都处理一遍
            if (!ResolveOrder) {
                // 非我司订单审核通过的，进行redis更新
                // 我司订单通过，缓存更新 走ResolveHook
                if (addDomainList.length > 0) {
                    // 将需要添加的加入缓存
                    await producer.send({
                        type: 'icp',
                        topic: 'AddDomainForResolveOrder',
                        data: {
                            Domain: addDomainList[0].Domain,
                        },
                    })
                }
                for (let domain of delDomainList) {
                    // 需要删除的丢到redis中操作，严谨一点儿，再查询一次
                    // 删除 这个一次只有一个 但是需要一个日志id，需要研究调整一下
                    await producer.send({
                        type: 'icp',
                        topic: 'DelDomainForResolveOrder',
                        data: {
                            Domain: domain,
                        },
                    })
                }
            }
            return self.cb(0)
        } catch (e) {
            let err = new Error('同步备案信息出错' + e.message)
            err.code = 10001
            self.err(err)
        }
    }
}
