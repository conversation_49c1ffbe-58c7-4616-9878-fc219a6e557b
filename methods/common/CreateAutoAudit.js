const Method = require('../../libs/method')
const { OrderModel, OrderWebModel, OrderHistoryModel } = require('../../models')
const { parseJSON, formatDateToYMD } = require('../../fns/kits')
const { checkAllDuplicateMobileEmailFields } = require('../../fns/checkAllDuplicateMobileEmailFields')
// const CheckEpicEntPersonRelation = require('./CheckEpicEntPersonRelation') // 四要素
// const CheckBusinessLicenseData = require('./CheckBusinessLicenseData') // 营业执照 ocr and check
// const GetIdenityOCRInfo = require('./GetIdenityOCRInfo') // 身份证ocr and check
// const CheckPersonalInfo = require('./CheckPersonalInfo') // 个人身份信息
// const GetDomainRegisterMsg = require('./GetDomainRegisterMsg') // 域名注册信息
// const CheckDomainRegisterMsg = require('./CheckDomainRegisterMsg') // 域名注册信息
// const CheckBusinessInfoWithTYC = require('./CheckBusinessInfoWithTYC') // 工商信息天眼查对比
const InternalApiReq = require('../../libs/ucloudinternalapi')
const logger = require("../../libs/logger")
const { fieldMappingFunction, OperateCheckRes, CompareTYCWithOCR,AnalyzePhotoRequirementsFunction, CheckPersonalInfoCurtainPicture, CheckPersonalInfoLicensePicture, CheckLicenseDateVaild } = require('../../fns/orderAudit/CheckOrderAudit')
const { GetIdenityOCRInfoFrontFunction, CheckBusinessInfoWithTYCFunction, CheckBusinessLicenseDataFunction, CheckEpicEntPersonRelationFunction, isNeedCheckAllDuplicateMobileEmailFields, GetIdenityOCRInfoBackFunction, CheckTycRegStatus, CheckPersonAgeByLicenseId } = require('../../fns/orderAudit/CheckOrderAudit')
// 双脸匹配校验
module.exports = class CreateAutoAudit extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({ __ssoUser, OrderNo,  AuditType} = {}) {
        let self = this
        let redis = this.redis.get()
        let MainEn2CnMap = await redis.hgetall(`Chinese-English-Map:Main`)
        let WebEn2CnMap = await redis.hgetall(`Chinese-English-Map:Web`)
        try {
            let order = await OrderModel.findAll({
                where: {
                    OrderNo,
                },
                distinct: true,
                include: [
                    {
                        model: OrderWebModel,
                        as: 'Website',
                        required: false,
                    },
                ],
            })
            if (order.length === 0) {
                // 未发现order
                let e = new Error('未发现order')
                e.code = 32151
                return this.err(e)
            }
            order = parseJSON(order)
            order = order[0]
            let CompanyId = order.CompanyId
            let orderError = parseJSON(order.Error)
            let checkOrderFuncArray = []

            logger.getLogger("access").info(`the orderError  is ${JSON.stringify(orderError)}, the AuditType is ${AuditType}`)
            // 增量检查，读取打回的error字段，获取检测的函数
            if (AuditType === 'IncrementAudit') {
                const otherKeys = Object.keys(orderError).filter(key => key !== 'Website')
                for (let checkKey of otherKeys) {
                    checkOrderFuncArray.push(...fieldMappingFunction(checkKey))
                }
            }

            logger.getLogger("access").info(`checkOrderFuncArray  is ${JSON.stringify(checkOrderFuncArray)}`)
            // 判断订单是否符合验证条件 企业➕主办单位证件类型：营业执照 且 主体负责人证件为身份证

            let promiseMainArray = []

            // 插入自动审核记录
            // 将打回信息写入日志
            let insertLog = await OrderHistoryModel.create({
                OrderNo,
                Status: order.Status, //此处记录打回的状态
                OperationTime: new Date(),
                Operator: __ssoUser,
                Action: 'audit_order',
                Info: JSON.stringify({}),
            })
            let logId = parseJSON(insertLog).Id
            // 根据order判断需要校验的内容 一个项目 可能有多项检查
            let checkRes = {
                OrganizerName: {},
                OrganizerLicenseType: {},
                OrganizerLicenseId: {},
                PICMainName: {},
                PICMainLicenseType: {},
                PICMainLicenseId: {},
                PICMainLicenseDate: {},
                OrganizerAddress: {},
                PICMainLicensePicture: {},
                OrganizerLicensePicture: {},
                OrganizerLicenseArea: {},
                PICMainMobile:{},
                EmergencyPhone:{},
                PICMainEmail:{},
                Web: {},
            }
            let BusinessLicensePicOCRInfo = {}
            let BusinessTYCInfo = {}
            // 类型是企业
            if (order.OrganizerType === 4) {
                // 企业
                if (AuditType === 'FullAudit') {
                    promiseMainArray.push(CheckEpicEntPersonRelationFunction(order, checkRes))
                    promiseMainArray.push(CheckBusinessLicenseDataFunction(order, checkRes, BusinessLicensePicOCRInfo))
                    promiseMainArray.push(CheckBusinessInfoWithTYCFunction(order, checkRes, BusinessTYCInfo))
                } else if (AuditType === 'IncrementAudit') {
                    if (checkOrderFuncArray.includes(global.CONFIG.checkAuditFunction.CheckEpicEntPersonRelation)) {
                        promiseMainArray.push(CheckEpicEntPersonRelationFunction(order, checkRes))
                    }
                    if (checkOrderFuncArray.includes(global.CONFIG.checkAuditFunction.CheckBusinessLicenseData)) {
                        promiseMainArray.push(CheckBusinessLicenseDataFunction(order, checkRes, BusinessLicensePicOCRInfo))
                    }
                    if (checkOrderFuncArray.includes(global.CONFIG.checkAuditFunction.CheckBusinessInfoWithTYC)) {
                        promiseMainArray.push(CheckBusinessInfoWithTYCFunction(order, checkRes, BusinessTYCInfo))
                    }
                }
            } else if (order.OrganizerLicenseType === 2) {
                checkRes.OrganizerLicenseType[
                    '主办单位类型为个人，无需核验'
                ] = true
            } else {
                checkRes.OrganizerLicenseType['主办单位类型非个人/企业'] =
                    '请人工核验'
            }
            if (order.PICMainLicenseType == 2) {
                if (AuditType === 'FullAudit') {
                    promiseMainArray.push(GetIdenityOCRInfoFrontFunction(order, checkRes, {}))
                    promiseMainArray.push(GetIdenityOCRInfoBackFunction(order, checkRes, {}))
                    CheckPersonAgeByLicenseId(order, checkRes, {})
                    CheckLicenseDateVaild(order, checkRes, {})
                } else if (AuditType === 'IncrementAudit') {
                    if (checkOrderFuncArray.includes(global.CONFIG.checkAuditFunction.GetIdenityOCRInfoFront)) {
                        promiseMainArray.push(GetIdenityOCRInfoFrontFunction(order, checkRes, {}))
                    }
                    if (checkOrderFuncArray.includes(global.CONFIG.checkAuditFunction.GetIdenityOCRInfoBack)) {
                        promiseMainArray.push(GetIdenityOCRInfoBackFunction(order, checkRes, {}))
                    }
                    if (checkOrderFuncArray.includes(global.CONFIG.checkAuditFunction.CheckPersonAgeByLicenseId)) {
                        CheckPersonAgeByLicenseId(order, checkRes, {})
                    }
                    if (checkOrderFuncArray.includes(global.CONFIG.checkAuditFunction.CheckLicenseDateVaild)) {
                        CheckLicenseDateVaild(order, checkRes, {})
                    }
                }
            } else {
                checkRes.PICMainLicenseType['主体负责人证件类型非身份证'] =
                    '请人工核验'
            }
            await Promise.allSettled(promiseMainArray)

            logger.getLogger("access").info(`the check res is ${JSON.stringify(checkRes)}, the BusinessLicensePicOCRInfo is ${JSON.stringify(BusinessLicensePicOCRInfo)}, BusinessTYCInfo is ${JSON.stringify(BusinessTYCInfo)}`)

            let webCheckFuncArray = {}
            for (let web of order.Website) {
                webCheckFuncArray[web.Id] = []
                if (AuditType === 'IncrementAudit') {
                    if (orderError.Website && orderError.Website[web.Id]) {
                        let webSiteKeys = Object.keys(orderError.Website[web.Id])
                        for (let websiteKey of webSiteKeys) {
                            webCheckFuncArray[web.Id].push(...fieldMappingFunction(websiteKey))
                        }
                    }
                }
            }
            logger.getLogger("access").info(`webCheckFuncArray  is ${JSON.stringify(webCheckFuncArray)}`)
            for (let web of order.Website) {
                let promiseWebArray = []
                checkRes.Web[web.Id] = {
                    PICName: {},
                    LicenseType: {},
                    LicenseId: {},
                    LicenseDate: {},
                    LicensePicture: {},
                    CurtainPicture: {},
                    Mobile:{},
                    Email:{},
                    EmergencyPhone:{},
                    Domain: {},
                }
                // 网站负责人证件OCR
                if (web.LicenseType == 2) {
                    // 如果网站负责人证件类型为身份证，则正反面OCR校验，三要素校验
                    if (AuditType === 'FullAudit') {
                        promiseWebArray.push(GetIdenityOCRInfoFrontFunction(order, checkRes, web))
                        promiseWebArray.push(GetIdenityOCRInfoBackFunction(order, checkRes, web))
                        promiseWebArray.push(CheckPersonalInfoLicensePicture(order, checkRes, web))
                        promiseWebArray.push(CheckPersonalInfoCurtainPicture(order, checkRes, web))
                        promiseWebArray.push(AnalyzePhotoRequirementsFunction(order, checkRes, web))
                        CheckPersonAgeByLicenseId(order, checkRes, web)
                        CheckLicenseDateVaild(order, checkRes, web)
                    } else if (AuditType === 'IncrementAudit') {
                        if (webCheckFuncArray[web.Id].includes(global.CONFIG.checkAuditFunction.GetIdenityOCRInfoFront)) {
                            promiseWebArray.push(GetIdenityOCRInfoFrontFunction(order, checkRes, web))
                        }
                        if (webCheckFuncArray[web.Id].includes(global.CONFIG.checkAuditFunction.GetIdenityOCRInfoBack)) {
                            promiseWebArray.push(GetIdenityOCRInfoBackFunction(order, checkRes, web))
                        }
                        if (webCheckFuncArray[web.Id].includes(global.CONFIG.checkAuditFunction.CheckPersonalInfo)) {
                            promiseWebArray.push(CheckPersonalInfoLicensePicture(order, checkRes, web))
                        }
                        if (webCheckFuncArray[web.Id].includes(global.CONFIG.checkAuditFunction.CheckPersonalInfoCurtainPicture)) {
                            promiseWebArray.push(CheckPersonalInfoCurtainPicture(order, checkRes, web))
                        }
                        if (webCheckFuncArray[web.Id].includes(global.CONFIG.checkAuditFunction.AnalyzePhotoRequirements)) {
                            promiseWebArray.push(AnalyzePhotoRequirementsFunction(order, checkRes, web))
                        }
                        if (webCheckFuncArray[web.Id].includes(global.CONFIG.checkAuditFunction.CheckPersonAgeByLicenseId)) {
                            CheckPersonAgeByLicenseId(order, checkRes, web)
                        }
                        if (webCheckFuncArray[web.Id].includes(global.CONFIG.checkAuditFunction.CheckLicenseDateVaild)) {
                            CheckLicenseDateVaild(order, checkRes, web)
                        }
                    }
                } else {
                    checkRes.Web[web.Id].LicenseType = {
                        网站负责人证件类型非身份证: '请人工核验',
                    }
                }
                await Promise.allSettled(promiseWebArray)

                logger.getLogger("access").info(`the ${web.Id} web check res is ${JSON.stringify(checkRes.Web[web.Id])}`)
            }

            // TYC和OCR结果对比
            if (order.OrganizerType === 4) {
                if (AuditType === 'FullAudit') {
                    CompareTYCWithOCR(checkRes, BusinessTYCInfo.TYCInfo, BusinessLicensePicOCRInfo.OCRInfo)
                    CheckTycRegStatus(checkRes, BusinessTYCInfo.TYCInfo)
                }
                if (AuditType === 'IncrementAudit' && checkOrderFuncArray.includes(global.CONFIG.checkAuditFunction.CompareTYCWithOCR)) {
                    CompareTYCWithOCR(checkRes, BusinessTYCInfo.TYCInfo, BusinessLicensePicOCRInfo.OCRInfo)
                }
                if (AuditType === 'IncrementAudit' && checkOrderFuncArray.includes(global.CONFIG.checkAuditFunction.CheckTycRegStatus)) {
                    CheckTycRegStatus(checkRes, BusinessTYCInfo.TYCInfo)
                }
            }

            if (isNeedCheckAllDuplicateMobileEmailFields(AuditType, checkOrderFuncArray, webCheckFuncArray)) {
                // 重复流程检测获取到的重复信息
                let checkResult = await checkAllDuplicateMobileEmailFields(OrderNo)
                if (JSON.stringify(checkResult) !== '{}') {
                    try {
                        // 处理CheckRes
                        OperateCheckRes(checkResult, checkRes, MainEn2CnMap, WebEn2CnMap)
                    } catch (error) {
                        // 出错拦截，不影响其他的检查
                        logger.getLogger("error").error(`OperateCheckRes error, the error is ${error}`)
                    }
                }
            }
            logger.getLogger("access").info(`the end checkRes is ${JSON.stringify(checkRes)}`)
            // 执行完毕后，更新
            await OrderHistoryModel.update(
                {
                    OperationTime: new Date(),
                    Info: JSON.stringify(checkRes),
                },
                {
                    where: {
                        Id: logId,
                    },
                }
            )
            return this.cb(0)
        } catch (e) {
            console.log(e)
            if (e instanceof Error) {
                return self.eh({
                    err: e,
                    code: 31410,
                })
            }
            self.eh(e)
        }
    }
}