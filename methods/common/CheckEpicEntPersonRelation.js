const Method = require('../../libs/method')
const verify_type = require('../../configs/common/verify_api_type.json')
const {
    insertLog,
    getVerifyLog,
    updateLog,
    verifyAPI,
} = require('../../fns/verifyFun')
const { getTableModel } = require('../../fns/kits')
const epicEntPersonRelation_error = require('../../configs/common/epic_ent_person_relation_error.json')

// 验证企业四要素API
// 为兼容前端，不改传参
module.exports = class CheckEpicEntPersonRelation extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({
        CompanyName,
        CompanyCode,
        LegalEntityName,
        LegalEntityId,
        CompanyId,
        __ssoUser,
        ForceSelect = false,
        OrderNo,
    } = {}) {
        let Operator, logId, result, multiplex
        // 字段是否复用
        multiplex = 1
        Operator = __ssoUser
        let self = this

        let icpDatabase = this.db.get('icp')
        let LogModel = getTableModel('t_verify_log', icpDatabase)

        try {
            let params = {
                Type: verify_type.CheckEpicEntPersonRelation,
                CompanyName,
                CompanyCode,
                LegalEntityName,
                LegalEntityId,
                CompanyId,
                Operator,
                OrderNo,
                Backend: 'IdAuth',
                Action: 'EpicEntPersonRelation',
            }

            // 执行日志
            logId = await insertLog(params, LogModel)

            // 如果没有强制验证，则查询旧记录
            try {
                if (!ForceSelect) {
                    // 验证是否此参数是否验证过
                    result = await getVerifyLog(
                        {
                            OrderNo: OrderNo || '',
                            CompanyName,
                            CompanyCode,
                            LegalEntityName,
                            LegalEntityId,
                            Type: verify_type.CheckEpicEntPersonRelation,
                        },
                        logId,
                        LogModel
                    )
                }

                // 如果未取到，或者之前的请求为出错，重试
                if (!result || result.Error === true) {
                    // result = await EpicEntPersonRelationAPI(params)
                    result = await verifyAPI({
                        Action: 'EpicEntPersonRelation',
                        body: params,
                    })
                    multiplex = 0
                }
            } catch (error) {
                await Promise.reject(new self.Err(error, 30703))
            }

            // 更新结果到数据库
            result.Multiplex = multiplex
            try {
                await updateLog(result, logId, LogModel)
            } catch (error) {
                await Promise.reject(new self.Err(error, 30702))
            }

            // 结果匹配
            let returnObject = {}
            let returnCode = 0

            if (result.Error) {
                returnObject = {
                    IsMatch: false,
                    Message:
                        typeof result.Response === 'string'
                            ? result.Response
                            : JSON.stringify(result.Response),
                }
            } else if (result.Response.RetCode !== 0) {
                returnObject = {
                    IsMatch: false,
                    Message:
                        typeof result.Response.Message === 'string'
                            ? result.Response.Message
                            : JSON.stringify(result.Response.Message),
                }
            } else if (result.Response.IsMatch) {
                returnObject = {
                    IsMatch: result.Response.IsMatch,
                    DataSet: result.Response.DataSet,
                }
            } else {
                // 出错信息拼
                returnObject = {
                    IsMatch: result.Response.IsMatch,
                    DataSet: result.Response.DataSet,
                    ErrorResult: result.Response.ErrorResult,
                }
                if (result.Response.Message) {
                    returnObject['Message'] = result.Response.Message
                } else {
                    let message = ''

                    for (let key of Object.keys(result.Response.DataSet)) {
                        if (
                            result.Response.DataSet[key] !== true &&
                            result.Response.DataSet[key] !== '1'
                        ) {
                            message =
                                message +
                                epicEntPersonRelation_error[key] +
                                '、'
                        }
                    }
                    message = message.substring(0, message.length - 1)
                    returnObject['Message'] = message
                    if (returnObject['Message'] === '未找到此信息') {
                        returnObject['Message'] =
                            '主体信息与权威库企业信息比对失败'
                    }

                    if (returnObject['Message'] === '请求协会后端返回出错') {
                        returnObject['Message'] =
                            '主体负责人信息与权威库法定代表人信息比对失败'
                    }
                }
            }
            await updateLog(
                {
                    ReturnObject: returnObject,
                    Result: returnObject.IsMatch ? 0 : 1,
                },
                logId,
                LogModel
            )

            return this.cb(returnCode, returnObject)
        } catch (e) {
            if (e instanceof Error) {
                return self.eh({
                    err: e,
                    code: 30701,
                })
            }
            self.eh(e)
        }
    }
}
