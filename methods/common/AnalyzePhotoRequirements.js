'use strict'

const Method = require('../../libs/method')
const callOpenAIGPT4 = require('../../libs/requestOpenAi')
const { convertUS3FileToBase64 } = require('../../fns/kits')
const { getPictureUrls } = require('../../fns/uflieFuns')
const moment = require('moment')
const {
    insertLog,
    getVerifyLog,
    updateLog,
    verifyAPI,
    getUrl,
} = require('../../fns/verifyFun')
const verify_type = require('../../configs/common/verify_api_type.json')
const { getTableModel, getVerifyResult } = require('../../fns/kits')

// GPT-4o,解决活体成像的质量要求
// 入参为文件名，得到base64后调AI接口，让AI来判断

module.exports = class AnalyzePhotoRequirements extends Method {
    constructor(cb) {
        super(cb)
    }

    async exec({ Picture, OrderNo, Source, ForceSelect } = {}) {
        let self = this
        let aiRes = ''
        let currentMonth = moment().format('M')
        let icpDatabase = this.db.get('icp')
        let LogModel = getTableModel('t_verify_log', icpDatabase)
        let logId,
            params = {},
            result = null
        let multiplex = 1

        try {
            params = {
                Picture,
                OrderNo,
                Source,
                Type: verify_type.GPTPictureAnalyze,
            }

            logId = await insertLog(params, LogModel)

            // 如果有订单号，同时不是强制直接查询，从缓存中取

            if (OrderNo && !ForceSelect) {
                result = await getVerifyLog(params, logId, LogModel)
            }
            console.log(result, 111)
            // 如果未取到，或者之前的请求为出错，重试
            if (
                result === null ||
                JSON.stringify(result) === '{}' ||
                result.Error === true ||
                JSON.stringify(result.Response) === '{}'
            ) {
                // 获取图片url
                let url = getUrl(null, Picture)
                let fileBase64
                let fileType = Picture.split('.').pop()
                fileBase64 =
                    `data:image/${fileType};base64,` +
                    (await convertUS3FileToBase64(url))

                const messages = [
                    {
                        role: 'system',
                        content:
                            '你是一个帮助判断照片的助手，照片一般是用户做活体检查得到的最佳成像照片',
                    },
                    {
                        role: 'user',
                        content: [
                            {
                                type: 'image_url',
                                image_url: {
                                    url: fileBase64,
                                },
                            },
                        ],
                    },
                    {
                        role: 'user',
                        content: `输出为标准JSON格式，需要去掉markdown格式内容`,
                    },
                    {
                        role: 'user',
                        content: `输出格式与取值如下
                        {
                        "穿着是否应季" : 请从着装风格确定这张照片是否在${currentMonth}月拍摄的,根据服装判断是否为当前的季节，值为Int型数字，不符合返回为0.符合返回为1.不能确定返回为2.原则上夏天不会穿毛衣、羽绒服、厚绵服，冬天不会穿短袖,
                       "嘴巴是否闭合" : 确定人物嘴巴是否闭合，闭合为true或者张开为false,
                        "照片是否清晰" : 值严格限定为true或者false,
                        "背景是否为白色" : 值严格限定为true或者false,
                        "是否仅有一个人物" : 值严格限定为true或者false,
                        "光线是否合适" : 值为Int型数字，过暗为0，过亮为1，正常为2,
                        "是否穿衣" : 值严格限定为true或者false,
                        "是否睁眼" : 值严格限定为true或者false,
                        "是否免冠" : 照片中的人物是否戴帽子值严格限定为true或者false；未带为true带为false,
                        "IsHeadFullyVisible" : Please check if the entire head, including all facial features and both ears, is fully visible and centered in the photo. Consider the head to be fully visible only if no parts like ears are cropped out of the frame.The value should be strictly true or false.
                        }`,
                    },
                    {
                        role: 'system',
                        content: `嘴巴是否闭合,判断需要相对松，只要嘴巴打开幅度不是特别大均视为true`,
                    },
                    //{
                    // role: 'system',
                    // content: `"Head Fully Contained Within Photo Borders": Ensure that the entire head is clearly visible and entirely within the photo's boundaries. Adhere strictly to the following criteria:
                    // 1. Top of the head: Must be visible without any part being cropped by the top edge of the photo.
                    // 2. Sides of the face: Both the left and right sides, including the ears if visible, must not extend beyond the photo’s edges. Ensure that the facial contour is fully captured within the frame.
                    //  3. Chin: Must be fully included within the photo, without being cropped by the bottom edge.
                    //    If any part of the head is missing or extends beyond the photo's edges, the result should be deemed non-compliant and return 'false'.`,
                    //  },
                ]

                try {
                    aiRes = await callOpenAIGPT4(messages, 'gpt4o', {
                        temperature: 0.1,
                        response_format: { type: 'json_object' },
                    })
                    // 日志记录，来源，时间，花费token
                    // 下次再整合再做
                    aiRes = aiRes.choices[0]?.message?.content
                    aiRes = aiRes.replace(/```json|```/g, '')
                    aiRes = JSON.parse(aiRes)
                    multiplex = 0
                    result = { Error: false, Response: aiRes }
                } catch (error) {
                    result = { Error: true, Response: { Error: error } }
                }
            }
            if (OrderNo) {
                result.Multiplex = multiplex
                console.log(result)
                await updateLog(result, logId, LogModel)
            }

            return self.cb(0, {
                AIRes: result.Response,
            })
        } catch (e) {
            console.log(e)
            if (e instanceof Error) {
                return self.eh({
                    err: e,
                    code: 36059,
                })
            }
            self.eh(e)
        }
    }
}
