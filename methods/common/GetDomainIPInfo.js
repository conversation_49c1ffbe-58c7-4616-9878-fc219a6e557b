/*
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2024-02-01 14:05:36
 * @LastEditors: <NAME_EMAIL>
 * @LastEditTime: 2024-02-04 15:31:47
 * @FilePath: /newicp/methods/common/GetDomainIPInfo.js
 * @Description: 根据域名获取IP地址信息等（或直接根据IP列表获取）
 */

const Method = require('../../libs/method')
const { getIPIPInfo } = require('../../fns/ipipFuns')
const { convertObjectKeysToCamel } = require('../../fns/parseModels')
const { Resolver } = require('dns').promises
module.exports = class GetDomainIPInfo extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({ Domain, IPList } = {}) {
        let self = this
        try {
            let resData = {
                Domain: Domain,
                IPList: [],
                MainLand: true,
                Country: '中国',
                Region: '大陆',
            }
            // 如果传入IP，直接用IP查询所在地址，地址格式筛选，IPV6暂不支持
            const ipReg =
                /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/
            let ips = IPList ? IPList.filter((ip) => ipReg.test(ip)) : []

            // 如果没传IP，dns解析Domain获取IP；IPV6暂不支持
            console.log(`没有IP 开始解析域名: ${JSON.stringify(Domain)}`)

            if (ips.length == 0) {
                try {
                    const resolver = new Resolver()
                    // resolver.setServers([
                    //     '*********',
                    //     '*******',
                    //     '[2001:4860:4860::8888]',
                    //     '*******:1053',
                    //     '[2001:4860:4860::8888]:1053',
                    // ])
                    if (process.env.SERVER_ENV) {
                        // 该环境变量为 k8s部署 调整为v6 域名解析服务器
                        resolver.setServers(['2002:a40:23e::*******'])
                    }
                    await resolver.resolve4(Domain).then((address) => {
                        console.log(`IP 地址: ${JSON.stringify(address)}`)
                        ips = address
                    })
                } catch (error) {
                    return self.eh(new this.Err(error, 36061))
                }
            }
            console.log(`IP 地址获取到了: ${JSON.stringify(ips)}`)
            resData.IPList = ips
            // 存在IP不是中国，即为非境内
            ips.forEach((ip) => {
                // console.log(ip)
                let IPInfo = getIPIPInfo('City', ip)
                IPInfo = convertObjectKeysToCamel(IPInfo)
                // 1.判断是否为港澳台ip 是的话 显示为非境内
                // 2.判断是否为海外ip 是的话 显示为非境内
                // 3.若有海外 则海外优先级最高
                if (
                    IPInfo.RegionName.indexOf('台湾') !== -1 ||
                    IPInfo.RegionName.indexOf('香港') !== -1 ||
                    IPInfo.RegionName.indexOf('澳门') !== -1
                ) {
                    resData.MainLand = false
                    resData.Country = IPInfo.CountryName
                    resData.Region = IPInfo.RegionName
                }
                if (IPInfo.CountryName !== '中国') {
                    resData.MainLand = false
                    resData.Country = IPInfo.CountryName
                    resData.Region = IPInfo.RegionName
                }
            })
            return self.cb(0, resData)
        } catch (e) {
            console.log(e)
            if (e instanceof Error) {
                return self.eh({
                    err: e,
                    code: 36061,
                })
            }
            self.eh(e)
        }
    }
}
