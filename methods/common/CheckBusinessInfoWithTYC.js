// 检查「企业法人、 企业名称、 企业统一信用代码 与 营业执照」是否一致

const Method = require('../../libs/method')
const verify_type = require('../../configs/common/verify_api_type.json')
const {
    insertLog,
    updateLog,
    verifyAPI,
} = require('../../fns/verifyFun')
const { getTableModel } = require('../../fns/kits')
const _ = require('lodash')
module.exports = class CheckBusinessInfoWithTYC extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({
        __ssoUser,
        ForceSelect = false,
        UUID,
        CompanyId,
        OrderNo,
        Source, //用来区分ICP还是CrossBorderApply服务
        CompanyName,
        CompanyCode,
        LegalEntityName,
        CompanyAddr, // 企业地址
        Remark,
    } = {}) {
        let Operator, logId, result, multiplex
        // 字段是否复用
        result = {}
        multiplex = 1
        Operator = __ssoUser || 'system.auto'
        let self = this

        let icpDatabase = this.db.get('icp')
        let LogModel = getTableModel('t_verify_log', icpDatabase)
        try {
            let params = {
                CompanyName,
                CompanyCode,
                LegalEntityName,
                CompanyAddr,
                Operator,
                OrderNo: OrderNo ?? (Source ? Source + '_' + CompanyId : ''), //无OrderNo使用Source_CompanyId标记，否则为''
                Remark,
                CompanyId: CompanyId,
                Backend: 'NewIdAuth',
                Action: 'GetCompanyInfoByTYC',
                Source: 'ICP',
            }

            if (UUID) {
                params.UUID = UUID
            }
            params['Type'] = verify_type.ValidateBusinessInfoWithTYC

            // 执行日志
            logId = await insertLog(params, LogModel)

            try {
                result = await verifyAPI({
                    Action: params.Action,
                    body: {
                        Action: params.Action,
                        TargetId: 1116,
                        Keyword: params.CompanyName,
                        ForceRefresh: ForceSelect,
                        Backend: params.Backend,
                    },
                })
                multiplex = 0
            } catch (error) {
                await Promise.reject(new self.Err(error, 31301))
            }
            // 更新结果到数据库
            result.Multiplex = multiplex

            let returnObject = {
                IsMatch: true,
                Info: {
                    ...(CompanyCode && { CompanyCode: true }),
                    ...(LegalEntityName && { LegalEntityName: true }),
                    ...(CompanyAddr && { CompanyAddr: true }),
                },
                Message: '',
                TYCInfo: {}
            }
            let returnCode = 0
            // 结果匹配
            if (result.Error && result.Response.RetCode !== 0) {
                returnCode = 31304
                returnObject = result.Response
            } else {
                // 对比输入与图片 若对比法人 则加入判断，否则默认为true
                if (CompanyCode && result.Response?.Result?.creditCode !== CompanyCode) {
                    returnObject.IsMatch = false
                    returnObject.Info.CompanyCode = false
                    returnObject.Message = `公司统一信用代码核验不通过；输入：${CompanyCode},TYC:${result.Response?.Result?.creditCode}。`
                }
                if (
                    LegalEntityName &&
                    result.Response?.Result?.legalPersonName !== LegalEntityName
                ){
                    returnObject.IsMatch = false
                    returnObject.Info.LegalEntityName = false
                    returnObject.Message += `法人核验不通过；输入：${LegalEntityName},TYC:${result.Response?.Result?.legalPersonName}。`
                }
                if (CompanyAddr && result.Response?.Result?.regLocation !== CompanyAddr) {
                    returnObject.IsMatch = false
                    returnObject.Info.CompanyAddr = false
                    returnObject.Message += `公司地址核验不通过；输入：${CompanyAddr},TYC:${result.Response?.Result?.regLocation}。`
                }
                if (returnObject.IsMatch) {
                    returnObject.Message = '认证一致(通过)'
                }
                returnObject.TYCInfo = _.pick(result.Response?.Result, ['creditCode', 'legalPersonName', 'regLocation', 'approvedTime', 'regStatus'])
            }
            result.ReturnObject = returnObject
            await updateLog(result, logId, LogModel)

            this.cb(returnCode, returnObject)
        } catch (e) {
            if (e instanceof Error) {
                return self.eh({
                    err: e,
                    code: 31303,
                })
            }
            self.eh(e)
        }
    }
}
