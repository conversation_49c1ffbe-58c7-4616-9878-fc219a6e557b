const Method = require('../../libs/method')
const verify_type = require('../../configs/common/verify_api_type.json')
const {
    insertLog,
    getVerifyLog,
    updateLog,
    verifyAPI,
    getFileName,
    getUrl,
} = require('../../fns/verifyFun')
const { getTableModel } = require('../../fns/kits')
// 双脸匹配校验
module.exports = class CheckTwoFaceMatch extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({
        __ssoUser,
        ForceSelect,
        UUID,
        Remark,
        CompanyId,
        OrderNo,
        PictureName1,
        PictureName2,
        Name1,
        Name2,
    } = {}) {
        let Operator, logId, result, multiplex
        // 字段是否复用
        PictureName1 = PictureName1 || Name1
        PictureName2 = PictureName2 || Name2

        result = {}
        multiplex = 1
        Operator = __ssoUser
        let self = this

        let icpDatabase = this.db.get('icp')
        let LogModel = getTableModel('t_verify_log', icpDatabase)
        try {
            let url1 = getUrl(null, PictureName1, null)
            let url2 = getUrl(null, PictureName2, null)

            // 取出文件名，为性能加速
            let params = {
                Action: 'VerifyTwoFace',
                CompanyId: CompanyId,
                Remark,
                Operator,
                UUID: UUID,
                OrderNo,
                body: {
                    Photo1: url1,
                    Photo2: url2,
                    Backend: 'IdAuth',
                    Action: 'VerifyTwoFace',
                    Source: 'ICP',
                },
            }

            if (UUID) {
                params.UUID = UUID
            }
            params['Type'] = verify_type.VerifyTwoFaceMatch

            // 执行日志
            logId = await insertLog(params, LogModel)

            // 如果没有强制验证，则查询旧记录
            try {
                if (!ForceSelect) {
                    // 验证是否此参数是否验证过,此处实际不需要，URL会更新
                    result = await getVerifyLog(
                        {
                            OrderNo: OrderNo || '',
                            Photo1: PictureName1,
                            Photo2: PictureName2,
                            Type: verify_type.VerifyTwoFaceMatch,
                        },
                        logId,
                        LogModel
                    )
                }
                // 如果未取到，或者之前的请求为出错，重试
                if (
                    result === null ||
                    JSON.stringify(result) === '{}' ||
                    JSON.stringify(result.Response) === '{}' ||
                    result.Error === true
                ) {
                    params.body.Photo1 = url1
                    params.body.Photo2 = url2
                    result = await verifyAPI(params)
                    multiplex = 0
                }
            } catch (error) {
                await Promise.reject(new self.Err(error, 31401))
            }

            // 更新结果到数据库
            result.Multiplex = multiplex

            try {
                await updateLog(result, logId, LogModel)
            } catch (error) {
                await Promise.reject(new self.Err(error, 31402))
            }

            let returnObject = {}
            let returnCode = 0
            // 结果匹配

            if (result.Error && result.Response.RetCode !== 0) {
                returnCode = 31404
            }
            returnObject = result.Response || {}
            returnObject.RetCode = returnCode
            await updateLog({ ReturnObject: returnObject }, logId, LogModel)

            this.cb(returnCode, returnObject)
        } catch (e) {
            console.log(e)
            if (e instanceof Error) {
                return self.eh({
                    err: e,
                    code: 31403,
                })
            }
            self.eh(e)
        }
    }
}
