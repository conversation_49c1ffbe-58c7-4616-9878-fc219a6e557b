/**
 * @file API GetBlockRule,获取屏蔽规则
 * <AUTHOR>
 * @return {Object} 返回符合条件的记录,格式如下
 * {
    "Config": {"main": {....},"website"{....}},
    "RetCode": 0
	}
 * 错误码:
 * 30101: "获取Redis线上配置时网络异常",	
 * 30103: "其它错误",	

 */

const Method = require('../../libs/method')
const { getKeyAsync } = require('../../fns/redisFuns')

module.exports = class GetBlockRule extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({ Time } = {}) {
        let self = this
        let redis = this.redis.get()
        let redisCheck
        try {
            // 查询Redis
            try {
                redisCheck = await getKeyAsync(redis, 'blockRule')
                redisCheck = JSON.parse(redisCheck)
            } catch (error) {
                return await Promise.reject(new self.Err(error, 30101))
            }

            if (redisCheck) {
                return self.cb(0, { Rule: redisCheck })
            }
        } catch (e) {
            console.log(e)
            if (e instanceof Error) {
                return self.eh({
                    err: e,
                    code: 30103,
                })
            }
            self.eh(e)
        }
    }
}
