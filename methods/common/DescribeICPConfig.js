const Method = require('../../libs/method')
const { getKeyAsync } = require('../../fns/redisFuns')
/**
 * @file API DescribeICPConfig,展示Redis中的配置
 * <AUTHOR> 钱俊烨(<EMAIL>)
 * @return {Object} 返回符合条件的记录
 * 在Redis中增加 Key为DescribeICPConfig，值为https://api.ucloud.cn/?Action=DescribeICPConfig 的返回
 */
module.exports = class DescribeICPConfig extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({ Time } = {}) {
        let self = this
        let redis = this.redis.get()
        let redisCheck
        try {
            // 查询Redis
            try {
                redisCheck = await getKeyAsync(redis, 'DescribeICPConfig')
                redisCheck = JSON.parse(redisCheck)
            } catch (error) {
                return await Promise.reject(new self.Err(error, 67111))
            }
            return self.cb(0, { ...redisCheck })
        } catch (e) {
            if (e instanceof Error) {
                return self.eh({
                    err: e,
                    code: 67113,
                })
            }
            self.eh(e)
        }
    }
}
