// 检查个人三要素（人相，人名，身份证）信息是否正确

const Method = require('../../libs/method')
const verify_type = require('../../configs/common/verify_api_type.json')
const {
    insertLog,
    getVerifyLog,
    updateLog,
    getFileName,
    verifyAPI,
    getUrl,
} = require('../../fns/verifyFun')
const { getTableModel } = require('../../fns/kits')

module.exports = class CheckPersonalInfo extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({
        PictureName,
        Picture,
        Url,
        __ssoUser,
        ForceSelect,
        UUID,
        CompanyId,
        OrderNo,
        Name,
        Id,
        Remark,
    } = {}) {
        let Operator, logId, result, multiplex
        // 字段是否复用
        result = {}
        multiplex = 1
        Operator = __ssoUser
        let self = this
        Picture = PictureName || Picture
        Url = getUrl(Url, Picture, 85)

        let icpDatabase = this.db.get('icp')
        let LogModel = getTableModel('t_verify_log', icpDatabase)
        try {
            let params = {
                Picture: Url,
                Name: Name,
                Id: Id,
                UUID: UUID,
                Operator,
                ActionIndex: 0,
                OrderNo,
                Remark,
                CompanyId: CompanyId,
                Backend: 'IdAuth',
                Action: 'CheckPersonalInfo',
                Source: 'ICP',
            }

            if (UUID) {
                params.UUID = UUID
            }
            params['Type'] = verify_type.ValidatePersonalInfo

            // 执行日志
            logId = await insertLog(params, LogModel)

            // 如果没有强制验证，则查询旧记录

            try {
                if (!ForceSelect) {
                    // 验证是否此参数是否验证过,Picture需要解析，从URL转到文件名getFileName

                    result = await getVerifyLog(
                        {
                            OrderNo: OrderNo || '',
                            Picture: getFileName(params.Picture),
                            ActionIndex: params.ActionIndex,
                            Id,
                            Name,
                            Type: verify_type.ValidatePersonalInfo,
                        },
                        logId,
                        LogModel
                    )
                }

                // 如果未取到，或者之前的请求为出错，重试
                if (
                    result === null ||
                    JSON.stringify(result) === '{}' ||
                    JSON.stringify(result.Response) === '{}' ||
                    (result.Response.Message && result.Response.Message !== '')
                ) {
                    params.Picture = getUrl(Url, PictureName, 50)
                    result = await verifyAPI({
                        Action: 'CheckPersonalInfo',
                        body: params,
                    })
                    multiplex = 0
                }
            } catch (error) {
                await Promise.reject(new self.Err(error, 31301))
            }

            // 更新结果到数据库
            result.Multiplex = multiplex
            try {
                await updateLog(result, logId, LogModel)
            } catch (error) {
                await Promise.reject(new self.Err(error, 31302))
            }

            let returnObject = {}
            let returnCode = 0
            // 结果匹配
            if (result.Error && result.Response.RetCode !== 0) {
                returnCode = 31304
                returnObject = result.Response
            } else {
                returnObject.IsMatch =
                    result.Response.RespInfo === '认证一致(通过)'
                returnObject.Message = result.Response.RespInfo
            }

            await updateLog({ ReturnObject: returnObject }, logId, LogModel)

            this.cb(returnCode, returnObject)
        } catch (e) {
            if (e instanceof Error) {
                return self.eh({
                    err: e,
                    code: 31303,
                })
            }
            self.eh(e)
        }
    }
}
