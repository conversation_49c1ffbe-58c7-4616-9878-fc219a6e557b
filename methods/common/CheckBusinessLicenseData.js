// 检查「企业法人、 企业名称、 企业统一信用代码 与 营业执照」是否一致

const Method = require('../../libs/method')
const verify_type = require('../../configs/common/verify_api_type.json')
const {
    insertLog,
    getVerifyLog,
    updateLog,
    getFileName,
    verifyAPI,
    getUrl,
} = require('../../fns/verifyFun')
const { getTableModel } = require('../../fns/kits')

module.exports = class CheckBusinessLicenseData extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({
        Url,
        __ssoUser,
        ForceSelect,
        UUID,
        CompanyId,
        OrderNo,
        Source, //用来区分ICP还是CrossBorderApply服务
        CompanyName,
        CompanyCode,
        LegalEntityName,
        BusinessLicensePic, //文件名
        CompanyAddr, // 企业地址
        Remark,
    } = {}) {
        let Operator, logId, result, multiplex
        // 字段是否复用
        result = {}
        multiplex = 1
        Operator = __ssoUser || 'system.auto'
        let self = this
        Url = getUrl(Url, BusinessLicensePic, 65)

        let icpDatabase = this.db.get('icp')
        let LogModel = getTableModel('t_verify_log', icpDatabase)
        try {
            let params = {
                Url,
                CompanyName,
                CompanyCode,
                LegalEntityName,
                UUID: UUID,
                Operator,
                ActionIndex: 1,
                OrderNo: OrderNo ?? (Source ? Source + '_' + CompanyId : ''), //无OrderNo使用Source_CompanyId标记，否则为''
                Remark,
                CompanyId: CompanyId,
                Backend: 'NewIdAuth',
                Action: 'GetBusinessLicenseData',
                Source: 'ICP',
            }

            if (UUID) {
                params.UUID = UUID
            }
            params['Type'] = verify_type.ValidateBusinessLicenseInfo

            // 执行日志
            logId = await insertLog(params, LogModel)

            try {
                // 如果没有强制验证，则查询旧记录
                if (!ForceSelect) {
                    result = await getVerifyLog(
                        {
                            OrderNo: params.OrderNo,
                            Picture: BusinessLicensePic, // 此处传picture 为了只对应图片名称模糊
                            ActionIndex: params.ActionIndex,
                            CompanyName,
                            CompanyCode,
                            LegalEntityName,
                            Type: verify_type.ValidateBusinessLicenseInfo,
                        },
                        logId,
                        LogModel
                    )
                }

                // 如果未取到，或者之前的请求为出错，重试
                if (
                    result === null ||
                    JSON.stringify(result) === '{}' ||
                    (result?.Response?.Message &&
                        result?.Response?.Message !== '')
                ) {
                    params.Picture = getUrl(Url, BusinessLicensePic, 50)
                    result = await verifyAPI({
                        Action: params.Action,
                        body: params,
                    })
                    multiplex = 0
                }
            } catch (error) {
                await Promise.reject(new self.Err(error, 31301))
            }
            // 更新结果到数据库
            result.Multiplex = multiplex

            let returnObject = {
                IsMatch: true,
                Message: '',
                Info: {
                    ...(CompanyName && { CompanyName: true }),
                    ...(CompanyCode && { CompanyCode: true }),
                    ...(LegalEntityName && { LegalEntityName: true }),
                    ...(CompanyAddr && { CompanyAddr: true }),
                },
                OCRInfo: {}
            }
            let returnCode = 0
            // 结果匹配
            if (result.Error && result.Response.RetCode !== 0) {
                returnCode = 31304
                returnObject = result.Response
            } else {
                // 对比输入与图片 若对比法人 则加入判断，否则默认为true
                if (CompanyName && result.Response.Name !== CompanyName) {
                    returnObject.IsMatch = false
                    returnObject.Info.CompanyName = false
                    // 这个各种标点符号很重要 ！！！  不要随便改格式， 如有新增 则按照现在的格式add
                    returnObject.Message += `公司名称核验不通过；输入：${CompanyName},OCR：${result.Response.Name}。`
                }

                if (CompanyCode && result.Response.Code !== CompanyCode) {
                    returnObject.IsMatch = false
                    returnObject.Info.CompanyCode = false
                    returnObject.Message += `公司统一信用代码核验不通过；输入：${CompanyCode},OCR:${result.Response.Code}。`
                }

                if (LegalEntityName && result.Response.Pers !== LegalEntityName) {
                    returnObject.IsMatch = false
                    returnObject.Info.LegalEntityName = false
                    returnObject.Message += `公司法人核验不通过；输入：${LegalEntityName},OCR:${result.Response.Pers}。`
                }

                if (CompanyAddr && result.Response.Address !== CompanyAddr) {
                    returnObject.IsMatch = false
                    returnObject.Info.CompanyAddr = false
                    returnObject.Message += `公司地址核验不通过；输入：${CompanyAddr},OCR:${result.Response.Address}。`
                }
                if (returnObject.IsMatch) {
                    returnObject.Message = '认证一致(通过)'
                }
                returnObject.OCRInfo = result.Response?.Data
            }
            result.ReturnObject = returnObject
            await updateLog(result, logId, LogModel)

            this.cb(returnCode, returnObject)
        } catch (e) {
            if (e instanceof Error) {
                return self.eh({
                    err: e,
                    code: 31303,
                })
            }
            self.eh(e)
        }
    }
}
