/*
 * @Author: william.qian <EMAIL>
 * @Date: 2023-08-28 17:35:15
 * @LastEditors: william.qian <EMAIL>
 * @LastEditTime: 2023-09-18 11:34:21
 * @FilePath: /newicp/methods/common/GetDomainPublicICP.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
/* 查询 域名 和主体是否在管局备案
 * @Date: 2023-08-15 15:30:05
 * @LastEditors: liyuewen <EMAIL>
 * @LastEditTime: 2023-08-15 17:44:08
 * @FilePath: /newicp/methods/common/GetDomainPublicICP.JS
 */

const Method = require('../../libs/method')
const { getKeyAsync } = require('../../fns/redisFuns')
const { requestLongYi } = require('../../fns/longYiAPIRequest')
const henganApi = require('../../libs/henganApiPromise')

// APP版本，查询类型值与实际对应关系;APP版本，证件号固定必填为FindCertificate
// 101 - 表示通过APP的证件号码和域名查询该服务是否已备案
// 102 - 标识通过APP的证件号码和包名查询是否已备案
// 103 - 标识通过小程序 / 快应用的证件号码和APPID查询是否已备案
// 104 - 表示通过APP的证件号码和APP名称查询是否已备案
// 105 - 表示通过小程序 / 快应用的证件号码和小程序 / 快应用名称查询是否已备案

// Web类型
// 0 - 域名便好
// 或者OrganizerLicenseId结合OrganizerLicenseType结合查询

// APP类型,以下参数均必填
// ServiceType = 'APP'
// AdditionalQueryCondition（补充查询类型）
// OrganizerLicenseId（证件号）
// OrganizerLicenseType(证件类型实际是查询类型)

// APP查询分2种
// 返回
/*
{
    "ICPInfos": [
        {
            "mainRecordNum": "苏ICP备**********号",
            "webSiteNum": "苏ICP备**********号-1"
        }
    ],
    "RetCode": 0,
    "Action": "GetDomainPublicICPResponse"
}
*/
module.exports = class GetDomainPublicICP extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({
        Domain,
        OrganizerLicenseType,
        OrganizerLicenseId,
        ServiceType = 'WEB',
        AdditionalQueryCondition, // 新增参数，补充查询条件，APP用
    } = {}) {
        let self = this
        try {
            let params
            if (ServiceType === 'WEB') {
                params = Domain
                    ? { FindContent: Domain, FindType: 0 }
                    : OrganizerLicenseType && OrganizerLicenseId
                    ? {
                          FindType: OrganizerLicenseType,
                          FindContent: OrganizerLicenseId,
                      }
                    : {}
            } else if (ServiceType === 'APP') {
                // 3个参数都必填
                params =
                    Domain && !AdditionalQueryCondition // 仅有域名，没有补充条件时为108
                        ? { FindContent: Domain, FindType: 108 }
                        : OrganizerLicenseType && OrganizerLicenseId
                        ? {
                              FindContent: OrganizerLicenseId, // 证件号码
                              FindType: OrganizerLicenseType, // 证件类型
                              FindContent2: AdditionalQueryCondition, // 补充查询条件，包名
                          }
                        : {}
            }
            if (JSON.stringify(params) === '{}') {
                let e = new Error('参数错误')
                e.code = 10003
                return self.err(e)
            }
            let icpInfo
            try {
                icpInfo = await henganApi('QueryPublicRecordAction', { ...params, timeout: 5000 })
            } catch (err) {
                if (ServiceType === 'APP') {
                    // App备案查询, 龙翊支持多种查询方式，目前我们只使用这两种 域名 或者包名
                    params =
                    Domain && !AdditionalQueryCondition // 仅有域名
                        ? { AppInfo: { Domain } }
                        : OrganizerLicenseId && AdditionalQueryCondition
                        ? {
                            AppInfo: {
                                OrganizerLicenseId,// 证件号码
                                Name: AdditionalQueryCondition,// 补充查询条件，APP名称
                            }
                          }
                        : {}
                } else {
                    // 网站备案查询
                    params = Domain
                        ? { Domain }
                        : OrganizerLicenseType && OrganizerLicenseId
                        ? { OrganizerLicenseType, OrganizerLicenseId }
                        : {}
                }
                if (JSON.stringify(params) === '{}') {
                    let e = new Error('参数错误')
                    e.code = 10003
                    return self.err(e)
                }
                icpInfo = await requestLongYi(params)
            }

            return this.cb(0, icpInfo)
        } catch (e) {
            console.log(e)
            if (e instanceof Error) {
                return self.eh({
                    err: e,
                    code: 30103,
                })
            }
            self.eh(e)
        }
    }
}
