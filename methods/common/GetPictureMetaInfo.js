const Method = require('../../libs/method')
const { getUrl } = require('../../fns/verifyFun')
const { getPictureMeta } = require('../../fns/uflieFuns')

// 报错提示最大和最小像素限制
// 最大值：宽3000 * 高3000 3M
// 最小边，不小于400

// 2021年07月06日11:58:15	使用mg重构，原因：Progressive图片的加载方式会影响ffmpeg处理

module.exports = class GetPictureMetaInfo extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({ PictureName, PictureURL } = {}) {
        let self = this

        const maxPix = 3000
        const minPix = 400
        const maxSize = 3

        let message, compliance, returnObject
        compliance = true
        message = ''

        try {
            // 获取图片URL
            PictureURL = getUrl(PictureURL, PictureName)

            // 获取图片buffer
            let PictureMetaInfo = await getPictureMeta(PictureURL)

            // 图片过大出错
            // PictureMetaInfo["size"] = 4347409
            if (PictureMetaInfo.Size > maxSize * 1024 * 1024) {
                compliance = false
                message = '证件照片不允许超过3M'
            }
            // 图片像素过大出错
            if (
                PictureMetaInfo.Width > maxPix ||
                PictureMetaInfo.Height > maxPix
            ) {
                compliance = false
                message = message + '最长边不能大于' + maxPix + '像素'
            }

            // 图片像素过小出错
            if (
                PictureMetaInfo.Width < minPix ||
                PictureMetaInfo.Height < minPix
            ) {
                compliance = false
                message = message + '最小边不能小于' + minPix + '像素'
            }

            returnObject = { PictureMetaInfo }
            returnObject.Compliance = compliance

            if (compliance === false) {
                returnObject.Message = message
            }

            return self.cb(0, returnObject)
        } catch (e) {
            if (e instanceof Error) {
                return self.eh({
                    err: e,
                    code: 67081,
                })
            }
            self.eh(e)
        }
    }
}
