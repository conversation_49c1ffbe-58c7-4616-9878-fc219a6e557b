/*
 * @Date: 2022-09-19 10:15:11
 * @LastEditors: l<PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2022-09-19 10:22:41
 * @FilePath: /newicp/methods/common/GetProxyCompanyList.js
 */
/**
 * @file API GetProxyCompanyList,获取代理商公司列表
 * <AUTHOR>
 * @return {Object} 返回符合条件的记录,格式如下
 * {
  "Action" : "GetProxyCompanyListResponse",
  "RetCode" : 0,
  "CompanyList" : [
    {
      "Note" : "于巍要求添加",
      "Quota" : 624,
      "Id" : 1584520972,
      "CompanyId" : 3019,
      "UpdateTime" : 1589877164,
      "CompanyType" : 1,
      "CompanyName" : "钱俊烨1111",
      "CreateTime" : 1589792590,
      "Operator" : "unknown"
    }
  ],
  "Count" : 89
}
 * 错误码:
	"30101": "获取Redis线上配置时网络异常",
	"30102": "获取Redis线上配置时结果解析异常",
	"30103": "获取Redis线上配置时其它异常",
 */

const Method = require('../../libs/method')
const { getTableModel } = require('../../fns/kits')

module.exports = class GetProxyCompanyList extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({ Offset = 0, Limit = 10, CompanyId, CompanyType } = {}) {
        let self = this
        const icpDatabase = this.db.get('icp')
        const ProxyCompanyList = getTableModel(
            't_proxy_company_list',
            icpDatabase
        )

        let CompanyList, Count
        try {
            // 查询结构
            let queryObject = {
                offset: Offset,
                limit: Limit,
                order: [['Id', 'DESC']],
                attributes: [
                    'Note',
                    'Quota',
                    'Id',
                    'CompanyId',
                    'UpdateTime',
                    'CompanyType',
                    'CompanyName',
                    'WebsiteQuota',
                    'CreateTime',
                    'Operator',
                    'OneClickRelease',
                ],
            }
            if (CompanyId) {
                queryObject.where = {
                    CompanyId,
                }
            }

            if (CompanyType) {
                queryObject.where = {
                    CompanyType,
                }
            }

            ;[CompanyList, Count] = await Promise.all([
                ProxyCompanyList.findAll(queryObject),
                ProxyCompanyList.count(queryObject),
            ])

            return self.cb(0, { CompanyList, Count })
        } catch (e) {
            if (e instanceof Error) {
                return self.eh({
                    err: e,
                    code: 30103,
                })
            }
            self.eh(e)
        }
    }
}
