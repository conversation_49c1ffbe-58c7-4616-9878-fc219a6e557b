/**
 * @file API CheckAuthNameAndPicName 根据实名、白名单、已备案数量，来确定客户是否可以备案
 * <AUTHOR>
 * @param {CompanyId,OrganizerLicenseId} 公司Id 必选参数,由网关传入条件
 * @return {Object} 返回符合条件的记录,格式如下
 * 是否超过备案限制的流程逻辑

    取实名信息，取白名单信息中的配额

    如果未实名，拒绝备案
    如果配额数超过100，说明是大客户，直接允许备案

    如果实名是个人，但此次需要备案客户与实名的不同，拒绝备案

    计算当前有效主体数据
    计算方式：新增订单的主办单位证件号去重列表A-备案完成列表中注销状态的备案的主办单位证件号列表B，得到A有B没有的
    将A有B没有的+备案完成列表中正常状态的备案的主办单位证件号列表，得到合集，后去重，为目前全部有效备案的列表C

    无论个人与企业，此次备案的证件号，在C列表中，则允许备案

    个人实名，C列表如果长度为0，允许备案
    企业实名，得可备案数，默认可备案6个，有白名单，取白名单中Quota与6中，最大值
    如果C列表如果长度大于可备案数，拒绝备案
    如果C列表如果长度不大于可备案数，实名相同，可备案
    如果C列表如果长度不大于可备案数，实名不相同，可备案，提示“账号实名认证信息与您填写的备案主体信息不一致，您可通过修改备案信息或授权的方式进行备案”

    限制网站总数
    CheckAuthNameAndPicName，检查实名与可备案主体数时增加判断。是否超过指定数，是否超过审核侧后台配置的最大数.
		具体实施逻辑：
		取实名信息，个人为5，企业为10；取配额设置。2者取最大值得限额。
		目前网站使用量=取已完成备案的网站数+流程中订单的网站数（3类新增，未审核完成，未删除）
		目前网站使用量>限额,则超过限制


 */

const Method = require('../../libs/method')
const { Op } = require('sequelize')
const { getTableModel, parseJSON, arrayMinus } = require('../../fns/kits')
const _ = require('lodash')
const { getAuthInfo } = require('../../fns/authFuns')
const logger = require("../../libs/logger");

// 白名单中配额超过多少后，可直接返回正常
const PassQuota = 500

module.exports = class CheckAuthNameAndPicName extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({ CompanyId, LicenseType, OrganizerLicenseId, Channel } = {}) {
        let self = this
        let icpDatabase = this.db.get('icp')

        const ICPModel = getTableModel('t_icp', icpDatabase)
        const OrderModel = getTableModel('t_order', icpDatabase)
        const WebModel = getTableModel('t_web', icpDatabase)
        const OrderWebModel = getTableModel('t_order_web', icpDatabase)

        const ProxyCompanyModel = getTableModel(
            't_proxy_company_list',
            icpDatabase
        )

        // 不知道为啥转一次，但老代码有，就加上吧
        // webCount 目前有效的网站备案数
        // isOverQuota 是否超过
        CompanyId = parseInt(CompanyId)
        let ip_white_info,
            authInfo,
            orderMap,
            icpMap,
            whiteQuota,
            webCount,
            maxWebQuota,
            isOverQuota

        try {
            // 先白名单，然后做值判断，如果已备案数量超过一个大的指定值，直接返回True。提前做的目的是防止代理商这类的大客户取已备案信息时信息过大。
            try {
                ;[ip_white_info, authInfo] = await Promise.all([
                    ProxyCompanyModel.findAll({
                        attributes: ['Quota', 'WebsiteQuota', 'OneClickRelease'],
                        where: {
                            CompanyId,
                        },
                    }),
                    getAuthInfo(CompanyId, Channel)
                ])
                ip_white_info = parseJSON(ip_white_info)
            } catch (error) {
                throw new self.Err(
                    new Error('确定可备案数时，查询出现异常'),
                    32701
                )
            }

            if (authInfo.AuthType === 'NO_AUTH') {
                return self.cb(0, {
                    Message: '未实名认证账号,不可备案',
                    AuthInfo: {
                        AuthType: 'NO_AUTH',
                    },
                    CanRecord: false,
                })
            }
            logger.getLogger("access").info(`CheckAuthNameAndPicName: ${JSON.stringify(ip_white_info)}`)

            if (
                ip_white_info.length !== 0 &&
                ip_white_info[0].OneClickRelease === 1
            ) {
                return self.cb(0, {
                    Message: '未超过限制，可以备案',
                    AuthInfo: authInfo,
                    CanRecord: true,
                })
            }

            whiteQuota = ip_white_info[0]?.Quota || 0

            // 当前备案的是否与实名一致，个人不一致直接驳回，企业不一致，需要提示授权
            const AUTHINFOMATCH =
                authInfo.CertificateNo.toUpperCase() ===
                OrganizerLicenseId.toUpperCase()

            // 2  如果是个人，当前备案的与实名的不一致，返回，不能备案
            if (authInfo.AuthType === '个人认证' && !AUTHINFOMATCH) {
                // 因实名的数据返回有问题，做个修复
                if (authInfo.CertificateType === '') {
                    authInfo.CertificateType = '身份证'
                }

                return self.cb(0, {
                    Message:
                        '个人实名账号仅可备案与实名信息一致的主体，如需增加备案主体请修改为企业实名认证信息。',
                    AuthInfo: authInfo,
                    CanRecord: false,
                })
            }
            // 预留的一个特性，超过100的公司id，可突破实名类型的限制。调整1、2顺序，可把限制加上

            try {
                // 获取实名信息与订单信息与已备案信息
                // 取有效的新增订单，与全部已备案信息
                ;[orderMap, icpMap] = await Promise.all([
                    getOrderMap(OrderModel, CompanyId),
                    getICPMap(ICPModel, CompanyId),
                ])
                orderMap = parseJSON(orderMap)
                icpMap = parseJSON(icpMap)

                // 取出目前在用的网站数
                // 取出订单号与网站Id
                let orderNoList = _.map(orderMap, 'OrderNo')
                let icpIdList = _.map(icpMap, 'Id')

                // 流程中的网站数与完成备案的流程数
                let inProcessWebCount, finishWebCount
                ;[inProcessWebCount, finishWebCount] = await Promise.all([
                    orderNoList.length === 0
                        ? 0
                        : OrderWebModel.count({
                              where: {
                                  OrderNo: {
                                      //   过滤已完成备案的订单记录
                                      [Op.in]: _.map(
                                          _.filter(orderMap, function (o) {
                                              return o.Status !== 12
                                          }),
                                          'OrderNo'
                                      ),
                                  },
                              },
                          }),
                    icpIdList === 0
                        ? 0
                        : WebModel.count({
                              where: {
                                  MainId: { [Op.in]: icpIdList },
                                  // Status: { [Op.ne]: 1 },
                              },
                          }),
                ])
                webCount = inProcessWebCount + finishWebCount
            } catch (error) {
                throw new self.Err(
                    new Error('确定可备案数时，查询出现异常'),
                    32702
                )
            }

            // 计算网站数，是否超过配额
            maxWebQuota = _.max([
                authInfo.AuthType === '个人认证' ? 5 : 10,
                ip_white_info[0]?.WebsiteQuota || 0,
            ])
            isOverQuota = webCount > maxWebQuota

            // 确定有效备案列表
            // 计算方式  新增订单的主办单位证件号去重列表orderLicenseIdList  - 备案完成列表中注销状态的备案的主办单位证件号列表icpexpireLicenseIdList  +  备案完成列表中正常状态的备案的主办单位证件号列表icpEffectiveLicenseIdList
            // 得到正常列表statisticsList后去重
            let orderLicenseIdList,
                icpexpireLicenseIdList,
                icpEffectiveLicenseIdList,
                currentList,
                currentCount

                // 用统一的fun处理
            ;[
                orderLicenseIdList,
                icpexpireLicenseIdList,
                icpEffectiveLicenseIdList,
            ] = mapDataParse(orderMap, icpMap)

            // 订单中的记录减已备案中注销的记录，得差集结
            currentList = arrayMinus(orderLicenseIdList, icpexpireLicenseIdList)

            // 开始做计算
            currentList = currentList.concat(icpEffectiveLicenseIdList)
            currentList = _.uniq(currentList)
            // 去空的元素
            currentList = currentList.filter(
                (licenseId) =>
                    licenseId !== undefined &&
                    licenseId !== '' &&
                    licenseId !== null
            )
            currentCount = currentList.length

            // 开始处理
            // 如果当前备案的证件呈，在已完成的记录中有。则允许备案(新增网站，与接入类)
            if (_.indexOf(currentList, OrganizerLicenseId) !== -1) {
                if (isOverQuota) {
                    return self.cb(0, {
                        Message: `备案的网站总数超过:${maxWebQuota}。`,
                        AuthInfo: authInfo,
                        CanRecord: false,
                        ICPLimit: true,
                    })
                } else {
                    return self.cb(0, {
                        Message: '未超过限制，可以备案',
                        AuthInfo: authInfo,
                        CanRecord: true,
                    })
                }
            }

            // 客户应该有的备案数
            if (authInfo.AuthType === '个人认证') {
                //    个人认证，只能有一个备案
                if (currentCount > 0) {
                    return self.cb(0, {
                        Message:
                            '个人实名账号仅可备案与实名信息一致的主体，如需增加备案主体请修改为企业实名认证信息。',
                        AuthInfo: authInfo,
                        CanRecord: false,
                    })
                } else {
                    if (isOverQuota) {
                        return self.cb(0, {
                            Message: `备案的网站总数超过:${maxWebQuota}`,
                            AuthInfo: authInfo,
                            CanRecord: false,
                            ICPLimit: true,
                        })
                    } else {
                        return self.cb(0, {
                            Message: '未超过限制，可以备案',
                            AuthInfo: authInfo,
                            CanRecord: true,
                        })
                    }
                }
            } else {
                // 企业，计算可备案数量，从（Quota,6中取最大值）
                let maxCount = _.max([whiteQuota, 6])
                if (currentCount < maxCount) {
                    // 可备案
                    if (!AUTHINFOMATCH) {
                        if (isOverQuota) {
                            return self.cb(0, {
                                Message: `备案的网站总数超过:${maxWebQuota}。`,
                                AuthInfo: authInfo,
                                CanRecord: false,
                                ICPLimit: true,
                            })
                        } else {
                            return self.cb(0, {
                                Message:
                                    '账号实名认证信息与您填写的备案主体信息不一致，您可通过修改备案信息或授权的方式进行备案。',
                                IsAuthTip: true,
                                AuthInfo: authInfo,
                                CanRecord: true,
                            })
                        }
                    }

                    if (isOverQuota) {
                        return self.cb(0, {
                            Message: `备案的网站总数超过:${maxWebQuota}。`,
                            AuthInfo: authInfo,
                            CanRecord: false,
                            ICPLimit: true,
                        })
                    }

                    return self.cb(0, {
                        Message: '信息一致，可以备案。',
                        AuthInfo: authInfo,
                        CanRecord: true,
                    })
                } else {
                    return self.cb(0, {
                        Message: `备案的主体总数超过:${maxCount}个。`,
                        AuthInfo: authInfo,
                        CanRecord: false,
                        ICPLimit: true,
                    })
                }
            }
        } catch (e) {
            console.log(e, 333)
            if (e instanceof Error) {
                return self.eh({
                    err: e,
                    code: 67078,
                })
            }
            self.eh(e)
        }
    }
}

// 取订单Map
// 条件，未删除，新增类
async function getOrderMap(OrderModel, CompanyId) {
    return OrderModel.findAll({
        attributes: ['OrganizerLicenseId', 'OrderNo', 'Status'],
        where: {
            Type: { [Op.in]: [1, 2, 3] },
            CompanyId,
            IsDeleted: 0,
        },
    })
}
async function getICPMap(ICPModel, CompanyId) {
    return ICPModel.findAll({
        attributes: ['OrganizerLicenseId', 'Status', 'Id'],
        where: {
            CompanyId,
        },
    })
}

// 订单与已备案记录的处理
function mapDataParse(orderMap, icpMap) {
    let orderLicenseIdList, icpexpireLicenseIdList, icpEffectiveLicenseIdList

    // 新增订单的主办单位证件号
    orderLicenseIdList = _.map(orderMap, 'OrganizerLicenseId')
    orderLicenseIdList = _.uniq(orderLicenseIdList)

    // 注销状态的已备案记录
    icpexpireLicenseIdList = _.find(icpMap, ['Status', 1])
    icpexpireLicenseIdList = _.map(icpexpireLicenseIdList, 'OrganizerLicenseId')
    icpexpireLicenseIdList = _.uniq(icpexpireLicenseIdList)

    // 正常状态的已经备案记录
    icpEffectiveLicenseIdList = _.find(icpMap, function (o) {
        return o.Status !== 1
    })
    icpEffectiveLicenseIdList = _.map(
        icpEffectiveLicenseIdList,
        'OrganizerLicenseId'
    )
    icpEffectiveLicenseIdList = _.uniq(icpEffectiveLicenseIdList)

    return [
        orderLicenseIdList,
        icpexpireLicenseIdList,
        icpEffectiveLicenseIdList,
    ]
}
