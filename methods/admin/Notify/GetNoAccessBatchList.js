/**
 * 查询已备案未接入批次列表
 */
const Method = require('../../../libs/method')
const { csvParse, parseJSON } = require('../../../fns/kits')
const { Op, BaseError } = require('sequelize')
const {
    model: UninsertBatchModel,
    BatchStatusEnum: BatchStatusEnum,
    TypeEnum: BatchTypeEnum,
} = require('../../../models/t_no_access_batch')
const {
    model: UninsertBatchRecordModel,
} = require('../../../models/t_no_access_record')
const _ = require('lodash')
const moment = require('moment')
const ip = require('ip')
const uuid = require('uuid/v4')
module.exports = class GetNoAccessBatchList extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({
        Limit,
        Offset,
        BatchId,
        IP,
        BatchName,
        Domain,
        StartTime,
        EndTime,
        GetAll,
    } = {}) {
        let self = this
        try {
            let options = {
                offset: Offset || 0,
                limit: Limit || 20,
            }
            if (GetAll) {
                //获取所有批次列表
                let { rows, count } = await UninsertBatchModel.findAndCountAll({
                    order: [['id', 'desc']],
                })
                rows = parseJSON(rows)
                return self.cb(0, {
                    TotalCount: count,
                    Rows: rows,
                })
            } else {
                //查询记录表条件
                let recordSearchObj = {}
                //查询批次表条件
                let batchSearchObj = {}
                if (BatchId) {
                    batchSearchObj.Id = BatchId
                }
                if (BatchName) {
                    batchSearchObj.BatchName = BatchName
                }
                if (Domain) {
                    recordSearchObj.Domain = {
                        [Op.like]: `%${Domain}%`,
                    }
                }
                if (IP) {
                    recordSearchObj.IP = {
                        [Op.like]: `%${IP}%`,
                    }
                }
                if (StartTime && EndTime) {
                    batchSearchObj.CreateTime = {
                        [Op.between]: [StartTime, EndTime],
                    }
                }
                let records = [],
                    batchIds
                if (JSON.stringify(recordSearchObj) !== '{}') {
                    records = await UninsertBatchRecordModel.findAll({
                        attributes: ['BatchId'],
                        where: {
                            ...recordSearchObj,
                        },
                    })
                    records = parseJSON(records)
                    if (records.length === 0) {
                        return this.cb(0, {
                            TotalCount: 0,
                            Rows: 0,
                        })
                    } else {
                        batchIds = _.map(records, (info) => info.BatchId)
                        batchSearchObj.Id = {
                            [Op.in]: batchIds,
                        }
                    }
                }
                console.log(batchSearchObj, 'batchSearchObj')
                let { rows, count } = await UninsertBatchModel.findAndCountAll({
                    where: {
                        ...batchSearchObj,
                    },
                    ...options,
                    order: [['id', 'desc']],
                })
                rows = parseJSON(rows)
                return self.cb(0, {
                    TotalCount: count,
                    Rows: rows,
                })
            }
        } catch (e) {
            console.log(e)
            if (e instanceof Error) {
                return this.eh({
                    err: e,
                    code: 36015,
                })
            }
            self.eh(e)
        }
    }
}
