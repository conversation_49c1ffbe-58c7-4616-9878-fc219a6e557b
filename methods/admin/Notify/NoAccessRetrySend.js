'use strict'

const Method = require('../../../libs/method')
const { parseJSON } = require('../../../fns/kits')
const {
    model: UninsertBatchModel,
    BatchStatusEnum,
} = require('../../../models/t_no_access_batch')
const {
    model: UninsertBatchRecordModel,
} = require('../../../models/t_no_access_record')
const {
    model: CompanyNotifyModel,
    RecordStatusEnum,
} = require('../../../models/t_no_access_org_info')
const {
    model: NoAccessNotifyModel,
    SendStatusEnum,
    NotifyTypeEnum,
} = require('../../../models/t_no_access_notify')
const { Op } = require('sequelize')
const Cron = require('../../../libs/cron')
const _ = require('lodash')
/**
 * 重新发送通知
 */
module.exports = class NoAccessRetrySend extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({ Id, Type }) {
        let self = this
        const producer = this.producer
        try {
            if (!Id) {
                let err = new Error('数据异常，不支持重发短信')
                err.code = 36003
                throw err
            }
            let obj = {}
            if (Type === NotifyTypeEnum.Email) {
                obj['EmailStatus'] = {
                    [Op.in]: [
                        SendStatusEnum.Timeout,
                        SendStatusEnum.Sendfailed,
                        SendStatusEnum.Recivefailed,
                    ],
                }
            } else {
                obj['SmsStatus'] = {
                    [Op.in]: [
                        SendStatusEnum.Timeout,
                        SendStatusEnum.Sendfailed,
                        SendStatusEnum.Recivefailed,
                    ],
                }
            }
            let notifyInfo = await NoAccessNotifyModel.findOne({
                where: {
                    Id,
                    ...obj,
                },
            })
            notifyInfo = parseJSON(notifyInfo)
            if (!notifyInfo) {
                let err = new Error('该通知状态不支持重新发送')
                err.code = 36004
                throw err
            }
            let companyInfo = await CompanyNotifyModel.findOne({
                where: {
                    Id: notifyInfo.BatchOrgId,
                },
            })
            companyInfo = parseJSON(companyInfo)
            console.log(companyInfo, 'companyInfo')
            let batchInfo = await UninsertBatchModel.findOne({
                where: {
                    Id: companyInfo.BatchId,
                },
            })
            batchInfo = parseJSON(batchInfo)
            console.log(batchInfo, 'batchInfo')
            if (batchInfo.Status === BatchStatusEnum.Finish) {
                let err = new Error('批次已完成，不支持重发短信')
                err.code = 36003
                throw err
            }
            let statusObj = {}
            if (Type === NotifyTypeEnum.Email) {
                statusObj.EmailStatus = SendStatusEnum.New
            } else {
                statusObj.SmsStatus = SendStatusEnum.New
            }
            //更新批次表状态，通知表状态，org表状态
            await Promise.all([
                NoAccessNotifyModel.update(
                    { ...statusObj },
                    {
                        where: {
                            Id,
                        },
                    }
                ),
                UninsertBatchModel.update(
                    {
                        Status: BatchStatusEnum.Sending,
                    },
                    {
                        where: {
                            Id: batchInfo.Id,
                        },
                    }
                ),
                CompanyNotifyModel.update(
                    {
                        Status: RecordStatusEnum.Sending,
                    },
                    {
                        where: {
                            Id: companyInfo.Id,
                        },
                    }
                ),
            ])
            //发送消息
            await producer.send({
                type: 'icp',
                topic: 'NoAccessNotifyRetry',
                data: {
                    ...notifyInfo,
                    BatchId: companyInfo.BatchId,
                    BatchOrgId: companyInfo.Id,
                    NotifyType: Type,
                },
            })
            this.cb(0)
        } catch (e) {
            console.log(e)
            self.err(e)
        }
    }
}
