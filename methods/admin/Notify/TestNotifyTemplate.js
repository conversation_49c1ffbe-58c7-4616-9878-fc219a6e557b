/*
 * @Date: 2022-09-15 15:51:31
 * @LastEditors: l<PERSON><PERSON><PERSON><PERSON> y<PERSON><EMAIL>
 * @LastEditTime: 2022-10-13 15:55:43
 * @FilePath: /newicp/methods/admin/Notify/TestNotifyTemplate.js
 */
/**
 * @file API ModifyTeTestNotifyTemplatemplate 测试模板通知
 * <AUTHOR>
 * @return {} 成功与否
 * todo 确定是否增加权限管制
 */

const Method = require('../../../libs/method')
const {
    getMailContent,
    getSmsContent,
} = require('../../../fns/notify/GetNotifyContent')
const { sendMail, sendSms } = require('../../../fns/umsMsgSend')

module.exports = class TestNotifyTemplate extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({ Type, NotifyType, SendTo }) {
        let self = this
        try {
            let taskId
            if (NotifyType) {
                // 邮件
                const { Content, Title, AttachFiles } = await getMailContent(
                    [],
                    Type
                )
                console.log(Content, 'ContentMail')
                console.log(Title, 'Title')
                //发送通知
                taskId = await sendMail({
                    email: SendTo,
                    content: Content,
                    title: Title,
                    attachFiles: AttachFiles,
                })
            } else {
                let content = await getSmsContent(Type, SendTo)
                console.log(content, 'ContentSms')
                taskId = await sendSms({
                    mobile: SendTo,
                    content,
                })
                return taskId
            }

            return this.cb(0, { taskId })
        } catch (e) {
            console.log(e)
            if (e instanceof Error) {
                return self.eh({
                    err: e,
                    code: 30401,
                })
            }
            self.eh(e)
        }
    }
}
