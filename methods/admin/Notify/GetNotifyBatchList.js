/**
 * @file API GetNotifyBatchList 获取通知批次列表
 * <AUTHOR>
 * @return {} 成功与否
 * todo 确定是否增加权限管制
 */

const Method = require('../../../libs/method')
const { Op } = require('sequelize')
const { parseJSON } = require('../../../fns/kits')
const {
    model: RecordModel,
    StatusEnum: SendStatusEnum,
} = require('../../../models/t_notify_companyInfo')
const {
    model: NotifyBatchModel,
    TypeEnum: BatchTypeEnum,
} = require('../../../models/t_notify_batch')
const _ = require('lodash')

module.exports = class GetNotifyBatchList extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({
        Id,
        CompanyId,
        Domain,
        Type,
        BeginTime,
        EndTime,
        Limit,
        Offset,
    } = {}) {
        let self = this
        try {
            let options = {
                offset: Offset || 0,
                limit: Limit || 20,
            }
            if (Id) {
                let { rows, count } = await NotifyBatchModel.findAndCountAll({
                    where: {
                        Id,
                        Type: {
                            [Op.ne]: BatchTypeEnum.Unresource,
                        },
                    },
                    ...options,
                    order: [['id', 'desc']],
                })
                rows = parseJSON(rows)
                return self.cb(0, {
                    TotalCount: count,
                    Rows: rows,
                })
            } else {
                let recordSearchObj = {}
                let batchSearchObj = {}
                batchSearchObj.Type = Type ?? {
                    [Op.ne]: BatchTypeEnum.Unresource,
                }
                if (CompanyId) {
                    recordSearchObj.CompanyId = CompanyId
                }
                if (Domain) {
                    recordSearchObj.ICPInfo = {
                        [Op.like]: `%"Domain":"${Domain}"%`,
                    }
                }
                if (BeginTime && EndTime) {
                    batchSearchObj.CreateTime = {
                        [Op.between]: [BeginTime, EndTime],
                    }
                }
                let records = [],
                    batchIds
                if (JSON.stringify(recordSearchObj) !== '{}') {
                    records = await RecordModel.findAll({
                        attributes: ['BatchId'],
                        where: {
                            ...recordSearchObj,
                        },
                    })
                    records = parseJSON(records)
                    if (records.length === 0) {
                        return this.cb(0, {
                            TotalCount: 0,
                            Rows: 0,
                        })
                    } else {
                        batchIds = _.map(records, (info) => info.BatchId)
                        batchSearchObj.Id = {
                            [Op.in]: batchIds,
                        }
                    }
                }
                let { rows, count } = await NotifyBatchModel.findAndCountAll({
                    where: {
                        ...batchSearchObj,
                    },
                    ...options,
                    order: [['id', 'desc']],
                })
                rows = parseJSON(rows)
                return self.cb(0, {
                    TotalCount: count,
                    Rows: rows,
                })
            }
        } catch (e) {
            console.log(e)
            if (e instanceof Error) {
                return self.eh({
                    err: e,
                    code: 30401,
                })
            }
            self.eh(e)
        }
    }
}
