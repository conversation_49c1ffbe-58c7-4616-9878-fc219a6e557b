/**
 * @file API NotifyBatch 发送批次通知
 * <AUTHOR>
 * @return {} 成功与否
 */

const Method = require('../../../libs/method')
const { Op } = require('sequelize')
const {
    model: NotifyBacthModel,
    BatchStatusEnum,
} = require('../../../models/t_notify_batch')
const {
    model: RecordModel,
    RecordStatusEnum,
} = require('../../../models/t_notify_companyInfo')
const {
    model: NotifyModel,
    SendStatusEnum,
    NotifyTypeEnum,
} = require('../../../models/t_notify')
const {
    NotifyTypeModel,
    NotifyTemplateModel,
} = require('../../../mongoModels/icp')
const _ = require('lodash')
const { parseJSON } = require('../../../fns/kits')

module.exports = class NotifyBatch extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({ Id } = {}) {
        let self = this
        const producer = this.producer
        const redis = this.redis.get()
        try {
            // 获取指定公司的特殊通知处理
            let companyIds = await redis.smembers('forbidden_notify_companyId')
            companyIds = companyIds.map((info) => parseInt(info))

            let [notifyBatch, batchRecords] = await Promise.all([
                NotifyBacthModel.findOne({
                    attributes: ['Type', 'Status'],
                    where: {
                        Id,
                    },
                }),
                RecordModel.findAll({
                    attributes: ['Id', 'CompanyId', 'AttachFiles'],
                    where: {
                        BatchId: Id,
                    },
                }),
            ])
            batchRecords = parseJSON(batchRecords)
            notifyBatch = parseJSON(notifyBatch)

            // 检查是否有相关的通知模板
            let notifyType = await NotifyTypeModel.findOne({
                Type: notifyBatch.Type,
            })
            //计费相关的类型 通知 不过滤任何的公司
            if (notifyType.Name.split('订单计费').length > 1) {
                companyIds = []
            }
            if (!notifyType) {
                // 请先设置该类型的通知模板
                let err = new Error('请先设置该类型的通知模板')
                err.code = 35221
                return this.err(err)
            }

            let tempInfo = await NotifyTemplateModel.findOne(
                {
                    TypeId: notifyType._id,
                },
                { EmailContent: 1, SmsContent: 1, Title: 1 }
            )

            let notNotifyType
            if (!tempInfo.EmailContent?.trim() || !tempInfo.Title?.trim()) {
                // 没有设定邮件通知
                notNotifyType = NotifyTypeEnum.Email
            }

            if (!tempInfo.SmsContent) {
                // 没有设定短信通知
                notNotifyType = NotifyTypeEnum.Phone
            }
            if (![BatchStatusEnum.Parsed].includes(notifyBatch.Status)) {
                let err = new Error('该批次状态不支持发送通知')
                err.code = 35203
                return this.err(err)
            }

            let notNotifyRecordIds = [],
                companyNotifyIds = []
            batchRecords.forEach((info) => {
                if (companyIds.includes(info.CompanyId)) {
                    notNotifyRecordIds.push(info.Id)
                } else {
                    companyNotifyIds.push(info.Id)
                }
            })
            let promiseArr = []
            // 直接修改禁止发送的 记录的发送状态
            if (notNotifyRecordIds.length > 0) {
                promiseArr.push(
                    RecordModel.update(
                        { Status: RecordStatusEnum.Forbidden },
                        {
                            where: {
                                Id: {
                                    [Op.in]: notNotifyRecordIds,
                                },
                            },
                        }
                    )
                )
                promiseArr.push(
                    NotifyModel.update(
                        { SendStatus: SendStatusEnum.Forbidden },
                        {
                            where: {
                                CompanyNotifyId: {
                                    [Op.in]: notNotifyRecordIds,
                                },
                            },
                        }
                    )
                )
            }
            // 将每条需要发送数据的 发送状态更改为禁止发送
            if (notNotifyType !== undefined && companyNotifyIds.length !== 0) {
                promiseArr.push(
                    NotifyModel.update(
                        { SendStatus: SendStatusEnum.Forbidden },
                        {
                            where: {
                                Type: notNotifyType,
                                CompanyNotifyId: {
                                    [Op.in]: companyNotifyIds,
                                },
                            },
                        }
                    )
                )
            }
            if (companyNotifyIds.length === 0) {
                // 若无可发送的数据 直接更改 批次状态为发送完成
                promiseArr.push(
                    NotifyBacthModel.update(
                        { Status: BatchStatusEnum.SendFinish },
                        {
                            where: {
                                Id,
                            },
                        }
                    )
                )
            }
            await Promise.all(promiseArr)

            if (companyNotifyIds.length === 0) {
                // 批次没有要发送的 或者只包含禁止发送的 直接返回
                return this.cb(0)
            }
            // 修改批次和记录的状态为发送中
            let [batchUpdated, recordUpdated, notifyInfos] = await Promise.all([
                NotifyBacthModel.update(
                    { Status: BatchStatusEnum.Sending },
                    {
                        where: {
                            Id,
                        },
                    }
                ),
                RecordModel.update(
                    { Status: RecordStatusEnum.Sending },
                    {
                        where: {
                            Id: {
                                [Op.in]: companyNotifyIds,
                            },
                        },
                    }
                ),
                NotifyModel.findAll({
                    where: {
                        [Op.and]: [
                            {
                                CompanyNotifyId: {
                                    [Op.in]: companyNotifyIds,
                                },
                            },
                            { Type: { [Op.ne]: notNotifyType ?? 2 } }, //使用2一个不存在的值过滤相当于未过滤
                        ],
                    },
                }),
            ])
            notifyInfos = parseJSON(notifyInfos)

            for (let i = 0; i < notifyInfos.length; i++) {
                // 发送消息队列
                await producer.send({
                    type: 'icp',
                    topic: 'NotifyEmailOrSms',
                    data: {
                        Id: notifyInfos[i].Id,
                        CompanyNotifyId: notifyInfos[i].CompanyNotifyId,
                        Contact: notifyInfos[i].Contact,
                        Type: notifyInfos[i].Type, // 短信or邮件
                        NotifyType: notifyBatch.Type, // 域名过期 or 企业四要素 or 实名与备案不一致
                        RetryTime: notifyInfos[i].RetryTime,
                        NotifyVersion: 'xxxx',
                        AttachFiles: _.find(batchRecords, [
                            'Id',
                            notifyInfos[i].CompanyNotifyId,
                        ])?.AttachFiles,
                    },
                })
            }
            // 将查询状态推入至延迟队列中 5分钟一次查询
            await producer.send({
                type: 'icp-delay',
                topic: 'CheckBatchSendStatus',
                data: {
                    BatchId: Id,
                },
                opts: {
                    headers: { 'x-delay': 5 * 60 * 1000 }, // 延迟5分钟检查时间
                },
            })
            return this.cb(0)
        } catch (e) {
            console.log(e)
            if (e instanceof Error) {
                return self.eh({
                    err: e,
                    code: 30401,
                })
            }
            self.eh(e)
        }
    }
}
