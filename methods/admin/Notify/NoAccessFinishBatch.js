'use strict'
/**
 * 完成批次处理
 */
const Method = require('../../../libs/method')
const { parseJSON } = require('../../../fns/kits')
const {
    model: UninsertBatchModel,
    BatchStatusEnum,
} = require('../../../models/t_no_access_batch')
const moment = require('moment')

module.exports = class NoAccessFinishBatch extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({ BatchId } = {}) {
        let self = this
        try {
            let batchInfo = await UninsertBatchModel.findOne({
                where: {
                    Id: BatchId,
                },
            })
            if (!batchInfo) {
                //批次不存在
                let err = new Error('批次不存在')
                err.code = 36001
                throw err
            }
            batchInfo = parseJSON(batchInfo)

            if (moment().format('X') - batchInfo.CreateTime > 2 * 60 * 60) {
                //两小时后 才可以操作完成批次
                UninsertBatchModel.update(
                    {
                        Status: BatchStatusEnum.Finish,
                    },
                    {
                        where: {
                            Id: BatchId,
                        },
                    }
                )
            } else {
                let err = new Error('该批次当前不允许直接操作为完成')
                err.code = 36009
                throw err
            }
            this.cb(0)
        } catch (e) {
            console.log(e)
            self.err(e)
        }
    }
}
