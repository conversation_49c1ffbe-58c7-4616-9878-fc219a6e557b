'use strict'

const Method = require('../../../libs/method')
const { parseJSON } = require('../../../fns/kits')
const {
    model: UninsertBatchRecordModel,
} = require('../../../models/t_no_access_record')
const _ = require('lodash')
const { Op, BaseError } = require('sequelize')

/**
 * 查看已备案未接入批次异常数据
 */
module.exports = class GetNoAccessAbnormalData extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({
        BatchId, //批次Id
        GetAll, //是否获取全量数据
        Offset = 0,
        Limit = 20,
    }) {
        let self = this
        try {
            let recordWhere = {
                [Op.or]: [
                    { BatchOrgId: 0 },
                    { RegisterStatus: { [Op.in]: [1, 2] } },
                ],
                BatchId,
            }
            let limit = {}
            if (!GetAll) {
                limit.limit = Limit
                limit.offset = Offset
            }
            let { rows: records, count } = parseJSON(
                await UninsertBatchRecordModel.findAndCountAll({
                    where: recordWhere,
                    ...limit,
                })
            )

            if (count === 0) {
                return this.cb(0, {
                    RecordList: [],
                    TotalCount: 0,
                })
            }
            records = records.map((item) => {
                //register_status,在白名单1，在我司接入2，不在0
                if (item.RegisterStatus === 1) {
                    item['Reason'] = '域名在白名单'
                } else if (item.RegisterStatus === 2) {
                    item['Reason'] = '域名已在我司接入'
                } else {
                    if (!item.BatchOrgId) {
                        item['Reason'] = '找不到公司信息的ip'
                    }
                }
                return item
            })
            return this.cb(0, {
                RecordList: records,
                TotalCount: count,
            })
        } catch (e) {
            console.log(e)
            self.err(e)
        }
    }
}
