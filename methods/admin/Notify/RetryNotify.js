'use strict'

const Method = require('../../../libs/method')
const { parseJSON } = require('../../../fns/kits')
const {
    model: BatchModel,
    BatchStatusEnum,
} = require('../../../models/t_notify_batch')
const {
    model: RecordModel,
    RecordStatusEnum,
} = require('../../../models/t_notify_companyInfo')
const {
    model: NotifyModel,
    SendStatusEnum,
    NotifyTypeEnum,
} = require('../../../models/t_notify')
const { Op } = require('sequelize')
const Cron = require('../../../libs/cron')
const _ = require('lodash')
/**
 * 重新发送通知
 */
module.exports = class RetryNotify extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({ Id }) {
        let self = this
        const producer = this.producer

        try {
            let notifyInfo = await NotifyModel.findOne({
                where: {
                    Id,
                    SendStatus: {
                        [Op.in]: [
                            SendStatusEnum.Timeout,
                            SendStatusEnum.Sendfailed,
                            SendStatusEnum.Recivefailed,
                        ],
                    },
                },
            })
            notifyInfo = parseJSON(notifyInfo)
            if (!notifyInfo) {
                let err = new Error('该通知不支持重新发送')
                err.code = 35204
                throw err
            }

            let recordInfo = await RecordModel.findOne({
                where: {
                    Id: notifyInfo.CompanyNotifyId,
                },
            })
            recordInfo = parseJSON(recordInfo)

            let batchInfo = await BatchModel.findOne({
                attributes: ['Id', 'Type'],
                where: {
                    Id: recordInfo.BatchId,
                },
            })
            batchInfo = parseJSON(batchInfo)

            if (
                batchInfo.Status === BatchStatusEnum.Finish ||
                recordInfo.Status === RecordStatusEnum.Forbidden
            ) {
                let err = new Error('该批次不支持重发短信')
                err.code = 35203
                throw err
            }
            await Promise.all([
                NotifyModel.update(
                    {
                        SendStatus: SendStatusEnum.New,
                    },
                    {
                        where: {
                            Id,
                        },
                    }
                ),
                BatchModel.update(
                    {
                        Status: BatchStatusEnum.Sending,
                    },
                    {
                        where: {
                            Id: batchInfo.Id,
                        },
                    }
                ),
                RecordModel.update(
                    {
                        Status: RecordStatusEnum.Sending,
                    },
                    {
                        where: {
                            Id: recordInfo.Id,
                        },
                    }
                ),
            ])
            await producer.send({
                type: 'icp',
                topic: 'NotifyEmailOrSms',
                data: {
                    Id: notifyInfo.Id,
                    CompanyNotifyId: notifyInfo.CompanyNotifyId,
                    Contact: notifyInfo.Contact,
                    Type: notifyInfo.Type,
                    NotifyType: batchInfo.Type, // 域名过期 or 企业四要素 or 实名与备案不一致
                    RetryTime: notifyInfo.RetryTime,
                    NotifyVersion: 'xxxx',
                },
            })
            // 查看是否存在该批次状态检查的任务， 若存在则不添加检查了，否则添加检查
            await producer.send({
                type: 'icp-delay',
                topic: 'CheckBatchSendStatus',
                data: {
                    BatchId: notifyInfo.Id,
                },
                opts: {
                    headers: { 'x-delay': 5 * 60 * 1000 }, // 延迟5分钟检查时间
                },
            })
            this.cb(0)
        } catch (e) {
            console.log(e)
            self.err(e)
        }
    }
}
