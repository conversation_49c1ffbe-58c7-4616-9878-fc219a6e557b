/*
 * @Date: 2022-12-01 09:46:37
 * @LastEditors: l<PERSON><PERSON><PERSON><PERSON> y<PERSON><EMAIL>
 * @LastEditTime: 2023-04-03 10:44:29
 * @FilePath: /newicp/methods/admin/Notify/GetNotifyTypeList.js
 */
/**
 * @file API GetNotifyTypeList 获取通知类型列表
 * <AUTHOR>
 * @return {} 成功与否
 * todo 确定是否增加权限管制
 */

const Method = require('../../../libs/method')
const { NotifyTypeModel } = require('../../../mongoModels/icp')
module.exports = class GetNotifyTypeList extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({ Offset, Limit }) {
        let self = this
        try {
            let [types, TotalCount] = await Promise.all([
                NotifyTypeModel.find().sort({ Type: -1 }),
                NotifyTypeModel.find().count(),
            ])

            return this.cb(0, { Rows: types, TotalCount })
        } catch (e) {
            console.log(e)
            if (e instanceof Error) {
                return self.eh({
                    err: e,
                    code: 30401,
                })
            }
            self.eh(e)
        }
    }
}
