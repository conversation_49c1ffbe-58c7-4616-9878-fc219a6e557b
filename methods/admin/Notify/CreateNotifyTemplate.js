/*
 * @Date: 2022-09-15 15:51:31
 * @LastEditors: l<PERSON><PERSON><PERSON><PERSON> y<PERSON><EMAIL>
 * @LastEditTime: 2022-10-19 16:30:13
 * @FilePath: /newicp/methods/admin/Notify/CreateNotifyTemplate.js
 */
/**
 * @file API CreateNotifyTemplate 创建通知模板
 * <AUTHOR>
 * @return {} 成功与否
 * todo 确定是否增加权限管制
 */

const Method = require('../../../libs/method')
const {
    NotifyTemplateModel,
    NotifyTypeModel,
} = require('../../../mongoModels/icp')
const moment = require('moment')
module.exports = class CreateNotifyTemplate extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({
        Name,
        TableTitle,
        Title,
        EmailContent,
        SmsContent,
        AttachFiles,
        __ssoUser,
    } = {}) {
        let self = this
        try {
            if (EmailContent.trim() === '' && SmsContent.trim() === '') {
                //参数校验错误
                let err = new Error('请设置通知内容')
                err.code = 10003
                throw err
            }
            let date = moment().format('X')
            let types = await NotifyTypeModel.findOne({
                Name,
            })

            if (types) {
                // 报错 该通知类型已存在
                let err = new Error('该通知类型已存在')
                err.code = 35222
                throw err
            }
            types = await NotifyTypeModel.findOne().sort({ Type: -1 })

            let Type = types ? types.Type + 1 : 0
            console.log(Type)
            let insertType = await new NotifyTypeModel({
                Type,
                Name,
                UpdatedBy: __ssoUser,
                CreatedTime: date,
                UpdatedTime: date,
            }).save()
            let TypeId = insertType._id
            await new NotifyTemplateModel({
                TypeId,
                Title,
                TableTitle,
                EmailContent,
                SmsContent,
                AttachFiles,
                Version: 'v1',
                CreatedTime: date,
                UpdatedTime: date,
            }).save()

            return this.cb(0)
        } catch (e) {
            self.err(e)
        }
    }
}
