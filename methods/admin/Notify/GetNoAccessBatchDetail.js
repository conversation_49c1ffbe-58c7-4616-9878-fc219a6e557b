/**
 * @file API GetNoAccessBatchDetail 查看已备案未接入批次通知详情
 */
const Method = require('../../../libs/method')
const { Op } = require('sequelize')
const { parseJSON } = require('../../../fns/kits')
const {
    model: CompanyNotifyModel,
    RecordStatusEnum: SendStatusEnum,
} = require('../../../models/t_no_access_org_info')
const {
    model: UninsertBatchModel,
    BatchStatusEnum: BatchStatusEnum,
    TypeEnum: BatchTypeEnum,
} = require('../../../models/t_no_access_batch')
const {
    model: UninsertBatchRecordModel,
} = require('../../../models/t_no_access_record')
const {
    model: NoAccessNotifyModel,
    NotifyTypeEnum: NotifyTypeEnum,
} = require('../../../models/t_no_access_notify')
const {
    getMailContent,
    getSmsContent,
} = require('../../../fns/notify/GetNotifyContent')
const {
    NotifyTemplateModel,
    NotifyTypeModel,
} = require('../../../mongoModels/icp')
const _ = require('lodash')
module.exports = class GetNoAccessBatchDetail extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({
        BatchId,
        OrganizationId,
        BatchName,
        BatchOrgId,
        CompanyId,
        CompanyName,
        Status,
        Domain,
        IP,
        Limit,
        Offset,
        GetAll,
        Id,
    } = {}) {
        let self = this
        try {
            let options = GetAll
                ? {}
                : { offset: Offset || 0, limit: Limit || 20 }
            if (Id) {
                //传 ID 是为了获取记录详情， 需要返回具体通知人的相关信息
                let [recordInfo, notifies] = await Promise.all([
                    CompanyNotifyModel.findOne({
                        where: {
                            Id,
                        },
                    }),
                    NoAccessNotifyModel.findAll({
                        where: {
                            BatchOrgId: Id,
                        },
                    }),
                ])
                recordInfo = parseJSON(recordInfo)
                notifies = parseJSON(notifies)
                let batchInfo = await UninsertBatchModel.findOne({
                    where: {
                        Id: BatchId,
                    },
                })
                if (recordInfo) {
                    recordInfo.NotifyDetail = notifies
                    //获取已备案未接入通知type
                    let getNotifyType = await NotifyTypeModel.findOne({
                        Name: '已备案未接入通知',
                    })
                    getNotifyType = parseJSON(getNotifyType)
                    //获取通知的ip和domain,SubDomain，可去数据库查询[{ip:"",domain:""}]
                    let ICPInfo = await UninsertBatchRecordModel.findAll({
                        attributes: ['IP', 'Domain', 'SubDomain', 'ICPWebNo'],
                        where: {
                            BatchOrgId: Id,
                            RegisterStatus: {
                                [Op.in]: [0],
                            },
                        },
                    })
                    ICPInfo = parseJSON(ICPInfo)
                    //封禁的信息
                    recordInfo.ICPInfo = ICPInfo
                    let [smsContent, emailContent] = await Promise.all([
                        getSmsContent(getNotifyType.Type, recordInfo.MainMail),
                        getMailContent(ICPInfo, getNotifyType.Type),
                    ])
                    recordInfo.SmsContent = smsContent
                    recordInfo.EmailContent = emailContent.Content
                    return this.cb(0, {
                        BatchStatus: batchInfo.Status,
                        RecordList: [recordInfo],
                        TotalCount: 1,
                    })
                }
                return this.cb(0, {
                    BatchStatus: batchInfo.Status,
                    RecordList: [],
                    TotalCount: 0,
                })
            }

            let searchArr = {}
            let recordArr = {}
            let batcArr = {}
            if (BatchName) {
                batcArr.Name = BatchName
            }
            if (BatchId) {
                searchArr.BatchId = BatchId
            }
            if (CompanyId) {
                searchArr.CompanyId = CompanyId
            }
            if (CompanyName) {
                searchArr.CompanyName = CompanyName
            }
            if (Status) {
                searchArr.Status = {
                    [Op.in]: Status,
                }
            }
            if (BatchOrgId) {
                searchArr.Id = BatchOrgId
            }
            if (OrganizationId) {
                searchArr.OrganizationId = OrganizationId
            }
            if (Domain) {
                recordArr.Domain = {
                    [Op.like]: `%${Domain}%`,
                }
            }
            if (IP) {
                recordArr.IP = {
                    [Op.like]: `%${IP}%`,
                }
            }
            let records = [],
                batchOrgIds
            if (JSON.stringify(recordArr) !== '{}') {
                records = await UninsertBatchRecordModel.findAll({
                    where: {
                        BatchId: BatchId,
                        ...recordArr,
                    },
                    order: [['id', 'desc']],
                })
                records = parseJSON(records)
                if (records.length === 0) {
                    return this.cb(0, {
                        TotalCount: 0,
                        Rows: 0,
                    })
                } else {
                    batchOrgIds = _.map(records, (info) => info.BatchOrgId)
                    searchArr.Id = {
                        [Op.in]: batchOrgIds,
                    }
                }
            }
            let [batchInfo, { count, rows }] = await Promise.all([
                UninsertBatchModel.findOne({
                    where: {
                        Id: BatchId,
                        ...batcArr,
                    },
                }),
                CompanyNotifyModel.findAndCountAll({
                    where: {
                        ...searchArr,
                    },
                    ...options,
                    order: [['id', 'desc']],
                }),
            ])
            batchInfo = parseJSON(batchInfo)
            rows = parseJSON(rows)
            return this.cb(0, {
                TotalCount: count,
                BatchStatus: batchInfo.Status,
                Rows: rows,
            })
        } catch (e) {
            console.log(e)
            if (e instanceof Error) {
                return self.eh({
                    err: e,
                    code: 36011,
                })
            }
            self.eh(e)
        }
    }
}
