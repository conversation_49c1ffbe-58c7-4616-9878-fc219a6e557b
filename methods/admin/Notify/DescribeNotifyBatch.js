/**
 * @file API DescribeNotifyBatch 查看通知批次详情
 * <AUTHOR>
 * @return {} 成功与否
 * todo 确定是否增加权限管制
 */

const Method = require('../../../libs/method')
const { Op } = require('sequelize')
const { parseJSON } = require('../../../fns/kits')
const {
    model: RecordModel,
    StatusEnum: SendStatusEnum,
} = require('../../../models/t_notify_companyInfo')
const { model: NotifyBatchModel } = require('../../../models/t_notify_batch')
const _ = require('lodash')

module.exports = class DescribeNotifyBatch extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({
        Id,
        CompanyId,
        Status,
        Domain,
        MainName,
        ICPMainNo,
        LicenseNo,
        ICPWebNo,
        Limit,
        Offset,
        GetAll,
    } = {}) {
        let self = this
        try {
            let searchArr = {}
            if (CompanyId) {
                searchArr.CompanyId = CompanyId
            }
            if (Status) {
                searchArr.Status = {
                    [Op.in]: Status,
                }
            }
            if (Domain) {
                searchArr.ICPInfo = { [Op.like]: `%"Domain":"${Domain}"%` }
            }
            if (MainName) {
                searchArr.ICPInfo = {
                    [Op.like]: `%"OrganizerName":"${MainName}"%`,
                }
            }
            if (ICPMainNo) {
                searchArr.ICPInfo = {
                    [Op.like]: `%"ICPMainNo":"${ICPMainNo}"%`,
                }
            }
            if (LicenseNo) {
                searchArr.ICPInfo = {
                    [Op.like]: `%"OrganizerLicenseId":"${LicenseNo}"%`,
                }
            }
            if (ICPWebNo) {
                searchArr.ICPInfo = {
                    [Op.like]: `%"ICPWebNo":"${ICPWebNo}"%`,
                }
            }
            let options = GetAll
                ? {}
                : {
                      offset: Offset || 0,
                      limit: Limit || 20,
                  }
            let [batchInfo, { count, rows }] = await Promise.all([
                NotifyBatchModel.findOne({
                    attributes: ['Type', 'Status'],
                    where: {
                        Id,
                    },
                }),
                RecordModel.findAndCountAll({
                    attributes: [
                        'Id',
                        'CompanyId',
                        'CompanyName',
                        'Status',
                        'ICPInfo',
                    ],
                    where: {
                        BatchId: Id,
                        ...searchArr,
                    },
                    ...options,
                    order: [['id', 'desc']],
                }),
            ])
            batchInfo = parseJSON(batchInfo)
            rows = parseJSON(rows)
            return this.cb(0, {
                TotalCount: count,
                Type: batchInfo.Type,
                BatchStatus: batchInfo.Status,
                Rows: rows,
            })
        } catch (e) {
            console.log(e)
            if (e instanceof Error) {
                return self.eh({
                    err: e,
                    code: 30401,
                })
            }
            self.eh(e)
        }
    }
}
