/*
 * @Date: 2022-09-15 15:51:31
 * @LastEditors: l<PERSON><PERSON><PERSON><PERSON> y<PERSON><EMAIL>
 * @LastEditTime: 2022-12-06 18:42:11
 * @FilePath: /newicp/methods/admin/Notify/GetNotifyTemplate.js
 */
/**
 * @file API GetNotifyTemplate 获取通知模板列表
 * <AUTHOR>
 * @return {} 成功与否
 * todo 确定是否增加权限管制
 */

const Method = require('../../../libs/method')
const _ = require('lodash')
const { dateReplace } = require('../../../fns/notify/GetNotifyContent')
const {
    NotifyTemplateModel,
    NotifyTypeModel,
} = require('../../../mongoModels/icp')
const moment = require('moment')
module.exports = class GetNotifyTemplate extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({ Name, Type, Offset, Limit }) {
        let self = this
        try {
            let date = moment().format('YYYY-MM-DD HH:mm:ss')
            let condition = {}
            if (Name) {
                condition.Name = new RegExp(Name.trim(), 'i')
            }
            let needAcctachFiles = {}
            if (Type !== undefined) {
                condition.Type = Type
                needAcctachFiles = { AttachFiles: '$temp.AttachFiles' }
            }
            let [temps, TotalCount] = await Promise.all([
                NotifyTypeModel.aggregate([
                    { $match: condition },
                    { $sort: { Type: -1 } },
                    { $skip: parseInt(Offset) || 0 },
                    { $limit: parseInt(Limit) || 20 },
                    {
                        $lookup: {
                            from: NotifyTemplateModel.collection.name,
                            localField: '_id',
                            foreignField: 'TypeId',
                            as: 'temp',
                        },
                    },
                    { $unwind: '$temp' },
                    {
                        $addFields: {
                            _id: '$temp._id',
                            Title: '$temp.Title',
                            SmsContent: '$temp.SmsContent',
                            EmailContent: '$temp.EmailContent',
                            TableTitle: '$temp.TableTitle',
                            ...needAcctachFiles,
                        },
                    },
                    {
                        $project: {
                            temp: 0,
                            __v: 0,
                        },
                    },
                ]),
                NotifyTypeModel.find(condition).count(),
            ])
            if (JSON.stringify(needAcctachFiles) === '{}') {
                temps.map((t) => {
                    if (t.EmailContent?.trim() && t.Title?.trim()) {
                        let titles = Object.values(t.TableTitle)
                        let tableHead = '<thead>'

                        titles.forEach((title) => {
                            tableHead += `<th>`
                            tableHead += title
                            tableHead += `</th>`
                        })
                        tableHead += '</thead>'
                        t.EmailContent = t.EmailContent.replace(
                            '${table}',
                            '<table  width:"100%" border="1"  cellspacing="0" >' +
                                tableHead +
                                '</table>'
                        )
                        t.EmailContent = dateReplace(t.EmailContent)
                        t.EmailContent = t.EmailContent.replace('${Date}', date)
                        return t
                    }
                })
            }
            return this.cb(0, { Rows: temps, TotalCount })
        } catch (e) {
            console.log(e)
            if (e instanceof Error) {
                return self.eh({
                    err: e,
                    code: 30401,
                })
            }
            self.eh(e)
        }
    }
}
