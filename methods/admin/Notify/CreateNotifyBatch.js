/**
 * @file API CreateNotifyBatch 创建通知批次
 * <AUTHOR>
 * @return {} 成功与否
 * todo 确定是否增加权限管制
 */

const Method = require('../../../libs/method')
const { csvParse, parseJSON } = require('../../../fns/kits')
const {
    model: NotifyBatchModel,
    BatchStatusEnum: BatchStatusEnum,
} = require('../../../models/t_notify_batch')
const {
    model: RecordModel,
    StatusEnum: SendStatusEnum,
} = require('../../../models/t_notify_companyInfo')
const {
    model: NotifyModel,
    NotifyTypeEnum,
} = require('../../../models/t_notify')
const _ = require('lodash')

module.exports = class CreateNotifyBatch extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({ FileName, File, Type, __ssoUser } = {}) {
        let self = this
        let producer = this.producer
        const redis = this.redis.get()

        try {
            // 解析导入的数据
            let record = await csvParse(File, 'CompanyId')
            // 检查导入内容
            if (record.length === 0) {
                //报错 没有数据
                let err = new Error('无导入数据')
                err.code = 35224
                return this.err(err)
            }
            record = record.filter(
                (record) => record['CompanyId'].trim() !== ''
            )

            // 检查完毕后 创建批次
            let batchInfo = await NotifyBatchModel.create({
                Remark: FileName,
                Type,
                Status: BatchStatusEnum.Parsing,
            })
            batchInfo = parseJSON(batchInfo)
            // 获取所有的公司ID
            let companyInfoMap = {}
            let companyNotifyMap = {}

            record.forEach((r) => {
                if (companyInfoMap[r.CompanyId] === undefined) {
                    companyInfoMap[r.CompanyId] = {}
                }
                companyInfoMap[r.CompanyId].CompanyId = r.CompanyId
                companyInfoMap[r.CompanyId].BatchId = batchInfo.Id

                if (companyInfoMap[r.CompanyId].ICPInfo === undefined) {
                    companyInfoMap[r.CompanyId].ICPInfo = []
                }
                let keys = Object.keys(r)
                keys.forEach((k) => {
                    r[k] = r[k].trim().replace('\t', '').replace('（86）', '')
                    if (companyNotifyMap[r.CompanyId] === undefined) {
                        companyNotifyMap[r.CompanyId] = []
                    }
                    if (k.toLocaleLowerCase().indexOf('mail') !== -1) {
                        companyNotifyMap[r.CompanyId].push({
                            Contact: r[k],
                            Type: NotifyTypeEnum.Email,
                        })
                    }
                    if (k.toLocaleLowerCase().indexOf('phone') !== -1) {
                        companyNotifyMap[r.CompanyId].push({
                            Contact: r[k],
                            Type: NotifyTypeEnum.Phone,
                        })
                    }
                })
                companyInfoMap[r.CompanyId].ICPInfo.push(r)
            })
            // 将公司信息插入公司表，批次信息插入批次表，若导入数据中包含通知信息，则将通知信息插入通知表
            let companyInfos = await RecordModel.bulkCreate(
                Object.values(companyInfoMap),
                { ignoreDuplicates: true }
            )
            companyInfos = parseJSON(companyInfos)

            let companyIds = Object.keys(companyInfoMap)
            await redis.sadd(`${batchInfo.Id}_notify_companyInfo`, companyIds)
            if (JSON.stringify(companyNotifyMap) !== '{}') {
                //  说明包含了通知的信息 。插入通知信息
                for (let i = 0; i < companyIds.length; i++) {
                    await producer.send({
                        type: 'icp',
                        topic: 'GetCompanyInfo',
                        data: {
                            BatchId: batchInfo.Id,
                            CompanyId: companyIds[i], // 公司Id
                            CompanyNotifyId: companyInfos[i].Id, // 通知表 和公司表的关联外键id
                        },
                    })
                    companyNotifyMap[companyIds[i]].map((info) => {
                        info.CompanyNotifyId = companyInfos[i].Id
                        return info
                    })
                }
                // 数据都是按照一个公司分一组的 所以 需要去一层数组
                await NotifyModel.bulkCreate(
                    _.flatten(Object.values(companyNotifyMap)),
                    {
                        ignoreDuplicates: true,
                    }
                )
            } else {
                for (let i = 0; i < companyIds.length; i++) {
                    await producer.send({
                        type: 'icp',
                        topic: 'GetCompanyInfo',
                        data: {
                            BatchId: batchInfo.Id,
                            CompanyId: companyIds[i],
                            CompanyNotifyId: companyInfos[i].Id,
                        },
                    })
                }
            }

            return this.cb(0)
        } catch (e) {
            console.log(e)
            if (e instanceof Error) {
                return self.eh({
                    err: e,
                    code: 30401,
                })
            }
            self.eh(e)
        }
    }
}
