/*
 * @Date: 2022-09-15 15:51:31
 * @LastEditors: l<PERSON><PERSON><PERSON><PERSON> y<PERSON><EMAIL>
 * @LastEditTime: 2023-04-24 18:37:38
 * @FilePath: /newicp/methods/admin/Notify/GetNotifyContent.js
 */
/**
 * @file API GetNotifyDetail 获取通知的具体邮件内容
 * <AUTHOR>
 * @return {} 成功与否
 */

const Method = require('../../../libs/method')
const { parseJSON } = require('../../../fns/kits')
const { model: RecordModel } = require('../../../models/t_notify_companyInfo')
const {
    model: NotifyModel,
    NotifyTypeEnum,
} = require('../../../models/t_notify')
const {
    getMailContent,
    getSmsContent,
} = require('../../../fns/notify/GetNotifyContent')
const _ = require('lodash')

module.exports = class GetNotifyContent extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({ Id, Type } = {}) {
        let self = this
        try {
            let notify = await NotifyModel.findOne({
                where: {
                    Id,
                },
            })
            notify = parseJSON(notify)
            if (!notify) {
                return this.cb(0, {
                    Title: '',
                    Content: '',
                    AttachFiles: [],
                })
            }
            let record = await RecordModel.findOne({
                where: {
                    Id: notify.CompanyNotifyId,
                },
            })
            // 短信返回
            if (notify.Type !== NotifyTypeEnum.Email) {
                let Content = await getSmsContent(Type, record.MainMail)
                return this.cb(0, {
                    Title: '',
                    Content,
                    AttachFiles: [],
                })
            }
            // 邮件返回
            let ICPInfo = record.ICPInfo
            let isAdmin = false
            if (
                notify.Contact === record.MainMail ||
                notify.Contact === record.Manager
            ) {
                isAdmin = true
            }

            const { Content, Title, AttachFiles } = await getMailContent(
                ICPInfo,
                Type,
                isAdmin,
                notify.Contact
            )

            return this.cb(0, {
                Title,
                Content,
                AttachFiles,
            })
        } catch (e) {
            console.log(e)
            if (e instanceof Error) {
                return self.eh({
                    err: e,
                    code: 30401,
                })
            }
            self.eh(e)
        }
    }
}
