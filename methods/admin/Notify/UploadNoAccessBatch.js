/**
 * 上传csv批次表格，创建已备案未接入批次
 */
const Method = require('../../../libs/method')
const { csvParse, parseJSON } = require('../../../fns/kits')
const { Op, BaseError } = require('sequelize')
const {
    model: UninsertBatchModel,
    BatchStatusEnum,
} = require('../../../models/t_no_access_batch')
const {
    model: UninsertBatchRecordModel,
} = require('../../../models/t_no_access_record')
const _ = require('lodash')
const moment = require('moment')
const ip = require('ip')
const uuid = require('uuid/v4')
module.exports = class UploadNoAccessBatch extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({ FileName, File, __ssoUser } = {}) {
        let producer = this.producer
        let now = moment().format('X')
        let batchNo = uuid()
        let records = []
        let BatchId
        let redis = this.redis.get()
        try {
            records = await csvParse(File, 'Domain')
            if (records && records.length > 30010) {
                throw Error('upload records length large then 30000')
            }
            // 检查导入内容
            if (records && records.length === 0) {
                //报错 没有数据
                let err = new Error('无导入数据')
                err.code = 36014
                return this.err(err)
            }
            //创建批次，存入已备案未接入批次表中
            let batchInfo = await UninsertBatchModel.create({
                Name: FileName,
                Status: BatchStatusEnum.Pull,
                Operater: __ssoUser,
            })
            batchInfo = parseJSON(batchInfo)
            BatchId = batchInfo.Id
            //组装插入的数据，存入redis缓存中
            let insertData = []
            console.log('records', records)
            records.forEach((record) => {
                if (record.Domain && record.IP) {
                    // 加入批次id精确更新条件
                    let params = {
                        Domain: record.Domain.trim(),
                        IP: record.IP.trim(),
                        BatchId: BatchId,
                        SubDomain: record.SubDomain.trim(),
                    }
                    params.Domain = params.Domain.toLowerCase()
                    params.SubDomain = params.SubDomain.toLowerCase() || ''
                    insertData.push(params)
                }
            })
            console.log('insertData', insertData)
            //去重数据
            let NoAccessDataDomainIP = filterRecordsByUnique(insertData)
            //数据批量存入表中
            await UninsertBatchRecordModel.bulkCreate(NoAccessDataDomainIP, {
                ignoreDuplicates: true,
            })
            //记录 需要ip和domain的数据量，为查询进度做准备
            await redis.set(
                `${BatchId}_NoAccessDataDomainIP`,
                JSON.stringify(NoAccessDataDomainIP)
            )
            await redis.set(
                `${BatchId}_NoAccessDataDomainIPLength`,
                NoAccessDataDomainIP.length
            )
            if (NoAccessDataDomainIP && NoAccessDataDomainIP.length > 0) {
                //域名过滤白名单，用公用方法.
                await producer.send({
                    type: 'icp',
                    topic: 'GetNoAccessDomainStatus',
                    data: { BatchId: BatchId },
                })
                //批次状态改为解析中
                await UninsertBatchModel.update(
                    { Status: BatchStatusEnum.Parsing },
                    {
                        where: {
                            Id: BatchId,
                        },
                    }
                )
            }
            return this.cb(0, {
                BatchId: BatchId,
                BatchNo: batchNo,
            })
        } catch (e) {
            console.log(e)
            if (e instanceof Error) {
                return this.eh({
                    err: e,
                    code: 36013,
                })
            }
            self.eh(e)
        }
    }
}
/*
 * @params {Array}
 * @return {Array} 去重后的数据
 */
function filterRecordsByUnique(records) {
    const ipDict = new Set()
    const newRecords = records.filter((record) => {
        if (ipDict.has(record)) {
            return false
        } else {
            ipDict.add(record)
            return true
        }
    })
    return newRecords
}
