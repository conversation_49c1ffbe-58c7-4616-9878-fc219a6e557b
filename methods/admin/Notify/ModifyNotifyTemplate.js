/*
 * @Date: 2022-09-15 15:51:31
 * @LastEditors: l<PERSON><PERSON><PERSON><PERSON> y<PERSON><EMAIL>
 * @LastEditTime: 2022-10-19 16:56:07
 * @FilePath: /newicp/methods/admin/Notify/ModifyNotifyTemplate.js
 */
/**
 * @file API ModifyTemplate 修改模板内容
 * <AUTHOR>
 * @return {} 成功与否
 * todo 确定是否增加权限管制
 */

const Method = require('../../../libs/method')
const {
    mongo: { ObjectId, MongoError },
} = require('mongoose')
const {
    NotifyTemplateModel,
    NotifyTypeModel,
} = require('../../../mongoModels/icp')
const moment = require('moment')
module.exports = class ModifyNotifyTemplate extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({
        _id,
        TableTitle,
        EmailContent,
        SmsContent,
        Name,
        Title,
        AttachFiles,
        __ssoUser,
    }) {
        let self = this
        try {
            let notifyInfo = await NotifyTemplateModel.findById(
                new ObjectId(_id)
            )
            if (!notifyInfo) {
                //不存在
                let err = new Error('该模板不存在')
                err.code = 35225
                throw err
            }

            if (EmailContent.trim() === '' && SmsContent.trim() === '') {
                //参数校验错误
                let err = new Error('请设置通知内容')
                err.code = 10003
                throw err
            }

            let date = moment().format('X')

            try {
                await NotifyTypeModel.findOneAndUpdate(
                    {
                        _id: notifyInfo.TypeId,
                    },
                    {
                        $set: {
                            Name,
                            UpdateBy: __ssoUser,
                            UpdateTime: date,
                        },
                    }
                )
            } catch (err) {
                if (err instanceof MongoError) {
                    if (err.code === 11000) {
                        if (err.codeName === 'DuplicateKey') {
                            let error = new Error('该类型已存在')
                            error.code = 35222
                            throw error
                        }
                    }
                } else {
                    throw err
                }
            }
            let updateJson = {
                UpdateTime: date,
            }
            if (TableTitle !== undefined) {
                updateJson.TableTitle = TableTitle
            }
            if (EmailContent !== undefined) {
                updateJson.EmailContent = EmailContent
            }
            if (SmsContent !== undefined) {
                updateJson.SmsContent = SmsContent
            }
            if (Title !== undefined) {
                updateJson.Title = Title
            }
            if (AttachFiles) {
                updateJson.AttachFiles = AttachFiles
            }
            await NotifyTemplateModel.findOneAndUpdate(
                {
                    _id: new ObjectId(_id),
                },
                {
                    $set: updateJson,
                }
            )

            return this.cb(0)
        } catch (e) {
            console.log(e)
            self.err(e)
        }
    }
}
