/*
 * @Date: 2022-09-15 15:51:31
 * @LastEditors: l<PERSON><PERSON><PERSON><PERSON> y<PERSON><EMAIL>
 * @LastEditTime: 2023-04-25 10:20:36
 * @FilePath: /newicp/methods/admin/Notify/GetNotifyDetail.js
 */
/**
 * @file API GetNotifyDetail 获取通知详情
 * <AUTHOR>
 * @return {} 成功与否
 */

const Method = require('../../../libs/method')
const { Op } = require('sequelize')
const { parseJSON } = require('../../../fns/kits')
const { model: NotifyBatchModel } = require('../../../models/t_notify_batch')
const { model: RecordModel } = require('../../../models/t_notify_companyInfo')
const { model: NotifyModel } = require('../../../models/t_notify')
const {
    getMailContent,
    getSmsContent,
} = require('../../../fns/notify/GetNotifyContent')
const _ = require('lodash')

module.exports = class GetNotifyDetail extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({ Id } = {}) {
        let self = this
        try {
            let [record, notifyInfos] = await Promise.all([
                RecordModel.findOne({
                    where: {
                        Id,
                    },
                }),
                NotifyModel.findAll({
                    attributes: ['Id', 'Contact', 'SendStatus', 'UpdateTime'],
                    where: {
                        CompanyNotifyId: Id,
                    },
                }),
            ])

            record = parseJSON(record)
            notifyInfos = parseJSON(notifyInfos)
            let batchInfo = await NotifyBatchModel.findOne({
                where: {
                    Id: record.BatchId,
                },
            })
            batchInfo = parseJSON(batchInfo)

            return this.cb(0, {
                Type: batchInfo.Type,
                CompanyInfos: record.ICPInfo,
                NotifyInfos: notifyInfos,
            })
        } catch (e) {
            console.log(e)
            if (e instanceof Error) {
                return self.eh({
                    err: e,
                    code: 30401,
                })
            }
            self.eh(e)
        }
    }
}
