/**
 * 已备案未接入批量发送通知
 */
const Method = require('../../../libs/method')
const { csvParse, parseJSON } = require('../../../fns/kits')
const { Op, BaseError } = require('sequelize')
const {
    model: UninsertBatchModel,
    BatchStatusEnum,
} = require('../../../models/t_no_access_batch')
const {
    model: UninsertBatchRecordModel,
} = require('../../../models/t_no_access_record')
const {
    model: NotifyCompanyInfoModel,
    RecordStatusEnum: RecordStatusEnum,
} = require('../../../models/t_no_access_org_info')
const {
    model: NoAccessNotifyModel,
    SendStatusEnum: SendStatusEnum,
} = require('../../../models/t_no_access_notify')
const _ = require('lodash')
const moment = require('moment')
const ip = require('ip')
const uuid = require('uuid/v4')
module.exports = class NoAccessNotifyBatch extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({ BatchId } = {}) {
        let producer = this.producer
        let batchNo = uuid()
        let records = []
        let redis = this.redis.get()
        try {
            //批次状态改为发送中
            await UninsertBatchModel.update(
                { Status: BatchStatusEnum.Sending },
                {
                    where: {
                        Id: BatchId,
                    },
                }
            )
            // 发送消息
            let OrgList = await NotifyCompanyInfoModel.findAll({
                attributes: ['Id', 'MainMail'],
                where: {
                    BatchId: BatchId,
                    Status: RecordStatusEnum.New,
                },
            })
            OrgList = parseJSON(OrgList)
            //获取全部推送消息的组织id
            let NotifyList = await NoAccessNotifyModel.findAll({
                where: {
                    [Op.or]: [
                        { SmsStatus: RecordStatusEnum.New },
                        { EmailStatus: RecordStatusEnum.New },
                    ],
                    BatchOrgId: {
                        [Op.in]: OrgList.map((item) => item.Id),
                    },
                },
            })
            NotifyList = parseJSON(NotifyList)
            //记录通知数量
            await redis.set(
                `${BatchId}_NoAccessNotifyListLength`,
                NotifyList.length
            )
            //批量更新通知状态
            const IDLIST = NotifyList.map((item) => item.BatchOrgId)
            await NotifyCompanyInfoModel.update(
                { Status: RecordStatusEnum.Sending },
                {
                    where: {
                        Id: {
                            [Op.in]: IDLIST,
                        },
                    },
                }
            )
            //批量发送通知
            for (var i = 0; i < NotifyList.length; i++) {
                //通知消息
                const notify = NotifyList[i]
                //t_no_access_org_info表记录状态改为发送中
                //获取主账户邮箱
                let MainMailInfo = OrgList.filter(
                    (item) => item.Id === notify.BatchOrgId
                )
                //推送通知消息到MQ
                await producer.send({
                    type: 'icp',
                    topic: 'NoAccessNotify',
                    data: {
                        ...notify,
                        BatchId: BatchId,
                        MainMail: MainMailInfo[0]?.MainMail || '',
                    },
                })
            }
            return this.cb(0)
        } catch (e) {
            console.log(e)
            if (e instanceof Error) {
                return this.eh({
                    err: e,
                    code: 36012,
                })
            }
            this.eh(e)
        }
    }
}
