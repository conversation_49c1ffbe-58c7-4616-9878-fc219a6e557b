/*
 * @Date: 2022-07-06 16:01:03
 * @LastEditors: william.qian <EMAIL>
 * @LastEditTime: 2023-06-28 16:30:01
 * @Describe: 得到当前会话的辅助回答
 * 通过接口得到内容，记录会取ID与Prompt的SHA1值，后续做为索引。保存时确定是否已经存在，存在则更新，不存在则创建
 * TicketId UCHat的会话ID
 * TicketIndex TicketId与Prompt的SHA1值来用做索引，区分同会话的不同问题
 */
'use strict'

const Method = require('../../../libs/method')
const { UChatLogModel } = require('../../../mongoModels/icp')
const axios = require('axios')
const parse = require('url-parse')
const crypto = require('crypto')

module.exports = class GetChatAuxiliaryAnswer extends Method {
    constructor(cb) {
        super(cb)
    }

    async exec({ TicketId, Prompt, LLMType = 'chatgpt-3.5' } = {}) {
        let self = this
        try {
            let resInfo = await getChatAIResult({ Prompt, LLMType })

            // 不是特别重要的业务，最终结果不先保存，后更新，而是最后一步保存
            // 转发请求
            // 将TicketId与Prompt做SHA1，后续做为索引
            let TicketIndex = crypto
                .createHash('sha1')
                .update(TicketId + Prompt)
                .digest('hex')

            // 保存到数据库时，解决MongoDB不支持.5的问题
            if (LLMType === 'chatgpt-3.5') {
                LLMType = 'chatgpt-3'
            }

            await UChatLogModel.findOneAndUpdate(
                { TicketIndex },
                {
                    Request: Prompt,
                    TicketId,
                    // TicketIndex,
                    [topToUppser(LLMType)]: resInfo.response,
                },
                { upsert: true }
            )

            return self.cb(0, {
                LLMResponse: { Response: resInfo.response, LLMType },
                PreferenceLLM: 'chatgpt-3.5',
            })
        } catch (e) {
            if (e instanceof Error) {
                return self.eh({
                    err: e,
                    code: 36059,
                })
            }
            self.eh(e)
        }
    }
}

// 使用axios，请求chat_ai API接口
function getChatAIResult({ Prompt, LLMType = 'chatgpt-3.5' } = {}) {
    // let parsed = parse(global.CONFIG['chat_ai_url'])
    console.log(global.CONFIG.chat_ai_url, 'global.CONFIG.chat_ai_url')
    return axios({
        method: 'post',
        timeout: 20 * 1000,
        url: '/chat',
        baseURL: global.CONFIG.chat_ai_url,
        headers: {
            'Content-Type': 'application/json',
        },
        data: JSON.stringify({
            prompt: Prompt,
            llm_type: LLMType,
            app_name: 'uchat_icp',
        }),
    })
        .then((response) => {
            console.log(response)

            if (response.data && response.data.code === 0) {
                return response.data
            } else {
                throw new Error(
                    '请求chat_ai接口失败' + JSON.stringify(response.data)
                )
            }
        })
        .catch((err) => {
            console.log(err)

            throw new Error('请求chat_ai接口失败' + err.toString())
        })
}

function topToUppser(str) {
    return str.toLowerCase().replace(/( |^)[a-z]/g, (L) => L.toUpperCase())
}
