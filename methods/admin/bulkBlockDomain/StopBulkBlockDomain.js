/*
 * @Author: william.qian <EMAIL>
 * @Date: 2022-10-31 14:37:08
 * @LastEditors: william.qian <EMAIL>
 * @LastEditTime: 2022-11-17 17:18:10
 * @FilePath: /newicp/methods/admin/bulkBlockDomain/CreateBulkBlockDomain.js
 * @Description: 开始执行批次的封禁操作，
 * 1，查询全部需要封禁的记录，保存的Redis中，批次状态更新掉，记录状态更新掉
 * 2，Redis开始消费，检查域名是否在白名单中，如是，过滤
 * 3，复核条件的，开始封禁
 */
const Method = require('../../../libs/method')

const { BulkBlockBatchModel, BatchStatusEnum } = require('../../../models/')
const { Op } = require('sequelize')

module.exports = class StopBulkBlockDomain extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({ BatchId, __ssoUser } = {}) {
        let self = this

        try {
            // 获取全部记录
            await BulkBlockBatchModel.update(
                { Status: BatchStatusEnum['Finish'] },
                {
                    where: {
                        Id: BatchId,
                    },
                }
            )

            self.cb(0)
        } catch (e) {
            console.log(e)
            if (e instanceof Error) {
                return self.eh({
                    err: e,
                    code: 31403,
                })
            }
            self.eh(e)
        }
    }
}
