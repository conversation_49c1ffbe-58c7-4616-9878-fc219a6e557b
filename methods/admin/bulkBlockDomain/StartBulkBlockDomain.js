/*
 * @Author: william.qian <EMAIL>
 * @Date: 2022-10-31 14:37:08
 * @LastEditors: william.qian <EMAIL>
 * @LastEditTime: 2022-12-28 18:01:07
 * @FilePath: /newicp/methods/admin/bulkBlockDomain/CreateBulkBlockDomain.js
 * @Description: 开始执行批次的封禁操作，
 * 1，查询全部需要封禁的记录，保存的Redis中，批次状态更新掉，记录状态更新掉
 * 2，Redis开始消费，检查域名是否在白名单中，如是，过滤
 * 3，复核条件的，开始封禁
 */
const Method = require('../../../libs/method')
const { parseJSON } = require('../../../fns/kits')
const {
    getEffectiveDomain,
} = require('../../../fns/domain_white_list/DomainWhiteListService')
const {
    BulkBlockBatchModel,
    BatchStatusEnum,
    BulkBlockRecordModel,
    BlockRecordStatusEnum,
} = require('../../../models/')
const _ = require('lodash')

const { Op } = require('sequelize')

module.exports = class StartBulkBlockDomain extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({ BatchId, __ssoUser } = {}) {
        let rows,
            effectiveDomain,
            accessDomain,
            recordBlockIng = [], // 正封禁中的
            recordEffective = [], // 有效期内不封禁的
            recordAccess = []

        let self = this
        const producer = self.producer
        const redis = self.redis.get()
        const redisBatchName = `${BatchId}|_bulk_block_domains`

        try {
            // 获取全部记录
            ;[rows, effectiveDomain, accessDomain] = await Promise.all([
                BulkBlockRecordModel.findAll({
                    attributes: ['Id', 'Domain'],
                    where: {
                        BatchId,
                        Status: {
                            [Op.or]: [
                                BlockRecordStatusEnum['Init'],
                                BlockRecordStatusEnum['Failed'],
                            ],
                        },
                    },
                }),
                getEffectiveDomain(),
                redis.hkeys(`domain_icp_in_ucloud_map`),
            ])

            effectiveDomain = parseJSON(effectiveDomain)
            rows = parseJSON(rows)
            rows = rows.map((row) => {
                // 在白名单中的
                if (effectiveDomain.indexOf(row.Domain) !== -1) {
                    recordEffective.push(row.Id)
                } else if (accessDomain.indexOf(row.Domain) !== -1) {
                    // 已接入中
                    recordAccess.push(row.Id)
                } else {
                    recordBlockIng.push(row.Id)
                    return JSON.stringify(row)
                }
            })
            rows = rows.filter(Boolean)

            if (rows.length !== 0) {
                // 如此有结果，保存Redis,推送Mq
                await redis.sadd(redisBatchName, rows)
                await redis.expire(redisBatchName, 60 * 60 * 24)

                for (let index = 0; index < 6; index++) {
                    await producer.send({
                        type: 'icp',
                        topic: 'BlockDoaminForNoAccess',
                        data: { BatchName: redisBatchName },
                    })
                }
            }
            //更新批次状态与记录状态

            await Promise.all([
                // 批次更新为封禁中
                BulkBlockBatchModel.update(
                    { Status: BatchStatusEnum['Blocking'] },
                    {
                        where: {
                            Id: BatchId,
                        },
                    }
                ),
                // 可执行封禁的，更新为封禁中
                BulkBlockRecordModel.update(
                    { Status: BlockRecordStatusEnum['Blocking'] },
                    {
                        where: {
                            Id: { [Op.in]: recordBlockIng },
                        },
                    }
                ),
                // 白名单有效期内的为封禁失败，同时更新原因
                BulkBlockRecordModel.update(
                    {
                        Status: BlockRecordStatusEnum['InWhite'],
                        Remark: '白名单有效期内',
                    },
                    {
                        where: {
                            Id: { [Op.in]: recordEffective },
                        },
                    }
                ),
                // 已接入
                BulkBlockRecordModel.update(
                    {
                        Status: BlockRecordStatusEnum['Accessed'],
                        Remark: '已接入',
                    },
                    {
                        where: {
                            Id: { [Op.in]: recordAccess },
                        },
                    }
                ),
            ])
            self.cb(0)
        } catch (e) {
            console.log(e)
            if (e instanceof Error) {
                return self.eh({
                    err: e,
                    code: 36056,
                })
            }
            self.eh(e)
        }
    }
}
