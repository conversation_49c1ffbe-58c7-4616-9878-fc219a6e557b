/*
 * @Author: william.qian <EMAIL>
 * @Date: 2022-10-31 14:37:08
 * @LastEditors: william.qian <EMAIL>
 * @LastEditTime: 2022-12-28 17:18:42
 * @FilePath: /newicp/methods/admin/bulkBlockDomain/CreateBulkBlockDomain.js
 * @Description: 展示封禁的结果
 */
const Method = require('../../../libs/method')
const { parseJSON } = require('../../../fns/kits')
const { BulkBlockRecordModel } = require('../../../models/')

module.exports = class DescribeBulkBlockRecord extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({
        Limit = 20,
        Offset = 0,
        Domain,
        Status,
        BatchId,
        GetAll,
    } = {}) {
        let queryObject = { where: { BatchId } }

        let self = this

        try {
            // 如果有域名
            if (Domain) {
                queryObject.where.Domain = Domain
            }
            if (Status) {
                queryObject.where.Status = Status
            }

            if (!GetAll) {
                queryObject.limit = Limit
                queryObject.offset = Offset
            }

            let { count, rows } = await BulkBlockRecordModel.findAndCountAll(
                queryObject
            )
            rows = parseJSON(rows)

            self.cb(0, { DataSet: rows, Count: count })
        } catch (e) {
            console.log(e)
            if (e instanceof Error) {
                return self.eh({
                    err: e,
                    code: 36054,
                })
            }
            self.eh(e)
        }
    }
}
