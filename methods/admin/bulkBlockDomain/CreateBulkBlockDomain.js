/*
 * @Author: william.qian <EMAIL>
 * @Date: 2022-10-31 14:37:08
 * @LastEditors: william.qian <EMAIL>
 * @LastEditTime: 2022-11-17 17:14:28
 * @FilePath: /newicp/methods/admin/bulkBlockDomain/CreateBulkBlockDomain.js
 * @Description: 创建批量封禁的批次，步骤如下
 * 获取当天需要拦截的域名，过滤屏蔽的公司，过滤已接入的域名
 * 得到的域名，确定是否白名单成功了，如果成功了，过滤
 * 剩下的，插入表，待封禁
 */
const Method = require('../../../libs/method')
const { csvParse, parseJSON } = require('../../../fns/kits')

const moment = require('moment')
const {
    model: UninsertBatchRecordModel,
} = require('../../../models/t_no_access_record')
const {
    BulkBlockBatchModel,
    BatchStatusEnum,
    BulkBlockRecordModel,
    BlockRecordStatusEnum,
} = require('../../../models/')

module.exports = class CreateBulkBlockDomain extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({ NotifyBatchId, __ssoUser, FileName, File } = {}) {
        let row

        let self = this

        try {
            // 拉取记录

            if (FileName) {
                try {
                    row = await csvParse(File, 'Domain')
                } catch (error) {
                    throw new this.Err(error, 36052)
                }
            } else {
                row = await UninsertBatchRecordModel.findAll({
                    attributes: ['Domain'],
                    where: { BatchId: NotifyBatchId, RegisterStatus: 0 },
                })
            }
            // 插入批次，获取插入Id
            let insertResult = await BulkBlockBatchModel.create({
                Description:
                    FileName ||
                    moment().format('YYYYMMDDHH') + '生成的封禁批次',
                Status: BatchStatusEnum['Init'],
            })
            insertResult = parseJSON(insertResult)

            // rows 结果 [{ Domain: 'qianjunye.com' }, { Domain: 'daodao.com' }]
            // 生成需要插入的记录
            let insertObject = []
            // 把域名批量插入表格
            for (var i = 0; i < row.length; i++) {
                insertObject.push({
                    BatchId: insertResult.Id,
                    Domain: row[i].Domain,
                    Status: BlockRecordStatusEnum['Init'],
                })
            }

            await BulkBlockRecordModel.bulkCreate(insertObject)
            self.cb(0)
        } catch (e) {
            console.log(e)
            if (e instanceof Error) {
                return self.eh({
                    err: e,
                    code: 36051,
                })
            }
            self.eh(e)
        }
    }
}
