/*
 * @Author: william.qian <EMAIL>
 * @Date: 2022-10-31 14:37:08
 * @LastEditors: william.qian <EMAIL>
 * @LastEditTime: 2022-11-17 18:51:02
 * @FilePath: /newicp/methods/admin/bulkBlockDomain/CreateBulkBlockDomain.js
 * @Description: 展示封禁的批次，支持域名搜索
 */
const Method = require('../../../libs/method')
const moment = require('moment')
const { getTableModel, parseJSON } = require('../../../fns/kits')

const {
    BulkBlockBatchModel,
    BatchStatusEnum,
    BulkBlockRecordModel,
    BlockRecordStatusEnum,
} = require('../../../models/')

module.exports = class DescribeBulkBlockBatch extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({ Limit = 20, Offset = 0, Domain, BatchId } = {}) {
        let batchIdList

        let self = this

        try {
            // 如果有域名
            if (BatchId) {
                batchIdList = [BatchId]
            } else if (Domain) {
                batchIdList = await BulkBlockRecordModel.findAll({
                    attributes: ['BatchId'],
                    where: {
                        Domain,
                    },
                })
                batchIdList = parseJSON(batchIdList)
                batchIdList = batchIdList.map((row) => {
                    return row.BatchId
                })
            }

            let queryObject = {
                attributes: ['Id', 'Description', 'Status', 'CreateTime'],
                limit: Limit,
                offset: Offset,
                order: [['id', 'DESC']],
            }

            if (batchIdList) {
                queryObject.where = { Id: batchIdList }
            }

            let { count, rows } = await BulkBlockBatchModel.findAndCountAll(
                queryObject
            )
            rows = parseJSON(rows)

            self.cb(0, { DataSet: rows, Count: count })
        } catch (e) {
            console.log(e)
            if (e instanceof Error) {
                return self.eh({
                    err: e,
                    code: 36053,
                })
            }
            self.eh(e)
        }
    }
}
