/*
 * @Date: 2022-07-06 16:01:03
 * @LastEditors: william.qian <EMAIL>
 * @LastEditTime: 2023-05-24 11:40:29
 * @FilePath: /newicp/methods/admin/UnResourcesNotify/GenerateUnresourceBatch.js
 * @Describe: 获取当前的IP白名单
 */
'use strict'

const Method = require('../../../libs/method')
const { parseJSON } = require('../../../fns/kits')
const { IPWhiteListModel } = require('../../../models/')

module.exports = class GetIpWhiteList extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({ CompanyId, Offset = 0, Limit = 20 } = {}) {
        let self = this
        try {
            // 查询
            let { count, rows } = await IPWhiteListModel.findAndCountAll({
                where: CompanyId ? { CompanyId } : {},
                offset: Offset,
                limit: Limit,
                order: [['Id', 'DESC']],
            })
            rows = parseJSON(rows)
            return this.cb(0, { IpWhiteList: rows, TotalCount: count })
        } catch (e) {
            if (e instanceof Error) {
                return self.eh({
                    err: e,
                    code: 35114,
                })
            }
            self.eh(e)
        }
    }
}
