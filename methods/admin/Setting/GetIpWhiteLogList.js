/*
 * @Date: 2022-07-06 16:01:03
 * @LastEditors: william.qian <EMAIL>
 * @LastEditTime: 2023-05-24 15:30:35
 * @Describe: 获取当前IP白名单日志
 */
'use strict'

const Method = require('../../../libs/method')
const { parseJSON } = require('../../../fns/kits')
const { LogModel } = require('../../../models/')

module.exports = class GetIpWhiteLogList extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({
        CompanyId,
        Offset = 0,
        Limit = 20,
        ActionList = ['AddIpWhite', 'UpdateIpWhite', 'DeleteIpWhite'],
    } = {}) {
        let self = this
        try {
            // 查询
            let queryObject = {
                Action: ActionList,
            }
            if (CompanyId) {
                queryObject.CompanyId = CompanyId
            }
            let { count, rows } = await LogModel.findAndCountAll({
                where: queryObject,
                offset: Offset,
                limit: Limit,
                order: [['Id', 'DESC']],
            })
            rows = parseJSON(rows)
            return this.cb(0, { Logs: rows, total: count })
        } catch (e) {
            if (e instanceof Error) {
                return self.eh({
                    err: e,
                    code: 35109,
                })
            }
            self.eh(e)
        }
    }
}
