/*
 * @Date: 2022-07-06 16:01:03
 * @LastEditors: william.qian <EMAIL>
 * @LastEditTime: 2023-05-24 14:53:22
 * @FilePath: /newicp/methods/admin/UnResourcesNotify/GenerateUnresourceBatch.js
 * @Describe: 创建IP白名单
 */
'use strict'

const Method = require('../../../libs/method')
const { parseJSON } = require('../../../fns/kits')
const { IPWhiteListModel, LogModel } = require('../../../models/')

module.exports = class AddIpWhite extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({ CompanyId, Remark, IP, __ssoUser } = {}) {
        let self = this
        let action = 'AddIpWhite'

        try {
            // 查询当前公司时否存在IP白名单
            let rows = await IPWhiteListModel.findAll({
                where: { CompanyId },
            })
            rows = parseJSON(rows)
            // 如不在白名单，创建
            if (rows.length === 0) {
                action = 'AddIpWhite'
                await IPWhiteListModel.create({
                    CompanyId: CompanyId,
                    Operator: __ssoUser,
                    IP: IP,
                    Remark: Remark,
                })
            } else {
                action = 'UpdateIpWhite'
                // 如在白名单，更新
                await IPWhiteListModel.update(
                    {
                        IP: IP,
                        Remark: Remark,
                        Operator: __ssoUser,
                    },
                    {
                        where: { CompanyId },
                    }
                )
            }

            // 保存日志
            await LogModel.create({
                Action: action,
                Params: JSON.stringify(CompanyId, Remark, IP, __ssoUser),
                CompanyId: CompanyId,
                Operator: __ssoUser,
                Status: 0,
                // IP: params.IP,
                Result: '成功',
                SourceCompanyId: CompanyId,
                Remark: Remark,
            })

            return this.cb(0)
        } catch (e) {
            console.log(e)
            if (e instanceof Error) {
                return self.eh({
                    err: e,
                    code: 35115,
                })
            }
            self.eh(e)
        }
    }
}
