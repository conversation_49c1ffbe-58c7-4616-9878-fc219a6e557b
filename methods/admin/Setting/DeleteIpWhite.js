/*
 * @Date: 2022-07-06 16:01:03
 * @LastEditors: william.qian <EMAIL>
 * @LastEditTime: 2023-05-24 15:19:06
 * @FilePath: /newicp/methods/admin/UnResourcesNotify/GenerateUnresourceBatch.js
 * @Describe: 删除白名单
 */
'use strict'

const Method = require('../../../libs/method')
const { parseJSON } = require('../../../fns/kits')
const { IPWhiteListModel, LogModel } = require('../../../models/')

module.exports = class DeleteIpWhite extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({ Id, Remark, __ssoUser } = {}) {
        let self = this
        let action = 'DeleteIpWhite'

        try {
            // 旧代码是查询后删除，这里直接删除
            await IPWhiteListModel.destroy({
                where: { Id },
            })

            // 保存日志
            await LogModel.create({
                Action: action,
                Params: JSON.stringify(Id, Remark, __ssoUser),
                Operator: __ssoUser,
                Status: 0,
                // IP: params.IP,
                Result: '成功',
                Remark: Remark,
            })

            return this.cb(0)
        } catch (e) {
            console.log(e)
            if (e instanceof Error) {
                return self.eh({
                    err: e,
                    code: 35115,
                })
            }
            self.eh(e)
        }
    }
}
