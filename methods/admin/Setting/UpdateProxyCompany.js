/**
 * @file API UpdateProxyCompany,更新代理商，主体配额、网站配额的数量
 * <AUTHOR>
 * @return {Object} 返回符合条件的记录,格式如下
 * 直接更新，如果影响行为0，则出错
 * {
   "Action" : "UpdateProxyCompany",
  "RetCode" : 0,
}
 * 错误码:

 */

const Method = require('../../../libs/method')
const { getTableModel } = require('../../../fns/kits')
const moment = require('moment')

module.exports = class UpdateProxyCompany extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({
        CompanyName,
        Quota,
        WebsiteQuota,
        __ssoUser,
        CompanyType,
        Note,
        CompanyId,
        OneClickRelease = 0,
    } = {}) {
        let self = this
        const icpDatabase = this.db.get('icp')
        const logModel = getTableModel('t_log', icpDatabase)
        const ProxyCompanyList = getTableModel(
            't_proxy_company_list',
            icpDatabase
        )

        let now = parseInt(moment().format('X')),
            effectRowsCount,
            updateObject
        try {
            // 更新
            try {
                updateObject = {
                    CompanyName,
                    Operator: __ssoUser,
                    UpdateTime: now,
                    Note,
                    CompanyType,
                }

                if (Quota) {
                    updateObject.Quota = Quota
                }

                if (WebsiteQuota) {
                    updateObject.WebsiteQuota = WebsiteQuota
                }

                updateObject.OneClickRelease = OneClickRelease

                ;[effectRowsCount] = await ProxyCompanyList.update(
                    updateObject,
                    {
                        where: { CompanyId },
                    }
                )
            } catch (error) {
                await Promise.reject(new self.Err(error, 35103))
            }

            // 如果更新的影响行数是0.出错
            if (effectRowsCount === 0) {
                await Promise.reject(
                    new self.Err(new Error('不存在的代理信息，无法修改'), 35104)
                )
            }

            self.cb(0)
            // 到这，说明更新成功了开始记日志

            await logModel.create({
                Action: 'UpdateProxyCompany',
                Params: updateObject,
                CompanyId: CompanyId,
                Operator: __ssoUser,
                Status: 0,
                Result: '成功',
                UpdateTime: now,
                CreateTime: now,
                SourceCompanyId: CompanyId,
                Remark: Note,
            })
        } catch (e) {
            if (e instanceof Error) {
                return self.eh({
                    err: e,
                    code: 35102,
                })
            }
            self.eh(e)
        }
    }
}
