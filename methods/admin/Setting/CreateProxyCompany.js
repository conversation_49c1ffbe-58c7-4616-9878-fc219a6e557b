/**
 * @file API CreateProxyCompany,创建代理商，主体配额、网站配额的数量
 * <AUTHOR>
 * @return {Object} 返回符合条件的记录,格式如下
 * 直接更新，如果影响行为0，则出错
 * {
   "Action" : "CreateProxyCompany",
  "RetCode" : 0,
}
 * 错误码:

 */

const Method = require('../../../libs/method')
const { getTableModel } = require('../../../fns/kits')
const moment = require('moment')

module.exports = class CreateProxyCompany extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({
        CompanyName,
        Quota,
        WebsiteQuota,
        __ssoUser,
        CompanyType,
        Note,
        CompanyId,
        OneClickRelease = 0,
    } = {}) {
        let self = this
        const icpDatabase = this.db.get('icp')
        const logModel = getTableModel('t_log', icpDatabase)
        const ProxyCompanyList = getTableModel(
            't_proxy_company_list',
            icpDatabase
        )

        let now = parseInt(moment().format('X')),
            insertObject
        try {
            // 更新
            try {
                insertObject = {
                    CompanyName,
                    Operator: __ssoUser,
                    CompanyId,
                    UpdateTime: now,
                    Note,
                    CompanyType,
                }

                if (Quota) {
                    insertObject.Quota = Quota
                }

                if (WebsiteQuota) {
                    insertObject.WebsiteQuota = WebsiteQuota
                }

                insertObject.OneClickRelease = OneClickRelease

                await ProxyCompanyList.create(insertObject)
            } catch (error) {
                await Promise.reject(
                    new self.Err(
                        new Error(
                            '创建代理商与配额时，创建记录失败，请确定公司是否已存在或参数格式正确'
                        ),
                        35106
                    )
                )
            }

            self.cb(0)
            // 到这，说明更新成功了开始记日志

            await logModel.create({
                Action: 'CreateProxyCompany',
                Params: insertObject,
                CompanyId: CompanyId,
                Operator: __ssoUser,
                Status: 0,
                Result: '成功',
                UpdateTime: now,
                CreateTime: now,
                SourceCompanyId: CompanyId,
                Remark: Note,
            })
        } catch (e) {
            if (e instanceof Error) {
                return self.eh({
                    err: e,
                    code: 35105,
                })
            }
            self.eh(e)
        }
    }
}
