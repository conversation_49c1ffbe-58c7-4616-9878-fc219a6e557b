/*
 * @Date: 2022-07-06 16:01:03
 * @LastEditors: william.qian <EMAIL>
 * @LastEditTime: 2023-05-18 17:04:44
 * @FilePath: /newicp/methods/admin/UnResourcesNotify/GenerateUnresourceBatch.js
 * @Describe: 屏蔽指定的公司
 */
'use strict'

const Method = require('../../../libs/method')
const { parseJSON } = require('../../../fns/kits')
const {
    CompanyBlockListModel,
    AuthenticationCodeModel,
    LogModel,
} = require('../../../models/')

module.exports = class AddBlockCompany extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({ CompanyId, Remark, __ssoUser } = {}) {
        let self = this
        try {
            // 查询当前公司是否被屏蔽
            let count = await CompanyBlockListModel.count({
                where: { CompanyId },
            })

            if (count !== 0) {
                await Promise.reject(
                    new self.Err(new Error('黑名单中已存在该公司'), 35110)
                )
            }
            console.log(2, count)
            // 屏蔽表中增加记录
            // 日志表中增加记录
            // 授权码表将此公司的授权置为失效
            try {
                await Promise.all([
                    CompanyBlockListModel.create({
                        CompanyId: CompanyId,
                        Operator: __ssoUser,
                        Remark: Remark,
                    }),
                    AuthenticationCodeModel.update(
                        {
                            IsBlock: 1,
                            Operator: __ssoUser,
                        },
                        {
                            where: {
                                CompanyId,
                            },
                        }
                    ),
                    LogModel.create({
                        Action: 'AddBlockCompany',
                        Params: JSON.stringify({
                            CompanyId,
                            Remark,
                            __ssoUser,
                        }),
                        CompanyId,
                        Operator: __ssoUser,
                        Status: 0,
                        Result: '成功',
                        SourceCompanyId: -1,
                        Remark: Remark,
                    }),
                ])
            } catch (e) {
                console.log(e)
                // 执行出错
                await Promise.reject(
                    new self.Err(new Error('屏蔽公司时，执行过程出错'), 35111)
                )
            }
            return this.cb(0)
        } catch (e) {
            if (e instanceof Error) {
                return self.eh({
                    err: e,
                    code: 35112,
                })
            }
            self.eh(e)
        }
    }
}
