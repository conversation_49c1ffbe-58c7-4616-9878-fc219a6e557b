/*
 * @Date: 2022-07-06 16:01:03
 * @LastEditors: william.qian <EMAIL>
 * @LastEditTime: 2023-05-18 17:07:39
 * @FilePath: /newicp/methods/admin/UnResourcesNotify/GenerateUnresourceBatch.js
 * @Describe: 删除已屏蔽的记录
 */
'use strict'

const Method = require('../../../libs/method')
const { parseJSON } = require('../../../fns/kits')
const {
    CompanyBlockListModel,
    AuthenticationCodeModel,
    LogModel,
} = require('../../../models/')

module.exports = class DeleteBlockCompany extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({ Id, Remark, __ssoUser } = {}) {
        let self = this
        try {
            // 查询当前公司是否被屏蔽
            let blockInfo = await CompanyBlockListModel.findAll({
                where: { Id },
            })
            blockInfo = parseJSON(blockInfo)
            if (blockInfo.length === 0) {
                await Promise.reject(
                    new self.Err(new Error('黑名单中不存在此记录'), 35113)
                )
            }
            let CompanyId = blockInfo[0].CompanyId
            // 屏蔽表中增加记录
            // 日志表中增加记录
            // 授权码表将此公司的授权置为失效
            try {
                await Promise.all([
                    CompanyBlockListModel.destroy({
                        where: {
                            CompanyId,
                        },
                    }),
                    AuthenticationCodeModel.update(
                        {
                            IsBlock: 0,
                            Operator: __ssoUser,
                        },
                        {
                            where: {
                                CompanyId,
                            },
                        }
                    ),
                    LogModel.create({
                        Action: 'DeleteBlockCompany',
                        Params: JSON.stringify({
                            Id,
                            Remark,
                            __ssoUser,
                        }),
                        CompanyId,
                        Operator: __ssoUser,
                        Status: 0,
                        Result: '成功',
                        SourceCompanyId: -1,
                        Remark: Remark,
                    }),
                ])
            } catch (e) {
                console.log(e)
                // 执行出错
                await Promise.reject(
                    new self.Err(new Error('屏蔽公司时，执行过程出错'), 35111)
                )
            }
            return this.cb(0)
        } catch (e) {
            if (e instanceof Error) {
                return self.eh({
                    err: e,
                    code: 35109,
                })
            }
            self.eh(e)
        }
    }
}
