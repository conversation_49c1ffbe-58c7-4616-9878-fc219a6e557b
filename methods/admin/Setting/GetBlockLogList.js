/* 获取黑名单日志列表
 * @Date: 2023-05-24 15:48:02
 * @LastEditors: l<PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2023-05-24 15:48:09
 * @FilePath: /newicp/methods/admin/Setting/GetBlockLogList.js
 */
const Method = require('../../../libs/method')
const { LogModel } = require('../../../models')
const { Op } = require('sequelize')
const { parseJSON } = require('../../../fns/kits')
module.exports = class GetBlockLogList extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec(params) {
        let self = this
        try {
            params.ActionList =
                !params.ActionList || params.ActionList.length === 0
                    ? ['AddBlockCompany', 'DeleteBlockCompany']
                    : params.ActionList
            let cond = {
                Action: {
                    [Op.in]: params.ActionList,
                },
            }
            if (params.CompanyId) {
                cond.CompanyId = params.CompanyId
            }

            let { rows: Logs, count: TotalCount } =
                await LogModel.findAndCountAll({
                    attributes: [
                        'Id',
                        'Params',
                        'CompanyId',
                        'Action',
                        'Remark',
                        'Operator',
                        'CreateTime',
                    ],
                    where: cond,
                    offset: params.Offset || 0,
                    limit: params.Limit || 20,
                })
            Logs = parseJSON(Logs)
            return this.cb(0, {
                Logs,
                TotalCount,
            })
        } catch (e) {
            let err = new Error('查看日志出错')
            err.code = 11000
            self.eh(err)
        }
    }
}
