/**
 * @file API DeleteProxyCompany,删除代理商配置
 * <AUTHOR>
 * 直接更新，如果影响行为0，则出错
 * {
   "Action" : "DeleteProxyCompany",
  "RetCode" : 0,
}
 * 错误码:

 */

const Method = require('../../../libs/method')
const { getTableModel } = require('../../../fns/kits')
const moment = require('moment')

module.exports = class DeleteProxyCompany extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({ __ssoUser, CompanyId, Note } = {}) {
        let self = this
        const icpDatabase = this.db.get('icp')
        const logModel = getTableModel('t_log', icpDatabase)
        const ProxyCompanyList = getTableModel(
            't_proxy_company_list',
            icpDatabase
        )

        let now = moment().format('X')

        try {
            // 更新
            try {
                await ProxyCompanyList.destroy({
                    where: {
                        CompanyId,
                    },
                })
            } catch (error) {
                await Promise.reject(
                    new self.Err(
                        new Error('删除代理商与配额时，执行失败'),
                        35107
                    )
                )
            }

            self.cb(0)
            // 到这，说明更新成功了开始记日志

            await logModel.create({
                Action: 'DeleteProxyCompany',
                Params: { __ssoUser, CompanyId },
                CompanyId: CompanyId,
                Operator: __ssoUser,
                Status: 0,
                Result: '成功',
                UpdateTime: now,
                CreateTime: now,
                SourceCompanyId: CompanyId,
                Remark: Note,
            })
        } catch (e) {
            if (e instanceof Error) {
                return self.eh({
                    err: e,
                    code: 35108,
                })
            }
            self.eh(e)
        }
    }
}
