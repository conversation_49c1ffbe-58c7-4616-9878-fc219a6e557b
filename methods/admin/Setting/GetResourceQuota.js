const { rp: ucloudInternalApiAsync } = require('../../../fns/ucloudApi')
const Method = require('../../../libs/method')
const { ICPModel, ICPWebModel, OrderModel } = require('../../../models')
const {Op} = require("sequelize");
const {arrayMinus} = require("../../../fns/kits");
const _ = require("lodash");
module.exports = class GetResourceQuota extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({ CompanyId } = {}) {
        let self = this
        try {
            // 获取境内云主机、境内混合机柜、轻量服务器、全球加速资源数
            let promises = [],
                ResourceTypes = [1, 44, 370, 142]
            ResourceTypes.forEach((type) => {
                promises.push(this.IGetResourceCountList(CompanyId, type))
            })
            // 查的全是网站
            let ICPNum = await ICPModel.count({
                include: [
                    {
                        model: ICPWebModel,
                        as: 'Website',
                    },
                ],
                where: { CompanyId: CompanyId },
            })
            // 和检查逻辑一样，获取已备案主体数目
            let ICPCount = await getICPCount(CompanyId)

            return Promise.all(promises)
                .then((res) => {
                    return self.cb(0, {
                        Data: [
                            {
                                CompanyId,
                                ICPNum,
                                ICPCount,
                                Uhost: res[0],
                                Uhybrid: res[1],
                                Uidc: res[2],
                                PathX: res[3],
                            },
                        ],
                    })
                })
                .catch((err) => {
                    return self.cb(-1, { err: err })
                })
        } catch (e) {
            if (e instanceof Error) {
                return self.eh({
                    err: e,
                    code: -1,
                })
            }

            self.eh(e)
        }
    }
    async IGetResourceCountList(CompanyId, ResourceType) {
        let options = {
            category: 'compass',
            action: 'IGetResourceList',
        }
        let data = {
            ResourceType: ResourceType,
            CompanyId: CompanyId,
            User: 'tequila', //该api版本比较老，需要添加user值(写死)
        }
        try {
            let res = await ucloudInternalApiAsync(options, data)
            let inRegionText = ['广东', '上海', '北京', '乌兰察布', '华北'] //筛选境内资源
            //全球加速产品直接返回数量
            if (ResourceType === 142) {
                return res.Data.Count
            } else {
                let resourceCountList = res.Data.List
                let inCnRegionCount = resourceCountList.filter((data) => {
                    return inRegionText.some((region) =>
                        data.RegionCN.startsWith(region)
                    )
                }).length
                return inCnRegionCount
            }
        } catch (err) {
            return err
        }
    }
}

async function getICPCount(CompanyId) {
    let orderMap = await getOrderMap(CompanyId)
    let icpMap = await getICPMap(CompanyId)

    let orderLicenseIdList,
        icpexpireLicenseIdList,
        icpEffectiveLicenseIdList,
        currentList

        // 用统一的fun处理
    ;[
        orderLicenseIdList,
        icpexpireLicenseIdList,
        icpEffectiveLicenseIdList,
    ] = mapDataParse(orderMap, icpMap)

    // 订单中的记录减已备案中注销的记录，得差集结
    currentList = arrayMinus(orderLicenseIdList, icpexpireLicenseIdList)

    // 开始做计算
    currentList = currentList.concat(icpEffectiveLicenseIdList)
    currentList = _.uniq(currentList)
    // 去空的元素
    currentList = currentList.filter(
        (licenseId) =>
            licenseId !== undefined &&
            licenseId !== '' &&
            licenseId !== null
    )
    return currentList.length
}

async function getOrderMap(CompanyId) {
    return OrderModel.findAll({
        attributes: ['OrganizerLicenseId', 'OrderNo', 'Status'],
        where: {
            Type: { [Op.in]: [1, 2, 3] },
            CompanyId,
            IsDeleted: 0,
        },
    })
}

async function getICPMap(CompanyId) {
    return ICPModel.findAll({
        attributes: ['OrganizerLicenseId', 'Status', 'Id'],
        where: {
            CompanyId,
        },
    })
}

// 订单与已备案记录的处理
function mapDataParse(orderMap, icpMap) {
    let orderLicenseIdList, icpexpireLicenseIdList, icpEffectiveLicenseIdList

    // 新增订单的主办单位证件号
    orderLicenseIdList = _.map(orderMap, 'OrganizerLicenseId')
    orderLicenseIdList = _.uniq(orderLicenseIdList)

    // 注销状态的已备案记录
    icpexpireLicenseIdList = _.find(icpMap, ['Status', 1])
    icpexpireLicenseIdList = _.map(icpexpireLicenseIdList, 'OrganizerLicenseId')
    icpexpireLicenseIdList = _.uniq(icpexpireLicenseIdList)

    // 正常状态的已经备案记录
    icpEffectiveLicenseIdList = _.find(icpMap, function (o) {
        return o.Status !== 1
    })
    icpEffectiveLicenseIdList = _.map(
        icpEffectiveLicenseIdList,
        'OrganizerLicenseId'
    )
    icpEffectiveLicenseIdList = _.uniq(icpEffectiveLicenseIdList)

    return [
        orderLicenseIdList,
        icpexpireLicenseIdList,
        icpEffectiveLicenseIdList,
    ]
}