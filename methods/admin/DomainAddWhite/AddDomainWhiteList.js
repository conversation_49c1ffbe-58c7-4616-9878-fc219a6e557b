/*
 * @Date: 2022-09-05 11:16:55
 * @LastEditors: william.qian <EMAIL>
 * @LastEditTime: 2023-03-15 16:05:54
 * @FilePath: /newicp/methods/admin/DomainAddWhite/AddDomainWhiteList.js
 */
'use strict'

const Method = require('../../../libs/method')
const { getToken, unSealDomain } = require('../../../fns/aodun/DomainService')
const {
    DomainWhiteListModel,
    ProxyCompanyWhiteDomainModel,
    LogModel,
} = require('../../../models')
const { parseJSON } = require('../../../fns/kits')
const moment = require('moment')

/**
 * 添加域名白名单API
 */
module.exports = class AddDomainWhiteList extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({ Domain, ExpiredTime, Remark, __ssoUser, Action }) {
        let self = this
        let redis = this.redis.get()
        try {
            // 检查订单是否加白过
            let [DomainWhiteInfo, ProxyWhiteInfo] = await Promise.all([
                DomainWhiteListModel.findOne({
                    where: {
                        Domain,
                    },
                }),
                ProxyCompanyWhiteDomainModel.findOne({
                    where: {
                        Domain,
                    },
                }),
            ])
            let logJSON = {
                Domain: [Domain],
                Operator: __ssoUser,
                Action,
                Remark: '加白域名',
                Result: '成功',
            }
            let conn = await redis.hmget('domain_icp_in_ucloud_map', Domain)
            DomainWhiteInfo = parseJSON(DomainWhiteInfo)
            ProxyWhiteInfo = parseJSON(ProxyWhiteInfo)
            if (ProxyWhiteInfo) {
                let err = new Error('该域名已在大客户白名单')
                err.code = 33100
                throw err
            }
            if (DomainWhiteInfo) {
                // 加白过
                let updateJson = {
                    IsDeleted: 0,
                    Connected: conn[0] !== null ? 1 : 0,
                    ExpiredTime,
                    Remark: Remark ?? '',
                    Operator: __ssoUser,
                }
                if (DomainWhiteInfo?.Sealed) {
                    // 解封
                    // 调用傲顿接口 查询封禁状态
                    try {
                        let token = await getToken()
                        await unSealDomain(token, [Domain])
                    } catch (err) {
                        let e = new Error('解封失败，原因：' + err)
                        e.code = 34003
                        throw e
                    }
                    logJSON.Remark = '加白并解封域名'
                    updateJson.Sealed = 0
                    updateJson.UnsealTimes = DomainWhiteInfo.UnsealTimes + 1
                }

                await DomainWhiteListModel.update(updateJson, {
                    where: {
                        Domain,
                    },
                })
            } else {
                await DomainWhiteListModel.create({
                    Domain,
                    ExpiredTime,
                    Operator: __ssoUser,
                    Remark: Remark ?? '',
                    Connected: conn[0] !== null ? 1 : 0,
                })
            }
            await LogModel.create(logJSON)

            return this.cb(0)
        } catch (e) {
            self.err(e)
        }
    }
}
