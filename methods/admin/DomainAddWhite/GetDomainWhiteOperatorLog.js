/*
 * @Date: 2022-09-05 11:16:55
 * @LastEditors: l<PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2022-11-14 11:53:24
 * @FilePath: /newicp/methods/admin/DomainAddWhite/GetDomainWhiteOperatorLog.js
 */
'use strict'

const Method = require('../../../libs/method')
const { DomainWhiteListModel, LogModel } = require('../../../models')
const { Op } = require('sequelize')
/**
 * 查看域名白名单操作日志API
 */
module.exports = class GetDomainWhiteOperatorLog extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec(params) {
        let self = this

        try {
            let searchCondition = {
                Action: {
                    [Op.in]: [
                        'AddDomainWhiteList',
                        'UpdateDomainWhiteList',
                        'DeleteDomainWhiteList',
                    ],
                },
            }
            if (params.Domain?.trim()) {
                searchCondition.Domain = {
                    [Op.like]: `%${params.Domain?.trim()}%`,
                }
            }
            let { rows: Rows, count: TotalCount } =
                await LogModel.findAndCountAll({
                    attributes: [
                        'Id',
                        'Action',
                        'Domain',
                        'Remark',
                        'Operator',
                        'CreateTime',
                        'Result',
                    ],
                    where: searchCondition,
                    offset: params.Offset,
                    limit: params.Limit,
                    order: [['create_time', 'desc']],
                })

            return this.cb(0, { Rows, TotalCount })
        } catch (e) {
            self.err(e)
        }
    }
}
