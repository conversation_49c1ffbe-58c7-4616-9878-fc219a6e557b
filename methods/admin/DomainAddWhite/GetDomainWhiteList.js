/*
 * @Date: 2022-11-03 10:23:23
 * @LastEditors: l<PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2022-11-08 15:53:30
 * @FilePath: /newicp/methods/admin/DomainAddWrite/GetDomainWhiteList.js
 */
'use strict'

const Method = require('../../../libs/method')
const { DomainWhiteListModel, LogModel } = require('../../../models')
const _ = require('lodash')
const { Op } = require('sequelize')
const moment = require('moment')
/**
 * 查看域名白名单API
 */
module.exports = class GetDomainWhiteList extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({ Domain, Expired, Connected, Offset, Limit }) {
        let self = this
        try {
            let curr = moment().format('X')
            let condition = {}
            if (Expired) {
                //  若要已删除的，则返回 已删除的（包含接入和未接入的数据）
                condition.ExpiredTime = {
                    [Op.lt]: curr,
                }
                condition.IsDeleted = 0
                condition.Connected = 0
            } else if (Connected) {
                // 若要已接入的 则返回 已接入的数据 (包含删除和未删除的)
                condition.Connected = Connected
            } else {
                // 默认返回 未删除 未接入的白名单数据
                condition.Connected = 0
                condition.IsDeleted = 0
                condition.ExpiredTime = {
                    [Op.gt]: curr,
                }
            }
            if (Domain) {
                condition.Domain = {
                    [Op.like]: `%${Domain}%`,
                }
            }
            let { rows: Rows, count: TotalCount } =
                await DomainWhiteListModel.findAndCountAll({
                    where: condition,
                    order: [
                        ['expired_time', 'asc'],
                        ['update_time', 'desc'],
                    ],
                    offset: Offset || 0,
                    limit: Limit || 20,
                })
            return this.cb(0, { Rows, TotalCount })
        } catch (e) {
            console.log(e)
            if (e instanceof Error) {
                return self.eh({
                    err: e,
                    code: 33301,
                })
            }
            self.eh(e)
        }
    }
}
