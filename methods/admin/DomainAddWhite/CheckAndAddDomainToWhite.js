/* 为spt提供 加白接口，若没有加白过，则可执行加白30天动作
 * @Date: 2022-09-05 11:16:55
 * @LastEditors: li<PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2023-01-06 10:53:05
 * @FilePath: /newicp/methods/admin/DomainAddWhite/CheckAndAddDomainToWhite.js
 */
'use strict'

const Method = require('../../../libs/method')
const { getToken, unSealDomain } = require('../../../fns/aodun/DomainService')
const { DomainWhiteListModel, LogModel } = require('../../../models')
const { Op } = require('sequelize')
const { parseJSON } = require('../../../fns/kits')
const moment = require('moment')

/**
 * 添加域名白名单API
 */
module.exports = class CheckAndAddDomainToWhite extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({ Domain, Remark, __ssoUser }) {
        let self = this
        let redis = this.redis.get()
        try {
            // 检查订单是否加白过
            let [DomainWhiteInfo, DomainAddLogs] = await Promise.all([
                DomainWhiteListModel.findOne({
                    where: {
                        Domain,
                    },
                }),
                LogModel.findAll({
                    where: {
                        Domain: {
                            [Op.like]: `%${Domain}%`,
                        },
                        Action: 'AddDomainWhiteList',
                    },
                }),
            ])
            let logJSON = {
                Domain: [Domain],
                Operator: __ssoUser,
                Action: 'AddDomainWhiteList',
                Remark: 'SPT加白并解封域名',
                Result: '成功',
            }
            let conn = await redis.hmget('domain_icp_in_ucloud_map', Domain)
            DomainWhiteInfo = parseJSON(DomainWhiteInfo)
            DomainAddLogs = parseJSON(DomainAddLogs)
            DomainAddLogs = DomainAddLogs.filter((d) =>
                d.Domain.includes(Domain)
            )
            if (DomainWhiteInfo) {
                if (DomainWhiteInfo?.Sealed === 0) {
                    // 域名未因为 未接入封禁
                    let err = new Error('域名未因未接入封禁')
                    err.code = 33102
                    throw err
                }
                let ExpiredTime = moment(moment().format('YYYY-MM-DD'))
                    .add(30, 'days')
                    .subtract(1, 'seconds')
                    .format('X')
                let updateJson = {
                    IsDeleted: 0,
                    Connected: conn[0] !== null ? 1 : 0,
                    ExpiredTime,
                    Remark: Remark ?? 'SPT加白并解封域名',
                    Operator: __ssoUser,
                }
                //  是封禁状态，并且  首次加白
                if (DomainAddLogs.length === 0) {
                    // 解封
                    // 调用傲顿接口 查询封禁状态
                    try {
                        let token = await getToken()
                        await unSealDomain(token, [Domain])
                    } catch (err) {
                        let e = new Error('解封失败，原因：' + err)
                        e.code = 34003
                        throw e
                    }
                    updateJson.Sealed = 0
                    updateJson.UnsealTimes = DomainWhiteInfo.UnsealTimes + 1
                    await DomainWhiteListModel.update(updateJson, {
                        where: {
                            Domain,
                        },
                    })
                    await LogModel.create(logJSON)
                } else {
                    let err = new Error(
                        '已备案未接入域名加白失败，请联系备案部于巍进行人工处理。'
                    )
                    err.code = 33103
                    throw err
                }
            } else {
                // 域名未因为 未接入封禁
                let err = new Error('域名未因未接入被封禁')
                err.code = 33102
                throw err
            }

            return this.cb(0)
        } catch (e) {
            self.err(e)
        }
    }
}
