/*
 * @Date: 2022-09-05 11:16:55
 * @LastEditors: l<PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2022-11-08 18:29:56
 * @FilePath: /newicp/methods/admin/DomainAddWrite/UpdateDomainWhiteList.js
 */
'use strict'

const Method = require('../../../libs/method')
const { DomainWhiteListModel, LogModel } = require('../../../models')
const _ = require('lodash')
const moment = require('moment')
const { parseJSON } = require('../../../fns/kits')
/**
 * 更新域名白名单API
 */

module.exports = class UpdateDomainWhiteList extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({ Id, ExpiredTime, Remark, __ssoUser, Action }) {
        let self = this

        try {
            let currTime = moment().format('X')
            let DomainWhiteInfo = await DomainWhiteListModel.findAll({
                where: { Id, IsDeleted: 0 },
            })
            DomainWhiteInfo = parseJSON(DomainWhiteInfo)
            if (DomainWhiteInfo.length === 0) {
                let err = new Error('没有发现有效记录')
                err.code = 33401
                throw err
            }
            if (ExpiredTime < currTime) {
                let err = new Error('请输入有效过期时间')
                err.code = 10003
                throw err
            }
            const Info = DomainWhiteInfo[0]
            let changeTime = Info.ExpiredTime !== ExpiredTime
            // 初始化日志内容
            let logContent = {
                Domain: [Info.Domain],
                OrderNo: Info.OrderNo || '',
                Action: Action,
                Operator: __ssoUser,
                Remark:
                    '更新域名信息' + changeTime
                        ? `过期时间from ${moment(
                              Info.ExpiredTime * 1000
                          ).format('YYYY-MM-DD')} to ${moment(
                              ExpiredTime * 1000
                          ).format('YYYY-MM-DD')}`
                        : '',
                Result: '成功',
            }
            await DomainWhiteListModel.update(
                {
                    ExpiredTime,
                    Remark: Remark || '',
                    Operator: __ssoUser,
                },
                {
                    where: {
                        Id,
                    },
                }
            )
            await LogModel.create(logContent)

            return this.cb(0)
        } catch (e) {
            self.err(e)
        }
    }
}
