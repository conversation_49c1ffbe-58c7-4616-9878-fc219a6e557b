/*
 * @Date: 2022-09-05 11:16:55
 * @LastEditors: william.qian <EMAIL>
 * @LastEditTime: 2023-03-15 16:09:38
 * @FilePath: /newicp/methods/admin/DomainAddWhite/AddDomainWhiteList.js
 */
'use strict'

const Method = require('../../../libs/method')
const { getToken, getSealStatus } = require('../../../fns/aodun/DomainService')
const psl = require('psl')

/**
 * 添加域名白名单API
 */
module.exports = class GetDomainBlockStatusInAodun extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({ Domain }) {
        let self = this
        try {
            var parsed = psl.parse(Domain)
            let token = await getToken()
            let domainInBlock = await getSealStatus(token, {
                domain: parsed,
            })
            return this.cb(0, { domainInBlock })
        } catch (e) {
            self.err(e)
        }
    }
}
