/*
 * @Date: 2022-09-05 11:16:55
 * @LastEditors: l<PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2022-11-07 11:01:00
 * @FilePath: /newicp/methods/admin/DomainAddWrite/DeleteDomainWhiteList.js
 */
'use strict'

const Method = require('../../../libs/method')
const { getToken, sealDomain } = require('../../../fns/aodun/DomainService')
const { DomainWhiteListModel, LogModel } = require('../../../models')
const { parseJSON } = require('../../../fns/kits')
/**
 * 删除域名白名单API
 */
module.exports = class DeleteDomainWhiteList extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({ Id, __ssoUser, Remark, Action }) {
        let self = this
        try {
            let DomainWhiteInfo = await DomainWhiteListModel.findAll({
                where: { Id, IsDeleted: 0 },
            })
            DomainWhiteInfo = parseJSON(DomainWhiteInfo)
            if (DomainWhiteInfo.length === 0) {
                let err = new Error('没有发现有效记录')
                err.code = 33401
                throw err
            }

            const info = DomainWhiteInfo[0]

            //请求日志
            let logContent = {
                Domain: [info.Domain],
                Action: Action,
                Operator: __ssoUser,
                Remark: '删除域名',
                Result: '成功',
            }
            let updateJson = {
                IsDeleted: 1,
                Operator: __ssoUser,
                Remark: Remark ?? '',
            }
            await DomainWhiteListModel.update(updateJson, {
                where: {
                    Domain: info.Domain,
                },
            })
            await LogModel.create(logContent)

            return this.cb(0)
        } catch (e) {
            self.err(e)
        }
    }
}
