/*
 * @Date: 2025-07-10 11:16:55
 * @LastEditors: <EMAIL>
 */
'use strict'

const Method = require('../../../libs/method')
const { IPSegModel } = require('../../../mongoModels/icp')
const logger = require("../../../libs/logger")
const { isIPRangeOverlap } = require("../../../fns/kits")
const validator = require("validator");

/**
 * 获取IP段列表
 */
module.exports = class GetIPSegList extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({ IPType, IPStart, IPEnd, }) {
        let self = this
        try {
            if (IPStart && !IPEnd || !IPStart && IPEnd) {
                throw new Error('IP段查询参数不完整，请检查后重新查询')
            }
            // 默认返回全部数据
            if ( IPType === undefined ) {
                let IPSegList = await IPSegModel.find({})
                let TotalCount = await IPSegModel.countDocuments({})
                return this.cb(0, {
                    IPSegList,
                    TotalCount
                })
            }
            let IPSegList = await IPSegModel.find({ IPType })
            let TotalCount = await IPSegModel.countDocuments({ IPType })
            // 如果传的有范围
            if (IPStart && IPEnd) {
                if ( !validator.isIP(IPStart, IPType === 0 ? 4 : 6) || !validator.isIP(IPEnd, IPType === 0 ? 4 : 6)) {
                    throw new Error('IP格式不正确')
                }
                // 将这个范围内的所有数据都查出来然后返回
                let filterIPSegList = []
                let filterTotalCount = 0
                for (let record of IPSegList) {
                    if (isIPRangeOverlap({ IPType, IPStart, IPEnd }, record)) {
                        filterIPSegList.push(record)
                        filterTotalCount++
                    }
                }
                return this.cb(0, {
                    IPSegList: filterIPSegList,
                    TotalCount: filterTotalCount
                })
            }
            return this.cb(0, {
                IPSegList,
                TotalCount
            })
        } catch (err) {
            logger.getLogger('error').error( 'AddIPSeg failed, the err is: ',err)
            err.code = 60601
            self.err(err)
        }
    }
}