/*
 * @Date: 2025-07-10 11:16:55
 * @LastEditors: <EMAIL>
 */
'use strict'

const Method = require('../../../libs/method')
const { ReportIPSegRecordModel } = require('../../../mongoModels/icp')
const logger = require("../../../libs/logger")

/**
 * 获取IP上报日志详情
 */
module.exports = class GetIPSegReportLogRecord extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({ BatchId, Offset = 0, Limit = 20 }) {
        let self = this
        try {
            if (!BatchId) {
                throw new Error('Invalid BatchId')
            }
            let List = await ReportIPSegRecordModel.find({BatchId})
                .skip(Offset)
                .limit(Limit)
            let TotalCount = await ReportIPSegRecordModel.countDocuments({BatchId})
            return this.cb(0, {
                List,
                TotalCount
            })

        } catch (err) {
            logger.getLogger('error').error( 'AddIPSeg failed, the err is: ',err)
            err.code = 60601
            self.err(err)
        }
    }
}