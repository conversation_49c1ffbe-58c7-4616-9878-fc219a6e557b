/*
 * @Date: 2025-07-10 11:16:55
 * @LastEditors: <EMAIL>
 */
'use strict'

const Method = require('../../../libs/method')
const { ReportIPSegBatchModel } = require('../../../mongoModels/icp')
const logger = require("../../../libs/logger")
const { isIPRangeOverlap } = require("../../../fns/kits")
const validator = require("validator");

/**
 * 获取IP上报日志列表
 */
module.exports = class GetIPSegReportLog extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({ Offset = 0, Limit = 20 }) {
        let self = this
        try {
            let List = await ReportIPSegBatchModel.find({})
                .skip(Offset)
                .limit(Limit)
            let TotalCount = await ReportIPSegBatchModel.countDocuments({})
            return this.cb(0, {
                List,
                TotalCount
            })

        } catch (err) {
            logger.getLogger('error').error( 'AddIPSeg failed, the err is: ',err)
            err.code = 60601
            self.err(err)
        }
    }
}