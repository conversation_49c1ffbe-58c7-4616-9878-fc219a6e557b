/*
 * @Date: 2025-07-10 11:16:55
 * @LastEditors: <EMAIL>
 */
'use strict'

const Method = require('../../../libs/method')
const { IPSegModel, ReportIPSegBatchModel, ReportIPSegRecordModel } = require('../../../mongoModels/icp')
const logger = require("../../../libs/logger")
const { parseStringPromise } = require('xml2js');
const { create } = require('xmlbuilder2');
const { infoEncrypt,generateRandomString,pwdHash, serializeToXML, deserializeFromXML, getCurrentFormattedTime } = require('../../../fns/kits')
const fs = require('fs').promises
const path = require('path')

/**
 * 向cnnic上报网段
 */
module.exports = class ReportIPSeg extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({ __ssoUser }) {
        let self = this
        try {
            logger.getLogger('access').info( 'report ip seg start, the user is: ', __ssoUser)
            // 获取所有的ip段
            let ipSegs = await IPSegModel.find({})
            let {_id: BatchId} = await ReportIPSegBatchModel.create({
                Operator: __ssoUser,
                IPSegCount: ipSegs.length
            })
            if (ipSegs.length === 0) {
                return
            }
            let ReportMessage = 'send'
            let filePath = await buildAndSaveXmlFile(ipSegs)
            // 将这个文件上报到cnnic

            await createReportLog(__ssoUser, ipSegs, BatchId, ReportMessage)

            return this.cb(0)
        } catch (err) {
            logger.getLogger('error').error( 'DeleteIPSeg failed, the err is: ',err)
            err.code = 60601
            self.err(err)
        }
    }
}
async function buildAndSaveXmlFile(records) {
    let params = {
        report: {}
    }
    params.report.ispId = global.CONFIG.cnnicConfig.ispId
    params.report.randVal = generateRandomString()
    params.report.pwdHash = pwdHash(global.CONFIG.cnnicConfig.key + params.report.randVal)
    params.report.username = global.CONFIG.cnnicConfig.username
    params.report.beianInfo = {
        company: 'ucloud',
        contact_name: '李田',
        contact_phone: '13408694891',
        contact_email: '<EMAIL>',
        report_time: getCurrentFormattedTime(),
        ip_list: []
    }
    for (let record of records) {
        params.report.beianInfo.ip_list.push({
            ip_start: record.IPStart,
            ip_end: record.IPEnd,
            type: record.IPType
        })
    }
    let xmlData = serializeToXML({beianInfo: params.report.beianInfo})
    let encryptInfo = infoEncrypt(removeXmlStartAndEnd(xmlData,'beianInfo'))
    params.report.beianInfo = encryptInfo
    logger.getLogger('access').info(`the ReportIPSeg params is: ${JSON.stringify(params)}`)

    let xmlContent = serializeToXML(params)
    // 将xmlFile字符串保存文件到test目录下名称为data.xml
    // 6. 保存 XML 文件到 test/data.xml
    const filePath = path.resolve(__dirname, '../../../test/data.xml');
    await fs.writeFile(filePath, xmlContent, 'utf8')
    logger.getLogger('access').info(`the ReportIPSeg data.xml path is: ${JSON.stringify(filePath)}`)
    return filePath
}
// 移除前面的头部数据
function removeXmlStartAndEnd(xmlData, removeField) {
    let startStr = `<${removeField}>`
    let endStr = `</${removeField}>`;
    if (xmlData.startsWith(startStr)) {
        xmlData = xmlData.substring(startStr.length);
    }
    if (xmlData.endsWith(endStr)) {
        xmlData = xmlData.substring(0, xmlData.length - endStr.length);
    }
    return xmlData;
}

// 将这个文件上传到cnnic目录上面
async function reportFileToCnnic(filePath) {
    // 使用ftp协议将一个文件上传到对应ftp服务下
}

async function createReportLog(__ssoUser, ipSegs, BatchId, ReportMessage = '') {
    let reportIPSegRecord = []
    for (let ipSeg of ipSegs) {
        reportIPSegRecord.push({
            IPType: ipSeg.IPType,
            IPStart: ipSeg.IPStart,
            IPEnd: ipSeg.IPEnd,
            Operator: __ssoUser,
            ReportMessage: ReportMessage,
            BatchId: BatchId
        })
    }

    await ReportIPSegRecordModel.insertMany(reportIPSegRecord)

}

