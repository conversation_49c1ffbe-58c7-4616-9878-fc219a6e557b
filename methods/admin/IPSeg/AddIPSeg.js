/*
 * @Date: 2025-07-10 11:16:55
 * @LastEditors: <EMAIL>
 */
'use strict'

const Method = require('../../../libs/method')
const { IPSegModel } = require('../../../mongoModels/icp')
const logger = require("../../../libs/logger")
const { isIPRangeOverlap, compareIPs} = require("../../../fns/kits")
const validator = require("validator");

/**
 * 添加网段
 */
module.exports = class AddIPSeg extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({ IPType, IPStart, IPEnd,  __ssoUser }) {
        let self = this
        try {
            // 检查IP格式是否正确
            if ( !validator.isIP(IPStart, IPType === 0 ? 4 : 6) || !validator.isIP(IPEnd, IPType === 0 ? 4 : 6)) {
                throw new Error('IP格式不正确')

            }
            if (!compareIPs(IPStart, IPEnd, IPType === 1)) {
                throw new Error('IPStart不能大于IPEnd')
            }
            // 检查一下IPStart和IPEnd是否有交集，如果有提示，没有返回
            if (await checkIPSegExist(IPType, IPStart, IPEnd)) {
                throw new Error('IP段已存在，请检查后重新添加')
            }

            await IPSegModel.create({
                IPType,
                IPStart,
                IPEnd,
                Operator: __ssoUser
            })

            return this.cb(0)
        } catch (err) {
            logger.getLogger('error').error( 'AddIPSeg failed, the err is: ',err)
            err.code = 60601
            self.err(err)
        }
    }
}

// 网段较少直接查询，比较即可
async function checkIPSegExist(IPType, IPStart, IPEnd) {
    let selector = {
        IPType
    }
    let records = await IPSegModel.find(selector)
    for (let record of records) {
        if (isIPRangeOverlap({IPType, IPStart, IPEnd}, record)) {
            return true
        }
    }
    return false
}
