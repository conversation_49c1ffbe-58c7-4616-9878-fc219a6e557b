/*
 * @Date: 2025-07-10 11:16:55
 * @LastEditors: <EMAIL>
 */
'use strict'

const Method = require('../../../libs/method')
const { IPSegModel } = require('../../../mongoModels/icp')
const logger = require("../../../libs/logger")

/**
 * 添加网段
 */
module.exports = class DeleteIPSeg extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({ Id,  __ssoUser }) {
        let self = this
        try {
            await IPSegModel.deleteOne({
                _id: Id
            })

            return this.cb(0)
        } catch (err) {
            logger.getLogger('error').error( 'DeleteIPSeg failed, the err is: ',err)
            err.code = 60601
            self.err(err)
        }
    }
}

