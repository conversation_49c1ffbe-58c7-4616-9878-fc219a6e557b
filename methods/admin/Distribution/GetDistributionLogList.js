/*
 * @Date: 2022-07-06 16:01:03
 * @LastEditors: william.qian <EMAIL>
 * @LastEditTime: 2023-09-18 12:03:18
 * @FilePath: /newicp/methods/admin/distribution/GetDistributionLogList.js
 */
'use strict'

const Method = require('../../../libs/method')
const { parseJSON } = require('../../../fns/kits')
const { Op } = require('sequelize')
const _ = require('lodash')
const { ICPModel, ICPWebModel, LogModel } = require('../../../models')
/**
 * 获取分配日志
 */
module.exports = class GetDistributionLogList extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({ OrganizerName, ICPMainNo, Offset, Limit }) {
        let self = this
        try {
            let ICPFilterCond = {}
            let LogFilterCond = {
                Action: 'DistributionICPRecord',
            }
            if (OrganizerName) {
                ICPFilterCond.OrganizerName = {
                    [Op.like]: `%${OrganizerName}%`,
                }
            }
            if (ICPMainNo) {
                ICPFilterCond.ICPMainNo = {
                    [Op.like]: `%${ICPMainNo}%`,
                }
            }
            // 如果通过备案信息过滤 则先查出 备案的ICPId
            if (JSON.stringify(ICPFilterCond) !== '{}') {
                let icpInfo = await ICPModel.findAll({
                    where: ICPFilterCond,
                    attributes: ['Id'],
                    offset: Offset,
                    limit: Limit,
                    order: [['Id', 'DESC']],
                })

                icpInfo = parseJSON(icpInfo)
                if (icpInfo.length === 0) {
                    return this.cb(0, { Logs: [], TotalCount: 0 })
                } else {
                    LogFilterCond.ICPId = {
                        [Op.in]: _.map(icpInfo, 'Id'),
                    }
                }
            }

            let { count: TotalCount, rows: Logs } =
                await LogModel.findAndCountAll({
                    where: LogFilterCond,
                    offset: Offset,
                    limit: Limit,
                    order: [['Id', 'DESC']],
                })
            Logs = parseJSON(Logs)
            let icpIds = _.map(Logs, 'ICPId')

            let icps = await ICPModel.findAll({
                where: {
                    Id: {
                        [Op.in]: icpIds,
                    },
                },
                distinct: true,
                include: [
                    {
                        model: ICPWebModel,
                        as: 'Website',
                    },
                ],
                attributes: [
                    'Id',
                    'ICPMainNo',
                    'OrganizerName',
                    'OrganizerLicenseId',
                ],
            })

            icps = parseJSON(icps)

            Logs = Logs.map((info) => {
                for (let icpinfo of icps) {
                    if (info.ICPId === icpinfo.Id) {
                        info.Domain = []
                        icpinfo.Website.forEach((web) => {
                            info.Domain.push(..._.map(web.Domain, 'Domain'))
                        })
                        info.ICPMainNo = icpinfo.ICPMainNo || ''
                        info.OrganizerName = icpinfo.OrganizerName || ''
                        info.OrganizerLicenseId =
                            icpinfo.OrganizerLicenseId || ''
                        info.Type =
                            info.SourceCompanyId === -1 ? '分配' : '迁移'
                        // break
                    }
                }
                return info
            })
            return this.cb(0, { Logs, TotalCount })
        } catch (e) {
            console.log(e)
            self.err(e)
        }
    }
}
