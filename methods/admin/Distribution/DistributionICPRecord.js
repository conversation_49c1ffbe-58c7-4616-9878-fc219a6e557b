/*
 * @Date: 2022-07-06 16:01:03
 * @LastEditors: william.qian <EMAIL>
 * @LastEditTime: 2023-09-18 12:02:45
 * @FilePath: /newicp/methods/admin/distribution/DistributionICPRecord.js
 */
'use strict'

const Method = require('../../../libs/method')
const { parseJSON } = require('../../../fns/kits')
const _ = require('lodash')
const {
    ICPModel,
    ICPWebModel,
    PictureModel,
    LogModel,
} = require('../../../models')
const CheckICPCanRecord = require('../../common/CheckAuthNameAndPicName')
/**
 * 获取分配日志
 */
module.exports = class DistributionICPRecord extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec(params) {
        let self = this
        try {
            let canRecord = false
            let RetCode = 0
            let Message = '分配成功'
            let { ICPId, UserEmail, CompanyId, ChannelId = 1, __ssoUser } = params
            let logInfo = {}
            // 检查备案是否还在
            let [icp, webs] = await Promise.all([
                ICPModel.findOne({
                    where: {
                        Id: ICPId,
                    },
                }),
                ICPWebModel.findAll({
                    where: {
                        MainId: ICPId,
                    },
                }),
            ])
            if (!icp) {
                let e = new Error('备案不存在')
                e.code = 32181
                return this.err(e)
            }
            icp = parseJSON(icp)
            webs = parseJSON(webs)
            let method = new CheckICPCanRecord((retCode, data) => {
                if (data.CanRecord === true) {
                    canRecord = true
                    logInfo = {
                        Action: 'DistributionICPRecord',
                        Params: JSON.stringify(params),
                        CompanyId,
                        SourceCompanyId: icp.CompanyId,
                        Operator: __ssoUser,
                        status: 1,
                        UserEmail: UserEmail,
                        Result: '成功',
                        ICPId: ICPId,
                    }
                } else {
                    // 保存失败记录
                    logInfo = {
                        Action: 'DistributionICPRecord',
                        Params: JSON.stringify(params),
                        CompanyId,
                        SourceCompanyId: icp.CompanyId,
                        Operator: __ssoUser,
                        status: 0,
                        UserEmail: UserEmail,
                        Result: '失败',
                        ICPId: ICPId,
                    }
                    Message = data.Message
                    RetCode = 32901
                }
            })
            await method.exec({
                CompanyId,
                OrganizerLicenseId: icp.OrganizerLicenseId,
                Channel: ChannelId,
            })
            if (canRecord) {
                // 获取主体和网站的备案中 包含的图片 //在分配的目标公司下创建这些图片
                let pictures = []
                pictures.push(...getPictureList(icp))
                for (let web of webs) {
                    pictures.push(...getPictureList(web))
                }
                let insertInfo = []
                pictures.forEach((picture) => {
                    insertInfo.push({ CompanyId, Url: picture })
                })
                await Promise.all([
                    ICPModel.update(
                        { CompanyId, Channel: ChannelId },
                        {
                            where: {
                                Id: ICPId,
                            },
                        }
                    ),
                    PictureModel.bulkCreate(insertInfo),
                ]).catch((err) => {
                    logInfo.Result = '失败'
                    RetCode = 11002
                })
            }
            await LogModel.create(logInfo)

            return this.cb(RetCode, { Message })
        } catch (e) {
            self.err(e)
        }
    }
}

function getPictureList(row) {
    let pictureList = []
    // pick可过滤空值
    row = _.pick(row, [
        'AppIcon',
        'OrganizerLicensePicture',
        'PICMainLicensePicture',
        'AuthVerificationPicture',
        'RelevantPromiseLetter', // 有关承诺书
        'PromiseLetterPicture', // 承诺书
        'OrganizerResidencePermitPicture',
        'CurtainPicture',
        'LicensePicture',
        'Domain',
        'PreAppoval',
    ])

    _.forEach(row, function (value, key) {
        if (key === 'Domain') {
            // 如果是域名，从JSON中取图片
            pictureList = _.concat(
                pictureList,
                _.map(value, 'CerificationPicture')
            )
        } else if (key === 'PreAppoval') {
            // 如果是前置审批，从JSON中取图片
            pictureList = _.concat(pictureList, _.map(value, 'Picture'))
        } else {
            console.log(key, value)
            pictureList = _.concat(pictureList, value)
        }
    })
    return _.flattenDeep(pictureList)
}
