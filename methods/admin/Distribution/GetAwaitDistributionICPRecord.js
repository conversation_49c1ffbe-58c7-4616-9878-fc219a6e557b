/*
 * @Date: 2022-07-06 16:01:03
 * @LastEditors: william.qian <EMAIL>
 * @LastEditTime: 2023-09-18 12:02:45
 * @FilePath: /newicp/methods/admin/distribution/DistributionICPRecord.js
 */
'use strict'

const Method = require('../../../libs/method')
const axiosApi = require('../../../libs/axiosApi')
const henganApi = require('../../../libs/henganApiPromise')
const _ = require('lodash')
const { sleep } = require('../../../fns/kits')
/**
 * 获取待分配的备案信息
 */
module.exports = class GetAwaitDistributionICPRecord extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({}) {
        let self = this
        let redis = this.redis.get()
        try {
            let result = []
            let records = await redis.hgetall('AwaitDistributeICP')
            for (let ICPMainNo in records) {
                sleep(300) // 控制一下并发速度，这个量不多 就先这么返回
                let icpInfoInUcloud = await henganApi('SelectICPInterface', {
                    KeyWordType: 1,
                    Keyword: ICPMainNo,
                })
                if (icpInfoInUcloud.ICPInfos.length > 0) {
                    result.push(icpInfoInUcloud.ICPInfos[0])
                }
            }
            return this.cb(0, { ICPInfos: result, TotalCount: result.length })
        } catch (e) {
            self.err(e)
        }
    }
}
