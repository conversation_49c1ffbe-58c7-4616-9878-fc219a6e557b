<!--
 * @Author: william.qian <EMAIL>
 * @Date: 2023-02-13 11:15:44
 * @LastEditors: william.qian <EMAIL>
 * @LastEditTime: 2023-02-13 15:09:41
 * @FilePath: /newicp/methods/admin/Sync/数据同步.md
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
# 与恒安的数据同步功能

标签： 数据同步 Redis MySQL

---

主要为2个功能
1.拉取恒安侧的备案域名与备案号的对应记录，对比Redis的缓存数据、MySQL线的数据，保持已备案记录的准确性
2.拉取24小时内恒安网站表更新的记录，执行一次字段的同步，解决在友商字段变更的情况


---

### PullHenganDominAndICPWebNo 拉取恒安的数据，生成处理批次

从恒安数据中查询域名与网站保存到Redis中，生成一个批次，供后续处理，原始数据有效期为7天，7天后，原始数据将清掉，只保存后续处理的结果

后续处理的结果包含，
恒安与我司线上缓存（Redis）的对比结果与同步执行结果
恒安与我司线上数据表（MySQL）的对比结果与同步执行结果

请求参数

| 字段   | 类型   | 必填 | 描述                       |
| ------ | ------ | ---- | -------------------------- |
| Action | String | Yes  | PullHenganDominAndICPWebNo |


返回值

| 字段    | 类型   | 描述                       |
| ------- | ------ | -------------------------- |
| Action  | String | PullHenganDominAndICPWebNo |
| RetCode | Int    | 状态码                     |

---

### CheckoutDataWithRedisCache 开始恒安的数据与Redis的对比

恒安的数据为预先拉取，线上Redis的数据为domain_icp_in_ucloud_map
检查得到的差异数据会保存，同时开始推送检查的MQ,调用接口确定数据在恒安侧是否正确


---

### CheckoutDataWithMySQLTable 开始恒安的数据与MySQL的对比

恒安的数据为预先拉取，MySQL的数据保存在t_icp与t_web。数据会先拉取2表的数据
同时会在t_icp与t_web的核验，既确定，是否存在t_web网站均已注销，但t_icp中主体状态为正常状态的数据
后数据会与恒安的数据做对比，得到差异数据后，会执行更新操作