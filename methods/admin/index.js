'use strict'

const fs = require('fs')
const path = require('path')
// const requireAll = require('require-all')

/*
 * 加载methods目录下的所有文件夹，并且把文件夹中的exports 原封不动的exports出去 实际导出的是文件名哦  注意 不是class名
 */
function loadDirModule(inputPath) {
    const files = fs.readdirSync(inputPath)

    files.forEach((filename) => {
        const filePath = path.join(inputPath, filename)
        const stats = fs.statSync(filePath)
        if (stats.isDirectory()) {
            loadDirModule(filePath)
        }
        if (stats.isFile() && !/.md/.test(filePath)) {
            // 不加载index.js
            const strArr = filePath.toString().split('/')
            const strArrLen = strArr.length
            if (
                strArr[strArrLen - 1] !== 'index.js' &&
                strArr[strArrLen - 1] !== 'examp.js_'
            ) {
                const moduleName = filename.split('.js')[0]
                exports[moduleName] = require(filePath)
            }
        }
    })
}

loadDirModule(__dirname)
