
/**
 * @file StartCompanyInfoCheckBatch,启动公司查询批次
 * <AUTHOR>
 * 第一步，传入BatchId,开始执行任务。
 * 第二类，得到全部的信息，推送到MQ
 * 第三类，推送完，结束
 * 推MQ，启动
 */

const Method = require('../../libs/method')
const { getTableModel } = require('../../fns/kits')
const { Op } = require('sequelize')
const Sequelize = require('sequelize');
const { parseJSON } = require('../../fns/kits')
const csv = require('csvtojson')
const _ = require('lodash')


module.exports = class StartCompanyInfoCheckBatch extends Method {
	constructor(cb) {
		super(cb)
	}
	async exec({
		BatchId, __ssoUser
	} = {}) {
		let fields, sqlObject, result, BatchId

		let self = this
		let icpDatabase = this.db.get('icp')
		let ICPModel = getTableModel('t_icp', icpDatabase)
		let BatchModel = getTableModel('t_company_info_check_batch', icpDatabase)
		let RecordModel = getTableModel('t_company_info_check_record', icpDatabase)

		try {
			// 确定批次状态
	
			// 获取数据
			try {
				 
			} catch (error) {
				throw new this.Err(error, 31504)
			}

			this.cb(0)
		} catch (e) {
			console.log(e)
			if (e instanceof Error) {
				return self.eh({
					err: e,
					code: 31403
				})
			}
			self.eh(e)
		}
	}
}