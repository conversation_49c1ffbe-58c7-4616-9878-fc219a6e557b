/**
 * @file GetCompanyInfoCheckBatchList,获取公司核验的列表
 * <AUTHOR>
 * 第一步，查询
 * 第二步，中文替换
 */

const Method = require('../../../libs/method')
const { getTableModel } = require('../../../fns/kits')
const {
    BATCHSTATUSLISTCOMMENT,
    SOURCETYPELISTCOMMENT,
} = require('../../../configs')

module.exports = class GetCompanyInfoCheckBatchList extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({ Offset, Limit } = {}) {
        let BatchList

        let self = this
        let icpDatabase = this.db.get('icp')
        let BatchModel = getTableModel(
            't_company_info_check_batch',
            icpDatabase
        )

        try {
            // 查询
            try {
                BatchList = await BatchModel.findAll({
                    offset: Offset || 0,
                    limit: Limit || 20,
                })
            } catch (error) {
                throw new this.Err(error, 31504)
            }

            // 中文替换
            for (let index = 0; index < BatchList.length; index++) {
                BatchList[index].Status =
                    BATCHSTATUSLISTCOMMENT[BatchList[index].Status]
                BatchList[index].SourceType =
                    SOURCETYPELISTCOMMENT[BatchList[index].SourceType]
            }

            this.cb(0, { BatchList })
        } catch (e) {
            console.log(e)
            if (e instanceof Error) {
                return self.eh({
                    err: e,
                    code: 31403,
                })
            }
            self.eh(e)
        }
    }
}
