/**
 * @file DescribeCompanyInfoCheckBatch,获取公司核验单个批次详情
 * <AUTHOR>
 * 第一步，查询
 */

const Method = require('../../../libs/method')
const { getTableModel, parseJSON } = require('../../../fns/kits')
const {
    BATCHSTATUSLISTCOMMENT,
    BATCHSTATUSLISTVALUE,
    CHECKRESULTLISTCOMMENT,
    CHECKRESULTLISTVALUE,
} = require('../../../configs')
const { Op } = require('sequelize')

module.exports = class DescribeCompanyInfoCheckBatch extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({ Offset, Limit, Status, CheckResult, BatchId } = {}) {
        let recordList, SQLObject, recordCount

        let self = this
        let icpDatabase = this.db.get('icp')
        let RecordModel = getTableModel(
            't_company_info_check_record',
            icpDatabase
        )

        try {
            // 查询SQL生成
            SQLObject = {
                BatchId,
            }

            if (Status) {
                SQLObject.Status = {
                    [Op.in]: Status.map((record) => {
                        return BATCHSTATUSLISTVALUE[record]
                    }),
                }
            }

            if (CheckResult) {
                SQLObject.CheckResult = {
                    [Op.in]: CheckResult.map((record) => {
                        return CHECKRESULTLISTVALUE[record]
                    }),
                }
            }

            try {
                recordList = await RecordModel.findAll({
                    offset: Offset || 0,
                    limit: Limit || 20,
                    where: SQLObject,
                })

                recordCount = await RecordModel.count({
                    where: SQLObject,
                })
            } catch (error) {
                throw new this.Err(error, 32002)
            }
            recordList = parseJSON(recordList)
            // 中文替换
            for (let index = 0; index < recordList.length; index++) {
                recordList[index].Status =
                    BATCHSTATUSLISTCOMMENT[recordList[index].Status]
                recordList[index].CheckResult =
                    CHECKRESULTLISTCOMMENT[recordList[index].CheckResult]
            }

            this.cb(0, { Record: recordList, Count: recordCount })
        } catch (e) {
            console.log(e)
            if (e instanceof Error) {
                return self.eh({
                    err: e,
                    code: 32001,
                })
            }
            self.eh(e)
        }
    }
}
