/**
 * @file StopCompanyInfoCheckBatch,结束公司查询批次
 * <AUTHOR>
 * 第一步，传入BatchId,检查是否有未完成记录。
 * 第二步，
 * 推MQ，启动
 */

const Method = require('../../../libs/method')
const { getTableModel, parseJSON } = require('../../../fns/kits')
const _ = require('lodash')
const { upload } = require('../../../libs/uploadFile')
const {
    BATCHSTATUSLISTCOMMENT,
    CHECKRESULTLISTCOMMENT,
} = require('../../../configs')
const csvjson = require('csvjson')

module.exports = class DownloadCompanyInfoCheckBatch extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({ BatchId } = {}) {
        let recordList, FileName, FileData, rowCount

        let self = this
        let icpDatabase = this.db.get('icp')
        let BatchModel = getTableModel(
            't_company_info_check_batch',
            icpDatabase
        )
        let RecordModel = getTableModel(
            't_company_info_check_record',
            icpDatabase
        )

        try {
            // 确定批次中是否有未完成的记录，有没有1的记录
            try {
                recordList = await RecordModel.findAll({
                    where: {
                        BatchId,
                    },
                    attributes: [
                        ['id', '记录编号'],
                        ['batch_id', '批次呈'],
                        ['organizer_type', '单位类型'],
                        ['organizer_name', '单位名'],
                        ['organizer_license_area', '单位证件地址'],
                        ['pic_main_name', '主办人名'],
                        ['pic_main_license_id', '主办人证件号'],
                        ['organizer_license_id', '单位证件号'],
                        // ['create_time', '创建时间'],
                        // ['update_time', '更新时间'],
                        ['status', '查询状态'],
                        ['check_result', '检查结果'],
                        ['message', '检查信息'],
                        ['remark', '备注'],
                        // ['return_object', '']
                    ],
                })
                recordList = parseJSON(recordList)
                for (let index = 0; index < recordList.length; index++) {
                    recordList[index]['查询状态'] =
                        BATCHSTATUSLISTCOMMENT[recordList[index]['查询状态']]
                    recordList[index]['检查结果'] =
                        CHECKRESULTLISTCOMMENT[recordList[index]['检查结果']]
                }
            } catch (error) {
                throw new self.Err(error, 31801)
            }

            // 没记录，不支持下载
            rowCount = recordList.length
            if (rowCount === 0) {
                throw new self.Err(
                    new Error('批次文件生成时，此批次不存在任务记录'),
                    31802
                )
            }

            // 转换与上传文件至UFile
            try {
                var options = {
                    delimiter: ';',
                    // headers: "ReturnObject;Id;BatchId;OrganizerType;OrganizerName;OrganizerLicenseArea;PICMainName;OrganizerLicenseId;PICMainLicenseId;CreateTime;UpdateTime;Status;CheckResult;Message;Remark",
                    wrap: false,
                }
                let csvData = await csvjson.toCSV(recordList, options)

                console.log(csvData)
                // 转Base64
                csvData = new Buffer(csvData).toString('base64')
                // 加meta信息
                csvData = 'data:text/csv;base64,' + csvData

                let file
                file = await upload(csvData)
                FileName = file[0]
                FileData = csvData
            } catch (error) {
                throw new this.Err(error, 31803)
            }

            // 更新到批次信息中
            try {
                await BatchModel.update(
                    {
                        FileName,
                    },
                    {
                        where: {
                            Id: BatchId,
                        },
                    }
                )
            } catch (error) {
                throw new this.Err(error, 31804)
            }

            let returnObject = { FileName }
            if (rowCount < 10000) {
                returnObject.FileData = FileData
            }

            this.cb(0, returnObject)
        } catch (e) {
            console.log(e)
            if (e instanceof Error) {
                return self.eh({
                    err: e,
                    code: 31805,
                })
            }
            self.eh(e)
        }
    }
}
