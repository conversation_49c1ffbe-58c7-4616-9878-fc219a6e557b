/*
 * @Author: william.qian <EMAIL>
 * @Date: 2023-03-20 10:41:17
 * @LastEditors: william.qian <EMAIL>
 * @LastEditTime: 2023-05-08 18:39:43
 * @FilePath: /newicp/methods/admin/InspectAndVerify/GetWebCheckRecordList.js
 * @Description: 获取网站检查结果记录的列表
 */
/**
 * @file GetWebCheckRecordList,
 * 如果是导出数据，获取主办单位的名称
 */

const Method = require('../../../libs/method')
const moment = require('moment')
const { parseJSON } = require('../../../fns/kits')
const { Op } = require('sequelize')
const _ = require('lodash')
const { WebCheckRecordModel, ICPModel } = require('../../../models/')

module.exports = class GetWebCheckRecordList extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({
        Offset = 0,
        Limit = 20,
        BatchId,
        Domain,
        CompanyId,
        ICPMainNo,
        ICPWebNo,
    } = {}) {
        try {
            try {
                let queryObject = { BatchId }

                if (Domain) {
                    queryObject.Domain = Domain
                }

                if (CompanyId) {
                    queryObject.CompanyId = CompanyId
                }

                if (ICPMainNo) {
                    queryObject.ICPMainNo = ICPMainNo
                }

                if (ICPWebNo) {
                    queryObject.ICPWebNo = ICPWebNo
                }

                let { count, rows } = await WebCheckRecordModel.findAndCountAll(
                    {
                        attributes: [
                            'Id',
                            'BatchId',
                            'ICPWebNo',
                            'ICPMainNo',
                            'WebName',
                            'CompanyId',
                            'Domain',
                            'HaveResolver',
                            'IsError',
                            'ICPMainNoTrue',
                            'UrlTrue',
                            'TitleTrue',
                            'ErrorMessage',
                            'CreateTime',
                            'UpdateTime',
                        ],
                        order: [['Id', 'DESC']],
                        where: queryObject,
                        offset: Offset,
                        limit: Limit,
                    }
                )
                rows = parseJSON(rows)

                // 如果输出数据总长与查询的数据总长相等，说明是导出数据，需要获取主办单位的名称
                if (Limit === count) {
                    console.log('导出数据，获取主办单位的名称')

                    // 取出主体备案号，查询主办单位的名称
                    let mainNoList = _.map(rows, 'ICPMainNo')
                    mainNoList = _.uniq(mainNoList)
                    // 通过主体表，使用主体备案号查询主办单位的名称
                    let mainList = await ICPModel.findAll({
                        attributes: ['ICPMainNo', 'OrganizerName'],
                        where: {
                            ICPMainNo: {
                                [Op.in]: mainNoList,
                            },
                        },
                    })
                    mainList = parseJSON(mainList)
                    console.log('mainList', mainList)
                    // rows附加主办单位名称
                    rows = _.map(rows, (row) => {
                        let main = _.find(mainList, {
                            ICPMainNo: row.ICPMainNo,
                        })
                        if (main) {
                            row.OrganizerName = main.OrganizerName
                        }
                        return row
                    })
                }

                return this.cb(0, { RecordList: rows, Count: count })
            } catch (error) {
                throw new this.Err(error, 31502)
            }
        } catch (e) {
            console.log(e)
            if (e instanceof Error) {
                return self.eh({
                    err: e,
                    code: 31505,
                })
            }
            self.eh(e)
        }
    }
}
