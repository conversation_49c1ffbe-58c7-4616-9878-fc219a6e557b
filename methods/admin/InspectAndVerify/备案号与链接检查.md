# 备案号与链接检查

标签（空格分隔）： 备案号 链接 爬虫

---

### 生成信息检查批次CreateWebCheckBatch

    1.根据省份(或者全部)得到需要检查的信息
    2.将得到的主体与网站信息以网站以维度组合
    3.将上一步的信息按Domian维度拆的更细。
    4.得到的结果，保存至Redis，开始启动分析

请求参数

| 字段      | 类型   | 必填 | 描述                                                                      |
| --------- | ------ | ---- | ------------------------------------------------------------------------- |
| Action    | String | Yes  | CreateWebCheckBatch                                                       |
| Province  | Int    | Yes  | 省份Id,参考ICPConfig中的Area部分，如110000为北京，310000为上海，全国时为0 |
| ICPMainNo | String | Yes  | 单次检查，只检查此主体备案号下的网站                                      |

返回参数

| 字段    | 类型   | 描述                |
| ------- | ------ | ------------------- |
| Action  | String | CreateWebCheckBatch |
| RetCode | Int    | 状态码              |

---

### 获取网站检查的批次列表GetWebCheckBatchList

请求参数

| 字段   | 类型   | 必填 | 描述                 |
| ------ | ------ | ---- | -------------------- |
| Action | String | Yes  | GetWebCheckBatchList |
| Limit  | String | Yes  | 查询数据量           |
| Offset | String | Yes  | 偏移量               |

返回参数

| 字段      | 类型   | 描述                 |
| --------- | ------ | -------------------- |
| Action    | String | GetWebCheckBatchList |
| BatchList | Array  | 批次列表             |
| RetCode   | Int    | 状态码               |

BatchList参数

| 字段          | 类型   | 描述                                       |
| ------------- | ------ | ------------------------------------------ |
| ProvinceRefer | String | 省份前缀、检查批次详述，没有具体的好的名字 |
| BatchName     | String | 批次名                                     |
| Id            | Int    | 批次Id                                     |
| Status        | String | 状态                                       |
| CreateTime    | Int    | 创建时间                                   |
| UpdateTime    | Int    | 更新时间                                   |
| Operator      | String | 操作人                                     |

```javascript
{
    "BatchList": [
        {
            "Id": 5,
            "ProvinceRefer": "沪ICP备12020087号",
            "BatchName": "WebCheck-2023-04-03-340",
            "Status": "Finish",
            "CreateTime": 1680514234,
            "UpdateTime": 1680514379,
            "Operator": "local_test"
        }
    ],
    "Count": 1,
    "Action": "GetWebCheckBatchListResponse",
    "RetCode": 0
}


```

---

### 获取网站检查内容的详情GetWebCheckRecordList

请求参数

| 字段   | 类型   | 必填 | 描述                 |
| ------ | ------ | ---- | -------------------- |
| Action | String | Yes  | GetWebCheckBatchList |
| Limit  | String | Yes  | 查询数据量           |
| Offset | String | Yes  | 偏移量               |

返回参数

| 字段       | 类型   | 描述                 |
| ---------- | ------ | -------------------- |
| Action     | String | GetWebCheckBatchList |
| RetCode    | Int    | 状态码               |
| RecordList | Array  | 记录列表             |

RecordList参数

| 字段          | 类型   | 描述           |
| ------------- | ------ | -------------- |
| Id            | Int    | 记录Id         |
| BatchId       | Int    | 批次Id         |
| ICPWebNo      | String | 网站备案号     |
| ICPMainNo     | String | 主体备案号     |
| WebName       | String | 网站名         |
| Domain        | String | 域名           |
| ICPMainNoTrue | Boolen | 备案号是否正确 |
| URLTrue       | Boolen | URL是否正确    |
| TitleTrue     | Boolen | 标题是否正确   |
| ErrorMessage  | String | 出错信息       |
| CreateTime    | Int    | 创建时间       |
| UpdateTime    | Int    | 更新时间       |

```javascript
{
    "RecordList": [
        {
            "Id": 355,
            "BatchId": 5,
            "ICPWebNo": "沪ICP备12020087号-52",
            "ICPMainNo": "沪ICP备12020087号",
            "WebName": "",
            "CompanyId": 50142593,
            "Domain": "ucloudstor.com",
            "ICPMainNoTrue": false,
            "URLTrue": false,
            "TitleTrue": false,
            "ErrorMessage": "Error: getaddrinfo ENOTFOUND ucloudstor.com",
            "CreateTime": 1680514378,
            "UpdateTime": 1680514378
        },
        {
            "Id": 354,
            "BatchId": 5,
            "ICPWebNo": "沪ICP备12020087号-12",
            "ICPMainNo": "沪ICP备12020087号",
            "WebName": "",
            "CompanyId": 50142593,
            "Domain": "************",
            "ICPMainNoTrue": false,
            "URLTrue": false,
            "TitleTrue": false,
            "ErrorMessage": "Error: ETIMEDOUT",
            "CreateTime": 1680514378,
            "UpdateTime": 1680514378
        }
    ],
    "Count": 104,
    "Action": "GetWebCheckRecordListResponse",
    "RetCode": 0
}

```
