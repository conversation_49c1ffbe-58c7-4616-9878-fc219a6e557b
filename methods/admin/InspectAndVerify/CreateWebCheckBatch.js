/*
 * @Author: william.qian <EMAIL>
 * @Date: 2023-03-20 10:41:17
 * @LastEditors: william.qian <EMAIL>
 * @LastEditTime: 2023-05-16 11:39:43
 * @FilePath: /newicp/methods/admin/InspectAndVerify/CreateWebCheckBatch.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
/**
 * @file CreateWebCheckBatch,创建网站信息检查批次
 * <AUTHOR>
 * 第一步，根据省份(或者全部)得到需要检查的信息
 * 第二步，将得到的主体与网站信息以网站以维度组合
 * 第三步，将上一步的信息按Domian维度拆的更细。
 * 得到的结果，保存至Redis，开始启动分析
 * 2023年05月04日18:51:44   钱俊烨  增加网站备案号的查询
 */

const Method = require('../../../libs/method')
const moment = require('moment')
const { parseJSON } = require('../../../fns/kits')
const { Op } = require('sequelize')
const getIpSegment = require('../../../libs/ipSegment')
const Area = require('../../../configs/common/area.json')
const _ = require('lodash')
const {
    ICPModel,
    ICPStatusEnum,
    ICPWebModel,
    WebCheckBatchModel,
    WebStatusEnum,
    WebCheckBatchStatusEnum,
} = require('../../../models/')

module.exports = class CreateWebCheckBatch extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({ Province, ICPMainNo, __ssoUser, ICPWebNo } = {}) {
        let mainResult,
            webResult,
            domainList,
            result = [],
            provinceRefer

        if (Province) {
            provinceRefer =
                Province === 0 ? '全国' : _.find(Area, { Id: Province }).Refer
        }

        if (ICPWebNo) {
            provinceRefer = ICPWebNo[0] + '等'
        } else if (ICPMainNo) {
            provinceRefer = ICPMainNo
        }

        if (!provinceRefer) {
            return self.eh({
                err: '省份参数错误',
                code: 31506,
            })
        }

        let self = this
        const redis = self.redis.get()

        try {
            try {
                // 执行数据查询，查询有效需要核验的省份
                // 根据省份，得到，增加到单独公司的验证。

                if (ICPWebNo) {
                    // 如果入参是网站备案号，通过网站备案号反向查询
                    webResult = await ICPWebModel.findAll({
                        attributes: ['MainId', 'Domain', 'ICPWebNo', 'Name'],
                        where: {
                            ICPWebNo: {
                                [Op.in]: ICPWebNo,
                            },
                            InternetServiceType: 1, // 只需要网站的 APP的不要
                            Status: {
                                [Op.notIn]: [
                                    WebStatusEnum['Weblogout'],
                                    WebStatusEnum['Connlogout'],
                                ],
                            },
                        },
                    })
                    webResult = parseJSON(webResult)

                    // 取出MainId后查询主体信息
                    mainResult = await ICPModel.findAll({
                        attributes: ['CompanyId', 'ICPMainNo', 'Id'],
                        where: {
                            Id: {
                                [Op.in]: webResult.map((item) => {
                                    return item.MainId
                                }),
                            },
                            Status: {
                                [Op.notIn]: [
                                    ICPStatusEnum['Logout'],
                                    ICPStatusEnum['Isdeleted'],
                                ],
                            },
                        },
                    })
                } else {
                    ;[mainResult, webResult] = await Promise.all([
                        ICPModel.findAll({
                            attributes: ['CompanyId', 'ICPMainNo', 'Id'],
                            where: {
                                Status: {
                                    [Op.notIn]: [
                                        ICPStatusEnum['Logout'],
                                        ICPStatusEnum['Isdeleted'],
                                    ],
                                },
                                ...( Province !== 0 && { ICPMainNo: { [Op.like]: provinceRefer + '%'} })
                            }
                        }),
                        ICPWebModel.findAll({
                            attributes: [
                                'MainId',
                                'Domain',
                                'ICPWebNo',
                                'Name',
                            ],
                            where: {
                                InternetServiceType: 1,
                                Status: {
                                    [Op.notIn]: [
                                        WebStatusEnum['Weblogout'],
                                        WebStatusEnum['Connlogout'],
                                    ],
                                },
                                ...(Province !== 0 && { ICPWebNo: { [Op.like]: provinceRefer + '%' } }),
                                },
                        }),
                    ])
                    webResult = parseJSON(webResult)
                }
                mainResult = parseJSON(mainResult)
            } catch (error) {
                console.log(error)
                throw new this.Err(error, 31507)
            }

            // 处理结果
            for (var i = 0; i < webResult.length; i++) {
                // 查找主体
                let thisMainRecord = _.find(mainResult, {
                    Id: webResult[i].MainId,
                })

                // 没找到主体的，继续循环
                if (thisMainRecord === undefined) {
                    continue
                }

                webResult[i].Domain.forEach((element) => {
                    // 拆到Domain级
                    result.push({
                        CompanyId: thisMainRecord.CompanyId,
                        ICPMainNo: thisMainRecord.ICPMainNo,
                        ICPWebNo: webResult[i].ICPWebNo,
                        Name: webResult[i].Name,
                        Domain: element.Domain,
                    })
                    result.push({
                        CompanyId: thisMainRecord.CompanyId,
                        ICPMainNo: thisMainRecord.ICPMainNo,
                        ICPWebNo: webResult[i].ICPWebNo,
                        Name: webResult[i].Name,
                        Domain: 'www.' + element.Domain,
                    })
                })
            }

            try {
                // 写Redis
                let batchName =
                    'WebCheck-' +
                    moment().format('YYYY-MM-DD-') +
                    Math.floor(Math.random() * 1000)

                let redisInsertResult = await redis
                    .pipeline()
                    .sadd(
                        batchName,
                        result.map((row) => {
                            return JSON.stringify(row)
                        })
                    )
                    .expire(
                        batchName,
                        3 * 24 * 60 * 60 // 3天内过期
                    )

                    .exec()

                await WebCheckBatchModel.create({
                    ProvinceRefer: provinceRefer,
                    BatchName: batchName,
                    Count: result.length,
                    Status: WebCheckBatchStatusEnum['CheckIng'],
                    Operator: __ssoUser,
                })

                // 更新一次UCloudIP的字段
                await getIpSegment.getUcloudIPSegment()

                for (let index = 0; index < 6; index++) {
                    await self.producer.send({
                        type: 'icp',
                        topic: 'WebCheckForICPInfo',
                        data: { BatchName: batchName },
                    })
                }
            } catch (error) {
                console.log(error, 'create')
                throw new this.Err(error, 31508)
            }
            return this.cb(0)
        } catch (e) {
            console.log(e)
            if (e instanceof Error) {
                return self.eh({
                    err: e,
                    code: 31509,
                })
            }
            self.eh(e)
        }
    }
}
