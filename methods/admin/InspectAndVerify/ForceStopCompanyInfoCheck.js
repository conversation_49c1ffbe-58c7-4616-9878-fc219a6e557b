/**
 * @file ForceStopCompanyInfoCheck,清空目前全部查询队列
 * <AUTHOR>
 * 第一步，清空queue-icp-CheckCompanyInfo的全部内容。
 */

const Method = require('../../../libs/method')
const { axiosRequest } = require('../../../fns/axiosRequestNoLog')
const urlencode = require('urlencode')

module.exports = class ForceStopCompanyInfoCheck extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({ QueueName, Vhost } = {}) {
        let requestOptions
        let self = this

        try {
            //
            QueueName = urlencode(QueueName || 'queue-icp-CheckCompanyInfo')
            Vhost = urlencode(Vhost || '/')

            requestOptions = {
                uri: global.CONFIG.queuesURL + `${Vhost}/${QueueName}/contents`,
            }
            try {
                await axiosRequest(requestOptions, 'delete', 5 * 1000)
            } catch (error) {
                throw new self.Err(error, 31901)
            }

            this.cb(0)
        } catch (e) {
            if (e instanceof Error) {
                return self.eh({
                    err: e,
                    code: 31902,
                })
            }
            self.eh(e)
        }
    }
}
