/**
 * @file StopCompanyInfoCheckBatch,结束公司查询批次
 * <AUTHOR>
 * 第一步，传入BatchId,检查是否有未完成记录。
 * 第二步，
 * 推MQ，启动
 */

const Method = require('../../../libs/method')
const { getTableModel } = require('../../../fns/kits')
const _ = require('lodash')
const producer = require('../../../libs/producer')

module.exports = class StopCompanyInfoCheckBatch extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({ BatchId } = {}) {
        let batchCount

        let self = this
        let icpDatabase = this.db.get('icp')
        let BatchModel = getTableModel(
            't_company_info_check_batch',
            icpDatabase
        )
        let RecordModel = getTableModel(
            't_company_info_check_record',
            icpDatabase
        )

        try {
            // 确定批次中是否有未完成的记录，有没有1的记录
            try {
                batchCount = await RecordModel.count({
                    where: {
                        Id: BatchId,
                        Status: 1,
                    },
                })
            } catch (error) {
                throw new self.Err(error, 31701)
            }

            // 存在，则无法成功
            if (batchCount != 0) {
                throw new self.Err(
                    new Error('结束批次时，存在未完成的记录'),
                    31702
                )
            }

            // 更新批次任务
            try {
                await BatchModel.update(
                    {
                        Status: 2,
                    },
                    {
                        where: {
                            Id: BatchId,
                        },
                    }
                )
            } catch (error) {
                throw new this.Err(error, 31703)
            }

            this.cb(0)
        } catch (e) {
            console.log(e)
            if (e instanceof Error) {
                return self.eh({
                    err: e,
                    code: 31704,
                })
            }
            self.eh(e)
        }
    }
}
