/**
 * @file StartCompanyInfoCheckBatch,启动公司查询批次
 * <AUTHOR>
 * 第一步，传入BatchId,开始执行任务。
 * 第二类，得到全部的信息，推送到MQ
 * 第三类，推送完，结束
 * 推MQ，启动
 */

const Method = require('../../../libs/method')
const { getTableModel } = require('../../../fns/kits')
const { Op } = require('sequelize')
const Sequelize = require('sequelize')
const { parseJSON } = require('../../../fns/kits')
const csv = require('csvtojson')
const _ = require('lodash')
const producer = require('../../../libs/producer')

const BatchStatusList = [
    {
        VALUE: 0,
        STATUS: 'INIT',
        COMMENT: '初始化',
    },
    {
        VALUE: 1,
        STATUS: 'Doing',
        COMMENT: '查询中',
    },
    {
        VALUE: 2,
        STATUS: 'Done',
        COMMENT: '完成',
    },
]

module.exports = class StartCompanyInfoCheckBatch extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({ BatchId, __ssoUser } = {}) {
        let fields, result, batchCount

        let self = this
        let icpDatabase = this.db.get('icp')
        let BatchModel = getTableModel(
            't_company_info_check_batch',
            icpDatabase
        )
        let RecordModel = getTableModel(
            't_company_info_check_record',
            icpDatabase
        )

        try {
            // 确定批次状态
            try {
                batchCount = await BatchModel.count({
                    where: {
                        Id: BatchId,
                        Status: 0,
                    },
                })
            } catch (error) {
                throw new self.Err(error, 31601)
            }
            if (batchCount != 1) {
                throw new self.Err(new Error('启动批次时，批次状态异常'), 31602)
            }
            // 获取数据
            try {
                result = await RecordModel.findAll({
                    // id: '135499',
                    where: {
                        BatchId,
                    },
                    attributes: [
                        'OrganizerName',
                        'OrganizerLicenseArea',
                        'PICMainName',
                        'PICMainLicenseId',
                        'Id',
                        'OrganizerLicenseId',
                    ],
                })
                result = parseJSON(result)
            } catch (error) {
                throw new this.Err(error, 31603)
            }

            // 推送MQ
            try {
                result.forEach((element) => {
                    element.Timeout = 0
                    producer.send({
                        type: 'icp',
                        topic: 'CheckCompanyInfo',
                        data: element,
                    })
                })

                // // 执行状态根据，
                Promise.all([
                    RecordModel.update(
                        {
                            Status: 1,
                        },
                        {
                            where: {
                                BatchId,
                            },
                        }
                    ),
                    BatchModel.update(
                        {
                            Status: 1,
                        },
                        {
                            where: {
                                Id: BatchId,
                            },
                        }
                    ),
                ])
            } catch (error) {
                throw new this.Err(error, 31504)
            }

            this.cb(0)
        } catch (e) {
            console.log(e)
            if (e instanceof Error) {
                return self.eh({
                    err: e,
                    code: 31605,
                })
            }
            self.eh(e)
        }
    }
}
