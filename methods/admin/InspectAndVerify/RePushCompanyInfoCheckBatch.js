/**
 * @file RePushCompanyInfoCheckBatch,防止MQ丢消息，如发生后，重新推送MQ
 * <AUTHOR>
 * 第一步，传入BatchId,确定目前是否有查询中的记录
 * 第二类，确定是否目前队列已为空
 * 第三类，推送完，结束
 * 推MQ，启动
 */

const Method = require('../../../libs/method')
const { getTableModel, parseJSON } = require('../../../fns/kits')
const _ = require('lodash')
const producer = require('../../../libs/producer')
const { axiosRequest } = require('../../../fns/axiosRequestNoLog')
const urlencode = require('urlencode')
const { configure } = require('log4js')

module.exports = class RePushCompanyInfoCheckBatch extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({ BatchId, __ssoUser } = {}) {
        let fields, queueInfo, result, queueCount

        let self = this
        let icpDatabase = this.db.get('icp')
        let BatchModel = getTableModel(
            't_company_info_check_batch',
            icpDatabase
        )
        let RecordModel = getTableModel(
            't_company_info_check_record',
            icpDatabase
        )

        try {
            // 确定队列中是否有消息
            let QueueName = urlencode('queue-icp-CheckCompanyInfo')
            let Vhost = urlencode('/')

            let requestOptions = {
                uri: global.CONFIG.mq.queuesURL + `${Vhost}/${QueueName}/`,
                headers: {
                    Authorization: global.CONFIG.mq.Authorization,
                },
            }
            try {
                console.log(requestOptions)
                queueInfo = await axiosRequest(requestOptions, 'get', 5 * 1000)
                queueCount =
                    queueInfo.messages_ready +
                    queueInfo.messages_unacknowledged +
                    queueInfo.messages
            } catch (error) {
                throw new self.Err(error, 32101)
            }

            if (queueCount !== 0) {
                throw new self.Err(
                    new Error(
                        '重新推送查询记录时，队列长度不为空，不能重新推送'
                    ),
                    32102
                )
            }

            // 获取数据
            try {
                result = await RecordModel.findAll({
                    where: {
                        BatchId,
                        // 状态应该是不成功，不失败，也不出错
                        CheckResult: 0,
                    },
                    attributes: [
                        'OrganizerName',
                        'OrganizerLicenseArea',
                        'PICMainName',
                        'PICMainLicenseId',
                        'Id',
                        'OrganizerLicenseId',
                    ],
                })
                result = parseJSON(result)
            } catch (error) {
                throw new this.Err(error, 32103)
            }

            // 推送MQ
            try {
                // 执行状态根据，
                result.forEach((element) => {
                    element.Timeout = 0
                    producer.send({
                        type: 'icp',
                        topic: 'CheckCompanyInfo',
                        data: element,
                    })
                })
            } catch (error) {
                throw new this.Err(error, 31504)
            }

            this.cb(0)
        } catch (e) {
            console.log(e)
            if (e instanceof Error) {
                return self.eh({
                    err: e,
                    code: 31605,
                })
            }
            self.eh(e)
        }
    }
}
