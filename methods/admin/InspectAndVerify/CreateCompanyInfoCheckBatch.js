/**
 * @file CreateCompanyInfoCheckBatch,创建信息检查批次，分检查类型
 * <AUTHOR>
 * 第一类，生成SQL或者直接得到
 * 第二类，自写SQL,做执行
 * 第三类，上传公司三要素或者4要素信息，
 * 推MQ，启动
 */

const Method = require('../../../libs/method')
const { getTableModel, parseJSON, splitArray } = require('../../../fns/kits')
const { Op } = require('sequelize')
const Sequelize = require('sequelize')
const csv = require('csvtojson')
const _ = require('lodash')

module.exports = class CreateCompanyInfoCheckBatch extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({ SourceType, SQLString, File, Remark, __ssoUser } = {}) {
        let fields, sqlObject, result, BatchId

        let self = this
        let icpDatabase = this.db.get('icp')
        let ICPModel = getTableModel('t_icp', icpDatabase)
        let BatchModel = getTableModel(
            't_company_info_check_batch',
            icpDatabase
        )
        let RecordModel = getTableModel(
            't_company_info_check_record',
            icpDatabase
        )

        try {
            // 需要取的字段
            if (SourceType !== 3) {
                // 不是上传的，通过SQL取内容 OrganizerLicenseId OrganizerLicenseId
                fields = [
                    'OrganizerType',
                    'OrganizerName',
                    'OrganizerLicenseArea',
                    'PICMainName',
                    'OrganizerLicenseId',
                    'PICMainLicenseId',
                    ['icp_main_no', 'Remark'],
                ]
                // 字段是否复用
                if (SourceType === 1) {
                    sqlObject = {
                        Status: { [Op.ne]: 1 },
                        OrganizerType: { [Op.ne]: 5 },
                    }
                }

                if (SourceType === 2) {
                    if (!SQLString) {
                        throw new this.Err(
                            new Error('创建企业信息核查批次，SQL语句异常'),
                            31501
                        )
                    }
                    // 如果是使用的原生String
                    sqlObject = Sequelize.literal(SQLString)
                }
                console.log(sqlObject)

                // 执行查询，做异常的获取
                try {
                    result = await ICPModel.findAll({
                        attributes: fields,
                        where: sqlObject,
                    })
                } catch (error) {
                    throw new this.Err(error, 31502)
                }

                result = parseJSON(result)
                console.log(result[0])
            } else {
                try {
                    // 去掉头部信息与Base64转码
                    File = File.replace(/^data:text\/\w+;base64,/, '')
                    File = new Buffer(File, 'base64').toString()

                    result = await csv({ flatKeys: true })
                        .fromString(File)
                        .subscribe((jsonObj) => {
                            console.log(jsonObj)
                            return Promise.resolve(jsonObj)
                        })
                } catch (error) {
                    throw new this.Err(error, 31503)
                }
            }

            // 对数据做内容检查
            _.forEach(result, function (row) {
                // 得到每个Object
                if (
                    !row.OrganizerName ||
                    !row.OrganizerType ||
                    !row.OrganizerLicenseArea ||
                    !row.PICMainName ||
                    !row.OrganizerLicenseId
                ) {
                    throw new self.Err(
                        new Error('存在数据缺失:' + row.OrganizerName),
                        31504
                    )
                }
            })

            // 完成内容获取与检查后，创建新批次
            try {
                BatchId = await BatchModel.create({
                    Remark,
                    SourceType,
                    Status: 0,
                    Creator: __ssoUser,
                })
                BatchId = parseJSON(BatchId).Id
                // Record.create(result)
                _.forEach(result, function (row) {
                    // 得到每个Object
                    row.BatchId = BatchId
                    row.OrganizerLicenseId = row.OrganizerLicenseId.trim()

                })

                // 可能超过最大数据量，分批次插入
                let arrList = splitArray(result, 10000)
                let promiseList = []
                arrList.forEach((element) => {
                    promiseList.push(RecordModel.bulkCreate(element))
                })
                await Promise.all(promiseList)
            } catch (error) {
                throw new this.Err(error, 31504)
            }

            this.cb(0, { BatchId })
        } catch (e) {
            console.log(e)
            if (e instanceof Error) {
                return self.eh({
                    err: e,
                    code: 31505,
                })
            }
            self.eh(e)
        }
    }
}
