/*
 * @Author: william.qian <EMAIL>
 * @Date: 2023-03-20 10:41:17
 * @LastEditors: william.qian <EMAIL>
 * @LastEditTime: 2023-05-12 18:37:01
 * @FilePath: /newicp/methods/admin/InspectAndVerify/GetWebCheckBatchList.js
 * @Description:获取网站检查列表
 */
/**
 * @file GetWebCheckBatchList,
 */

const Method = require('../../../libs/method')
const moment = require('moment')
const { parseJSON } = require('../../../fns/kits')
const { Op } = require('sequelize')
const _ = require('lodash')
const {
    WebCheckBatchModel,
    WebCheckRecordModel,
    WebCheckBatchStatusEnum,
} = require('../../../models/')

module.exports = class GetWebCheckBatchList extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({
        Offset = 0,
        Limit = 20,
        CompanyId,
        Domain,
        ICPWebNo,
        ICPMainNo,
    } = {}) {
        try {
            try {
                // 如果有搜索条件
                let queryObject = {}
                if (CompanyId || Domain || ICPWebNo || ICPMainNo) {
                    //  执行详情集内的搜索
                    let webQueryObject = {}
                    if (CompanyId) {
                        webQueryObject.CompanyId = CompanyId
                    }
                    if (Domain) {
                        webQueryObject.Domain = Domain
                    }
                    if (ICPWebNo) {
                        webQueryObject.ICPWebNo = ICPWebNo
                    }
                    if (ICPMainNo) {
                        webQueryObject.ICPMainNo = ICPMainNo
                    }
                    let webList = await WebCheckRecordModel.findAll({
                        attributes: ['BatchId'],
                        order: [['Id', 'DESC']],
                        where: webQueryObject,
                    })
                    webList = parseJSON(webList)
                    if (webList && webList.length > 0) {
                        queryObject.Id = webList.map((item) => {
                            return item.BatchId
                        })
                    }
                }

                let { count, rows } = await WebCheckBatchModel.findAndCountAll({
                    where: queryObject,
                    attributes: [
                        'Id',
                        'ProvinceRefer',
                        'BatchName',
                        'Status',
                        'CreateTime',
                        'Count',
                        'UpdateTime',
                        'Operator',
                    ],
                    order: [['Id', 'DESC']],
                    offset: Offset,
                    limit: Limit,
                })
                rows = parseJSON(rows)
                // WebCheckBatchStatusEnum

                let toCheckList = []
                for (let index = 0; index < rows.length; index++) {
                    rows[index].Status = _.findKey(
                        WebCheckBatchStatusEnum,
                        function (o) {
                            return o === rows[index].Status
                        }
                    )
                    if (rows[index].Status === 'CheckIng') {
                        toCheckList.push(rows[index].BatchName)
                    }
                }

                this.cb(0, { BatchList: rows, Count: count })
                if (toCheckList.length !== 0) {
                    for (var i = 0; i < toCheckList.length; i++) {
                        await this.producer.send({
                            type: 'icp',
                            topic: 'FinishWebCheckForICPInfo',
                            data: { BatchName: toCheckList[i] + '_Response' },
                        })
                    }
                }
            } catch (error) {
                throw new this.Err(error, 31512)
            }
        } catch (e) {
            console.log(e)
            if (e instanceof Error) {
                return self.eh({
                    err: e,
                    code: 31513,
                })
            }
            self.eh(e)
        }
    }
}
