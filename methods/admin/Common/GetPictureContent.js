/*
 * @Date: 2023-05-23 17:00:20
 * @LastEditors: l<PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2023-05-25 11:54:48
 * @FilePath: /newicp/methods/admin/Common/GetPictureContent.js
 */
/* 获取图片内容
 * @Date: 2023-05-23 17:00:20
 * @LastEditors: li<PERSON><PERSON>en <EMAIL>
 * @LastEditTime: 2023-05-23 17:00:30
 * @FilePath: /newicp/methods/admin/Common/GetPictureContent.js
 */
const Method = require('../../../libs/method')
const { checkImgType, convertUS3FileToBase64 } = require('../../../fns/kits')
const { getPictureUrls } = require('../../../fns/uflieFuns')
module.exports = class GetPictureContent extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({ Version, Picture, FileName } = {}) {
        let self = this
        try {
            // 获取图片url
            let url = getPictureUrls([Picture])[Picture]
            let fileBase64
            // 判断是否是新版本,旧版本直接返回，不是图片也直接返回
            if (Version !== '4.6' || !checkImgType(Picture)) {
                // 获取图片base64
                fileBase64 = await convertUS3FileToBase64(url)
            } else {
                if (FileName && /域名证书/.test(FileName)) {
                    url = url + `&iopcmd=thumbnail&type=4&width=1024`
                } else {
                    url = url + `&iopcmd=thumbnail&type=5&height=1024`
                }
                fileBase64 = await convertUS3FileToBase64(url)
            }
            return this.cb(0, { Content: fileBase64 })
        } catch (e) {
            let err = new Error(`图片内容获取失败` + e.message)
            err.code = 32159
            self.err(err)
        }
    }
}
