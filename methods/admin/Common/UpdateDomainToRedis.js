/**
 * @file API UpdateDomainToRedis,更新域名至Redis,
 * <AUTHOR>
 * {Dommains:[{'xxxx.com':'苏xxxx备-1'}]}
 * @return { } 返回符合成功与否
 * 执行步骤，
 * 1、取最新域名，自己计算或者上传
 * 2、执行Redis 的PIPE。Sadd到线上暂存区
 * 3、SDIFF得到与线上差集
 * 4、Sadd到线上
 * 5、SREM差集中的数据
 * update 2021年11月08日16:30:05 针对上传，但是系统中没有的域名，推到Del的MQ中，不直接删除
 * {
    "Domains": {
        "solardrv.cn": "浙ICP备14017580号-3",
        "hibox.me": "沪ICP备15039673号-1",
        "swiftcn.io": "粤ICP备15084972号-1",
        "qeeyou.com": "滇ICP备13003131号-1",
        "egzg.org": "京ICP备14026030号-1",
        "uniperfect.com.cn": "粤ICP备15038917号-1"}
    }
 */

const Method = require('../../../libs/method')
const _ = require('lodash')
const { getTableModel, parseJSON } = require('../../../fns/kits')
const { Op } = require('sequelize')
const producer = require('../../../libs/producer')
const moment = require('moment')
const { CONNECTSYNCVALUE, CONNECTSYNCCOMMENT } = require('../../../configs')
const now = parseInt(moment().format('X'))
module.exports = class UpdateDomainToRedis extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({ Domains, UseDB } = {}) {
        let self = this
        let redis = this.redis.get()
        let domainList = []

        try {
            // 新增操作

            // 取出域名列表
            _.mapKeys(Domains, function (value, key) {
                domainList.push(key)
            })
            const addPromise = redis
                .pipeline()
                // 暂存域名结构
                .sadd('parse_domain', domainList)
                // 线上全部域名
                .sadd('domain_in_ucloud', domainList)
                .hmset('domain_icp_in_ucloud_map', Domains)
                // 找出线上有，目前无的，为待删除
                .sdiff('domain_in_ucloud', 'parse_domain')
                .exec()

            let diffDomain = await addPromise.then((result) => {
                // 确定执行中有没有出错的
                result.forEach((element) => {
                    if (element[0] !== null) {
                        return Promise.reject(new this.Err(element[0], 32403))
                    }
                })
                return Promise.resolve(result[3][1])
            })

            // 删除操作
            let removePromise = redis.pipeline().del('parse_domain')

            if (diffDomain.length === 0) {
                removePromise = await removePromise.exec()
            } else {
                removePromise = await removePromise
                    .srem('domain_in_ucloud', diffDomain)
                    // .hdel('domain_icp_in_ucloud_map', diffDomain)        2021年11月08日16:44:32  不直接删除，推给MQ确定
                    .exec()


                for (const domainNeedDel of diffDomain) {
                    await producer.send({
                        type: 'icp',
                        topic: 'DelDomainForResolveOrder',
                        data: {
                            Domain: domainNeedDel,
                            ICPWebNo: Domains[domainNeedDel],
                        },
                    })
                }
            }

            // 出错控制
            removePromise.forEach((element) => {
                if (element[0] !== null) {
                    return Promise.reject(new this.Err(element[0], 32404))
                }
            })

            self.cb(0)
        } catch (e) {
            console.log(e)
            if (e instanceof Error) {
                return self.eh({
                    err: e,
                    code: 32401,
                })
            }
            self.eh(e)
        }
    }
}
