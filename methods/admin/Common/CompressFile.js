/*
 * @Date: 2023-06-06 15:59:36
 * @LastEditors: william.qian <EMAIL>
 * @LastEditTime: 2023-09-18 18:19:20
 * @FilePath: /newicp/methods/admin/Common/CompressFile.js
 */
/**
 * @file API CompressFile,传入文件名，压缩，上传，得到新的文件名。返回
 * <AUTHOR>
 * @return {TargetFileName} 目标文件名
 * 错误码:
 * 30101: " ",
 * 30103: " ",

 */

const Method = require('../../../libs/method')
const { getUrl } = require('../../../fns/verifyFun')
const { convertPicture } = require('../../../fns/fileParse')
const { getPictureMeta, getTargetPicture } = require('../../../fns/uflieFuns')
const { getTableModel } = require('../../../fns/kits')
const { upload } = require('../../../libs/uploadFile')
const _ = require('lodash')
const fs = require('fs')
module.exports = class CompressFile extends Method {
    constructor(cb) {
        super(cb)
    }
    /**
     * 压缩图片至 指定的尺寸 以及像素 目前只支持宽边最小限制
     * @param {*} FileName 文件名
     * @param {*} MinSize 文件最小存储空间
     * @param {*} MaxSize 文件最大存储空间
     * @param {*} WidthLimit 像素宽边限制 最小
     * @param {*} CompanyId 公司id
     * @returns
     */
    async exec({
        FileName,
        MinSize,
        MaxSize,
        WidthLimit,
        CompanyId,
        PictureType = 'Common',
    } = {}) {
        let fileUrl, fileMetaInfo, targetFileName, PictureModel, fileBase64

        let self = this
        let icpDatabase = self.db.get('icp')
        PictureModel = getTableModel('t_picture', icpDatabase)

        let sizeRange = {
            minSize: MinSize || 110, // kb
            maxSize: MaxSize || 190, // kb
        }
        WidthLimit = WidthLimit || 1700

        try {
            // 生成图片URL
            fileUrl = getUrl(null, FileName, null, true)
            // 无论哪种逻辑，都需要保存到本地，那一步到位先下载吧

            try {
                // 获取文件Meta信息,{ sourceFilePath:xxxxx, sourceBase64: 'data:' + mineType.lookup(sourceFilePath) + ';base64,' + thisbase64 }
                fileMetaInfo = await getPictureMeta(fileUrl, false, true)
            } catch (error) {
                throw new this.Err(error, 32201)
            }
            // 计算得到kb
            fileMetaInfo.Size = (fileMetaInfo.Size / 1024).toFixed(2)
            if (
                fileMetaInfo.Size > sizeRange.minSize &&
                fileMetaInfo.Size < sizeRange.maxSize &&
                _.max([fileMetaInfo.Height, fileMetaInfo.Width]) > WidthLimit
            ) {
                fileBase64 =
                    'data:' +
                    'image/jpg' + // 此处强制使用jpg
                    ';base64,' +
                    Buffer.from(fileMetaInfo.Buffer).toString('base64')
            } else {
                fileBase64 = await convertPicture(
                    fileUrl,
                    fileMetaInfo,
                    WidthLimit,
                    sizeRange,
                    PictureType
                )
            }
            try {
                // 执行上传，大于的传新文件的Base64，小的传原始文件
                targetFileName = await upload(fileBase64)

                // 更新到公司名下
                await PictureModel.create({ CompanyId, Url: targetFileName[0] })
            } catch (error) {
                throw new this.Err(error, 32203)
            }

            self.cb(0, {
                TargetFileName: targetFileName[0],
                FileBase64: fileBase64,
            })
        } catch (e) {
            console.log(e)
            let err = new Error(e.message)
            err.code = 32155
            self.err(err)
        }
    }
}
