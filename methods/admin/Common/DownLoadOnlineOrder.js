const Method = require('../../../libs/method')
const { getTableModel, parseJSON } = require('../../../fns/kits')
const { getPictures } = require('../../../fns/checkOrder')
const ENV = require('../../../configs/env')
const axios = require('../../../libs/axiosApi')
const { OrderWebPlatInfoModel } = require('../../../mongoModels/icp')
const _ = require('lodash')
/**
 * 根据订单号拉取 线上该订单的信息到测试环境中
 */
module.exports = class DownLoadOnlineOrder extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec(params = {}) {
        let self = this
        try {
            //测试 从测试数据库 导出 到空数据库
            const icpDatabase = this.db.get('icp')
            const OrderModel = getTableModel('t_order', icpDatabase)
            const OrderWebModel = getTableModel('t_order_web', icpDatabase)
            const PictureModel = getTableModel('t_picture', icpDatabase)

            //检查是否是测试环境
            if (ENV.toLocaleLowerCase() == 'production') {
                return this.eh({
                    err: new Error('this api can only be used test env'),
                    code: 32304,
                })
            }

            //检查订单号是否已存在
            const existOrderNo = await OrderModel.findOne({
                where: { OrderNo: params.OrderNo, IsDeleted: 0 },
            })

            //查看线上订单信息
            let config = {
                method: 'POST',
                url: global.CONFIG.ucloudInternalApi,
                headers: {
                    'Content-Type': 'application/json',
                    'SourceEnv': 'production' // 指定访问线上
                },
                data: {
                    Backend: 'ICPV4',
                    Action: 'GetOrderInfo',
                    OrderNo: params.OrderNo,
                    staff_name_en: 'gray',
                },
                json: true,
                timeout: 2000,
            }
            let res = await axios(config)
            const onlineOrderInfo = res.data.Order

            if (onlineOrderInfo) {
                if (existOrderNo) {
                    // 删除本地该订单重新下拉
                    await Promise.all([
                        OrderModel.destroy({
                            where: {
                                OrderNo: params.OrderNo,
                            },
                        }),
                        OrderWebModel.destroy({
                            where: {
                                OrderNo: params.OrderNo,
                            },
                        }),
                    ])
                }
                let targetOrderQuery
                //将订单数据copy, 如果订单状态是已删除，则更改为 未删除 再存储
                targetOrderQuery = _.omit(onlineOrderInfo, [
                    'History',
                    'Website',
                    'Curtain',
                    'AddWrite',
                ])
                targetOrderQuery.isDelete = 0
                //更改 订单所属的companyId
                targetOrderQuery.CompanyId = params.CompanyId
                //删除参数的id信息
                delete targetOrderQuery.Id
                delete targetOrderQuery.Domain // get获取到domain的是空[] 字段不使用

                //在本地创建该order信息
                await OrderModel.upsert(targetOrderQuery)

                //查找订单下的所有网站
                let onlineOrderWebMsg = _.pick(onlineOrderInfo, [
                    'Website',
                ]).Website

                //获取该订单号下 主体和网站中的所有的图片
                const pictures = getPictures(onlineOrderInfo)

                if (onlineOrderWebMsg.length > 0) {
                    const insertOrderWebArr = []
                    for (let orderWeb of onlineOrderWebMsg) {
                        //将此订单号下的所有网站 迁移到测试环境中
                        let targetOrderWebQuery = Object.assign({}, orderWeb)
                        delete targetOrderWebQuery.Id
                        insertOrderWebArr.push(
                            OrderWebModel.create(targetOrderWebQuery).then(
                                (newRecord) => {
                                    newRecord = parseJSON(newRecord)
                                    if (orderWeb.AppPlatformInformationList) {
                                        // APP类型 查看 是否有APP平台信息 有的话拉下来
                                        new OrderWebPlatInfoModel({
                                            OrderWebId: newRecord.Id,
                                            AppPlatformInformationList:
                                                orderWeb.AppPlatformInformationList ||
                                                [],
                                        }).save()
                                    }
                                }
                            )
                        )
                    }
                    await Promise.all(insertOrderWebArr)
                }
                if (pictures.length > 0) {
                    let insertPictureArr = []
                    pictures.forEach((url) => {
                        const pictureQuery = {
                            CompanyId: params.CompanyId,
                            Url: url,
                        }
                        insertPictureArr.push(PictureModel.upsert(pictureQuery))
                    })
                    await Promise.all(insertPictureArr)
                }
                return this.cb(0)
            } else {
                //找不到订单
                return this.eh({
                    err: new Error('not found order'),
                    code: 32302,
                })
            }
            //将订单信息插入测试数据库
        } catch (e) {
            if (e instanceof Error) {
                return self.eh({
                    err: e,
                    code: 32301,
                })
            }

            this.eh(e)
        }
    }
}
