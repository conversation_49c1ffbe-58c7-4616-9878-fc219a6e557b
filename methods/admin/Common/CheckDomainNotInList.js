/**
 * @file API UpdateDomainToRedis,更新域名至Redis,
 * <AUTHOR>
 * {Dommains:[{'xxxx.com':'苏xxxx备-1'}]}
 * @return { } 返回符合成功与否
 * 执行步骤，
 * 1、对比传入的域名，判断线下与传入的差异项
 */

const Method = require('../../../libs/method')
const _ = require('lodash')
const {
    getTableModel,
    parseJSON,
    getICPMainNoByICPWebNo,
} = require('../../../fns/kits')
const { Op } = require('sequelize')
const producer = require('../../../libs/producer')
const moment = require('moment')
const { CONNECTSYNCVALUE, CONNECTSYNCCOMMENT } = require('../../../configs/')
const now = parseInt(moment().format('X'))

module.exports = class CheckDomainNotInList extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({ ICPWebNoList } = {}) {
        let self = this
        let icpDatabase = self.db.get('icp')

        let WebModel = getTableModel('t_web', icpDatabase)

        try {
            let icpWebNoINDb = await WebModel.findAll({
                attributes: ['ICPWebNo'],
                where: { Status: { [Op.ne]: 1 } },
            })

            icpWebNoINDb = parseJSON(icpWebNoINDb)

            icpWebNoINDb = _.map(icpWebNoINDb, function (o) {
                return o.ICPWebNo
            })

            let listHave = []
            for (const iterator of ICPWebNoList) {
                // console.log(iterator,1)
                let indexId = _.indexOf(icpWebNoINDb, iterator)
                if (indexId === -1) {
                    listHave.push(getICPMainNoByICPWebNo(iterator))
                }
            }

            let dbhave = []

            for (const iterator of icpWebNoINDb) {
                let indexId = _.indexOf(ICPWebNoList, iterator)
                if (indexId === -1) {
                    dbhave.push(getICPMainNoByICPWebNo(iterator))
                }
            }

            self.cb(0, { DBHave: dbhave, ListHave: listHave })
        } catch (e) {
            console.log(e)
            if (e instanceof Error) {
                return self.eh({
                    err: e,
                    code: 32401,
                })
            }
            self.eh(e)
        }
    }
}
