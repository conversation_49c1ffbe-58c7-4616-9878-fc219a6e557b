/**
 * @file API GetIcpCommonConfigOnlineList,获取Mongo中的配置列表
 * <AUTHOR>
 * @return {Object} 返回符合条件的记录,格式如下
 * {
    "Config": [{}],
    "RetCode": 0
   }
 * 错误码:
	"30103": "获取Redis线上配置时其它异常",
 */

const Method = require('../../../libs/method')
const { findAsync, countAsync } = require('../../../fns/mongoFuns')
const ObjectId = require('mongodb').ObjectId

module.exports = class GetIcpCommonConfigOnlineList extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({ Offset, Limit, Id, Name } = {}) {
        let self = this
        let mongo = this.mongo.get('icp-config')
        let commonConfigCollection = mongo.collection('common_config')

        let result, findObject, countNumber
        try {
            // 查询Redis
            findObject = {}
            if (Name) {
                findObject.Name = Name
            }
            if (Id) {
                findObject['_id'] = ObjectId(Id)
            }

            ;[result, countNumber] = await Promise.all([
                findAsync(commonConfigCollection, findObject, {
                    Limit,
                    Offset,
                    sort: { UnixTime: 1 },
                }),
                countAsync(commonConfigCollection, findObject),
            ])

            result.map((record) => {
                record.Id = record['_id']
                delete record['_id']
            })
            return self.cb(0, { List: result, Count: countNumber })
        } catch (e) {
            if (e instanceof Error) {
                return self.eh({
                    err: e,
                    code: 30103,
                })
            }
            self.eh(e)
        }
    }
}
