const Method = require('../../../libs/method')
const { getTableModel, parseJSON } = require('../../../fns/kits')
const _ = require('lodash')
const { Op } = require('sequelize')
// 检查订单中的主体负责的信息，是否与其它订单或者已完成的备案重复

module.exports = class CheckPICMainPersonRepetitionInfo extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({ OrderNo } = {}) {
        try {
            let icpDatabase = this.db.get('icp')
            let OrderModel = getTableModel('t_order', icpDatabase)
            let ICPModel = getTableModel('t_icp', icpDatabase)
            // 获取订单的主体信息
            let orderInfo = await OrderModel.findOne({
                attributes: [
                    'PICMainMobile',
                    'PICMainLicenseId',
                    'EmergencyPhone',
                    'PICMainEmail',
                ],
                where: {
                    OrderNo,
                },
            })

            orderInfo = parseJSON(orderInfo)
            // 如果没有值，直接退出
            if (!orderInfo) {
                return this.cb(0, {
                    OrderList: [],
                    ICPList: [],
                })
            }
            // 找出4项中存在值的情况
            let selectJSON = []

            // _.forEach(orderInfo, function (value, key) {
            //     if (value) {
            //         selectJSON.push({ value, key })
            //     }
            // })
            // 作用域范围的问题，这样执行不了。只能脏一点了
            // {
            //     PICMainMobile: '',
            //         PICMainLicenseId: '32058219920229421X',
            //             EmergencyPhone: null,
            //                 PICMainEmail: ''
            // }

            // 生成查询条件
            if (orderInfo.PICMainMobile) {
                selectJSON.push({ PICMainMobile: orderInfo.PICMainMobile })
            }

            if (orderInfo.PICMainLicenseId) {
                selectJSON.push({
                    PICMainLicenseId: orderInfo.PICMainLicenseId,
                })
            }

            if (orderInfo.EmergencyPhone) {
                selectJSON.push({ EmergencyPhone: orderInfo.EmergencyPhone })
            }

            if (orderInfo.PICMainEmail) {
                selectJSON.push({ PICMainEmail: orderInfo.PICMainEmail })
            }

            console.log(selectJSON)

            let orderList = await OrderModel.findAll({
                attributes: [
                    'PICMainMobile',
                    'PICMainLicenseId',
                    'EmergencyPhone',
                    'PICMainEmail',
                    'OrderNo',
                ],
                where: {
                    [Op.or]: selectJSON,
                    IsDeleted: 0,
                },
            })

            let icpList = await ICPModel.findAll({
                attributes: [
                    'PICMainMobile',
                    'PICMainLicenseId',
                    'EmergencyPhone',
                    'PICMainEmail',
                    'ICPMainNo',
                ],
                where: {
                    [Op.or]: selectJSON,
                    Status: { [Op.ne]: 1 },
                },
            })
            // 取出
            orderList = parseJSON(orderList)
            icpList = parseJSON(icpList)
            console.log(icpList)

            let OrderList = {}
            let ICPList = {}
            // 结构体
            // OrderList = {
            //     "O202003172329170033": {
            //         'PICMainMobile': true, 'PICMainLicenseId': true, 'EmergencyPhone': true, 'PICMainEmail': true
            //     }
            // }

            if (orderList.length !== 0) {
                orderList.forEach((order) => {
                    OrderList[order.OrderNo] = {}
                    selectJSON.forEach((element) => {
                        _.forEach(element, function (value, key) {
                            if (order[key] === value) {
                                OrderList[order.OrderNo][key] = true
                            }
                        })
                    })
                })
            }

            if (icpList.length !== 0) {
                icpList.forEach((icp) => {
                    ICPList[icp.ICPMainNo] = {}
                    selectJSON.forEach((element) => {
                        _.forEach(element, function (value, key) {
                            if (icp[key] === value) {
                                ICPList[icp.ICPMainNo][key] = true
                            }
                        })
                    })
                })
            }

            delete OrderList[OrderNo]

            return this.cb(0, {
                OrderList,
                ICPList,
            })
        } catch (e) {
            console.log(e)
            if (e instanceof Error) {
                return this.eh({
                    err: e,
                    code: 67061,
                })
            }
            this.eh(e)
        }
    }
}
