/* 查看公司信息 通过EMail
 * @Date: 2023-05-23 16:34:27
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2023-05-23 16:34:39
 * @FilePath: /newicp/methods/admin/Common/GetCompanyInfoByEmail.js
 */
const Method = require('../../../libs/method')
const ucloudinternalapi = require('../../../libs/ucloudinternalapi')
const authFuns = require('../../../fns/authFuns')

module.exports = class GetCompanyInfoByEmail extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({ ChannelId, UserEmail } = {}) {
        let self = this
        try {
            let companyInfo, authInfo
            let redis = this.redis.get()
            try {
                // 获取公司信息
                companyInfo = await ucloudinternalapi({
                    Backend: 'UAccount',
                    Action: 'IGetUserInfo',
                    ChannelID: ChannelId,
                    UserEmail: UserEmail,
                })
            } catch (error) {
                throw new Error('获取公司信息，网络请求失败')
            }
            if (companyInfo.RetCode !== 0) {
                throw new Error('获取公司信息，结果异常')
            }

            if (companyInfo.DataSet.length !== 1) {
                throw new Error('获取公司信息，未得到有效公司')
            }

            try {
                // 获取实名信息
                authInfo = await authFuns.getAuthInfo(
                    companyInfo.DataSet[0].CompanyId,
                    ChannelId,
                )
            } catch (error) {
                throw new Error('获取实名信息，请求出错')
            }

            // 兼容旧格式定义
            return self.cb(0, {
                CompanyInfo: companyInfo.DataSet,
                AuthInfo: authInfo,
            })
        } catch (e) {
            self.err(e)
        }
    }
}
