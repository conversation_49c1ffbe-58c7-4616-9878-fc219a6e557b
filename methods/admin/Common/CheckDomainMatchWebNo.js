/*
 * @Date: 2023-06-08 15:09:18
 * @LastEditors: l<PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2023-06-12 14:19:09
 * @FilePath: /newicp/methods/admin/Common/CheckDomainMatchWebNo.js
 */

const Method = require('../../../libs/method')
const _ = require('lodash')
const { VerifyLogModel } = require('../../../models')
const { getDomainICPInGov } = require('../../../fns/aodun/DomainService')
const verify_type = require('../../../configs/common/verify_api_type.json')
const moment = require('moment')

module.exports = class CheckDomainMatchWebNo extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({
        OrderNo,
        __ssoUser,
        UUID,
        CompanyId,
        DomainMap, // {webno: domains }
        ForceSelect,
        Remark,
    } = {}) {
        let self = this
        let redis = this.redis.get()
        let currDate = moment().format('YYYY-MM-DD')
        try {
            let params = {
                UUID: UUID,
                Operator: __ssoUser,
                OrderNo,
                Remark,
                Multiplex: 0,
                Type: verify_type.CheckDomainWebNoMatch,
                CompanyId: CompanyId,
                Source: 'ICP',
                ForceSelect,
            }
            let dataContent = JSON.parse(JSON.stringify(params))
            params.DataContent = dataContent

            let IsMatch = true
            let Message = ''
            try {
                // 此处利用一下缓存 因为恒安的服务无法承受太大的req压力，一方面提高我们的处理速度 一方面降低对对方服务器的压力
                let len = await redis.hlen(`domain_webno_cache_${currDate}`)

                for (let ICPWebNo in DomainMap) {
                    let domains = DomainMap[ICPWebNo]
                    for (let domain of domains) {
                        let webno = await redis.hget(
                            `domain_webno_cache_${currDate}`,
                            domain
                        )

                        webno = ForceSelect ? null : webno
                        if (
                            (webno !== null && webno !== ICPWebNo) ||
                            webno === null
                        ) {
                            let res = await getDomainICPInGov(domain)
                            if (res.RetCode !== 0) {
                                Message += `${domain}${res.Message}；`
                                IsMatch = false
                            } else {
                                let resWebNo = res.ICPWebNo
                                if (resWebNo !== ICPWebNo.trim()) {
                                    IsMatch = false
                                    Message += `${domain} 提交备案号"${ICPWebNo}",真实备案号"${resWebNo}"；`
                                }
                                await redis.hset(
                                    `domain_webno_cache_${currDate}`,
                                    {
                                        [domain]: resWebNo,
                                    }
                                )
                                if (len === 0) {
                                    // 设置有效期
                                    await redis.expire(
                                        `domain_webno_cache_${currDate}`,
                                        60 * 60 * 24
                                    )
                                }
                            }
                        }
                    }
                }
            } catch (err) {
                Message = err.message
                IsMatch = false
            }
            Message = IsMatch ? '认证一致(通过)' : Message
            params.ReturnObject = { IsMatch, Message }
            params.Response = { IsMatch, Message }
            await VerifyLogModel.create(params)
            return this.cb(0, { IsMatch, Message })
        } catch (e) {
            if (e instanceof Error) {
                return self.eh({
                    err: e,
                    code: 30301,
                })
            }
            self.eh(e)
        }
    }
}
