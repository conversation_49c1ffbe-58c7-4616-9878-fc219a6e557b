/**
 * @file API ApplyRule,从Mongo取配置，发布到线上，同时线上的配置更新到Redis的历史记录中
 * <AUTHOR>
 * @return {} 返回符合条件的记录,格式如下
 * {
     "RetCode": 0
	}
 * 错误码:
 * "30301": "配置生效时遇到异常错误",
 * "30302": "线上配置格式化失败",
 * "30303": "写入Mongo失败",
 * "30304": "写入Mongo检查失败",
 * "30305": "写入Redis失败",
 */

const Method = require('../../../libs/method')
const ObjectId = require('mongodb').ObjectId
const moment = require('moment')
const { findAsync, insertManyAsync } = require('../../../fns/mongoFuns')

module.exports = class ApplyRule extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({ Id, __ssoUser } = {}) {
        let self = this
        let mongo = this.mongo.get('icp-config')
        let redis = this.redis.get()
        let ruleCollection = mongo.collection('rules')
        let now = moment().format('X')

        let result, onLineRule, changeObject, redisPromiseResult
        try {
            // 从Mongo中取到对应的记录
            result = await findAsync(
                ruleCollection,
                { _id: ObjectId(Id) },
                { sort: { UnixTime: 1 } }
            )

            // 如果取到的结果为不为1，则出错
            if (result.length !== 1) {
                return await Promise.reject(
                    new self.Err(Error('找不到Id对应的数据'), 30601)
                )
            }

            // 把其它Id置为enable faluse,这条打开,promise
            let runPromiseResult = await new Promise((resolve) => {
                // 执行更新，全部置为失效
                resolve(
                    ruleCollection.updateOne(
                        { Enable: true },
                        { $set: { Enable: false } }
                    )
                )
            })
                .then((updateResult) => {
                    console.log(updateResult)
                    if (!updateResult) {
                        return Promise.reject(
                            new self.Err(
                                Error('Mongo更新失败,关闭其它Enable状态'),
                                30603
                            )
                        )
                    }
                    // 执行更新，打开指定Id的Enable状态
                    return Promise.resolve(
                        ruleCollection.updateOne(
                            { _id: ObjectId(Id) },
                            { $set: { Enable: true } }
                        )
                    )
                })
                .catch((e) => {
                    return Promise.reject(new self.Err(e, 30302))
                })

            // 将配置发布到Redis
            try {
                redisPromiseResult = await new Promise((resolve, reject) => {
                    redis
                        .pipeline()
                        .get('rule', function (err, result) {
                            onLineRule = result
                        })
                        .set(
                            'rule',
                            JSON.stringify(result[0].Rule),
                            function (err) {
                                if (err) {
                                    reject(new this.Err(err, 30604))
                                }
                                resolve(true)
                            }
                        )
                        .exec(function (err, result) {
                            if (err) {
                                reject(new this.Err(err, 30004))
                            }
                            // 执行Mongo插入
                            changeObject = {
                                Rule: onLineRule,
                                UnixTime: parseInt(now),
                                Operator: __ssoUser,
                            }
                            changeObject.Name =
                                '线上自动保存' + moment().format('YYMMDDhhmmss')
                            // 写Mongo
                            resolve(
                                insertManyAsync(ruleCollection, [changeObject])
                            )
                        })
                })
            } catch (error) {
                return await Promise.reject(new self.Err(error, 30302))
            }

            console.log(redisPromiseResult)

            return this.cb(0)
        } catch (e) {
            console.log(e)
            if (e instanceof Error) {
                return self.eh({
                    err: e,
                    code: 30602,
                })
            }
            self.eh(e)
        }
    }
}
