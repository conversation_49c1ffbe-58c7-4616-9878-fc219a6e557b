/**
 * @file API TestConfig,从取出需要的订单与规则做匹配，看是否可以通过
 * <AUTHOR>
 * @return {Object} 返回符合条件的记录,格式如下
 * {
    "CheckResult":{
		xxx:xxx,
		Website:{
			XXX:XXX
		}
	},
    "RetCode": 0
	}
 * 错误码:
 * 30101: "获取Redis线上配置时网络异常",	
 * 30102: "获取Redis线上配置时结果解析异常",
 * 30103: "获取Redis线上配置时其它异常",
 */

const Method = require('../../../libs/method')
const { findAsync } = require('../../../fns/mongoFuns')
const _ = require('lodash')
const ObjectId = require('mongodb').ObjectId
const { rp } = require('../../../fns/icpAdminAPI')
const { getMainCheckMap, getWebsiteCheckMap } = require('general-verification')

module.exports = class TestConfig extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({ Id, OrderNo } = {}) {
        let self = this
        let mongo = this.mongo.get('icp-config')

        const validatorConfigCollection = mongo.collection('validator')
        let mongoConfig, order, mainParams, websiteParams
        mainParams = []
        websiteParams = []
        try {
            ;[mongoConfig, order] = await Promise.all([
                findAsync(validatorConfigCollection, { _id: ObjectId(Id) }, {}),
                GetOrderInfo(OrderNo),
            ])
            order = order.Order
            mongoConfig = mongoConfig[0].Config
            console.log(mongoConfig)
            _.mapKeys(mongoConfig.main, function (value, key) {
                mainParams.push(key)
            })

            _.mapKeys(mongoConfig.web, function (value, key) {
                websiteParams.push(key)
            })

            let mainCheckMap = await getMainCheckMap(mongoConfig)
            let webCheckMap = await getWebsiteCheckMap(mongoConfig)

            let mainErrResult = {}
            for (const i of mainParams) {
                const name = i
                const checkFuns = await mainCheckMap[name]
                const value = order[name]
                if (_.isFunction(checkFuns)) {
                    const checkResult = await checkFuns(value, name, order)
                    // console.log(checkResult, name)
                    if (checkResult !== true) {
                        mainErrResult[name] = checkResult
                    }
                }
            }

            let websiteErrResult = []
            for (let i in order.Website) {
                const website = order.Website[i]
                websiteErrResult[i] = {}
                for (let j in websiteParams) {
                    const name = websiteParams[j]

                    const checkFun = webCheckMap[name]
                    const value = website[name]
                    if (_.isFunction(checkFun)) {
                        const checkResult = await checkFun(
                            value,
                            website,
                            order
                        )
                        console.log(checkResult, name)
                        if (checkResult !== true) {
                            websiteErrResult[i][name] = checkResult
                        }
                    }
                }
            }
            console.log(mainErrResult, websiteErrResult)
            mainErrResult.Website = websiteErrResult
            return this.cb(0, { CheckResult: mainErrResult })
        } catch (e) {
            console.log(e)
            if (e instanceof Error) {
                return self.eh({
                    err: e,
                    code: 30201,
                })
            }
            self.eh(e)
        }
    }
}

/*
 *	调用接口
 *
 * @params params   OrderNo
 *
 */
function GetOrderInfo(OrderNo) {
    return rp({ OrderNo: OrderNo, Action: 'GetOrderInfo' })
}
