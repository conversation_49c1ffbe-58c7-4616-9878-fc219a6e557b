const Method = require('../../../libs/method')
const _ = require('lodash')
const { getTableModel, parseJSON, csvParse } = require('../../../fns/kits')
const { Op } = require('sequelize')
const producer = require('../../../libs/producer')
const moment = require('moment')
const { CONNECTSYNCVALUE, CONNECTSYNCCOMMENT } = require('../../../configs')
const now = parseInt(moment().format('X'))
const redis = require('../../../libs/redis').get()
module.exports = class UploadHenganDataToRedis extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({ File, FileName }) {
        let self = this
        let redis = this.redis.get()

        let domainList = []
        try {
            let InputInfo = await csvParse(File, '网站备案号')
            if (InputInfo.length === 0) {
                let err = new Error('没有数据')
                err.code = 35205
                throw err
            }
            let CompareHenganRedisIcpWebNoRedis = {}
            InputInfo.map((item) => {
                CompareHenganRedisIcpWebNoRedis[item['域名']] = item['网站备案号']
            })
            //取出文件的结果存储到redis中给接口调用
            await redis
                .pipeline()
                .hmset(
                    'CompareHenganRedisIcpWebNo',
                    CompareHenganRedisIcpWebNoRedis
                )
                .expire('CompareHenganRedisIcpWebNo', 24 * 60 * 60)
                .exec()
            //恒安数据和线上比对
            await producer.send({
                type: 'icp',
                topic: 'CompareHenganOnlineData',
                data: { BatchName: FileName },
            })
            //恒安数据和redis比对
            await producer.send({
                type: 'icp',
                topic: 'CompareHenganRedisIcpWebNo',
                data: { BatchName: FileName },
            })
            self.cb(0)
        } catch (e) {
            if (e instanceof Error) {
                return self.eh({
                    err: e,
                    code: 69200,
                })
            }
            self.eh(e)
        }
    }
}
