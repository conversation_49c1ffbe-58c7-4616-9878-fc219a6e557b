/*
 * @Author: william.qian <EMAIL>
 * @Date: 2022-02-16 11:11:48
 * @LastEditors: william.qian <EMAIL>
 * @LastEditTime: 2023-05-18 16:06:00
 * @FilePath: /newicp/methods/console/GetOrderPicture.js
 * @Description: 传入文件名，得到文件URL
 */
const Method = require('../../../libs/method')
const { signature } = require('../../../fns/uflieFuns')

module.exports = class GetPicture extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({ Picture } = {}) {
        let self = this

        try {
            if (!Picture) {
                throw new Error('Picture不能为空')
            }
            return this.cb(0, {
                PictureInfo:
                    global.CONFIG.ufile.target +
                    '/' +
                    Picture +
                    signature(Picture, Math.floor(Date.now() / 1000) + 600),
            })
        } catch (e) {
            if (e instanceof Error) {
                return self.eh({
                    err: e,
                    code: 32159,
                })
            }
            this.eh(e)
        }
    }
}
