/*
 * @Author: william.qian <EMAIL>
 * @Date: 2022-07-05 17:46:18
 * @LastEditors: william.qian <EMAIL>
 * @LastEditTime: 2023-09-14 18:30:02
 * @FilePath: /newicp/methods/admin/Common/HeathCheck.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
const Method = require('../../../libs/method')
const {
    sealDomain,
    getToken,
    unSealDomain,
    getSealStatus,
} = require('../../../fns/aodun/DomainService.js')
const {
    BulkBlockRecordModel,
    BulkBlockBatchModel,
    OrderModel,
    OrderWebModel,
    UninsertBatchModel,
    ICPModel,
    ICPWebModel,
    NoAccessNotifyModel,
    DomainWhiteListModel,
    WebCheckBatchModel,
    WebCheckRecordModel,
    CompanyNotifyModel,
    NoAccessBUNotifyModel,
    UninsertBatchRecordModel,
} = require('../../../models')

// 增加与傲盾的同步解封
// 为兼容前端，不改传参
module.exports = class HeathCheck extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({ Sync } = {}) {
        try {
            // 同步Model

            if (Sync) {
                // BulkBlockBatchModel.sync({ alter: true })
                // BulkBlockRecordModel.sync({ alter: true })
                // UninsertBatchModel.sync({ alter: true })
                // NoAccessNotifyModel.sync({ alter: true })
                // CompanyNotifyModel.sync({ alter: true })
                // NoAccessBUNotifyModel.sync({ alter: true })
                // N await WebCheckBatchModel.sync({ alter: true })
                // N await WebCheckBatchModel.sync({ alter: true })
                await OrderModel.sync({ alter: true })
                await OrderWebModel.sync({ alter: true })
            }

            return this.cb(0)
        } catch (e) {
            console.log(e)
            if (e instanceof Error) {
                return self.eh({
                    err: e,
                    code: 30801,
                })
            }
            self.eh(e)
        }
    }
}
