/**
 * @file A检查域名是否在新网、时代互连接入
 * <AUTHOR>
 * 入参 Domain 域名列表，不建议多个， ForceBackToSource 是否回源，布尔
 * 默认查询缓存后如存在未检查域名继续检查接口，查到有接入记录后更新到Redis中的Map。查时代互联时如果目前时代互联的Key还有效不查时代互联
 * 如果是强制模式，则不读缓存，查接口。
 */
const Method = require('../../../libs/method')
const { getAllDomainInNOWCN } = require('../../../fns/connect_check_fns/nowCN')
const {
    getDomainICPInfoInXinnet,
} = require('../../../fns/connect_check_fns/xinNet')
const _ = require('lodash')
const punycode = require('punycode')

module.exports = class CheckDomainInOtherISP extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({ Domain, ForceBackToSource } = {}) {
        let self = this
        let redis = this.redis.get()

        let nowcnInfo,
            xinnetInfo,
            domainNeedDel = [],
            result = {}

        const Domains = Array.isArray(Domain) ? Domain : [Domain]

        // 后续看是否限制最大长度
        if (Domains.length === 0) {
            return self.eh({
                code: 32604,
            })
        }

        //初始化 返回的数据
        Domains.forEach((domain) => {
            if (result[domain] === undefined) {
                result[domain] = {}
            }
            result[domain] = {
                IsICPInOtherISP: false,
                MetaInfo: {
                    IsICPInNowcn: false,
                    IsICPInXinnet: false,
                    IsICPInCache: false,
                    ICPWebNo: '',
                },
            }
        })

        let existNowcnInfo = {}, // {domain: icpwebno,domain: icpwebno}
            existNowcnDomain = [],
            needSelectnowcn = true,
            domainNeedCheck = [] // 一层一层过滤后，剩下需要查询的Domian

        try {
            // 如果不强制回源，则从缓存中取记录，同时如果新网的更新标记还在有效期则不强刷新网
            if (ForceBackToSource !== true) {
                try {
                    let domainInCache = [],
                        keyIsExists
                    // 管道的方式查是否在Map中，缓存一层，同时

                    let selectPromise = await redis
                        .pipeline()
                        // 检查域名是否在记录中
                        .hmget('domain_icp_in_other_map', Domain)
                        // 检查时代互联的检查标记
                        .exists('select_nowcn_flag')
                        .exec()
                    // 缓存中的数据做匹配[ [ null, [ '浙ICP备08113678号-2', null ] ], [ null, 1 ] ]

                    // 取值
                    domainInCache = selectPromise[0]
                    keyIsExists = selectPromise[1]
                    domainInCache = domainInCache[1]
                    console.log(keyIsExists)
                    if (keyIsExists[1] === 1) {
                        // 如为1说明目前还有效果，不更新
                        needSelectnowcn = false
                    }

                    // 根据下标来赋值结果
                    for (let index = 0; index < domainInCache.length; index++) {
                        if (domainInCache[index] !== null) {
                            // 如果此Key不是null说明是命中的
                            result[Domain[index]].IsICPInOtherISP = true
                            result[Domain[index]].MetaInfo.ICPWebNo =
                                domainInCache[index]
                            result[Domain[index]].MetaInfo.IsICPInCache = true
                        } else {
                            // 从缓存中查不到的，侧的待查询信息中
                            domainNeedCheck.push(Domain[index])
                        }
                    }
                } catch (error) {
                    return await Promise.reject(new self.Err(error, 32601))
                }
            } else {
                // 如果是强制回源
                domainNeedCheck = Domain
            }

            if (needSelectnowcn === true && domainNeedCheck.length !== 0) {
                try {
                    // 时代互联查询 并处理数据
                    nowcnInfo = await getAllDomainInNOWCNAndSaveToRedis(redis)
                    domainNeedCheck.forEach((domain) => {
                        //tmp == [{domain: ICPWebNo}]
                        const tmp = _.filter(nowcnInfo, domain)
                        if (tmp?.length > 0) {
                            result[domain].IsICPInOtherISP = true
                            result[domain].MetaInfo.IsICPInNowcn = true
                            result[domain].MetaInfo.ICPWebNo = Object.values(
                                tmp[0]
                            )[0]
                        }
                        Object.assign(existNowcnInfo, ...tmp)
                    })

                    existNowcnDomain = Object.keys(existNowcnInfo)
                    // 不在时代互联的，到新网去查
                    domainNeedCheck = _.difference(
                        domainNeedCheck,
                        existNowcnDomain
                    )
                } catch (error) {
                    return await Promise.reject(new self.Err(error, 32601))
                }
            }
            let domainAllTrue = true

            if (domainNeedCheck.length !== 0) {
                try {
                    // 新网查询，并处理数据
                    const xinnetInfoArr = []
                    domainNeedCheck.forEach(async (domain) => {
                        // 对中文进行转码

                        xinnetInfoArr.push(
                            getDomainICPInfoInXinnetAndSaveToRedis(
                                redis,
                                domain
                            )
                            // getDomainICPInfoInXinnet(punyDomain || domain)
                        )
                    })

                    xinnetInfo = await Promise.all(xinnetInfoArr)

                    for (let i = 0; i < xinnetInfo.length; i++) {
                        // 新网 数据处理
                        if (xinnetInfo[i].localRecord === true) {
                            result[domainNeedCheck[i]].IsICPInOtherISP = true
                            result[
                                domainNeedCheck[i]
                            ].MetaInfo.IsICPInXinnet = true
                            result[domainNeedCheck[i]].MetaInfo.ICPWebNo =
                                xinnetInfo[i].websiteCode
                        } else {
                            domainAllTrue = false
                            domainNeedDel.push(domainNeedCheck[i])
                        }
                    }
                } catch (error) {
                    return await Promise.reject(new self.Err(error, 32602))
                }
            }

            this.cb(0, { domainInfo: result, domainAllTrue })

            if (ForceBackToSource === true && domainNeedDel.length !== 0) {
                // 如果是强制回源时发现没有的，执行一次map中hash的删除
                await redis.hdel('domain_icp_in_other_map', domainNeedDel)
            }
        } catch (e) {
            if (e instanceof Error) {
                return self.eh({
                    err: e,
                    code: 30301,
                })
            }
            self.eh(e)
        }
    }
}

// 在原查询接口上，附加Redis批量保存
function getAllDomainInNOWCNAndSaveToRedis(redis) {
    let nowcnInfo
    return getAllDomainInNOWCN()
        .then((row) => {
            nowcnInfo = row
            let allDomainInNowCN = {}
            row.forEach((element) => {
                _.forEach(element, function (value, key) {
                    allDomainInNowCN[key] = value
                })
            })

            return redis
                .pipeline()
                .hmset('domain_icp_in_other_map', allDomainInNowCN)
                .set('select_nowcn_flag', row.length)
                .expire('select_nowcn_flag', 60 * 60)
                .exec()
        })
        .catch(() => {
            return Promise.resolve(nowcnInfo)
        })
}

// 单个域名查询如果在新网，则更新到Redis中
function getDomainICPInfoInXinnetAndSaveToRedis(redis, domain) {
    var pattern = new RegExp('[\u4E00-\u9FA5]+')
    let result

    return getDomainICPInfoInXinnet(
        pattern.test(domain) ? punycode.toASCII(domain) : domain
    )
        .then((row) => {
            result = row

            if (row.localRecord === true) {
                // 这么写很冗余，但不这么写会有问题，
                let setObject = {}
                setObject[result.domain] = row.websiteCode
                return redis.hmset('domain_icp_in_other_map', setObject)
            }
        })
        .then(() => {
            return Promise.resolve(result)
        })
}
