/**
 * @file API ApplyConfig,从Mongo取配置，发布到线上，同时线上的配置更新到Redis的历史记录中
 * <AUTHOR>
 * @return {} 返回符合条件的记录,格式如下
 * {
     "RetCode": 0
	}
 * 错误码:
 * "30301": "配置生效时遇到异常错误",
 * "30302": "线上配置格式化失败",
 * "30303": "写入Mongo失败",
 * "30304": "写入Mongo检查失败",
 * "30305": "写入Redis失败",
 */

const Method = require('../../../libs/method')
const { findAsync, insertManyAsync } = require('../../../fns/mongoFuns')
const ObjectId = require('mongodb').ObjectId
const { getKeyAsync } = require('../../../fns/redisFuns')
const { mongoResultCheck } = require('../../../fns/kits')
const moment = require('moment')
const stringRandom = require('string-random')

module.exports = class ApplyConfig extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({ Id } = {}) {
        let self = this
        let mongo = this.mongo.get('icp-config')
        let redis = this.redis.get()

        const validatorConfigCollection = mongo.collection('validator')
        let now = moment()

        let redisConfig, mongoConfig, monGoRecordName, insertMongoResult
        try {
            // 在API处做了核验，不验Id了
            // 双线操作，取redis当前配置，取传入的IdMongo的数据
            ;[redisConfig, mongoConfig] = await Promise.all([
                getKeyAsync(redis, 'onlineConfig'),
                findAsync(validatorConfigCollection, { _id: ObjectId(Id) }, {}),
            ])

            try {
                redisConfig = JSON.parse(redisConfig)
            } catch (error) {
                return await Promise.reject(new self.Err(error, 30302))
            }
            // 此处理可以考虑做配置做下验证

            monGoRecordName =
                moment().format('YYYYMMDD') +
                stringRandom(3, { numbers: false })

            // 先写Mongo
            try {
                now = now.format('X')
                insertMongoResult = await insertManyAsync(
                    validatorConfigCollection,
                    [
                        {
                            Config: redisConfig,
                            Name: monGoRecordName,
                            UnixTime: parseInt(now),
                        },
                    ]
                )
            } catch (error) {
                return await Promise.reject(new self.Err(error, 30303))
            }

            // 如果没有插入成功
            if (insertMongoResult.insertedCount !== 1) {
                return await Promise.reject(
                    new self.Err(Error('数据插入失败'), 30304)
                )
            }
            //  如果成功，执行Redis更新
            await new Promise((resolve, reject) => {
                redis.set(
                    'onlineConfig',
                    JSON.stringify(mongoConfig),
                    function (err) {
                        if (err) {
                            reject(new this.Err(err, 30305))
                        }
                        resolve(true)
                    }
                )
            })

            return this.cb(0)
        } catch (e) {
            if (e instanceof Error) {
                return self.eh({
                    err: e,
                    code: 30301,
                })
            }
            self.eh(e)
        }
    }
}
