# 代理商与配额相关API


标签： 代理商 配额

---

### 确认客户是否有额外配额 确定客户是否是代理商 GetProxyCompanyList

确定是否为代理商，传入CompanyType为1，如数组长度不为0，表示其为代理商

确定配额数量，不需要传入参数，取第一个元素中的Quota为配额数


#### 请求参数

| 字段          | 类型     | 必填  | 描述                                   |
|-------------|--------|-----|--------------------------------------|
| Action      | String | Yes | GetProxyCompanyList                  |
| CompanyId   | Int    | Yes | 必填，但不需要前端传入，网关会传的                    |
| CompanyType | Int    | No  | 非必填，由前端传入，如想确定是否是代理商，传入1。如查知道配额情况，不传 |

#### 返回参数

| 字段          | 类型            | 描述                                  |
|-------------|---------------|-------------------------------------|
| Action      | String        | GetProxyCompanyList |
| CompanyList | Array[Object] | 如目的是确定是否代理商，判断长度是否为1便可              |
| RetCode     | Int           | 返回码                                 |


### RequestParams

```JSON
    {
    "CompanyType":1
    "Action": "GetProxyCompanyList"
   }
```

### ResponseParams

```JSON

{
  "CompanyList" : [
    {
      "Note" : "代理商账号",
      "Quota" : 101,
      "Id" : 1,
      "CompanyId" : 34278,
      "UpdateTime" : 1650247359,
      "CompanyType" : 1,
      "CompanyName" : "广州凡科互联网科技股份有限公司",
      "WebsiteQuota" : 200,
      "CreateTime" : 1553148267,
      "Operator" : "wei.yu"
    }
  ],
  "Action" : "GetProxyCompanyListResponse",
  "Count" : 1,
  "RetCode" : 0
}

```
