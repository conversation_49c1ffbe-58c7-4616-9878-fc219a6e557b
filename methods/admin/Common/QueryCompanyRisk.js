/*
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2023-11-02 14:51:54
 * @LastEditors: <NAME_EMAIL>
 * @LastEditTime: 2023-11-02 18:26:23
 * @FilePath: /newicp/methods/admin/Common/QueryCompanyRisk.js
 * @Description: 根据CompanyID查询用户关联公司，是否存在于黑名单
 * 1. 确定是否是代理商，代理商量大（建议写死或者通过代理商表得到 t_proxy_company_list company_type=1）
 * 2、确定是否有关联公司（通过合规接口查询HEGUI.GetAssociatedCompany  https://uauth.ucloudadmin.com/gateway）
 * 3、关联公司是否在黑名单（备案系统，黑名单表 t_company_block_list）
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved.
 */

const Method = require('../../../libs/method')
const axios = require('../../../libs/axiosApi')
const { CompanyBlockListModel } = require('../../../models')
const { parseJSON } = require('../../../fns/kits')
const { Op } = require('sequelize')

/**
 * 根据CompanyId查询是否关联黑名单账户(关联公司)
 */
module.exports = class QueryCompanyRisk extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({ CompanyId } = {}) {
        let self = this

        const proxy_companies = [
            233, 396, 20967, 34278, 56007947, 62935358, 12189, 28258, 12979,
            65949138, 20377, 39492, 31662, 562, 13968, 55974045,
        ]

        if (proxy_companies.indexOf(CompanyId) > -1) {
            console.log('company_id in proxy companies.')
            return this.cb(0, {
                IfRisk: 0,
                CompanyId: CompanyId,
                DataSet: [],
            })
        }

        try {
            let options = {
                method: 'POST',
                url: global.CONFIG.ucloudApi,
                headers: {
                    'Content-Type': 'application/json',
                },
                data: {
                    Action: 'HEGUI.GetAssociatedCompany',
                    PublicKey: global.CONFIG.internalApiKeys.publicKey,
                    CompanyId: [CompanyId],
                },
                json: true,
            }

            console.log(options)

            let result = await axios(options)
            if (result.status !== 200 || result.data.RetCode !== 0) {
                let err = new Error(
                    `查询HEGUI关联账号失败，公司Id: ${CompanyId}`
                )
                throw err
            }

            let a_data = result.data.DataSet
            let a_companyids = []

            if (a_data.length > 0) {
                console.log('存在关联公司，提取关联公司 a_companyids')
                a_companyids = a_data.map((c) =>
                    c['AssociatedCompanyId'].toString()
                )
            }
            console.log(a_companyids)
            a_companyids.push(CompanyId.toString()) // 追加该用户本身

            let searchArr = {}
            searchArr.CompanyId = { [Op.in]: a_companyids }

            let rows = await CompanyBlockListModel.findAll({
                where: searchArr,
                order: [['Id', 'DESC']],
            })
            rows = parseJSON(rows)

            let responseData = {
                IfRisk: rows.length > 0 ? 1 : 0,
                SourceCompanyId: CompanyId,
                DataSet: rows,
            }

            return this.cb(0, responseData)
        } catch (e) {
            self.err(e)
        }
    }
}
