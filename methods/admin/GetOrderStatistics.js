

 /**
 * @file API GetOrderStatistics,获取订单统计数据
 * <AUTHOR>
 * @return {Object} 返回订单统计数据
 * 错误码:
 * "11000": "数据库查询失败",
 * "11001": "数据库插入失败",
 * "11002": "数据库更新失败",
 * "11003": "服务器内部错误",
 */

 const Method = require('../../libs/method')
 const { OrderModel, OrderStatusEnum } = require('../../models')
 const { Op } = require('sequelize')
 const moment = require('moment')
 const _ = require('lodash')
 
 module.exports = class GetOrderStatistics extends Method {
     constructor(cb) {
         super(cb)
     }
 
     async exec({ StartDate, EndDate, DateType, OnlyPass = false } = {}) {
         let self = this
         try {
             // 验证参数
             if (!StartDate || !EndDate || !DateType) {
                 return self.cb(12001, { Message: '缺少必要参数' })
             }
 
             // 验证日期格式
             if (!moment(StartDate).isValid() || !moment(EndDate).isValid()) {
                 return self.cb(12001, { Message: '日期格式错误' })
             }
 
             // 验证时间聚合维度
             const validDateTypes = ['years', 'months', 'weeks', 'days']
             if (!validDateTypes.includes(DateType)) {
                 return self.cb(12001, { Message: '时间聚合维度错误' })
             }
 
             // ------- 使用 order_no 推断 Id 边界 -------
             const fmt = (d)=> moment(d).format('YYYYMMDD')
             const startNoPrefix = 'O' + fmt(StartDate)
             const endNoPrefix   = 'O' + fmt(EndDate)

             // 构造最小/最大 order_no 字符串（00.. / 99..）
             const startNo = `${startNoPrefix}000000000000`   // OYYYYMMDD 00:00:00 000000
             const endNo   = `${endNoPrefix}235959999999`     // OYYYYMMDD 23:59:59 999999

             // 获取 startId：order_no < startNo 的最大 id
             const startRow = await OrderModel.findOne({
                 where: { OrderNo: { [Op.lt]: startNo } },
                 order: [['Id', 'DESC']],
                 attributes: ['Id'],
                 raw: true,
             })
             const startId = startRow ? startRow.Id : 0
 
             // 获取 endId：order_no <= endNo 的最大 id
             const endRow = await OrderModel.findOne({
                 where: { OrderNo: { [Op.lte]: endNo } },
                 order: [['Id', 'DESC']],
                 attributes: ['Id'],
                 raw: true,
             })
             const endId = endRow ? endRow.Id : 0
 
             // 构建查询条件（用 id 区间 + order_no 范围）
             const whereCondition = {
                 Id: { [Op.gt]: startId, [Op.lte]: endId },
                 OrderNo: { [Op.between]: [startNo, endNo] },
             }
 
             if (OnlyPass) {
                 whereCondition.Status = OrderStatusEnum.GovAuditPass // 12
             }
 
             const ordersRaw = await OrderModel.findAll({
                 where: whereCondition,
                 attributes: ['Id', 'CompanyId', 'Status', 'Type', 'OrderNo'],
                 raw: true,
             })

             // 将 OrderNo 解析为时间戳，补充 CreateTime 字段供后续分组
             const orders = ordersRaw.map((o)=>{
                const ts = this.orderNoToTs(o.OrderNo)
                return { ...o, CreateTime: ts }
             })
 
             // 按时间维度分组统计
             const groupedData = this.groupByTimeRange(orders, DateType, StartDate, EndDate)
 
             // 计算各项指标
             const result = this.calculateStatistics(groupedData)
 
             return self.cb(0, { Data: result })
         } catch (e) {
             console.error('GetOrderStatistics error:', e)
             return self.cb(11000, { Message: '数据库查询失败' })
         }
     }
 
     /**
      * 获取开始时间戳
      */
     getStartTimestamp(date, dateType) {
         const momentDate = moment(date)
         
         switch (dateType) {
             case 'years':
                 return momentDate.startOf('year').unix()
             case 'months':
                 return momentDate.startOf('month').unix()
             case 'weeks':
                 return momentDate.startOf('day').unix()
             case 'days':
                 return momentDate.startOf('day').unix()
             default:
                 return momentDate.startOf('day').unix()
         }
     }
 
     /**
      * 获取结束时间戳
      */
     getEndTimestamp(date, dateType) {
         const momentDate = moment(date)
         
         switch (dateType) {
             case 'years':
                 return momentDate.endOf('year').unix()
             case 'months':
                 return momentDate.endOf('month').unix()
             case 'weeks':
                 return momentDate.endOf('day').unix()
             case 'days':
                 return momentDate.endOf('day').unix()
             default:
                 return momentDate.endOf('day').unix()
         }
     }
 
     /**
      * 按时间维度分组数据
      */
     groupByTimeRange(orders, dateType, startDate, endDate) {
         const grouped = {}
         const start = moment(startDate)
         const end = moment(endDate)
 
         // 生成时间范围
         const timeRanges = this.generateTimeRanges(start, end, dateType)
 
         // 初始化分组
         timeRanges.forEach(range => {
             grouped[range] = []
         })
 
         // 将订单按时间范围分组 - 使用大驼峰字段名
         orders.forEach(order => {
             const orderDate = moment.unix(order.CreateTime)
             const timeRange = this.getTimeRange(orderDate, dateType)
             
             if (grouped[timeRange]) {
                 grouped[timeRange].push(order)
             }
         })
 
         return grouped
     }
 
     /**
      * 生成时间范围
      */
     generateTimeRanges(start, end, dateType) {
         const ranges = []
         let current = start.clone()
 
         while (current.isSameOrBefore(end)) {
             ranges.push(this.getTimeRange(current, dateType))
             
             switch (dateType) {
                 case 'years':
                     current.add(1, 'year')
                     break
                 case 'months':
                     current.add(1, 'month')
                     break
                 case 'weeks':
                     current.add(1, 'week')
                     break
                 case 'days':
                     current.add(1, 'day')
                     break
             }
         }
 
         return ranges
     }
 
     /**
      * 获取时间范围标识
      */
     getTimeRange(date, dateType) {
         switch (dateType) {
             case 'years':
                 return date.format('YYYY')
             case 'months':
                 return date.format('YYYY-MM')
             case 'weeks':
                 return date.format('YYYY-[W]WW')
             case 'days':
                 return date.format('YYYY-MM-DD')
             default:
                 return date.format('YYYY-MM')
         }
     }
 
     /**
      * 计算统计数据
      */
     calculateStatistics(groupedData) {
         const result = []
 
         Object.keys(groupedData).forEach(timeRange => {
             const orders = groupedData[timeRange]
             
             // 基础统计
             const totalCount = orders.length
             const fankeOrders = orders.filter(order => order.CompanyId === 34278)
             const directOrders = orders.filter(order => order.CompanyId !== 34278)
 
             // 凡科统计
             const fankeTotal = fankeOrders.length
             const fankeApproved = fankeOrders.filter(order => order.Status === OrderStatusEnum.GovAuditPass).length
             const fankeNewOrders = fankeOrders.filter(order => [1, 2, 3].includes(order.Type)).length
             const fankeNewOrderRatio = fankeTotal > 0 ? fankeNewOrders / fankeTotal : 0
             const fankeApprovalRate = fankeTotal > 0 ? fankeApproved / fankeTotal : 0
 
             // 直客统计
             const directTotal = directOrders.length
             const directApproved = directOrders.filter(order => order.Status === OrderStatusEnum.GovAuditPass).length
             const directNewOrders = directOrders.filter(order => [1, 2, 3].includes(order.Type)).length
             const directNewOrderRatio = directTotal > 0 ? directNewOrders / directTotal : 0
             const directApprovalRate = directTotal > 0 ? directApproved / directTotal : 0
 
             // 凡科占比
             const fankeRatio = totalCount > 0 ? fankeTotal / totalCount : 0
 
             result.push({
                 TimeRange: timeRange,
                 TotalCount: totalCount,
                 Fanke: {
                     TotalCount: fankeTotal,
                     ApprovedCount: fankeApproved,
                     NewOrderRatio: parseFloat(fankeNewOrderRatio.toFixed(3)),
                     ApprovalRate: parseFloat(fankeApprovalRate.toFixed(3))
                 },
                 Direct: {
                     TotalCount: directTotal,
                     ApprovedCount: directApproved,
                     NewOrderRatio: parseFloat(directNewOrderRatio.toFixed(3)),
                     ApprovalRate: parseFloat(directApprovalRate.toFixed(3))
                 },
                 FankeRatio: parseFloat(fankeRatio.toFixed(3))
             })
         })
 
         return result
     }

    /**
     * 从 OrderNo 提取时间戳
     * OYYYYMMDDhhmmssXXXXXXXX -> YYYY-MM-DD hh:mm:ss -> unix
     */
    orderNoToTs(orderNo){
        // 去掉开头 O 并截取 14 位日期时间
        const dtStr = orderNo.slice(1,15) // YYYYMMDDhhmmss
        const m = moment(dtStr, 'YYYYMMDDHHmmss')
        return m.isValid()? m.unix(): 0
    }
 } 
 