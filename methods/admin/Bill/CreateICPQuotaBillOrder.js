/**
 * @file API 传入公司Id与计费周期，接口会统计期间内的有效备案订单，生成订单明细。
 * 与凡科备案订单不同的是，这是手动触发，同时账户侧的扣费Id也不同。
 * <AUTHOR>
 * @return {} 返回符合条件的记录,格式如下
 */

const Method = require('../../../libs/method')
const ObjectId = require('mongodb').ObjectId
const { Op } = require('sequelize')
const _ = require('lodash')
const { requestAsync } = require('../../../fns/ucloudInternalApi')
const { lock } = require('../../../fns/lockFuns')
const { parseJSON } = require('../../../fns/kits')
const { OrderHistoryModel, OrderModel } = require('../../../models')
const moment = require('moment')
const billMap = require('../../../configs/common/bill_map.json')

module.exports = class CreateICPQuotaBillOrder extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({ CompanyId, StartTime, Remark, EndTime } = {}) {
        let self = this
        let mongo = this.mongo.get('icp')
        const billDetailsCollection = mongo.collection('t_bill_details')
        const billListCollection = mongo.collection('t_bill_list')

        try {
            let companyInfo, organizerInfo, billMonth
            let now = parseInt(moment().format('X'))
            const billInfo = _.find(billMap, {
                ProductSmallClassId: 2130008,
            })

            // 确定项目Id是否在公司Id的名下
            try {
                ;[companyInfo, organizerInfo] = await Promise.all([
                    requestAsync(
                        {
                            category: 'UAccount',
                            method: 'POST',
                            action: 'UAccount.IGetCompanyInfo',
                        },
                        {
                            Action: 'IGetCompanyInfo',
                            Backend: 'UAccount',
                            CompanyId,
                        }
                    ),
                    requestAsync(
                        {
                            category: 'UAccount',
                            method: 'POST',
                            action: 'UAccount.IGetProjectList',
                        },
                        {
                            Action: 'IGetProjectList',
                            Backend: 'UAccount',
                            CompanyId,
                        }
                    ),
                ])

                // organizerInfo = await GetCompanyInfo({ CompanyId })
            } catch (error) {
                // 如果出错，说明有锁
                await Promise.reject(
                    new self.Err(
                        Error('确定项目Id与公司Id关联时出错' + error.message),
                        35309
                    )
                )
            }

            if (
                companyInfo.RetCode !== 0 ||
                !companyInfo.DataSet ||
                companyInfo.DataSet.length === 0 ||
                organizerInfo.RetCode !== 0 ||
                !organizerInfo.ProjectSet ||
                organizerInfo.ProjectSet.length === 0
            ) {
                throw new self.Err(
                    Error(
                        '确定项目Id与公司Id关联信息时,信息返回出错' +
                            companyInfo.Message
                    ),
                    35310
                )
            }

            // 取项目信息
            organizerInfo = _.filter(organizerInfo.ProjectSet, {
                Deleted: 0,
            })[0]
            companyInfo = companyInfo.DataSet[0]
            console.log(organizerInfo, companyInfo)

            // 公司信息没问题，开始查询订单信息
            let lastMonthStartTime = moment(StartTime, 'X')
            let lastMonthEndTime = parseInt(moment(EndTime, 'X').format('X'))
            billMonth = lastMonthStartTime.format('YYYY-MM')
            lastMonthStartTime = parseInt(lastMonthStartTime.format('X'))

            // 还是分开查询，先查日志
            let orderMap = await OrderHistoryModel.findAll({
                attributes: ['OrderNo', 'OperationTime'],
                group: 'OrderNo',
                where: {
                    status: 11, // 提交到管局阶段
                    operation_time: {
                        [Op.between]: [
                            lastMonthStartTime * 1000,
                            lastMonthEndTime * 1000,
                        ],
                    },
                },
            })
            orderMap = parseJSON(orderMap)
            let orderList = _.map(orderMap, 'OrderNo')
            orderList = _.uniq(orderList)
            console.log(orderList, 133)
            if (!orderList || orderList.length === 0) {
                return self.cb(36057)
            }

            // 后确定类型,过滤出需要计费的类型
            orderList = await OrderModel.findAll({
                attributes: ['OrderNo'],
                where: {
                    Type: {
                        [Op.in]: [1, 2, 3],
                    },
                    CompanyId,
                    OrderNo: {
                        [Op.in]: orderList,
                    },
                },
            })

            if (!orderList || orderList.length === 0) {
                return self.cb(36057)
            }
            orderList = parseJSON(orderList)
            orderList = _.map(orderList, 'OrderNo')
            // orderMap中，用orderList过滤出有效的订单信息
            orderMap = _.filter(orderMap, function (o) {
                return orderList.indexOf(o.OrderNo) !== -1
            })
            // 到此，得到需要计费的订单列表t_bill_list t_bill_details
            // 查询订单是否，收费过ObjectId(Id)
            let payedList = await billDetailsCollection
                .find(
                    {
                        UId: {
                            $in: orderList,
                        },
                    },
                    { projection: { UId: 1 } }
                )
                .toArray()
            // 保存账单信息
            payedList = _.map(payedList, 'UId')
            // 如果已经支付过，过滤掉
            orderMap = _.filter(orderMap, function (o) {
                return payedList.indexOf(o.OrderNo) === -1
            })
            // 批次情况
            if (orderMap.length === 0) {
                return this.cb(0)
            }
            // 插入数据
            let billInsertInfo = await billListCollection.insertOne({
                BillType: billInfo.ProductSmallClassId,
                BillNameCN: [
                    billInfo.ProductSmallClassCN,
                    billMonth,
                    CompanyId,
                    companyInfo.CompanyName,
                ].join('-'), // 增加公司名，方便区分
                BillName: 'hegui-' + billInfo.ProductSmallClass + '-' + now,
                Type: billInfo.Type,
                CompanyId,
                OrganizationId: organizerInfo.OrganizationId,
                Count: orderMap.length,
                StartTime,
                EndTime,
                CreateTime: now,
                UpdateTime: now,
                TotalAmount:
                    Math.floor(billInfo.Price * orderMap.length * 100) / 100,
                Remark,
                WhetherToPush: 0,
            })

            orderMap = orderMap.map((row) => {
                row.BillId = billInsertInfo.insertedId
                row.BillName = billInfo.ProductSmallClassCN + billMonth
                row.CompanyId = CompanyId
                row.Count = 1
                row.TriggerTime = parseInt(
                    moment(row.OperationTime).format('X')
                )
                row.UId = row.OrderNo
                delete row.OrderNo
                delete row.OperationTime
                return row
            })

            await billDetailsCollection.insertMany(orderMap)

            return this.cb(0)
        } catch (e) {
            console.log(e)
            if (e instanceof Error) {
                return self.eh({
                    err: e,
                    code: 36058,
                })
            }
            self.eh(e)
        }
    }
}
