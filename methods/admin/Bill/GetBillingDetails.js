/**
 * @file API 根据参数从Mongo中获取指定的账单相关的数据
 * <AUTHOR>
 * @return {} 返回符合条件的记录,格式如下
 */

const Method = require('../../../libs/method')
const ObjectId = require('mongodb').ObjectId
const _ = require('lodash')

module.exports = class GetBillingDetails extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({ Id, Offset, Limit } = {}) {
        let self = this
        let mongo = this.mongo.get('icp')
        const billDetailsCollection = mongo.collection('t_bill_details')
        let billList, Count

        try {
            // 查询Mongo，确定记录是否存在,确定是否推送过订单

            ;[billList, Count] = await Promise.all([
                billDetailsCollection
                    .find(
                        { BillId: ObjectId(Id) },
                        {
                            projection: {
                                Count: 1,
                                BillName: 1,
                                TriggerTime: 1,
                                UId: '_id',
                            },
                        }
                    )
                    .sort({ CreateTime: -1 })
                    .skip(Offset || 0)
                    .limit(Limit || 20)
                    .toArray(),
                billDetailsCollection.find({ BillId: ObjectId(Id) }).count(),
            ])

            billList = billList.map((row) => {
                row.Id = row._id
                // 为空数据赋值
                // if (!row.Remark) {
                //     row.Remark = ''
                // }
                // if (!row.Amount) {
                //     row.Amount = 0
                // }
                delete row._id
                return row
            })

            return this.cb(0, { BillingList: billList, Count })
        } catch (e) {
            if (e instanceof Error) {
                return self.eh({
                    err: e,
                    code: 35313,
                })
            }
            self.eh(e)
        }
    }
}
