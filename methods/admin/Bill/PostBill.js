/**
 * @file  API PostBill,使用Id从Mongo中取订单相关的信息，生成参数请求账户的账号推送接口
 * <AUTHOR>
 * @return {} 返回符合条件的记录,格式如下
 */

const Method = require('../../../libs/method')
const ObjectId = require('mongodb').ObjectId
const _ = require('lodash')
const Redlock = require('redlock')
const { lock, unLock } = require('../../../fns/lockFuns')
const { requestAsync } = require('../../../fns/ucloudInternalApi')
const moment = require('moment')

module.exports = class PostBill extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({ Id, __ssoUser } = {}) {
        let self = this
        let mongo = this.mongo.get('icp')
        let redis = this.redis.get()
        const producer = this.producer
        const redlock = new Redlock([redis])
        const billListCollection = mongo.collection('t_bill_list')
        let lockInstance

        try {
            // 一致性锁，防止重复推送
            try {
                lockInstance = await lock(redlock, Id, 20 * 1000)
            } catch (error) {
                // 如果出错，说明有锁
                await Promise.reject(
                    new self.Err(
                        Error(
                            '推送账单时，Id正常推送计费，请刷新' + error.message
                        ),
                        35306
                    )
                )
            }
            // 查询Mongo，确定记录是否存在,确定是否推送过订单
            let billResult = await billListCollection
                .find(
                    { _id: ObjectId(Id), WhetherToPush: 0 },
                    {
                        projection: {
                            BillName: 1,
                            _id: 1,
                            BillType: 1,
                            CompanyId: 1,
                            Count: 1,
                            EndTime: 1,
                            OrganizationId: 1,
                            StartTime: 1,
                        },
                    }
                )
                .toArray()
            if (billResult.length === 0) {
                await Promise.reject(
                    new self.Err(Error('推送账单时，未查到此Id'), 35307)
                )
            }

            // 开始执行
            let pushResult = await PushPaid(billResult[0])

            let currTime = parseInt(moment().format('X'))
            await billListCollection.findOneAndUpdate(
                { _id: ObjectId(Id) },
                {
                    $set: {
                        WhetherToPush: pushResult.RetCode === 0 ? 1 : 2,
                        Amount: pushResult.Amount / 100,
                        PushOperator: __ssoUser,
                        PushTime: currTime,
                        UpdateTime: currTime,
                        OrderNo: pushResult.OrderNo,
                    },
                }
            )
            // 如果推送成功，WhetherToPush为1，失败为2
            // 解一致锁
            await unLock(redlock, lockInstance)
            if (pushResult.RetCode !== 0) {
                await Promise.reject(
                    new self.Err(Error(pushResult.Message), 35307)
                )
            }

            await producer.send({
                type: 'icp-delay',
                topic: 'CheckBillPayStatus',
                data: {
                    Id,
                },
                opts: {
                    headers: { 'x-delay': 60 * 1000 }, // 延迟1分钟检查时间
                },
            })
            return this.cb(0)
        } catch (e) {
            if (e instanceof Error) {
                return self.eh({
                    err: e,
                    code: 35305,
                })
            }
            self.eh(e)
        }
    }
}

function PushPaid(billInfo) {
    // 接口标准请参考https://uaccount-wiki.ucloudadmin.com/ubill/api/internal.api.ucloud.cn/ubill-order/PostPaid.md
    let reqData = {
        Action: 'PostPaid',
        Backend: 'UBill',
        TopOrganizationId:
            global.CONFIG.env === 'production' ? parseInt(billInfo.CompanyId) : ********,
        OrganizationId:
            global.CONFIG.env === 'production'
                ? parseInt(billInfo.OrganizationId)
                : ********,
        OutItemId: _.toLower(billInfo.BillName),
        ProductType: 213, // 合规大类，固定不变
        ZoneId: 0,
        ChargeType: 102, // 后付费(chargeType = 102)。部分产品支持按量(chargeType = 101)
        Quantity: 1,
        OrderDetail: [
            {
                ProductId: billInfo.BillType,
                Multiple:
                    global.CONFIG.env === 'production' ? billInfo.Count : 1, // 从订单中取值，测试时，用数字1
            },
        ],
        StartTime: billInfo.StartTime,
        EndTime: billInfo.EndTime,
        uuid: billInfo._id,
    }
    return requestAsync(
        {
            category: 'UBill',
            method: 'POST',
            action: 'UBill.PostPaid',
        },
        reqData
    )
}
