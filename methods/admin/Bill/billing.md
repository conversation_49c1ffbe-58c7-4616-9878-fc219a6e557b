标签： 账单 费用 推送

---

备案系统收费相关的功能，本版暂时为后付费相关。大部分账单由系统自动生成，少部分，如备案迁移，需要于巍手工创建。


---

### GetBillingList

获取账单列表


请求参数

| 字段       | 类型   | 必填 | 描述                     |
| ---------- | ------ | ---- | ------------------------ |
| Action     | String | Yes  | GetBillingList           |
| BeginTime  | Int    | No   | 按时间查询，开始的时间   |
| EndTime    | Int    | No   | 按时间查询，结束的时间   |
| CompanyId  | Int    | No   | 公司Id                   |
| BillNameCN | String | No   | 账单中文名，支持模糊搜索 |
| Id         | String | No   | 账单Id                   |
| Offset     | Int    | No   | 数据偏移值，默认0        |
| Limit      | Int    | No   | 返回数据总数，默认20     |


返回值

| 字段        | 类型          | 描述           |
| ----------- | ------------- | -------------- |
| Action      | String        | GetBillingList |
| BillingList | Array[Object] | 账单列表       |
| RetCode     | Int           | 状态码         |
| Count       | Int           | 数据量         |


```JSON

{
  "BillingList" : [
    {
      "Id":  "62b97ba9c6670075e5f43fb3" , //  Id
      "BillName": "hegui-ICPOrderForFK-2022-04",
      "BillNameCN": "备案订单（凡科）2022-04",    // 账单名
      "BillType": 2130006,
      "CompanyId": 34278,
      "Count": 2,                  // 计费次数
      "EndTime": 1659283199,      // 账单结束时间（订单的计费周期）
      "OrganizationId": 34278,
      "StartTime": 1648742400,    // 账单开始时间 （订单的计费周期）
      "CreateTime":1657532440,    // 本计费记录的生成时间
      "UpdateTime":1657532440,   // 本计费记录的更新时间
      "TotalAmount": 50,          // 账单总额，注这是根据次数*单价得到，可能存在前端
      "SendStatus": 1, //发送状态 详见StatusEnum
      "OrderStatus": 1, // 订单真实扣费状态
      "WhetherToPush": 0,          //   是否扣费过，0未扣，1扣过，2扣过但出错
      "Amount": 50,          //   实际账单金额
      "OrderNo": "20220708032062739184977",        //   是否扣费过，0未扣，1扣过，2扣过但出错
      "PushOperator":"操作人",
      "PushTime": 12344 ,// 推送时间 
      "Remark":"备注"
    }
  ],
  "Count": 21,
  "RetCode" : 0,
  "Action" : "GetBillingList"
}
//SendStatusEnum
{
  null: "未发送",
  0: "待发送",
  1: "发送中",
  2: "发送完成",
  3: "发送失败",
  4: "超时未响应",
  5: "禁止发送"
}

// OrderStatus
{
    0: "新建中",
    1: "待支付",
    2: "订单出错删除",
    3: "已支付",
    4: "订单金额取消",
    5: "订单已支付，不可回撤"
}
```

 ---

### GetBillingDetails

获取账单列表详情


请求参数

| 字段   | 类型   | 必填 | 描述                 |
| ------ | ------ | ---- | -------------------- |
| Action | String | Yes  | GetBillingDetails    |
| Id     | String | No   | 账单Id               |
| Offset | Int    | No   | 数据偏移值，默认0    |
| Limit  | Int    | No   | 返回数据总数，默认20 |



返回值

| 字段        | 类型          | 描述              |
| ----------- | ------------- | ----------------- |
| Action      | String        | GetBillingDetails |
| BillingList | Array[Object] | 账单列表          |
| RetCode     | Int           | 状态码            |
| Count       | Int           | 数据量            |



```javascript

{
    "RetCode" : 0,
    "Action" : "GetBillingDetails",
    "Count" :100,
    "BillingList" : [
    {
        "Id" : "62c504ef9fecc8f8a4ce35ab",        // 详情Id
        "BillId" : "62b97ba9c6670075e5f43fb3",    // 账单Id
        "BillName" : "域名信息检查2022-06",
        "CompanyId" : 65979125,
        "Count" : 3,                           // 计价次数
        "UId" : "O20220707121309002190",      // 计价单位(标识)
        "TriggerTime" : 1654853903    // 触发时间
    },
    {
        "Id" : "62c504ef9fecc8f8a4ce35ac",
        "BillId" : "62b97ba9c6670075e5f43fb3",
        "BillName" : "域名信息检查2022-06",
        "CompanyId" : 34278,
        "UId" : "O20210623124821003887",
        "Count" : 1,
        "TriggerTime" : "1649622329"
    }
]
}


```


 ---

### PostBill

推送账单，仅在订单信息中，"WhetherToPush"为0时可推送，后端有限制，前端也需要将按钮置灰


请求参数

| 字段   | 类型   | 必填 | 描述     |
| ------ | ------ | ---- | -------- |
| Action | String | Yes  | PostBill |
| Id     | String | No   | 账单Id   |


返回值

| 字段    | 类型   | 描述     |
| ------- | ------ | -------- |
| Action  | String | PostBill |
| RetCode | Int    | 状态码   |


```javascript
{
    "RetCode" : 0,
        "Action" : "PostBill"
}
```

 ---


### CreateICPMigrationBillOrder

生成备案迁移的计费订单,后端会检查公司Id与组织Id的一致性


请求参数

| 字段           | 类型   | 必填 | 描述                                          |
| -------------- | ------ | ---- | --------------------------------------------- |
| Action         | String | Yes  | CreateICPMigrationBillOrder                   |
| CompanyId      | Int    | Yes  | 公司Id                                        |
| OrganizationId | Int    | Yes  | 项目Id                                        |
| UId            | String | Yes  | 计费Id,此处为网站备案号，实际值与BillType关联 |
| Count          | Int    | Yes  | 计费次数，此处为1                             |
| Remark         | String | No   | Remark                                        |
| BillType       | Int    | No   | 可不填，当前默认为2130007，为后续拓展考虑     |


返回值

| 字段    | 类型   | 描述                        |
| ------- | ------ | --------------------------- |
| Action  | String | CreateICPMigrationBillOrder |
| RetCode | Int    | 状态码                      |


```javascript
{
        "UId" : "9c6670075abc",
        "RetCode" : 0,
        "Action" : "CreateICPMigrationBillOrder"
}
```



 ---


### CreateICPQuotaBillOrder

传入公司Id与计费周期，接口会统计期间内的有效备案订单，生成订单明细。

与凡科备案订单不同的是，这是手动触发，同时账户侧的扣费Id也不同。


请求参数

| 字段      | 类型   | 必填 | 描述                            |
| --------- | ------ | ---- | ------------------------------- |
| Action    | String | Yes  | CreateICPMigrationBillOrder     |
| CompanyId | Int    | Yes  | 公司Id                          |
| StartTime | Int    | Yes  | 账单开始时间 （订单的计费周期） |
| EndTime   | Int    | Yes  | 账单结束时间 （订单的计费周期） |


返回值

| 字段    | 类型   | 描述                    |
| ------- | ------ | ----------------------- |
| Action  | String | CreateICPQuotaBillOrder |
| RetCode | Int    | 状态码                  |


```javascript
{
  "RetCode" : 0,
  "Action" : "CreateICPQuotaBillOrder"
}
```


