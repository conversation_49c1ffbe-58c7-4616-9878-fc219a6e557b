/**
 * @file API 根据参数从Mongo中获取指定的账单相关的数据
 * <AUTHOR>
 * @return {} 返回符合条件的记录,格式如下
 */

const Method = require('../../../libs/method')
const ObjectId = require('mongodb').ObjectId
const _ = require('lodash')
const { requestAsync } = require('../../../fns/ucloudInternalApi')
const { lock } = require('../../../fns/lockFuns')
const moment = require('moment')
const billMap = require('../../../configs/common/bill_map.json')

module.exports = class CreateICPMigrationBillOrder extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({
        CompanyId,
        OrganizationId,
        UId,
        Count = 1,
        Remark,
        BillType = 2130005,
    } = {}) {
        let self = this
        let mongo = this.mongo.get('icp')
        const billDetailsCollection = mongo.collection('t_bill_details')
        const billListCollection = mongo.collection('t_bill_list')

        try {
            let companyInfo
            let now = parseInt(moment().format('X'))
            let billMonth = moment().format('YYYY-MM')
            const billInfo = _.find(billMap, {
                ProductSmallClassId: BillType,
            })

            // 确定项目Id是否在公司Id的名下
            try {
                companyInfo = await GetCompanyInfo(OrganizationId)
            } catch (error) {
                // 如果出错，说明有锁
                await Promise.reject(
                    new self.Err(
                        Error('确定项目Id与公司Id关联时出错' + error.message),
                        35309
                    )
                )
            }

            if (companyInfo.RetCode !== 0 || companyInfo.DataSet.length === 0) {
                throw new self.Err(
                    Error(
                        '确定项目Id与公司Id关联时,信息返回出错' +
                            companyInfo.Message
                    ),
                    35310
                )
            }

            // 插入数据
            let billInsertInfo = await billListCollection.insertOne({
                BillType: billInfo.ProductSmallClassId,
                BillNameCN: [
                    billInfo.ProductSmallClassCN,
                    billMonth,
                    CompanyId,
                    companyInfo.DataSet[0].CompanyName,
                ].join('-'),
                BillName: 'hegui-' + billInfo.ProductSmallClass + '-' + now,
                Type: billInfo.Type,
                CompanyId,
                OrganizationId, //因为是凡科，限定都是34278,
                Count,
                StartTime: now,
                EndTime: now + 2,
                CreateTime: now,
                UpdateTime: now,
                TotalAmount: Math.floor(billInfo.Price * Count * 100) / 100,
                Remark,
                WhetherToPush: 0,
            })

            await billDetailsCollection.insertMany([
                {
                    BillId: billInsertInfo.insertedId,
                    BillName: billInfo.ProductSmallClassCN + billMonth,
                    CompanyId,
                    Count: 1,
                    TriggerTime: '**********',
                    UId,
                },
            ])

            return this.cb(0)
        } catch (e) {
            if (e instanceof Error) {
                return self.eh({
                    err: e,
                    code: 35305,
                })
            }
            self.eh(e)
        }
    }
}

function GetCompanyInfo(OrganizationId) {
    // 接口标准请参考https://uaccount-wiki.ucloudadmin.com/uaccount/api/internal.api.ucloud.cn/IGetCompanyInfo.md
    let reqData = {
        Action: 'IGetCompanyInfo',
        Backend: 'UAccount',
        OrganizationId: OrganizationId,
    }
    return requestAsync(
        {
            category: 'UAccount',
            method: 'POST',
            action: 'UAccount.IGetCompanyInfo',
        },
        reqData
    )
}
