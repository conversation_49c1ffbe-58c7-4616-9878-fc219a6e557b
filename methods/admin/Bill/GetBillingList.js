/**
 * @file API 根据参数从Mongo中获取指定的批次列表
 * <AUTHOR>
 * @return {} 返回符合条件的记录,格式如下
 */

const Method = require('../../../libs/method')
const ObjectId = require('mongodb').ObjectId
const _ = require('lodash')
const { CompanyNotifyModel } = require('../../../models/')
const { BillOrderStatusEnum } = require('../../../mongoModels/icp')
const { Op } = require('sequelize')
const { parseJSON } = require('../../../fns/kits')
module.exports = class GetBillingList extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({
        BeginTime,
        EndTime,
        CompanyId,
        BillNameCN,
        Type,
        Id,
        Offset,
        Limit,
    } = {}) {
        let self = this
        let mongo = this.mongo.get('icp')
        const billListCollection = mongo.collection('t_bill_list')
        let billList, Count

        try {
            // 查询Mongo，确定记录是否存在,确定是否推送过订单
            let queryObject = {
                ...(CompanyId && { CompanyId }),
                ...(BillNameCN && { BillNameCN: new RegExp(BillNameCN) }),
                ...(BeginTime &&
                    EndTime && {
                        CreateTime: { $gt: BeginTime, $lt: EndTime },
                    }),
                ...(Type && { Type }),
                ...(Id && { _id: ObjectId(Id) }),
            }

            console.log(queryObject)
            ;[billList, Count] = await Promise.all([
                billListCollection
                    .find(queryObject, {
                        projection: {
                            BillNameCN: 1,
                            Id: '_id',
                            CompanyId: 1,
                            Count: 1,
                            EndTime: 1,
                            TotalAmount: 1,
                            StartTime: 1,
                            OrderNo: 1,
                            PushOperator: 1,
                            OrganizationId: 1,
                            CreateTime: 1,
                            UpdateTime: 1,
                            Remark: 1,
                            Amount: 1,
                            WhetherToPush: 1,
                            NotifyRecordId: 1,
                            OrderStatus: 1,
                            Type: 1,
                        },
                    })
                    .sort({ CreateTime: -1 })
                    .skip(Offset || 0)
                    .limit(Limit || 20)
                    .toArray(),
                billListCollection.find(queryObject).count(),
            ])
            let notifyInfos = await CompanyNotifyModel.findAll({
                where: {
                    Id: {
                        [Op.in]: _.map(billList, 'NotifyRecordId'),
                    },
                },
                attributes: ['Id', 'Status'],
            })
            notifyInfos = parseJSON(notifyInfos)
            billList = billList.map((row) => {
                row.Id = row._id
                // 为空数据赋值
                if (!row.Remark) {
                    row.Remark = ''
                }
                if (!row.Amount) {
                    row.Amount = 0
                }
                if (!row.OrderStatus) {
                    row.OrderStatus = BillOrderStatusEnum.UNPAID_NEW
                }
                row.SendStatus =
                    _.find(notifyInfos, ['Id', row.NotifyRecordId])?.Status ||
                    null //之前未通知过的 默认为null
                delete row._id
                return row
            })

            return this.cb(0, {
                BillingList: billList,
                Count,
            })
        } catch (e) {
            if (e instanceof Error) {
                return self.eh({
                    err: e,
                    code: 35312,
                })
            }
            self.eh(e)
        }
    }
}
