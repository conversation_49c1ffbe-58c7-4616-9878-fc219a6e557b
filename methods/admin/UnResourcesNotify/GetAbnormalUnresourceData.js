'use strict'

const Method = require('../../../libs/method')
const { parseJSON } = require('../../../fns/kits')
const { model: RecordModel } = require('../../../models/t_notify_companyInfo')
const _ = require('lodash')
/**
 * 查看有备案无资源批次异常数据
 */
module.exports = class GetAbnormalUnresourceData extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({
        BatchId, //批次Id
        GetAll, //是否获取全量数据
        Offset = 0,
        Limit = 20,
    }) {
        let self = this
        try {
            let recordWhere = { IsError: true, BatchId }
            let limit = {}

            if (!GetAll) {
                limit.limit = Limit
                limit.offset = Offset
            }
            let { rows: records, count } = parseJSON(
                await RecordModel.findAndCountAll({
                    attributes: ['CompanyId', 'Reason'],
                    where: recordWhere,
                    ...limit,
                })
            )

            if (count === 0) {
                return this.cb(0, {
                    RecordList: [],
                    TotalCount: 0,
                })
            }
            return this.cb(0, {
                RecordList: records,
                TotalCount: count,
            })
        } catch (e) {
            console.log(e)
            self.err(e)
        }
    }
}
