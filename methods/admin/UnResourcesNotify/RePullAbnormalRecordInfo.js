'use strict'

const Method = require('../../../libs/method')
const { parseJSON } = require('../../../fns/kits')
const {
    model: RecordModel,
    RecordStatusEnum,
    ErrorReasonEnum,
} = require('../../../models/t_notify_companyInfo')
const {
    model: BatchModel,
    BatchStatusEnum,
} = require('../../../models/t_notify_batch')
const { Op } = require('sequelize')
const _ = require('lodash')
/**
 * 重新拉取异常的数据信息
 */
module.exports = class RePullAbnormalRecordInfo extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({ BatchId }) {
        let self = this
        let redis = this.redis.get()
        const producer = this.producer

        try {
            let [batchInfo, records] = await Promise.all([
                BatchModel.findOne({
                    attributes: ['Id', 'Status'],
                    where: {
                        Id: BatchId,
                    },
                }),
                RecordModel.findAll({
                    attributes: ['Id', 'CompanyId', 'Reason'],
                    where: {
                        BatchId,
                        IsError: true,
                    },
                }),
            ])
            batchInfo = parseJSON(batchInfo)
            records = parseJSON(records)
            if (!batchInfo) {
                let err = new Error('批次不存在')
                err.code = 35201
                throw err
            }
            if (batchInfo.Status !== BatchStatusEnum.Parsed) {
                let err = new Error('该状态下不支持重新获取数据')
                err.code = 35208
                throw err
            }
            if (records.length === 0) {
                let err = new Error('没有需要重新获取的数据')
                err.code = 35207
                throw err
            }
            // 资源数据获取错误数组
            let resourceSearchArr = []
            // 所有发生错误的数据数组
            let companyIds = []
            records.forEach((record) => {
                if (record.Reason === ErrorReasonEnum.GetResource) {
                    resourceSearchArr.push(record.CompanyId)
                }
                companyIds.push(record.CompanyId)
            })

            //若存在资源获取失败的 就全部从资源获取开始，因为涉及到 解析完成后批次状态变化的问题， 若分类型处理 不容易控制批次状态变化控制
            if (resourceSearchArr.length > 0) {
                await Promise.all([
                    // 更新批次状态为获取中
                    BatchModel.update(
                        {
                            Status: BatchStatusEnum.PUll,
                        },
                        {
                            where: {
                                Id: BatchId,
                                Status: BatchStatusEnum.Parsed,
                            },
                        }
                    ),
                    // 更新记录的错误为初始情况
                    RecordModel.update(
                        {
                            IsError: false,
                            Reason: '',
                        },
                        {
                            where: {
                                Id: {
                                    [Op.in]: _.map(records, 'Id'),
                                },
                            },
                        }
                    ),
                    // 将所有的公司Id记录在redis中， 处理一个删除一个，当处理完成后 就可以更改记录的状态了
                    redis.sadd(
                        `${BatchId}_search_resource_companyIds`,
                        companyIds
                    ),
                ])
                for (let i = 0; i < records.length; i++) {
                    //需要检查资源 如果有资源 需要从记录中删除
                    await producer.send({
                        type: 'icp',
                        topic: 'GetResourceFromCompanyId',
                        data: {
                            BatchId,
                            Id: records[i].Id,
                            CompanyId: records[i].CompanyId,
                        },
                    })
                }
            } else {
                await Promise.all([
                    // 更新批次状态为解析中
                    BatchModel.update(
                        {
                            Status: BatchStatusEnum.Parsing,
                        },
                        {
                            where: {
                                Id: BatchId,
                                Status: BatchStatusEnum.Parsed,
                            },
                        }
                    ),
                    // 更新记录的错误为初始情况
                    RecordModel.update(
                        {
                            IsError: false,
                            Reason: '',
                        },
                        {
                            where: {
                                Id: {
                                    [Op.in]: _.map(records, 'Id'),
                                },
                            },
                        }
                    ),
                    // 用于记录备案和 公司数据的解析进度
                    redis.sadd(`${BatchId}_unresource_icp`, companyIds),
                    redis.sadd(`${BatchId}_unresource_companyInfo`, companyIds),
                ])
                for (let i = 0; i < records.length; i++) {
                    await Promise.all([
                        // 推入公司信息查询 队列
                        self.producer.send({
                            type: 'icp',
                            topic: 'GetResourceCompanyInfo',
                            data: {
                                BatchId,
                                Id: records[i].Id,
                                CompanyId: records[i].CompanyId,
                            },
                        }),
                        // 推入备案信息查询队列
                        self.producer.send({
                            type: 'icp',
                            topic: 'GetResourceICPInfo',
                            data: {
                                BatchId,
                                Id: records[i].Id,
                                CompanyId: records[i].CompanyId,
                            },
                        }),
                    ])
                }
            }

            this.cb(0)
        } catch (e) {
            console.log(e)
            self.err(e)
        }
    }
}
