/*
 * @Date: 2022-07-06 16:01:03
 * @LastEditors: l<PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2022-12-23 14:29:10
 * @FilePath: /newicp/methods/admin/UnResourcesNotify/GenerateUnresourceBatch.js
 */
'use strict'

const Method = require('../../../libs/method')
const { parseJSON } = require('../../../fns/kits')
const { Op, BaseError } = require('sequelize')
const { model: BatchModel } = require('../../../models/t_notify_batch')
const { model: ICPModel, ICPStatusEnum } = require('../../../models/t_icp')
const moment = require('moment')
/**
 * 生成有备案 无资源的批次
 */
module.exports = class GenerateUnresourceBatch extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec() {
        let self = this
        const redis = this.redis.get()
        const producer = this.producer
        try {
            // 不需要扫描的companyId
            let whiteCompanyIds = await redis.smembers(
                'noResource_companyId_whiteList'
            )
            // 查看所有 备案状态正常的 公司ID
            let icps = await ICPModel.findAll({
                attributes: ['CompanyId'],
                where: {
                    IsDeleted: 0,
                    Status: {
                        [Op.notIn]: [
                            ICPStatusEnum.Logout,
                            ICPStatusEnum.Isdeleted,
                        ],
                    },
                    CompanyId: {
                        [Op.notIn]: whiteCompanyIds,
                    },
                },
            })
            icps = parseJSON(icps)
            // 所有去重后的公司Id
            let companyIds = []

            icps.forEach((icp) => {
                if (!companyIds.includes(icp.CompanyId)) {
                    companyIds.push(icp.CompanyId)
                }
            })

            let BatchId = await BatchModel.create({
                Remark: `取消接入${moment().format('YYYY-MM-DD')}`,
            })
            BatchId = parseJSON(BatchId).Id
            // 将所有的公司Id记录在redis中， 处理一个删除一个，当处理完成后 就可以更改记录的状态了
            await redis.sadd(
                `${BatchId}_search_resource_companyIds`,
                companyIds
            )
            //将所有的公司Id推入至资源查询的MQ中
            for (let i = 0; i < companyIds.length; i++) {
                await producer.send({
                    type: 'icp',
                    topic: 'GetResourceFromCompanyId',
                    data: { BatchId, CompanyId: companyIds[i] },
                })
            }

            return this.cb(0, { BatchId })
        } catch (e) {
            console.log(e)
            self.err(e)
        }
    }
}
