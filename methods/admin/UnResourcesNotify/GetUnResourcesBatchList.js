'use strict'

const Method = require('../../../libs/method')
const { parseJSON } = require('../../../fns/kits')
const { Op, BaseError } = require('sequelize')
const {
    model: BatchModel,
    TypeEnum,
} = require('../../../models/t_notify_batch')
const { model: RecordModel } = require('../../../models/t_notify_companyInfo')
const _ = require('lodash')
/**
 * 获取有备案 无资源的批次 列表
 */
module.exports = class GetUnResourcesBatchList extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({
        CompanyId,
        BatchId,
        Domain,
        StartTime,
        EndTime,
        Offset = 0,
        Limit = 20,
    }) {
        let self = this
        try {
            // 查看所有 备案状态正常的 公司ID
            let batchs, records
            let batchWhere = {}
            batchWhere.Type = TypeEnum.Unresource
            if (BatchId) {
                batchWhere.Id = BatchId
            }
            if (StartTime && EndTime) {
                batchWhere.CreateTime = {
                    [Op.between]: [StartTime, EndTime],
                }
            }

            if (CompanyId || Domain) {
                let recordWhere = {}
                if (CompanyId) {
                    recordWhere.CompanyId = CompanyId
                }
                if (Domain) {
                    recordWhere.ICPInfo = {
                        [Op.like]: `%${Domain}%`,
                    }
                }
                records = await RecordModel.findAll({
                    attributes: ['BatchId'],
                    where: recordWhere,
                })

                records = parseJSON(records)

                if (records.length === 0) {
                    return this.cb(0, {
                        BatchList: [],
                        TotalCount: 0,
                    })
                }
                batchWhere.Id = {
                    [Op.in]: _.map(records, 'BatchId'),
                }
                batchs = await BatchModel.findAndCountAll({
                    where: batchWhere,
                    offset: Offset,
                    limit: Limit,
                    order: [['id', 'desc']],
                })
            } else {
                batchs = await BatchModel.findAndCountAll({
                    where: batchWhere,
                    offset: Offset,
                    limit: Limit,
                    order: [['id', 'desc']],
                })
            }
            batchs = parseJSON(batchs)

            return this.cb(0, {
                BatchList: batchs.rows,
                TotalCount: batchs.count,
            })
        } catch (e) {
            console.log(e)
            self.err(e)
        }
    }
}
