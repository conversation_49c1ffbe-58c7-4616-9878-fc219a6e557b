'use strict'

const Method = require('../../../libs/method')
const { parseJSON } = require('../../../fns/kits')
const { Op, BaseError } = require('sequelize')
const {
    model: BatchModel,
    TypeEnum,
} = require('../../../models/t_notify_batch')
const { model: RecordModel } = require('../../../models/t_notify_companyInfo')
const { model: NotifyModel } = require('../../../models/t_notify')
const {
    getMailContent,
    getSmsContent,
} = require('../../../fns/notify/GetNotifyContent')
const _ = require('lodash')
/**
 * 查看有备案无资源批次详情
 */
module.exports = class DescribeUnResourcesBatch extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({
        Id, // 公司记录Id
        BatchId, //批次Id
        CompanyId, // 公司Id
        CompanyName, // 公司名称
        Domain, // 域名
        Status, // 公司记录发送状态
        GetAll, //是否获取全量数据
        Offset = 0,
        Limit = 20,
    }) {
        let self = this
        try {
            // 查看所有 备案状态正常的 公司ID
            let batchInfo = await BatchModel.findOne({
                attributes: ['NotifyDay', 'Status', 'Type'],
                where: {
                    Id: BatchId,
                },
            })
            batchInfo = parseJSON(batchInfo)
            if (!batchInfo) {
                let err = new Error('没有发现该批次')
                err.code = 35201
                throw err
            }

            if (Id) {
                //传 ID 是为了获取记录详情， 需要返回具体通知人的相关信息
                let [recordInfo, notifies] = await Promise.all([
                    RecordModel.findOne({
                        where: {
                            Id,
                        },
                    }),
                    NotifyModel.findAll({
                        attributes: [
                            'Id',
                            'Contact',
                            'SendStatus',
                            'Type',
                            'UpdateTime',
                            'HistorySendStatus',
                            'HistorySendTime',
                        ],
                        where: {
                            CompanyNotifyId: Id,
                        },
                    }),
                ])

                recordInfo = parseJSON(recordInfo)
                notifies = parseJSON(notifies)
                if (recordInfo) {
                    recordInfo.NotifyDetail = notifies
                    return this.cb(0, {
                        Type: batchInfo.Type,
                        BatchStatus: batchInfo.Status,
                        NotifyDay: batchInfo.NotifyDay,
                        RecordList: [recordInfo],
                        TotalCount: 1,
                    })
                }

                return this.cb(0, {
                    Type: batchInfo.Type,
                    BatchStatus: batchInfo.Status,
                    NotifyDay: batchInfo.NotifyDay,
                    RecordList: [],
                    TotalCount: 0,
                })
            }

            let recordWhere = { BatchId, IsError: false }
            let limit = {}

            if (CompanyId || CompanyName || Domain || Status) {
                if (CompanyId) {
                    recordWhere.CompanyId = CompanyId
                }
                if (CompanyName) {
                    recordWhere.CompanyName = CompanyName
                }
                if (Status) {
                    recordWhere.Status = {
                        [Op.in]: Status,
                    }
                }
                if (Domain) {
                    recordWhere.ICPInfo = {
                        [Op.like]: `%${Domain}%`,
                    }
                }
            }
            if (!GetAll) {
                limit.limit = Limit
                limit.offset = Offset
            }
            let { rows: records, count } = parseJSON(
                await RecordModel.findAndCountAll({
                    where: recordWhere,
                    ...limit,
                })
            )

            if (count === 0) {
                return this.cb(0, {
                    Type: batchInfo.Type,
                    BatchStatus: batchInfo.Status,
                    NotifyDay: batchInfo.NotifyDay,
                    RecordList: [],
                    TotalCount: 0,
                })
            }
            return this.cb(0, {
                Type: batchInfo.Type,
                BatchStatus: batchInfo.Status,
                NotifyDay: batchInfo.NotifyDay,
                RecordList: records,
                TotalCount: count,
            })
        } catch (e) {
            console.log(e)
            self.err(e)
        }
    }
}
