'use strict'

const Method = require('../../../libs/method')
const { parseJSON } = require('../../../fns/kits')
const {
    model: NotifyModel,
    SendStatusEnum,
    NotifyTypeEnum,
} = require('../../../models/t_notify')
const {
    model: RecordModel,
    RecordStatusEnum,
} = require('../../../models/t_notify_companyInfo')
const {
    model: BatchModel,
    BatchStatusEnum,
} = require('../../../models/t_notify_batch')
const { Op } = require('sequelize')
const Cron = require('../../../libs/cron')
const _ = require('lodash')
/**
 * 通知批次
 */
module.exports = class NotifyUnResourcesBatch extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({ BatchId }) {
        let self = this
        const producer = this.producer
        try {
            // 检查批次状态是否允许发送
            let batchInfo = await BatchModel.findOne({
                where: {
                    Id: BatchId,
                },
            })

            batchInfo = parseJSON(batchInfo)

            if (!batchInfo) {
                let err = new Error('没有发现该批次')
                err.code = 35201
                return this.err(err)
            }
            // 首次通知 批次状态需要时已解析 二次通知 上次的发送状态需要发送完成才行
            if (
                ![BatchStatusEnum.Parsed, BatchStatusEnum.SendFinish].includes(
                    batchInfo.Status
                )
            ) {
                let err = new Error('该批次状态不支持发送通知')
                err.code = 35203
                return this.err(err)
            }
            //只通知解析成功的记录
            let company_notify_ids = await RecordModel.findAll({
                attributes: ['Id'],
                where: {
                    BatchId,
                    IsError: false,
                },
            })
            company_notify_ids = parseJSON(company_notify_ids)
            if (company_notify_ids.length === 0) {
                return this.cb(0)
            }

            let [notifyInfos, batchUpdate, recordUpdate] = await Promise.all([
                NotifyModel.findAll({
                    where: {
                        CompanyNotifyId: {
                            [Op.in]: _.map(company_notify_ids, 'Id'),
                        },
                    },
                }),
                // 更新批次状态为发送中
                BatchModel.update(
                    {
                        Status: BatchStatusEnum.Sending,
                    },
                    {
                        where: {
                            Id: BatchId,
                        },
                    }
                ),
                RecordModel.update(
                    { Status: RecordStatusEnum.Sending },
                    {
                        where: {
                            BatchId,
                            IsError: false,
                        },
                    }
                ),
            ])
            notifyInfos = parseJSON(notifyInfos)
            for (let i = 0; i < notifyInfos.length; i++) {
                // 若为二次发送 更新历史状态 以及发送状态初始化
                if (batchInfo.Status === BatchStatusEnum.SendFinish) {
                    await NotifyModel.update(
                        {
                            SendStatus: SendStatusEnum.New,
                            HistorySendStatus: notifyInfos[i].SendStatus,
                            HistorySendTime: notifyInfos[i].UpdateTime,
                        },
                        {
                            where: {
                                Id: notifyInfos[i].Id,
                            },
                        }
                    )
                }
                // 发送消息队列
                await producer.send({
                    type: 'icp',
                    topic: 'NotifyEmailOrSms',
                    data: {
                        Id: notifyInfos[i].Id,
                        CompanyNotifyId: notifyInfos[i].CompanyNotifyId,
                        Contact: notifyInfos[i].Contact,
                        Type: notifyInfos[i].Type,
                        NotifyType: batchInfo.Type,
                        RetryTime: notifyInfos[i].RetryTime,
                        NotifyVersion: 'xxxx',
                    },
                })
            }
            // 将查询状态推入至延迟队列中 5分钟一次查询
            await producer.send({
                type: 'icp-delay',
                topic: 'CheckBatchSendStatus',
                data: {
                    BatchId: BatchId,
                },
                opts: {
                    headers: { 'x-delay': 5 * 60 * 1000 }, // 延迟5分钟检查时间
                },
            })
            // 通过bull库的延迟队列
            // Cron.setCron('CheckSendStatus', {
            //     data: BatchId,
            //     delay: 5 * 60 * 1000, //单位毫秒
            // })
            this.cb(0)
        } catch (e) {
            console.log(e)
            self.err(e)
        }
    }
}
