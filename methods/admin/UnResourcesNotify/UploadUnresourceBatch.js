'use strict'

const Method = require('../../../libs/method')
const { parseJSON, csvParse } = require('../../../fns/kits')
const { Op, BaseError } = require('sequelize')
const { model: BatchModel } = require('../../../models/t_notify_batch')
const { model: ICPModel, ICPStatusEnum } = require('../../../models/t_icp')
const { model: WebModel, WebStatusEnum } = require('../../../models/t_web')
const _ = require('lodash')

/**
 * 上传无资源 有备案检查批次
 */
module.exports = class UploadUnresourceBatch extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({ File, FileName }) {
        let self = this
        const redis = this.redis.get()
        const producer = this.producer
        try {
            let inputInfo = await csvParse(File, '网站备案号')
            if (inputInfo.length === 0) {
                let err = new Error('没有数据')
                err.code = 35205
                throw err
            }
            // 查看所有 备案状态正常的 公司ID
            let websites = await WebModel.findAll({
                attributes: ['MainId'],
                where: {
                    IsDeleted: 0,
                    ICPWebNo: {
                        [Op.in]: _.map(inputInfo, '网站备案号'),
                    },
                    Status: {
                        [Op.notIn]: [
                            WebStatusEnum.Weblogout,
                            WebStatusEnum.Connlogout,
                        ],
                    },
                },
            })
            websites = parseJSON(websites)

            if (websites.length === 0) {
                let err = new Error('备案号中没有正常备案数据')
                err.code = 35206
                throw err
            }
            // 不需要扫描的companyId
            let whiteCompanyIds = await redis.smembers(
                'noResource_companyId_whiteList'
            )
            let icps = await ICPModel.findAll({
                attributes: ['CompanyId'],
                where: {
                    Id: {
                        [Op.in]: _.map(websites, 'MainId'),
                    },
                    IsDeleted: 0,
                    Status: {
                        [Op.notIn]: [
                            ICPStatusEnum.Logout,
                            ICPStatusEnum.Isdeleted,
                        ],
                    },
                    CompanyId: {
                        [Op.notIn]: whiteCompanyIds,
                    },
                },
            })
            icps = parseJSON(icps)

            if (icps.length === 0) {
                let err = new Error('备案号所在主体中没有正常备案数据')
                err.code = 35206
                throw err
            }
            // 所有去重后的公司Id
            let companyIds = []

            icps.forEach((icp) => {
                if (!companyIds.includes(icp.CompanyId)) {
                    companyIds.push(icp.CompanyId)
                }
            })

            let BatchId = await BatchModel.create({
                Remark: FileName,
            })
            BatchId = parseJSON(BatchId).Id
            // 将所有的公司Id记录在redis中， 处理一个删除一个，当处理完成后 就可以更改记录的状态了
            await redis.sadd(
                `${BatchId}_search_resource_companyIds`,
                companyIds
            )
            //将所有的公司Id推入至资源查询的MQ中, 一次异步执行两个插入
            for (let i = 0; i < companyIds.length; i++) {
                await producer.send({
                    type: 'icp',
                    topic: 'GetResourceFromCompanyId',
                    data: { BatchId, CompanyId: companyIds[i] },
                })
            }

            return this.cb(0, { BatchId })
        } catch (e) {
            console.log(e)
            self.err(e)
        }
    }
}
