云企业对接ISMS系统接口规范v1.0接口规范

# 云企业对接

# ISMS

# 系统

# 接口规范

**天津市国瑞数码安全系统有限公司**

**201**** 7 ****年**** 5 ****月**

| **日期** | **名称** | **版本** | **状态** | **签名** |
| --- | --- | --- | --- | --- |
| 2017年5月 | 云企业对接ISMS系统接口规范 | 1.0 | 1 |
 |
|
 |
 |
 |
 |
 |
|
 |
 |
 |
 |
 |
|
 |
 |
 |
 |
 |
|
 |
 |
 |
 |
 |
|
 |
 |
 |
 |
 |
|
 |
 |
 |
 |
 |

**备注：** 状态表示为：1.新建 2.修订3.完成未审4.完成已审5.完成归档

## 接口功能要求

1.
## 用户信息同步

需要把IRCS系统的中的用户信息同步到ISMS系统，

包括：

用户的新增；

用户的变更；

用户的删除；

注：同步数据参照接口节点规范

## 二、动态资源信息同步

### 1.首次同步：

IRCS系统应向ISMS系统同步用户所属动态资源数据（域名，ip）。

### 2.变更同步：

当用户操作IRCS系统变更用户的虚拟资源（域名，ip）后，IRCS 系统应立即向ISMS系统同步变更后用户所属动态资源数据（域名，ip）。

变更操作包括：

添加IP或域名

删除IP或域名

注：同步数据参照接口节点规范

## 接口节点规范

| 编号 | 节点 | 节点名称 | 必填 | 长度 | 数据类型 | 描述 |
| --- | --- | --- | --- | --- | --- | --- |
| 1 | state | 状态位 | 必填 | / | 整型 | 1-新增 2-修改 3-删除 |
| 2 | userId | 数据库id | 必填 | / | 长整型 | 上报来的userId |
| 3 | unitName | 用户单位名称 | 必填 | 128 | 字符串 | 单位名称 |
| 4 | idType | 证件类型 | 必填 | / | 整型 | 证件类型 见证件类型（表6） |
| 5 | idNo | 证件号码 | 必填 | 32 | 字符串 | 证件号码 |
| 6 | unitNature | 单位属性 | 必填 | / | 整型 | 单位属性 见单位属性表（表5） |
| 7 | officerName | 责任人姓名 | 必填 | 32 | 字符串 | 人员姓名 |
| 8 | officerMobile | 责任人移动电话 | 必填 | 32 | 字符串 | 责任人移动电话 |
| 9 | officerTel | 责任人固定电话 | 选填 | 32 | 字符串 | 责任人固定电话 |
| 10 | officerEmail | 责任人邮箱 | 必填 | 64 | 字符串 | 责任人邮箱 |
| 11 | add | 单位地址 | 选填 | 128 | 字符串 | 单位地址 |
| 12 | zipCode | 邮政编码 | 选填 | 6 | 字符串 | 邮政编码 |
| 13 | registerTime | 服务开通时间 | 必填 | 10 | 字符串 | 用户服务开通时间,采用yyyy-MM-dd格式 |

## 表1 用户信息节点(userInfo)

## 表2 动态资源节点(dynamicResource)

##

| 编号 | 节点 | 节点名称 | 子节点 | 子节点名称 | 必填 | 数据类型 | 长度 | 描述 |
| --- | --- | --- | --- | --- | --- | --- | --- | --- |
| 1 | userResources | 用户信息 | userId | 用户ID | 必填 | 长整形 | / | 用户编号信息 |
| 2 |
 |
 | domainInfo | 域名 | 选填 | / | / | 用户对应的域名列表，节点可重复，见表3 |
| 3 |
 |
 | ipInfo | 用户公网IP | 选填 | / | / | 用户对应的IP列表，节点可重复，见表4 |
| 4 | timeStamp | 资源变更时间 | / | / | 必填 | / | / | 资源变更时间 |

表3 域名节点（domainInfo）

| 编号 | 节点 | 节点名称 | 必填 | 数据类型 | 长度 | 描述 |
| --- | --- | --- | --- | --- | --- | --- |
| 1 | domain | 用户信息 | 必填 | 字符串 | 128 | 域名 |
| 2 | ownerTime | 域名归属时间 | 选填 | 字符串 | 19 | 域名归属时间，采用yyyy-MM-dd HH:mm:ss格式 |

## 表4 IP节点(ipInfo)

| 编号 | 节点 | 节点名称 | 必填 | 数据类型 | 长度 | 描述 |
| --- | --- | --- | --- | --- | --- | --- |
| 1 | ip | 公网IP地址 | 必填 | 字符串 | 64 | 公网IP地址 |
| 2 | virtualId | 虚拟资源Id | 必填 | 字符串 | 128 | 公网IP对应的虚拟资源Id |
| 3 | virtualType | 虚拟资源类型 | 必填 | 整形 | / | 虚拟资源类型(表7) |
| 4 | ownerTime | IP归属时间 | 必填 | 字符串 | 19 | 公网IP归属时间，采用yyyy-MM-dd HH:mm:ss格式 |

## 表5 单位属性代码表

| 代 码 | 单位属性名称 |
| --- | --- |
| 1 | 军队 |
| 2 | 政府机关 |
| 3 | 事业单位 |
| 4 | 企业 |
| 5 | 个人 |
| 6 | 社会团体 |
| 999 | 其他 |

## 表6 证件类型代码表

| 代 码 | 证件类型 |
| --- | --- |
| 1 | 工商营业执照号码 |
| 2 | 身份证 |
| 3 | 组织机构代码证书 |
| 4 | 事业法人证书 |
| 5 | 军队代号 |
| 6 | 社团法人证书 |
| 7 | 护照 |
| 8 | 军官证 |
| 9 | 台胞证 |
| 999 | 其他 |

## 表7虚拟资源类型代码表

| 类型序号 | 虚拟资源类型 |
| --- | --- |
| 0 | 虚拟主机 |
| 1 | 虚拟存储 |
| 2 | 应用托管容器 |
| 999 | 其他 |

## 接口方法定义

## 方法原型

public String dynamic\_resource\_command(String ircsId, String randVal, String pwdHash , String command,String commandHash,int commandType,Long commandSequence, int encryptAlgorithm, int hashAlgorithm, int compressionFormat,String commandVersion)。

## 服务请求地址

http://IRCS服务器 IP地址/DynamicResourceWebService/dynamic\_resource\_command?wsdl。

## 参数描述

本方法共使用11个参数，各项参数的描述如表11所示。

表11 dynamic\_resource\_command()方法参数

| 参数名称 | 参数类型 | 参数描述 |
| --- | --- | --- |
| ircsId | 字符串 | IRCS/ISP许可证号，长度上限是18字节 |
| randVal | 字符串 | IRCS调用该方法时生成的随机字符串，长度上限是20字节 |
| pwdHash | 字符串 | 将用户口令和随机字符串连接后使用hashAlgorithm指定的哈希算法进行哈希运算，然后进行base64编码得到的结果。用户口令由设备所在IRCS维护管理，长度至少为6字节，最多32字节 |
| command | 字符串 | 对数据文件使用compressionFormat指定的压缩算法进行压缩，再对压缩后的信息按照encryptAlgorithm参数的要求进行加密，然后进行base64编码运算得到的结果；数据包括用户数据同步（表1）、动态资源数据同步（表3） |
| commandHash | 字符串 | 对数据文件使用compressionFormat指定的压缩算法进行压缩，压缩后串接消息认证密钥，然后使用hashAlgorithm指定的哈希算法进行哈希运算得到哈希值，并对哈希值进行base64编码运算形成commandHash，用于验证完整性。消息认证密钥由IRCS与ISMS事先配置确定，长度至少为20字节，最多32字节 |
| commandType | 整型 | 数据类型如下。0：用户数据同步；1：动态资源同步； |
| commandSequence | 长整型 | 本次数据同步的惟一编号 |
| encryptAlgorithm | 整型 | 对称加密算法。0：不进行加密，明文传输；1：AES加密算法。加密密钥由IRCS与ISMS事先配置确定，长度至少为20字节，最多32字节。 |
| hashAlgorithm | 整型 | 哈希算法如下。0：无hash；1：MD5；2：SHA-1； |
| compressionFormat | 整型 | 压缩格式如下。0：无压缩；1：Zip压缩格式； |
| commandVersion | 字符串 | 接口方法版本。符合本文件所规定要求的ISMI接口方法为"v1.0"，长度4字节 |

## 方法描述

IRCS调用该方法向ISMS同步数据。 IRCS调用同步数据时，同时完成认证和对数据文件的压缩加密处理。ISMS数据件认证和处理过程中哈希计算结果应采用十六进制字符串（英文字母小写）形式。

认证过程如下：

IRCS产生长度上限为20的随机字符串（字符串由数字和大、小写字母构成），将该字符串与IRCS中存储的用户口令进行连接（例如，口令是字符串"1234567890"，生成的随机字符串是 "abcdefghij"，那么连接后的结果即字符串"1234567890abcdefghij"）。IRCS将连接后的字符串使用hashAlgorithm定义的哈希算法进行哈希计算，再将其结果进行base64编码，得到参数pwdHash值即为IRCS的认证信息。

ISMS采用同样的方法产生pwdHash值，将其与收到的值进行比较，如果一致，则ISMS对IRCS的认证通过。

### 数据文件处理过程如下：

IRCS对原始数据XML文件使用参数compressionFormat指定的压缩格式进行压缩；对压缩的信息串接消息认证密钥后使用参数hashAlgorithm指定的哈希算法计算哈希值，并对哈希值进行base64编码运算形成commandHash；对压缩后的信息使用参数encryptAlgorithm指定的加密算法加密，并对加密结果进行base64编码运算形成command。

ISMS收到数据文件后，首先对command进行base64反解码，然后采用参数encryptAlgorithm指定的加密算法对解码后的数据进行解密处理，得到data；针对data串接消息认证密钥后，使用hashAlgorithm指定的哈希算法计算哈希值，将得到的哈希值与收到的commandHash进行比较，如果一致，则对数据文件的完整性校验通过。进一步按照compressionFormat指定的压缩格式对data进行解压后即得到数据信息。

天津市国瑞数码安全系统股份有限公司