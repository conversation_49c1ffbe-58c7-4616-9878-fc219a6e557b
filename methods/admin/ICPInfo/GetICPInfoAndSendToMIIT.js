// methods/admin/ICPInfo/GetICPInfoAndSendToMIIT.js
// 导入所需的库和模型
const Method = require('../../../libs/method')
const { ICPModel, ICPWebModel } = require('../../../models')
const organizerLicenseType = require('../../../configs/common/organizer_license_type.json')
const organizerType = require('../../../configs/common/organizer_type.json')
const { dynamicResourceCommand } = require('../../../fns/miit/ircs')
const moment = require('moment')
const crypto = require('crypto')
const snowflake = require('node-snowflake').Snowflake
const { createCipheriv } = require('crypto')
const xml2js = require('xml2js')
const { Op } = require('sequelize')

// 定义GetICPInfoAndSendToMIIT类，继承自Method类
module.exports = class GetICPInfoAndSendToMIIT extends Method {
    constructor(cb) {
        super(cb)
    }
    // 定义exec方法，接收ICPMainNo作为参数
    async exec({ ICPMainNo, Source } = {}) {
        let self = this
        const redis = self.redis.get()
        const redisSetKey = 'uploaded_miit_icp_list'
        let userPassword = 'LVVKVXWF56496929'
        let messageAuthenticationKey = 'LVVKVXWF56496929'
        let aesKey = 'LVVKVXWF56496929'
        let aesIv = 'LVVKVXWF56496929'
        let cipher = createCipheriv('aes-128-cbc', aesKey, aesIv)

        try {
            // 从数据库中查找对应的ICP记录
            let icpRecord = await ICPModel.findOne({
                where: {
                    Status: {
                        [Op.ne]: 1,
                    },
                    ICPMainNo: ICPMainNo,
                },
            })

            // 如果没有找到对应的ICP记录，抛出错误
            if (!icpRecord) {
                let error = new Error('没有找到对应的ICP记录')
                error.code = 36060
                throw error
            }

            // 从数据库中查找对应的网站记录
            let webRecords = await ICPWebModel.findAll({
                where: {
                    Status: {
                        [Op.ne]: 1,
                    },
                    MainId: icpRecord.Id,
                },
            })

            // 检查ICPMainNo是否在Redis的set中
            let isUploaded = await redis.sismember(redisSetKey, ICPMainNo)

            let userInfoXml = `<?xml version="1.0" encoding="UTF-8"?>
<userInfo>
    <state>${isUploaded ? 2 : 1}</state>
    <userId>${icpRecord.Id}</userId>
    <unitName>${icpRecord.OrganizerName}</unitName>
    <idType>${convertIdType(icpRecord.OrganizerLicenseType)}</idType>
    <idNo>${icpRecord.OrganizerLicenseId}</idNo>
    <unitNature>${convertUnitNature(icpRecord.OrganizerType)}</unitNature>
    <officerName>${icpRecord.PICMainName}</officerName>
    <officerMobile>${icpRecord.PICMainMobile}</officerMobile>
    <officerTel></officerTel>
    <officerEmail>${icpRecord.PICMainEmail}</officerEmail>
    <add>${icpRecord.OrganizerAddress}</add>
    <zipCode>100000</zipCode>
    <registerTime>${moment
        .unix(icpRecord.CreateTime)
        .format('YYYY-MM-DD HH:mm:ss')}</registerTime>
</userInfo>
`
            // 生成随机字符串用于用户信息的密码哈希
            let randValForUserInfo = generateRandVal()

            // 使用crypto库生成用户信息的密码哈希
            let pwdHashForUserInfo = encodeBase64String(
                userPassword + randValForUserInfo
            )

            // 将userInfoXml转换为base64格式
            let encrypted = cipher.update(userInfoXml, 'utf8', 'base64')
            encrypted += cipher.final('base64')
            // 将加密后的userInfoXml转换为base64格式

            // 调用dynamicResourceCommand函数，传入相关参数，获取用户信息结果
            let resultForUserInfo = await dynamicResourceCommand({
                ircsId: global.CONFIG.ICPId, // ICP信息系统的ID
                randVal: randValForUserInfo, // 生成的随机字符串
                pwdHash: pwdHashForUserInfo, // 用户信息的密码哈希
                command: encrypted, // 加密后的用户信息XML
                commandHash: encodeBase64String(
                    userInfoXml + messageAuthenticationKey
                ),
                commandType: 0, // 命令类型，0表示用户信息
                commandSequence: snowflake.nextId(), // 命令序列号，使用雪花算法生成的唯一ID
                encryptAlgorithm: 1, // 加密算法，1表示AES
                hashAlgorithm: 1, // 哈希算法，1表示MD5
                compressionFormat: 0, // 压缩格式，0表示不压缩
                commandVersion: 'v1.0', // 命令版本
            })

            if (!isUploaded) {
                // 在Redis的set中增加ICPMainNo，如果它还不在set中
                // isUploaded 0 未提交，1已提交
                await redis.sadd(redisSetKey, ICPMainNo)
            }

            let resourceInfoXml = `<?xml version="1.0" encoding="UTF-8"?>
<dynamicResource>
    <userResources>`
            resourceInfoXml += `<userId>${icpRecord.Id}</userId>`

            for (let webRecord of webRecords) {
                resourceInfoXml += `
    <domainInfo>
        <domain>${webRecord.Domain[0].Domain}</domain>
        <ownerTime>${moment(webRecord.UpdateTime * 1000).format(
            'YYYY-MM-DD HH:mm:ss'
        )}</ownerTime>
    </domainInfo>`
            }

            for (let webRecord of webRecords) {
                for (let ip of webRecord.IP) {
                    resourceInfoXml += `
        <ipInfo>
            <ip>${ip}</ip>
            <virtualId>${webRecord.CWebsiteId}</virtualId>
            <virtualType>0</virtualType>
            <ownerTime>${moment(webRecord.UpdateTime * 1000).format(
                'YYYY-MM-DD HH:mm:ss'
            )}</ownerTime>
        </ipInfo>`
                }
            }

            resourceInfoXml += `</userResources>
  <timeStamp>${moment(webRecords[0].UpdateTime * 1000).format(
      'YYYY-MM-DD HH:mm:ss'
  )}</timeStamp>
</dynamicResource>`

            // 生成随机字符串用于资源信息的密码哈希
            let randValForResourceInfo = generateRandVal()

            // 使用crypto库生成资源信息的密码哈希
            let pwdHashForResourceInfo = encodeBase64String(
                userPassword + randValForResourceInfo
            )

            // 将resourceInfoXml转换为base64格式
            let cipherForResourceInfo = createCipheriv(
                'aes-128-cbc',
                aesKey,
                aesIv
            )
            let encryptedForResourceInfo = cipherForResourceInfo.update(
                resourceInfoXml,
                'utf8',
                'base64'
            )
            encryptedForResourceInfo += cipherForResourceInfo.final('base64')

            let resultForResourceInfo = await dynamicResourceCommand({
                ircsId: global.CONFIG.ICPId, // ICP信息系统的ID
                randVal: randValForResourceInfo, // 生成的随机字符串
                pwdHash: pwdHashForResourceInfo, // 资源信息的密码哈希
                command: encryptedForResourceInfo, // 加密后的资源信息XML
                commandHash: encodeBase64String(
                    resourceInfoXml + messageAuthenticationKey
                ),
                commandType: 1, // 命令类型，1表示动态资源同步
                commandSequence: snowflake.nextId(), // 命令序列号，使用雪花算法生成的唯一ID
                encryptAlgorithm: 1, // 加密算法，1表示AES
                hashAlgorithm: 1, // 哈希算法，1表示MD5
                compressionFormat: 0, // 压缩格式，0表示不压缩
                commandVersion: 'v1.0', // 命令版本
            })

            resultForUserInfo = await parseSoapResult(resultForUserInfo)
            resultForResourceInfo = await parseSoapResult(resultForResourceInfo)

            let resultCodeForUserInfo =
                resultForUserInfo?.['return']?.['resultCode']?.[0] === '0'
            let resultCodeForResourceInfo =
                resultForResourceInfo?.['return']?.['resultCode']?.[0] === '0'
            if (
                Source === 'MQ' &&
                resultCodeForUserInfo &&
                resultCodeForResourceInfo
            ) {
                // 来源是MQ的记录
                // 使用Redis保存一次成功的记录做+1操作INCR，记录名用取今年的第几周，使用moment取出值。同时修改过期时间为1个月后
                let weekOfYear = moment().week() // 获取当前年份的第几周
                let recordName = `sync_icp_to_miit_record_${weekOfYear}` // 记录名
                await redis.incr(recordName) // 对记录进行+1操作
                await redis.expire(recordName, 60 * 60 * 24 * 30) // 设置过期时间为1个月后
                return
            }
            if (resultCodeForUserInfo && resultCodeForResourceInfo) {
                return self.cb(0)
            } else {
                // 如果用户信息的msg提示 [ 'userId已存在,新增失败' ]也当作成功，执行
                if (
                    resultForUserInfo?.['return']?.['msg']?.[0] ===
                    'userId已存在,新增失败'
                ) {
                    await redis.sadd(redisSetKey, ICPMainNo)
                    if (resultCodeForResourceInfo) {
                        return self.cb(0)
                    }
                }

                let msgForUserInfo = resultForUserInfo['return']['msg'][0]
                let msgForResourceInfo =
                    resultForResourceInfo['return']['msg'][0]
                return this.cb(0, {
                    Message: msgForUserInfo + ' ' + msgForResourceInfo,
                })
            }
        } catch (e) {
            console.log(e)
            if (e instanceof Error) {
                return self.eh({
                    err: e,
                    code: 67117,
                })
            }
            self.eh(e)
        }
    }
}

// 转换证件类型
function convertIdType(organizerLicenseTypeKey) {
    const licenseTypeMap = {
        '营业执照（个人或企业）': 1,
        居民身份证: 2,
        组织机构代码证: 3,
        事业单位法人证书: 4,
        部队代号: 5,
        社会团体法人登记证书: 6,
        护照: 7,
        // 其他类型映射...
    }
    const licenseTypeName = organizerLicenseType[organizerLicenseTypeKey]
    return licenseTypeMap[licenseTypeName] || 999
}

// 转换单位性质
function convertUnitNature(organizerTypeKey) {
    const unitNatureMap = {
        国防机构: 1,
        政府机关: 2,
        事业单位: 3,
        企业: 4,
        个人: 5,
        社会团体: 6,
        // 其他类型映射...
    }
    const unitNatureName = organizerType[organizerTypeKey]
    return unitNatureMap[unitNatureName] || 999
}

// 生成随机字符串
function generateRandVal(length = 20) {
    // return 'VfGZmNbuNQpwwPjyyDOf'

    let result = ''
    let characters =
        'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
    let charactersLength = characters.length
    for (let i = 0; i < length; i++) {
        result += characters.charAt(
            Math.floor(Math.random() * charactersLength)
        )
    }
    return result
}
function encodeBase64String(data) {
    const md5Hash = crypto.createHash('md5').update(data).digest('hex')
    const base64String = Buffer.from(md5Hash, 'utf-8').toString('base64')
    return base64String
}
function parseSoapResult(result) {
    return new Promise((resolve, reject) => {
        xml2js.parseString(result.out, (err, result) => {
            if (err) {
                reject(err)
            } else {
                resolve(result)
            }
        })
    })
}
