/*
 * @Date: 2023-05-23 13:47:35
 * @LastEditors: l<PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2023-06-08 11:53:56
 * @FilePath: /newicp/methods/admin/ICPInfo/GetICPInfoList.js
 */
const Method = require('../../../libs/method')
const { parseJSON } = require('../../../fns/kits')
const { ICPModel, ICPWebModel } = require('../../../models')
const { Op } = require('sequelize')
const _ = require('lodash')
const { ICPWebPlatInfoModel } = require('../../../mongoModels/icp')

module.exports = class GetICPInfoList extends Method {
    constructor(cb) {
        super(cb)
    }
    // 此接口 Name类的模糊搜索，其他均精确搜索
    async exec({
        Id,
        OrganizerName,
        OrganizerLicenseId,
        ICPMainNo,
        Name,
        ICPWebNo,
        Domain,
        IP,
        CompanyId,
        Offset,
        Limit,
    } = {}) {
        let self = this
        try {
            let icpCond = {},
                icpwebCond = {}
            if (Id) {
                icpCond.Id = Id
            }
            if (OrganizerName) {
                icpCond.OrganizerName = {
                    [Op.like]: `%${OrganizerName}%`,
                }
            }
            if (OrganizerLicenseId) {
                icpCond.OrganizerLicenseId = OrganizerLicenseId
            }
            if (ICPMainNo) {
                icpCond.ICPMainNo = ICPMainNo
            }
            if (CompanyId) {
                icpCond.CompanyId = CompanyId
            }
            if (Name) {
                icpwebCond.Name = {
                    [Op.like]: `%${Name}%`,
                }
            }
            if (ICPWebNo) {
                icpwebCond.ICPWebNo = ICPWebNo
            }
            if (Domain) {
                icpwebCond.Domain = {
                    [Op.like]: `%"${Domain}"%`,
                }
            }
            if (IP) {
                icpwebCond.IP = {
                    [Op.like]: `%${IP}%`,
                }
            }
            let { rows: ICPs, count: TotalCount } =
                await ICPModel.findAndCountAll({
                    where: icpCond,
                    offset: Offset || 0,
                    limit: Limit || 20,
                    distinct: true,
                    include: [
                        {
                            model: ICPWebModel,
                            as: 'Website',
                            where: icpwebCond,
                        },
                    ],
                })
            ICPs = parseJSON(ICPs)
            for (let icp of ICPs) {
                if (icp.Status === 0) {
                    // ICP State: 20：主体已删除
                    // Website State: 1：网站已注销、2：接入已注销
                    icp.Status =
                        _.findIndex(icp.Website, function (o) {
                            return o.Status !== 1 && o.Status !== 2
                        }) === -1
                            ? 20
                            : 0
                }
                icp.CanOperated =
                    icp.Status === 0 &&
                    _.findIndex(icp.Website, function (o) {
                        return o.Status > 4
                    }) === -1
                for (let website of icp.Website) {
                    website.CanOperated =
                        website.Status === 0 && [0, 8].includes(icp.Status)
                    if (website.InternetServiceType == 6) {
                        let platInfoList = await ICPWebPlatInfoModel.findOne(
                            { ICPWebId: website.Id },
                            { _id: 0, ICPWebId: 0 }
                        )
                        website.AppPlatformInformationList =
                            platInfoList?.AppPlatformInformationList || []
                    }
                }
            }
            return self.cb(0, { ICPs, TotalCount })
        } catch (e) {
            console.log(e)
            let err = new Error('查询备案信息出错')
            err.code = 10001
            self.err(err)
        }
    }
}
