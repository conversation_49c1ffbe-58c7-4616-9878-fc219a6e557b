const Method = require('../../../libs/method')
const { getTableModel, parseJSON } = require('../../../fns/kits')
const henganApi = require('../../../libs/henganApiPromise')
const { parse } = require('request/lib/cookies')
const _ = require('lodash')

module.exports = class DownloadICPInfoFromHengan extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({ CompanyId, ICPMainNo } = {}) {
        let self = this
        try {
            const icpDatabase = this.db.get('icp')
            const ICPModel = getTableModel('t_icp', icpDatabase)
            const ICPWeb = getTableModel('t_web', icpDatabase)
            const redis = this.redis.get()
            // 确定主体是否存在
            let mainNoICPLIST = await ICPModel.findAll({
                attributes: ['Status'],
                where: { ICPMainNo: ICPMainNo },
            })
            mainNoICPLIST = parseJSON(mainNoICPLIST)
            //备案存在，状态正常（Status:0),出错提示。注销状态（Status:1）,执行更新操作。
            if (mainNoICPLIST.length > 0) {
                const Status = mainNoICPLIST[0].Status
                if (Status === 0) {
                    throw new this.Err(new Error('备案已存在且状态正常'), 69000)
                } else if (Status === 1) {
                    await ICPModel.update(
                        {
                            Status: 0,
                            CompanyId: CompanyId,
                        },
                        {
                            where: {
                                ICPMainNo: ICPMainNo,
                            },
                        }
                    )
                    await redis.hdel('AwaitDistributeICP', ICPMainNo)
                    return self.cb(0)
                }
            } else {
                //备案不存在，t_icp插入记录
                let icpInfoInUcloud = await henganApi('SelectICPInterface', {
                    Keyword: ICPMainNo,
                    KeyWordType: 1,
                })
                if (
                    _.isEmpty(icpInfoInUcloud) ||
                    icpInfoInUcloud.RetCode !== 0
                ) {
                    throw new this.Err(new Error('查询恒安接口失败'), 69004)
                }
                if (icpInfoInUcloud.ICPInfos.length === 0) {
                    throw new this.Err(
                        new Error('在恒安未查到此ICP备案信息'),
                        69002
                    )
                }
                icpInfoInUcloud = icpInfoInUcloud.ICPInfos[0]
                const CMainId = icpInfoInUcloud.cMainId
                let result
                let webInsertList = []
                let mainInsertObject = {
                    ICPMainNo: ICPMainNo,
                    CompanyId: CompanyId,
                    Stauts: 0,
                    CMainId: CMainId,
                }
                let webObject
                let CId
                try {
                    //执行前，确认CMainId、 CWebsiteId是否全。如不全，则都不做插入
                    let websiteList = icpInfoInUcloud.websiteList
                    if (websiteList && websiteList.length > 0) {
                        let isAll = _.findIndex(websiteList, function (o) {
                            return _.isNumber(o.cWebsiteId) === false
                        })
                        //如果isAll的值不是-1 说明是有网站没有cWebsiteId
                        if (isAll !== -1 || !CMainId) {
                            mainInsertObject = {
                                ICPMainNo: ICPMainNo,
                                CompanyId: CompanyId,
                                Stauts: 0,
                            }
                            websiteList.map((item) => {
                                webObject = {
                                    ICPWebNo: item.phylicnum,
                                    Stauts: 0,
                                }
                                webInsertList.push(webObject)
                            })
                        } else {
                            websiteList.map((item) => {
                                webObject = {
                                    ICPWebNo: item.phylicnum,
                                    Stauts: 0,
                                    CWebsiteId: item.cWebsiteId,
                                }
                                webInsertList.push(webObject)
                            })
                        }
                        //插入主体信息
                        result = await ICPModel.create(mainInsertObject)
                        CId = result.Id
                        //批量插入网站信息
                        webInsertList = webInsertList.map((item) => {
                            item['MainId'] = CId
                            return item
                        })
                        await ICPWeb.bulkCreate(webInsertList)
                        await redis.hdel('AwaitDistributeICP', ICPMainNo)
                        return self.cb(0, { CId: CId ? true : false })
                    } else {
                        throw new this.Err(
                            new Error('恒安接口返回无网站'),
                            69006
                        )
                    }
                } catch (e) {
                    throw new this.Err(new Error(e), 69003)
                }
            }
        } catch (e) {
            if (e instanceof Error) {
                return self.eh({
                    err: e,
                    code: 69001,
                })
            }
            this.eh(e)
        }
    }
}
