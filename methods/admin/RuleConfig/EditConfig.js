/*
 * @Author: william.qian <EMAIL>
 * @Date: 2022-08-22 16:16:03
 * @LastEditors: william.qian <EMAIL>
 * @LastEditTime: 2023-10-08 15:42:17
 * @FilePath: /newicp/methods/admin/RuleConfig/EditConfig.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
/**
 * @file API EditConfig,编辑Mongo中的配置
 * <AUTHOR>
 * @return { } 返回符合成功与否
 * {
    "RetCode": 0
	}
 * 错误码:
 * "30201": "更新配置时发生其它异常",
 * "30202": "新建配置请输入配置名!",
 * "30203": "线上配置不允许更新",
 */

const Method = require('../../../libs/method')
const { insertManyAsync } = require('../../../fns/mongoFuns')
const ObjectId = require('mongodb').ObjectId
const moment = require('moment')
const { mongoResultCheck } = require('../../../fns/kits')

module.exports = class EditConfig extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({ Id, Config, Name, OriginalData } = {}) {
        let self = this
        let mongo = this.mongo.get('icp-config')
        let now = moment().format('X')

        const validatorConfigCollection = mongo.collection('validator')
        let result, changeObject
        try {
            // 是否有Id，没有则是新建
            // 更新与输入的Object

            changeObject = { Config, UnixTime: parseInt(now), IsDeleted: 0 }
            if (Name) {
                changeObject.Name = Name
            }

            if (OriginalData) {
                changeObject.OriginalData = OriginalData
            }

            if (!Id) {
                if (!Name) {
                    // 新建记录请输入模板名
                    return await Promise.reject(
                        new self.Err(new Error('新建配置时请输入配置名'), 30202)
                    )
                }

                if (/线上配置/.test(Name)) {
                    // 新建记录请输入模板名
                    return await Promise.reject(
                        new self.Err(new Error('线上配置不允许更新'), 30203)
                    )
                }
                // 写Mongo
                result = await insertManyAsync(validatorConfigCollection, [
                    changeObject,
                ])
            } else {
                // 更新，线上配置不可更新
                result = await validatorConfigCollection.updateOne(
                    { _id: ObjectId(Id), Name: { $not: /线上配置/ } },
                    { $set: changeObject }
                )
            }

            return this.cb(0)
        } catch (e) {
            console.log(e)
            if (e instanceof Error) {
                return self.eh({
                    err: e,
                    code: 30201,
                })
            }
            self.eh(e)
        }
    }
}
