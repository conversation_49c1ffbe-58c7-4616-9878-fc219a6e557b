/**
 * @file API DeleteConfig,删除配置，实际是把IsDelete设置为1
 * <AUTHOR>
 * @return {} 成功与否
 * {
    "RetCode": 0
	}
 * 错误码:
 * 30401: "获取Redis线上配置时网络异常",	
 * 30402: "获取Redis线上配置时结果解析异常",
 */

const Method = require('../../../libs/method')
const ObjectId = require('mongodb').ObjectId
const { mongoResultCheck } = require('../../../fns/kits')

module.exports = class DeleteConfig extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({ Id } = {}) {
        let self = this
        let mongo = this.mongo.get('icp-config')
        let validatorConfigCollection = mongo.collection('validator')

        let result
        try {
            result = await validatorConfigCollection.updateOne(
                {
                    _id: ObjectId(Id),
                },
                { $set: { IsDeleted: 1 } }
            )

            if (mongoResultCheck(result)) {
                return this.cb(0)
            } else {
                await Promise.reject(
                    new self.Err(new Error('Mongo返回结果异常'), 30402)
                )
            }
        } catch (e) {
            console.log(e)
            if (e instanceof Error) {
                return self.eh({
                    err: e,
                    code: 30401,
                })
            }
            self.eh(e)
        }
    }
}
