/**
 * @file API SetBlockRule,新增的屏蔽规则
 * <AUTHOR>
 * @return {} 返回符合条件的记录,格式如下
 * {
     "RetCode": 0
	}
 * 错误码:
 * "30301": "配置生效时遇到异常错误",
 * "30302": "线上配置格式化失败",
 * "30303": "写入Mongo失败",
 * "30304": "写入Mongo检查失败",
 * "30305": "写入Redis失败",
 */

const Method = require('../../../libs/method')
const { findAsync, insertManyAsync } = require('../../../fns/mongoFuns')
const ObjectId = require('mongodb').ObjectId
const { getKeyAsync } = require('../../../fns/redisFuns')
const { mongoResultCheck } = require('../../../fns/kits')
const moment = require('moment')
const stringRandom = require('string-random')

module.exports = class SetBlockRule extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({ Rule } = {}) {
        let self = this
        let mongo = this.mongo.get('icp-config')
        let redis = this.redis.get()

        const blockRuleCollection = mongo.collection('block_rule')
        let now = moment()

        let redisBlockReule, monGoRecordName, insertMongoResult
        try {
            // 取Redis中的配置
            redisBlockReule = await getKeyAsync(redis, 'blockRule')

            try {
                redisBlockReule = JSON.parse(redisBlockReule)
            } catch (error) {
                return await Promise.reject(new self.Err(error, 30902))
            }
            // 此处理可以考虑做配置做下验证
            console.log(redisBlockReule, 3333111)
            monGoRecordName =
                moment().format('YYYYMMDD') +
                stringRandom(3, { numbers: false })

            // 先写Mongo
            try {
                now = now.format('X')
                insertMongoResult = await insertManyAsync(blockRuleCollection, [
                    {
                        BlockReule: redisBlockReule,
                        Name: monGoRecordName,
                        UnixTime: parseInt(now),
                    },
                ])
            } catch (error) {
                return await Promise.reject(new self.Err(error, 30903))
            }
            console.log(insertMongoResult)
            // 如果没有插入成功
            if (
                insertMongoResult.acknowledged !== true ||
                insertMongoResult.insertedCount === 0
            ) {
                return await Promise.reject(
                    new self.Err(Error('数据插入失败'), 30304)
                )
            }

            //  如果成功，执行Redis更新
            await new Promise((resolve, reject) => {
                redis.set('blockRule', JSON.stringify(Rule), function (err) {
                    if (err) {
                        reject(new this.Err(err, 30305))
                    }
                    resolve(true)
                })
            })

            return this.cb(0)
        } catch (e) {
            if (e instanceof Error) {
                return self.eh({
                    err: e,
                    code: 30901,
                })
            }
            self.eh(e)
        }
    }
}
