/*
 * @Author: william.qian <EMAIL>
 * @Date: 2022-06-01 10:29:02
 * @LastEditors: william.qian <EMAIL>
 * @LastEditTime: 2024-01-03 14:27:15
 * @FilePath: /newicp/methods/admin/RuleConfig/EditRule.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
/**
 * @file API UpdateRule,编辑Mongo中的配置
 * <AUTHOR>
 * @return { } 返回符合成功与否
 * {
    "RetCode": 0
	}
 * 错误码:
 * "30201": "更新配置时发生其它异常",
 * "30202": "新建配置请输入配置名!",
 * "30203": "线上配置不允许更新",
 */

const Method = require('../../../libs/method')
const { insertManyAsync } = require('../../../fns/mongoFuns')
const ObjectId = require('mongodb').ObjectId
const moment = require('moment')
const { mongoResultCheck } = require('../../../fns/kits')

module.exports = class EditRule extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({ Id, Rule, __ssoUser, Name } = {}) {
        let self = this
        let mongo = this.mongo.get('icp-config')
        let now = moment().format('X')

        const ruleCollection = mongo.collection('rules')
        let result, changeObject
        try {
            // 是否有Id，没有则是新建
            // 更新与输入的Object

            changeObject = {
                Rule,
                UnixTime: parseInt(now),
                Operator: __ssoUser,
            }

            if (!Id) {
                changeObject.Name = Name
                    ? Name
                    : moment().format('YYMMDDhhmmss')

                // 写Mongo
                await insertManyAsync(ruleCollection, [changeObject])
            } else {
                await ruleCollection.updateOne(
                    { _id: ObjectId(Id) },
                    { $set: changeObject }
                )
            }
            // 更换了Mongo库，原有方式不需要
            return this.cb(0)
        } catch (e) {
            console.log(e)
            if (e instanceof Error) {
                return self.eh({
                    err: e,
                    code: 30201,
                })
            }
            self.eh(e)
        }
    }
}
