/**
 * @file API EditCommonConfig,编辑Mongo中的配置
 * <AUTHOR>
 * @return { } 返回符合成功与否
 * {
    "RetCode": 0,
    "Id" :""
	}
 * 错误码:
 * "30201": "更新配置时发生其它异常",
 * "30202": "新建配置请输入配置名!",
 */

const Method = require('../../../libs/method')
const { insertManyAsync } = require('../../../fns/mongoFuns')
const ObjectId = require('mongodb').ObjectId
const moment = require('moment')
const { mongoResultCheck } = require('../../../fns/kits')

module.exports = class EditCommonConfig extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({ Id, Config, Name, OriginalData } = {}) {
        let self = this
        let mongo = this.mongo.get('icp-config')
        let now = moment().format('X')
        const commonConfigCollection = mongo.collection('common_config')
        let result, changeObject

        delete Config.Action
        delete Config.RetCode
        try {
            // 是否有Id，没有则是新建
            // 更新与输入的Object
            console.log('Name', Name)
            changeObject = { Config, UnixTime: parseInt(now) }
            if (Name) {
                changeObject.Name = Name
            }

            if (OriginalData) {
                changeObject.OriginalData = OriginalData
            }
            let returnId
            if (!Id) {
                if (!Name) {
                    // 新建记录请输入模板名
                    return await Promise.reject(
                        new self.Err(new Error('新建配置时请输入配置名'), 30202)
                    )
                }
                // 写Mongo
                result = await insertManyAsync(commonConfigCollection, [
                    changeObject,
                ])
                returnId = result.insertedIds['0']
            } else {
                // 更新
                returnId = Id
                result = await commonConfigCollection.updateOne(
                    { _id: ObjectId(Id) },
                    { $set: changeObject }
                )
            }
            if (result.insertedCount === 1) {
                return this.cb(0, { Id: returnId })
            } else {
                await Promise.reject(
                    new self.Err(new Error('Mongo返回结果异常'), 30204)
                )
            }
        } catch (e) {
            console.log(e)
            if (e instanceof Error) {
                return self.eh({
                    err: e,
                    code: 30205,
                })
            }
            self.eh(e)
        }
    }
}
