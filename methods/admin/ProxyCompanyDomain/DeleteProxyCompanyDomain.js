'use strict'

const Method = require('../../../libs/method')
const { Op } = require('sequelize')
const { getTableModel, parseJSON } = require('../../../fns/kits')
const moment = require('moment')
const _ = require('lodash')

/**
 * 增加大客户与代理的白名单
 */
module.exports = class DeleteProxyCompanyDomain extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({ Id, __ssoUser, staff_name_en, Remark }) {
        let self = this

        try {
            const icpDatabase = this.db.get('icp')
            const DomainwhiteListModel = getTableModel(
                't_proxy_company_white_domain',
                icpDatabase
            )
            const DomainWriteActionListModel = getTableModel(
                't_proxy_company_domain_action',
                icpDatabase
            )

            let now = parseInt(moment().format('X'))

            // 查询记录是否存在

            let domainWhiteNameInfo = await DomainwhiteListModel.findOne({
                where: { Id },
            })

            if (domainWhiteNameInfo === null) {
                // 如果没查到出错
                return await Promise.reject(
                    new self.Err(new Error('不存在的待删除域名'), 67102)
                )
            }

            domainWhiteNameInfo = parseJSON(domainWhiteNameInfo)

            await Promise.all([
                DomainwhiteListModel.destroy({ where: { Id } }),
                DomainWriteActionListModel.create({
                    CompanyId: domainWhiteNameInfo.CompanyId,
                    CompanyName: domainWhiteNameInfo.CompanyName,
                    Domain: domainWhiteNameInfo.Domain,
                    Operator: __ssoUser || staff_name_en,
                    CreateTime: now,
                    UpdateTime: now,
                    ActionName: '删除白名单',
                    Remark,
                }),
            ])
            return this.cb(0)
        } catch (e) {
            console.log(e)
            if (e instanceof Error) {
                return self.eh({
                    err: e,
                    code: 67103,
                })
            }
            self.eh(e)
        }
    }
}
