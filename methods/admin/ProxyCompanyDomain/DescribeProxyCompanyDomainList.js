'use strict'

const Method = require('../../../libs/method')
const {
    getTableModel,
    parseJSON,
    getQueryObject,
} = require('../../../fns/kits')
const moment = require('moment')
const _ = require('lodash')
const { Op } = require('sequelize')
const { options } = require('request')

/**
 * 大客户与代理的白名单展示功能
 */
module.exports = class DescribeProxyCompanyDomainList extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({
        CompanyId,
        CompanyName,
        Domain,
        NeedAll,
        Limit,
        Offset,
        BeginTime,
        EndTime,
    }) {
        let self = this

        try {
            const icpDatabase = this.db.get('icp')
            const DomainwhiteListModel = getTableModel(
                't_proxy_company_white_domain',
                icpDatabase
            )

            let queryObject = getQueryObject({
                CompanyId,
                Domain,
                BeginTime,
                EndTime,
            })

            // 公司名支持模糊搜索
            if (CompanyName) {
                queryObject.CompanyName = { [Op.like]: `%${CompanyName}%` }
            }

            let options = {
                where: queryObject,
                order: [['id', 'desc']],
            }

            if (NeedAll !== true) {
                options.offset = Offset || 0
                options.limit = Limit || 20
            }

            let { count, rows } = await DomainwhiteListModel.findAndCountAll(
                options
            )

            rows = parseJSON(rows)

            return this.cb(0, { DomainList: rows, Count: count })
        } catch (e) {
            if (e instanceof Error) {
                return self.eh({
                    err: e,
                    code: 67100,
                })
            }
            self.eh(e)
        }
    }
}
