'use strict'

const Method = require('../../../libs/method')
const { Op } = require('sequelize')
const { getTableModel, parseJSON } = require('../../../fns/kits')
const moment = require('moment')
const _ = require('lodash')
const csv = require('csvtojson')
const axios = require('../../../libs/axiosApi')

/**
 * 增加大客户与代理的白名单
 */
module.exports = class AddProxyCompanyDomain extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({
        CompanyId,
        CompanyName,
        Domain,
        DomainList,
        __ssoUser,
        staff_name_en,
    }) {
        const producer = this.producer

        let self = this
        let domainRecord,
            resultInsertRows = [],
            logInsertRows = []
        let redis = this.redis.get()

        try {
            const icpDatabase = this.db.get('icp')
            const DomainwhiteListModel = getTableModel(
                't_proxy_company_white_domain',
                icpDatabase
            )
            const DomainWriteActionListModel = getTableModel(
                't_proxy_company_domain_action',
                icpDatabase
            )

            let now = parseInt(moment().format('X'))
            // 如果有DomainList,则做转换
            if (DomainList !== undefined) {
                try {
                    // 去掉头部信息与Base64转码
                    DomainList = DomainList.replace(
                        /^data:text\/\w+;base64,/,
                        ''
                    )
                    DomainList = new Buffer(DomainList, 'base64').toString()

                    domainRecord = await csv({ flatKeys: true })
                        .fromString(DomainList)
                        .subscribe((jsonObj) => {
                            console.log(jsonObj)
                            return Promise.resolve(jsonObj)
                        })

                    if (domainRecord.length === 0) {
                        // 不存在的待删除域名
                        throw new Error('解析后得到的域名列表为空')
                    }

                    let companyNameMap = {}

                    for (let index = 0; index < domainRecord.length; index++) {
                        const element = domainRecord[index]
                        // 确定公司名是否存在MAP(得到的Key为String)
                        if (!companyNameMap[element.CompanyId]) {
                            companyNameMap[element.CompanyId] =
                                await selectCompanyInfo(element.CompanyId)
                        }
                        resultInsertRows.push({
                            CompanyId: element.CompanyId,
                            Domain: element.Domain,
                            CompanyName: companyNameMap[element.CompanyId],
                            Operator: __ssoUser || staff_name_en,
                            CreateTime: now,
                            UpdateTime: now,
                        })
                        logInsertRows.push({
                            CompanyId: element.CompanyId,
                            CompanyName: companyNameMap[element.CompanyId],
                            Domain: element.Domain,
                            Operator: __ssoUser || staff_name_en,
                            CreateTime: now,
                            UpdateTime: now,
                            ActionName: '增加白名单',
                        })
                    }
                } catch (error) {
                    throw new this.Err(error, 67104)
                }
            } else {
                let companyName = await selectCompanyInfo(CompanyId)
                resultInsertRows.push({
                    CompanyId,
                    CompanyName: companyName,
                    Domain: Domain,
                    Operator: __ssoUser || staff_name_en,
                    CreateTime: now,
                    UpdateTime: now,
                })
                logInsertRows.push({
                    CompanyId,
                    CompanyName: companyName,
                    Domain: Domain,
                    Operator: __ssoUser || staff_name_en,
                    CreateTime: now,
                    UpdateTime: now,
                    ActionName: '增加白名单',
                })
            }

            // 创建记录
            try {
                await Promise.all([
                    DomainwhiteListModel.bulkCreate(resultInsertRows, {
                        validate: true,
                        ignoreDuplicates: true,
                    }),
                    DomainWriteActionListModel.bulkCreate(logInsertRows),
                ])
                await producer.send({
                    type: 'icp',
                    topic: 'SyncProxyCompanyWhiteToOtherRedis',
                    data: { Type: 0 },
                })
            } catch (error) {
                // 生成出错String
                let errorString = ''
                error.errors.forEach((element) => {
                    errorString =
                        errorString +
                        element.value +
                        ':' +
                        element.message +
                        ';'
                })
                return await Promise.reject(
                    new self.Err(new Error(errorString), 67105)
                )
            }

            // Todo推送MQ,同步域名到Redis

            return this.cb(0)
        } catch (e) {
            if (e instanceof Error) {
                return self.eh({
                    err: e,
                    code: 67101,
                })
            }
            self.eh(e)
        }
    }
}

// 得到通用的，需要查询的对象
async function selectCompanyInfo(CompanyId) {
    let options = {
        method: 'POST',
        url: global.CONFIG.compass.target + '?Action=GetCompanyInfoFuzzy',
        headers: {
            'Content-Type': 'application/json',
        },
        data: {
            CompanyId: CompanyId + '',
        },
        json: true,
    }
    let result = await axios(options)
    if (
        result.status !== 200 ||
        result.data.RetCode !== 0 ||
        result.data.Data.List.length === 0
    ) {
        let err = new Error(
            `账户查询公司信息失败，公司Id: ${CompanyId},错误信息：${
                result.status !== 200 ? result.status : result.data.Message
            }`
        )
        throw err
    }
    return result.data.Data.List[0].CompanyName
}
