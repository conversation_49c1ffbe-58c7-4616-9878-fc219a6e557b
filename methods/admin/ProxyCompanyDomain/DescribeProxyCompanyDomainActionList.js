'use strict'

const Method = require('../../../libs/method')
const {
    getTableModel,
    parseJSON,
    getQueryObject,
} = require('../../../fns/kits')
const moment = require('moment')
const _ = require('lodash')

/**
 * 大客户与代理的白名单展示功能
 */
module.exports = class DescribeProxyCompanyDomainActionList extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({ CompanyId, Domain, Limit, Offset }) {
        let self = this

        try {
            const icpDatabase = this.db.get('icp')
            const DomainwhiteActionModel = getTableModel(
                't_proxy_company_domain_action',
                icpDatabase
            )

            let queryObject = getQueryObject({ CompanyId, Domain })

            // 公司名支持模糊搜索
            let { count, rows } = await DomainwhiteActionModel.findAndCountAll({
                where: queryObject,
                order: [['id', 'desc']],
                offset: Offset || 0,
                limit: Limit || 20,
            })

            rows = parseJSON(rows)

            return this.cb(0, { ActionList: rows, Count: count })
        } catch (e) {
            console.log(e)
            if (e instanceof Error) {
                return self.eh({
                    err: e,
                    code: 67100,
                })
            }
            self.eh(e)
        }
    }
}
