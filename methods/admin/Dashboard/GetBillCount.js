/*
 * @Date: 2023-07-27 16:55:56
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2023-07-28 17:17:53
 * @FilePath: /newicp/methods/admin/Dashboard/GetBillCount.js
 */
'use strict'
const Method = require('../../../libs/method')
const moment = require('moment')
const _ = require('lodash')
const { BillListModel } = require('../../../mongoModels/icp')
// 根据入参查询公司的提交量与审核通过的量
// 限制，除非年维度的查询，否则时间跨度最长为1年(366gd )

module.exports = class GetBillCount extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({
        Type,
        BeginTime, // 查询的开始时间
        EndTime, // 查询的结束时间
        Step, // 步长 月or年
    }) {
        // 都根据订单的开始时间来计算，因为是属于当月的
        // 迁移的计费也会大于当月的第一天的时间 所以会被正确计算在内
        let self = this
        try {
            let info = await BillListModel.aggregate([
                {
                    $match: {
                        StartTime: { $gte: BeginTime, $lt: EndTime },
                    },
                },
                {
                    $project: {
                        _id: 0,
                        StartTime: 1,
                        Amount: 1,
                        Type: 1,
                        CompanyId: 1,
                    },
                },
            ])
            // 如果是按照公司类别的 或者账单类别的 需要将同一个时间的放在一组
            let result = {}
            switch (Type) {
                case 'Count':
                    info.forEach((i) => {
                        let date =
                            Step === 'MONTH'
                                ? moment(i.StartTime * 1000).format('YYYY-MM')
                                : moment(i.StartTime * 1000).format('YYYY')
                        if (result[date] === undefined) {
                            result[date] = 0
                        }
                        if (i.Amount) {
                            result[date] += i.Amount
                        }
                    })
                    break
                case 'Company':
                    info.forEach((i) => {
                        let date =
                            Step === 'MONTH'
                                ? moment(i.StartTime * 1000).format('YYYY-MM')
                                : moment(i.StartTime * 1000).format('YYYY')
                        if (result[date] === undefined) {
                            result[date] = {}
                        }
                        if (result[date][i.CompanyId] === undefined) {
                            result[date][i.CompanyId] = 0
                        }
                        if (i.Amount) {
                            result[date][i.CompanyId] += i.Amount
                        }
                    })
                    break
                case 'Type':
                    info.forEach((i) => {
                        let date =
                            Step === 'MONTH'
                                ? moment(i.StartTime * 1000).format('YYYY-MM')
                                : moment(i.StartTime * 1000).format('YYYY')
                        if (result[date] === undefined) {
                            result[date] = {}
                        }
                        if (result[date][i.Type] === undefined) {
                            result[date][i.Type] = 0
                        }
                        if (i.Amount) {
                            result[date][i.Type] += i.Amount
                        }
                    })
                    break
            }

            return this.cb(0, { DataSet: result })
        } catch (e) {
            self.err(e)
        }
    }
}
