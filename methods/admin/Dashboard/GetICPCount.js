'use strict'
const Method = require('../../../libs/method')
const { parseJSON } = require('../../../fns/kits')
const {
    ICPModel,
    ICPStatusEnum,
    ICPWebModel,
    WebStatusEnum,
} = require('../../../models')
const { Op } = require('sequelize')

// 返回备案相关的各中信息分类的计数
module.exports = class GetICPCount extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec() {
        let self = this
        let redis = this.redis.get()
        try {
            let [
                NormalCount, // 备案正常
                LogoutCount, // 备案注销
                LogoutingCount, // 注销中
                ICPChangingCount, // 变更备案中
                MainChangingCount, // 主体变更中
                NormalWebCount, // 有效备案网站数
                NormalDomainCount, // 有效备案域名数
            ] = await Promise.all([
                ICPModel.count({
                    where: {
                        Status: ICPStatusEnum.Normal,
                    },
                }),
                ICPModel.count({
                    where: {
                        Status: ICPStatusEnum.Logout,
                    },
                }),
                ICPModel.count({
                    where: {
                        Status: ICPStatusEnum.Logouting,
                    },
                }),
                ICPModel.count({
                    where: {
                        Status: ICPStatusEnum.Icpchanging,
                    },
                }),
                ICPModel.count({
                    where: {
                        Status: ICPStatusEnum.Mainchanging,
                    },
                }),
                ICPWebModel.count({
                    where: {
                        Status: {
                            [Op.notIn]: [
                                WebStatusEnum.Connlogout,
                                WebStatusEnum.Weblogout,
                            ],
                        },
                    },
                }),
                redis.hlen(`domain_icp_in_ucloud_map`),
            ])

            return this.cb(0, {
                DataSet: {
                    NormalCount,
                    LogoutCount,
                    LogoutingCount,
                    ICPChangingCount,
                    MainChangingCount,
                    NormalWebCount,
                    NormalDomainCount,
                },
            })
        } catch (e) {
            console.log(e)
            if (e instanceof Error) {
                return self.eh({
                    err: e,
                    code: 67119,
                })
            }
            self.eh(e)
        }
    }
}
