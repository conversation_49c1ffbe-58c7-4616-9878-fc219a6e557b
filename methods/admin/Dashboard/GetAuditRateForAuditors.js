'use strict'
const Method = require('../../../libs/method')
const { parseJSON, getTableModel } = require('../../../fns/kits')
const { Op, fn, col } = require('sequelize')
const _ = require('lodash')
const icpDatabase = require('../../../libs/mysql').get('icp')
const historyModel = getTableModel('t_order_history', icpDatabase)
const {
    unificationDate,
    timeBaseCheck,
} = require('../../../fns/dashboard_fns/index')
const operatorList = global.CONFIG.operatorList

// 根据入参查询审核员的审核通过率
// 限制，除非年维度的查询，否则时间跨度最长为1年(366gd )
module.exports = class GetAuditRateForAuditors extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({
        BeginTime, // 查询的开始时间
        EndTime, // 查询的结束时间
        Step, // 步长
    }) {
        let self = this
        try {
            timeBaseCheck(BeginTime, EndTime, Step)
            let rows = await historyModel.findAll({
                attributes: [
                    [fn('Substring', col(`operation_time`), 1, 10), 'Date'],
                    [fn('COUNT', col(`id`)), 'Count'],
                    'Operator',
                    'Status',
                ],
                group: [[fn(Step, col(`date`))], 'Operator', 'Status'],
                where: {
                    Status: [8, 4],
                    Action: 'modify_order',
                    Operator: operatorList, // 暂时代码中取，长期更新可以考虑从Redis中取
                    operation_time: {
                        [Op.between]: [BeginTime * 1000, EndTime * 1000],
                    },
                },
            })

            rows = parseJSON(rows)
            // 数据日期取第一次出现的时间，可能不是一周、一月的开始时间，因此需要统一
            rows = unificationDate(rows, Step)

            // 按日期分组,先构造结构
            let result = {}
            let dataList = _.map(rows, 'Date')
            for (const dataListElement of dataList) {
                result[dataListElement] = {}
            }

            _.forEach(result, function (value, key) {
                // 生成各审核员的数据
                for (const operatorElement of operatorList) {
                    // 统计2项取值
                    let thisOperatorResolveCount =
                        _.find(rows, {
                            Operator: operatorElement,
                            Date: key,
                            Status: 8,
                        })?.Count || 0
                    let thisOperatorRejectCount =
                        _.find(rows, {
                            Operator: operatorElement,
                            Date: key,
                            Status: 4,
                        })?.Count || 0
                    // 汇总
                    result[key][operatorElement] = {
                        ResolveCount: thisOperatorResolveCount,
                        RejectCount: thisOperatorRejectCount,
                        TotalCount:
                            thisOperatorRejectCount + thisOperatorResolveCount,
                        Rate:
                            thisOperatorResolveCount /
                                (thisOperatorRejectCount +
                                    thisOperatorResolveCount) || 0,
                    }
                }
                // 完成通过与驳回的合计数据
                let thisALLResolveCount = _.filter(rows, {
                    Date: key,
                    Status: 8,
                })

                thisALLResolveCount = _.sum(_.map(thisALLResolveCount, 'Count'))

                let thisAllRejectCount = _.filter(rows, {
                    Date: key,
                    Status: 4,
                })
                thisAllRejectCount = _.sum(_.map(thisAllRejectCount, 'Count'))

                result[key]['ALL'] = {
                    ResolveCount: thisALLResolveCount,
                    RejectCount: thisAllRejectCount,
                    TotalCount: thisAllRejectCount + thisALLResolveCount,
                    Rate:
                        thisALLResolveCount /
                            (thisALLResolveCount + thisAllRejectCount) || 0,
                }
            })

            return this.cb(0, { DataSet: result })
        } catch (e) {
            console.log(e)
            if (e instanceof Error) {
                return self.eh({
                    err: e,
                    code: 67113,
                })
            }
            self.eh(e)
        }
    }
}
