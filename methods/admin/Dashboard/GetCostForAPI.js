'use strict'
const Method = require('../../../libs/method')
const { parseJSON, getTableModel } = require('../../../fns/kits')
const { Op, fn, col } = require('sequelize')
const _ = require('lodash')
const icpDatabase = require('../../../libs/mysql').get('icp')
let verifyLogModel = getTableModel('t_verify_log', icpDatabase)
const {
    unificationDate,
    sumThisDayAllTypeAmount,
} = require('../../../fns/dashboard_fns/index')
const { timeBaseCheck } = require('../../../fns/dashboard_fns')

// 根据入参查询公司的提交量与审核通过的量
// 限制，除非年维度的查询，否则时间跨度最长为1年(366gd )

module.exports = class GetCostForAPI extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({
        BeginTime, // 查询的开始时间
        EndTime, // 查询的结束时间
        Step, // 步长
    }) {
        let self = this
        try {
            timeBaseCheck(BeginTime, EndTime, Step)

            let allQuery = {
                CreateTime: {
                    [Op.between]: [BeginTime, EndTime],
                },
                Type: [2, 4, 5, 7, 10], // 收费接口
            }

            let fkQuery = JSON.parse(JSON.stringify(allQuery))
            fkQuery.CompanyId = 34278
            fkQuery.CreateTime = {
                [Op.between]: [BeginTime, EndTime],
            }

            let groupCondition = [[fn(Step, col(`Date`))], 'Type']
            if (Step === 'WEEK') {
                groupCondition = [[fn(Step, col(`Date`), 1)], 'Type'] // 如是周维度，默认周日为每周的第一天，加上参数1，调整为周一为每周的第一天
            }

            let [allList, fkList] = await Promise.all([
                verifyLogModel.findAll({
                    attributes: [
                        [
                            fn('from_unixtime', col(`create_time`), '%Y-%m-%d'),
                            'Date',
                        ],
                        [fn('COUNT', col(`Id`)), 'Count'],
                        'Type',
                    ],
                    group: groupCondition,
                    where: allQuery,
                }),
                verifyLogModel.findAll({
                    attributes: [
                        [
                            fn('from_unixtime', col(`create_time`), '%Y-%m-%d'),
                            'Date',
                        ],
                        [fn('COUNT', col(`Id`)), 'Count'],
                        'Type',
                    ],
                    group: groupCondition,
                    where: fkQuery,
                }),
            ])

            allList = parseJSON(allList)
            fkList = parseJSON(fkList)

            // 数据日期取第一次出现的时间，可能不是一周、一月的开始时间，因此需要统一
            allList = unificationDate(allList, Step)
            fkList = unificationDate(fkList, Step)

            // 按日期分组,先构造结构，然后汇总金额数据
            let result = {}
            let dataList = _.map(allList, 'Date')
            for (const dataListElement of dataList) {
                result[dataListElement] = {}
                // 取出数据
                result[dataListElement]['ALL'] = sumThisDayAllTypeAmount(
                    _.filter(allList, {
                        Date: dataListElement,
                    })
                )
                result[dataListElement]['34278'] = sumThisDayAllTypeAmount(
                    _.filter(fkList, {
                        Date: dataListElement,
                    })
                )
                result[dataListElement]['Rato'] =
                    result[dataListElement]['34278'] /
                    result[dataListElement]['ALL']
            }

            return this.cb(0, { DataSet: result })
        } catch (e) {
            console.log(e)
            if (e instanceof Error) {
                return self.eh({
                    err: e,
                    code: 67119,
                })
            }
            self.eh(e)
        }
    }
}
