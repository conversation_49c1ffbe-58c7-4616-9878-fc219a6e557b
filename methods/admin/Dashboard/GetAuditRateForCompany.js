'use strict'
const Method = require('../../../libs/method')
const { parseJSON } = require('../../../fns/kits')
const { Op, fn, col } = require('sequelize')
const _ = require('lodash')
const { model: OrderModel } = require('../../../models/t_order')
const {
    unificationDate,
    timeBaseCheck,
} = require('../../../fns/dashboard_fns/index')

// 根据入参查询公司的提交量与审核通过的量
// 限制，除非年维度的查询，否则时间跨度最长为1年(366gd )

module.exports = class GetAuditRateForCompany extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({
        BeginTime, // 查询的开始时间
        EndTime, // 查询的结束时间
        Step, // 步长
        CompanyId, // 公司Id
    }) {
        let self = this
        try {
            timeBaseCheck(BeginTime, EndTime, Step)

            let otherQuery = {
                CreateTime: {
                    [Op.between]: [BeginTime, EndTime],
                },
                Type: [1, 2, 3, 7, 8, 9], //  只记新增与变更
            }

            if (CompanyId) {
                otherQuery.CompanyId = CompanyId
            }

            let finishQuery = JSON.parse(JSON.stringify(otherQuery))
            finishQuery.Status = 12

            finishQuery.CreateTime = {
                [Op.between]: [BeginTime, EndTime],
            }

            // 查询其它时，不包括完成的
            otherQuery.Status = { [Op.notIn]: [12] }

            let groupCondition = [[fn(Step, col(`Date`))]]
            if (Step === 'WEEK') {
                groupCondition = [[fn(Step, col(`Date`), 1)]]
            }

            let [otherInfo, finishInfo] = await Promise.all([
                OrderModel.findAll({
                    attributes: [
                        [
                            fn('from_unixtime', col(`create_time`), '%Y-%m-%d'),
                            'Date',
                        ],
                        [fn('COUNT', col(`Id`)), 'Count'],
                    ],
                    group: groupCondition,
                    where: otherQuery,
                }),
                OrderModel.findAll({
                    attributes: [
                        [
                            fn('from_unixtime', col(`create_time`), '%Y-%m-%d'),
                            'Date',
                        ],
                        [fn('COUNT', col(`Id`)), 'Count'],
                    ],
                    group: groupCondition,
                    where: finishQuery,
                }),
            ])

            otherInfo = parseJSON(otherInfo)
            finishInfo = parseJSON(finishInfo)

            // 数据日期取第一次出现的时间，可能不是一周、一月的开始时间，因此需要统一
            console.log(otherInfo, finishInfo)

            otherInfo = unificationDate(otherInfo, Step)
            finishInfo = unificationDate(finishInfo, Step)
            console.log(otherInfo, finishInfo)

            // 按日期分组,先构造结构
            let result = {}
            let dataList = _.map(otherInfo, 'Date')
            for (const dataListElement of dataList) {
                result[dataListElement] = {}
                result[dataListElement].Resolved =
                    _.find(finishInfo, { Date: dataListElement })?.Count || 0
                result[dataListElement].Other =
                    _.find(otherInfo, { Date: dataListElement })?.Count || 0
                result[dataListElement].Rate =
                    result[dataListElement].Resolved /
                    (result[dataListElement].Resolved +
                        result[dataListElement].Other)
            }

            return this.cb(0, { DataSet: result })
        } catch (e) {
            console.log(e)
            if (e instanceof Error) {
                return self.eh({
                    err: e,
                    code: 67117,
                })
            }
            self.eh(e)
        }
    }
}
