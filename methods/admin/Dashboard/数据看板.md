# 备案侧数据展示

标签： 通过率 看板 数据

---

统计数据

---

### GetAuditRateForAuditors

查询UCloud审核率

请求参数

| 字段        | 类型     | 必填  | 描述                                     |
|-----------|--------|-----|----------------------------------------|
| Action    | String | Yes | GetAuditRateForAuditors                |
| BeginTime | Int    | Yes | 按时间查询，开始的时间                            |
| EndTime   | Int    | Yes | 按时间查询，结束的时间                            |
| Step      | String | Yes | 步长周期取值['DAY', 'WEEK', 'MONTH', 'YEAR'] |

返回值

| 字段      | 类型            | 描述                      |
|---------|---------------|-------------------------|
| Action  | String        | GetAuditRateForAuditors |
| DataSet | Array[Object] | 配置列表                    |
| RetCode | Int           | 状态码                     |

数据字段含义

| 字段           | 类型  | 描述  |
|--------------|-----|-----|
| ResolveCount | Int | 通过量 |
| TotalCount   | Int | 审核量 |
| Rate         | Int | 通过率 |

```JSON
{
  "DataSet": {
    "2021-01-04": {
      "yan.meng": {
        "ResolveCount": 13,
        "RejectCount": 17,
        "TotalCount": 30,
        "Rate": 0.43333333333333335
      },
      "di.kang": {
        "ResolveCount": 9,
        "RejectCount": 19,
        "TotalCount": 28,
        "Rate": 0.32142857142857145
      },
      "jeff.li": {
        "ResolveCount": 13,
        "RejectCount": 7,
        "TotalCount": 20,
        "Rate": 0.65
      },
      "ALL": {
        "ResolveCount": 35,
        "RejectCount": 43,
        "TotalCount": 78,
        "Rate": 0.44871794871794873
      }
    },
    "2021-01-05": {
      "yan.meng": {
        "ResolveCount": 13,
        "RejectCount": 9,
        "TotalCount": 22,
        "Rate": 0.5909090909090909
      },
      "di.kang": {
        "ResolveCount": 10,
        "RejectCount": 8,
        "TotalCount": 18,
        "Rate": 0.5555555555555556
      },
      "jeff.li": {
        "ResolveCount": 1,
        "RejectCount": 3,
        "TotalCount": 4,
        "Rate": 0.25
      },
      "ALL": {
        "ResolveCount": 24,
        "RejectCount": 20,
        "TotalCount": 44,
        "Rate": 0.5454545454545454
      }
    },
    "2021-01-06": {
      "yan.meng": {
        "ResolveCount": 11,
        "RejectCount": 14,
        "TotalCount": 25,
        "Rate": 0.44
      },
      "di.kang": {
        "ResolveCount": 12,
        "RejectCount": 5,
        "TotalCount": 17,
        "Rate": 0.7058823529411765
      },
      "jeff.li": {
        "ResolveCount": 7,
        "RejectCount": 6,
        "TotalCount": 13,
        "Rate": 0.5384615384615384
      },
      "ALL": {
        "ResolveCount": 30,
        "RejectCount": 25,
        "TotalCount": 55,
        "Rate": 0.5454545454545454
      }
    },
    "2021-01-07": {
      "yan.meng": {
        "ResolveCount": 11,
        "RejectCount": 5,
        "TotalCount": 16,
        "Rate": 0.6875
      },
      "di.kang": {
        "ResolveCount": 15,
        "RejectCount": 1,
        "TotalCount": 16,
        "Rate": 0.9375
      },
      "jeff.li": {
        "ResolveCount": 3,
        "RejectCount": 4,
        "TotalCount": 7,
        "Rate": 0.42857142857142855
      },
      "ALL": {
        "ResolveCount": 29,
        "RejectCount": 10,
        "TotalCount": 39,
        "Rate": 0.7435897435897436
      }
    },
    "2021-01-08": {
      "yan.meng": {
        "ResolveCount": 0,
        "RejectCount": 0,
        "TotalCount": 0,
        "Rate": 0
      },
      "di.kang": {
        "ResolveCount": 14,
        "RejectCount": 28,
        "TotalCount": 42,
        "Rate": 0.3333333333333333
      },
      "jeff.li": {
        "ResolveCount": 0,
        "RejectCount": 0,
        "TotalCount": 0,
        "Rate": 0
      },
      "ALL": {
        "ResolveCount": 14,
        "RejectCount": 28,
        "TotalCount": 42,
        "Rate": 0.3333333333333333
      }
    },
    "2021-01-09": {
      "yan.meng": {
        "ResolveCount": 2,
        "RejectCount": 7,
        "TotalCount": 9,
        "Rate": 0.2222222222222222
      },
      "di.kang": {
        "ResolveCount": 0,
        "RejectCount": 0,
        "TotalCount": 0,
        "Rate": 0
      },
      "jeff.li": {
        "ResolveCount": 3,
        "RejectCount": 4,
        "TotalCount": 7,
        "Rate": 0.42857142857142855
      },
      "ALL": {
        "ResolveCount": 5,
        "RejectCount": 11,
        "TotalCount": 16,
        "Rate": 0.3125
      }
    }
  },
  "Action": "GetAuditRateForAuditorsResponse",
  "RetCode": 0
}

```

 ---

### GetAuditRateForSupervision

查询审核员提交到管局审核的率

请求参数

| 字段        | 类型     | 必填  | 描述                                     |
|-----------|--------|-----|----------------------------------------|
| Action    | String | Yes | GetAuditRateForSupervision             |
| BeginTime | Int    | Yes | 按时间查询，开始的时间                            |
| EndTime   | Int    | Yes | 按时间查询，结束的时间                            |
| Step      | String | Yes | 步长周期取值['DAY', 'WEEK', 'MONTH', 'YEAR'] |

返回值

| 字段      | 类型            | 描述                         |
|---------|---------------|----------------------------|
| Action  | String        | GetAuditRateForSupervision |
| DataSet | Array[Object] | 配置列表                       |
| RetCode | Int           | 状态码                        |

```JSON
{
  "DataSet": {
    "2021-02-18": {
      "yan.meng": {
        "ResolveCount": 31,
        "RejectCount": 4,
        "TotalCount": 35,
        "Rate": 0.8857142857142857
      },
      "di.kang": {
        "ResolveCount": 25,
        "RejectCount": 7,
        "TotalCount": 32,
        "Rate": 0.78125
      },
      "jeff.li": {
        "ResolveCount": 9,
        "RejectCount": 0,
        "TotalCount": 9,
        "Rate": 1
      },
      "ALL": {
        "ResolveCount": 65,
        "RejectCount": 11,
        "TotalCount": 76,
        "Rate": 0.8552631578947368
      }
    },
    "2021-02-19": {
      "yan.meng": {
        "ResolveCount": 23,
        "RejectCount": 22,
        "TotalCount": 45,
        "Rate": 0.5111111111111111
      },
      "di.kang": {
        "ResolveCount": 16,
        "RejectCount": 16,
        "TotalCount": 32,
        "Rate": 0.5
      },
      "jeff.li": {
        "ResolveCount": 0,
        "RejectCount": 8,
        "TotalCount": 8,
        "Rate": 0
      },
      "ALL": {
        "ResolveCount": 39,
        "RejectCount": 46,
        "TotalCount": 85,
        "Rate": 0.4588235294117647
      }
    },
    "2021-02-20": {
      "yan.meng": {
        "ResolveCount": 0,
        "RejectCount": 10,
        "TotalCount": 10,
        "Rate": 0
      },
      "di.kang": {
        "ResolveCount": 0,
        "RejectCount": 3,
        "TotalCount": 3,
        "Rate": 0
      },
      "jeff.li": {
        "ResolveCount": 0,
        "RejectCount": 6,
        "TotalCount": 6,
        "Rate": 0
      },
      "ALL": {
        "ResolveCount": 0,
        "RejectCount": 19,
        "TotalCount": 19,
        "Rate": 0
      }
    },
    "2021-02-21": {
      "yan.meng": {
        "ResolveCount": 10,
        "RejectCount": 2,
        "TotalCount": 12,
        "Rate": 0.8333333333333334
      },
      "di.kang": {
        "ResolveCount": 16,
        "RejectCount": 1,
        "TotalCount": 17,
        "Rate": 0.9411764705882353
      },
      "jeff.li": {
        "ResolveCount": 6,
        "RejectCount": 1,
        "TotalCount": 7,
        "Rate": 0.8571428571428571
      },
      "ALL": {
        "ResolveCount": 32,
        "RejectCount": 4,
        "TotalCount": 36,
        "Rate": 0.8888888888888888
      }
    },
    "2021-02-22": {
      "yan.meng": {
        "ResolveCount": 10,
        "RejectCount": 13,
        "TotalCount": 23,
        "Rate": 0.43478260869565216
      },
      "di.kang": {
        "ResolveCount": 6,
        "RejectCount": 2,
        "TotalCount": 8,
        "Rate": 0.75
      },
      "jeff.li": {
        "ResolveCount": 2,
        "RejectCount": 12,
        "TotalCount": 14,
        "Rate": 0.14285714285714285
      },
      "ALL": {
        "ResolveCount": 18,
        "RejectCount": 27,
        "TotalCount": 45,
        "Rate": 0.4
      }
    },
    "2021-02-23": {
      "yan.meng": {
        "ResolveCount": 7,
        "RejectCount": 32,
        "TotalCount": 39,
        "Rate": 0.1794871794871795
      },
      "di.kang": {
        "ResolveCount": 20,
        "RejectCount": 6,
        "TotalCount": 26,
        "Rate": 0.7692307692307693
      },
      "jeff.li": {
        "ResolveCount": 6,
        "RejectCount": 19,
        "TotalCount": 25,
        "Rate": 0.24
      },
      "ALL": {
        "ResolveCount": 33,
        "RejectCount": 57,
        "TotalCount": 90,
        "Rate": 0.36666666666666664
      }
    },
    "2021-02-24": {
      "yan.meng": {
        "ResolveCount": 38,
        "RejectCount": 20,
        "TotalCount": 58,
        "Rate": 0.6551724137931034
      },
      "di.kang": {
        "ResolveCount": 24,
        "RejectCount": 9,
        "TotalCount": 33,
        "Rate": 0.7272727272727273
      },
      "jeff.li": {
        "ResolveCount": 9,
        "RejectCount": 9,
        "TotalCount": 18,
        "Rate": 0.5
      },
      "ALL": {
        "ResolveCount": 71,
        "RejectCount": 38,
        "TotalCount": 109,
        "Rate": 0.6513761467889908
      }
    }
  },
  "Action": "GetAuditRateForSupervisionResponse",
  "RetCode": 0
}

```

 ---

### GetAuditRateForCompany

查询总体，或者单位个公司的管局审核通过率

注意：除非按年查询，否则最长日期不能超过1年

请求参数

| 字段        | 类型     | 必填  | 描述                                     |
|-----------|--------|-----|----------------------------------------|
| Action    | String | Yes | GetAuditRateForCompany                 |
| BeginTime | Int    | Yes | 按时间查询，开始的时间                            |
| EndTime   | Int    | Yes | 按时间查询，结束的时间                            |
| Step      | String | Yes | 步长周期取值['DAY', 'WEEK', 'MONTH', 'YEAR'] |
| CompanyId | Int    | NO  | 公司Id                                   |

返回值

| 字段      | 类型            | 描述                     |
|---------|---------------|------------------------|
| Action  | String        | GetAuditRateForCompany |
| DataSet | Array[Object] | 配置列表                   |
| RetCode | Int           | 状态码                    |

```JSON

{
  "Action": "GetAuditRateForCompany",
  "DataSet": {
    "2022-05-01": {
      "Other": 1,
      "Resolved": 2,
      "Rate:": 0.3
    },
    "2022-05-08": {
      "Other": 1,
      "Resolved": 2,
      "Rate:": 0.3
    },
    "2022-05-15": {
      "Other": 1,
      "Resolved": 2,
      "Rate:": 0.3
    }
  },
  "RetCode": 0
}

```

---

### GetCostForAPI

查询接口成本

请求参数

| 字段        | 类型     | 必填  | 描述                                     |
|-----------|--------|-----|----------------------------------------|
| Action    | String | Yes | GetCostForAPI                          |
| BeginTime | Int    | Yes | 按时间查询，开始的时间                            |
| EndTime   | Int    | Yes | 按时间查询，结束的时间                            |
| Step      | String | Yes | 步长周期取值['DAY', 'WEEK', 'MONTH', 'YEAR'] |
返回值

| 字段      | 类型            | 描述            |
|---------|---------------|---------------|
| Action  | String        | GetCostForAPI |
| DataSet | Array[Object] | 配置列表          |
| RetCode | Int           | 状态码           |

```JSON
{
    "DataSet": {
        "2021-02-18": {
            "34278": 0,
            "ALL": 11.8,
            "Rato": 0
        },
        "2021-02-19": {
            "34278": 0,
            "ALL": 16.6,
            "Rato": 0
        },
        "2021-02-20": {
            "34278": 0,
            "ALL": 14.000000000000002,
            "Rato": 0
        },
        "2021-02-22": {
            "34278": 0,
            "ALL": 80.80000000000001,
            "Rato": 0
        },
        "2021-02-23": {
            "34278": 0.4,
            "ALL": 167.8,
            "Rato": 0.0023837902264600714
        },
        "2021-02-24": {
            "34278": 0.8,
            "ALL": 222.3,
            "Rato": 0.003598740440845704
        }
    },
    "Action": "GetCostForAPIResponse",
    "RetCode": 0
}
```