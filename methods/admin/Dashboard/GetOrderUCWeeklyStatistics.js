const Method = require('../../../libs/method')
const { OrderModel, OrderHistoryModel, OrderTypeEnum } = require('../../../models')
const { Op } = require('sequelize')
const moment = require('moment')

// 工具函数：环比
function calcRate(current, prev) {
  if (prev === 0) return current === 0 ? 0 : 1
  return Number(((current - prev) / prev).toFixed(2))
}

// 新统计区间函数：上周五~本周四
function getStatRange(offset = 0) {
  // offset=0: 本期（上周五~本周四），-1: 上期（上上周五~上周四）
  const now = moment();
  const thisFriday = now.clone().isoWeekday(5); // isoWeekday(5) 周五
  
  const lastFriday = thisFriday.clone().subtract(7, 'days');
  const thisThursday = thisFriday.clone().subtract(1, 'days');
  // 区间起止
  const start = lastFriday.clone().add(offset * 7, 'days').startOf('day');
  const end = thisThursday.clone().add(offset * 7, 'days').endOf('day');
  return {
    start: start.unix(),
    end: end.unix(),
    label: `${start.format('YYYY-MM-DD')} ~ ${end.format('YYYY-MM-DD')}`
  }
}

module.exports = class GetOrderUCWeeklyStatistics extends Method {
  constructor(cb) {
    super(cb)
  }

  async exec() {
    let self = this
    try {
      // 1. 计算本期、上期区间
      const thisPeriod = getStatRange(0)
      const lastPeriod = getStatRange(-1)

      // 2. 查询本期、上期订单
      const [thisOrders, lastOrders] = await Promise.all([
        OrderModel.findAll({
          where: {
            CreateTime: { [Op.between]: [thisPeriod.start, thisPeriod.end] },
            Status: { [Op.gt]: 1 }
          },
          attributes: ['OrderNo', 'CompanyId', 'Status', 'Type', 'CreateTime'],
          raw: true
        }),
        OrderModel.findAll({
          where: {
            CreateTime: { [Op.between]: [lastPeriod.start, lastPeriod.end] },
            Status: { [Op.gt]: 1 }
          },
          attributes: ['OrderNo', 'CompanyId', 'Status', 'Type', 'CreateTime'],
          raw: true
        })
      ])

      // 3. 分类统计
      function stat(orders) {
        const isFanke = o => Number(o.CompanyId) === 34278
        const isZhike = o => Number(o.CompanyId) !== 34278
        const isAdd = o => [1,2,3].includes(Number(o.Type))
        const isChange = o => [7,8,9,10].includes(Number(o.Type))
        return {
          add: orders.filter(isAdd).length,
          addFanke: orders.filter(o => isAdd(o) && isFanke(o)).length,
          addZhike: orders.filter(o => isAdd(o) && isZhike(o)).length,
          change: orders.filter(isChange).length,
          changeFanke: orders.filter(o => isChange(o) && isFanke(o)).length,
          changeZhike: orders.filter(o => isChange(o) && isZhike(o)).length,
        }
      }
      const thisStat = stat(thisOrders)
      const lastStat = stat(lastOrders)

      // 4. UCloud审核总量（复审通过+打回）
      const [thisAudit, lastAudit] = await Promise.all([
        OrderHistoryModel.count({
          where: {
            Action: 'modify_order',
            Status: { [Op.in]: [8,4] },
            OperationTime: { [Op.between]: [thisPeriod.start, thisPeriod.end] }
          }
        }),
        OrderHistoryModel.count({
          where: {
            Action: 'modify_order',
            Status: { [Op.in]: [8,4] },
            OperationTime: { [Op.between]: [lastPeriod.start, lastPeriod.end] }
          }
        })
      ])

      // 5. 组装返回
      const data = {
        Period: thisPeriod.label,
        NewOrderCount: thisStat.add,
        NewOrderWoW: calcRate(thisStat.add, lastStat.add),
        NewOrderCountFanke: thisStat.addFanke,
        NewOrderWoWFanke: calcRate(thisStat.addFanke, lastStat.addFanke),
        NewOrderCountDirect: thisStat.addZhike,
        NewOrderWoWDirect: calcRate(thisStat.addZhike, lastStat.addZhike),
        ChangeOrderCount: thisStat.change,
        ChangeOrderWoW: calcRate(thisStat.change, lastStat.change),
        ChangeOrderCountFanke: thisStat.changeFanke,
        ChangeOrderWoWFanke: calcRate(thisStat.changeFanke, lastStat.changeFanke),
        ChangeOrderCountDirect: thisStat.changeZhike,
        ChangeOrderWoWDirect: calcRate(thisStat.changeZhike, lastStat.changeZhike),
        UcloudAuditCount: thisAudit,
        UcloudAuditWoW: calcRate(thisAudit, lastAudit)
      }
      return self.cb(0, { Data: data })
    } catch (e) {
      console.error('GetOrderUCWeeklyStatistics error:', e)
      return self.cb(11000, { Message: '数据库查询失败' })
    }
  }
} 