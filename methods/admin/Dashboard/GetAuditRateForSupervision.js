'use strict'
const Method = require('../../../libs/method')
const { parseJSON } = require('../../../fns/kits')
const { Op, fn, col } = require('sequelize')
const _ = require('lodash')
const moment = require('moment')
const { model: dashAuditModel } = require('../../../models/t_dash_audit')

// 根据入参查询审核员到管局的审核通过率
module.exports = class GetAuditRateForSupervision extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({
        BeginTime, // 查询的开始时间
        EndTime, // 查询的结束时间
        Step, // 步长
    }) {
        let self = this
        try {
            // 1. 时间验证
            validateTimeParams(BeginTime, EndTime)

            // 2. 根据Step调整查询时间范围
            const { queryBeginTime, queryEndTime } = adjustTimeRangeByStep(BeginTime, EndTime, Step)

            // 3. 从数据库中获取指定时间范围内的审核员列表
            const operatorRows = await dashAuditModel.findAll({
                attributes: [[fn('DISTINCT', col('Operator')), 'Operator']],
                where: {
                    Date: {
                        [Op.between]: [
                            moment.unix(queryBeginTime).toDate(),
                            moment.unix(queryEndTime).toDate()
                        ],
                    },
                },
                raw: true
            })
            const operatorList = operatorRows.map(row => row.Operator).filter(op => op !== 'ALL')

            // 4. 查询数据
            let rows = await dashAuditModel.findAll({
                attributes: [
                    [fn('DATE', col('Date')), 'Date'],
                    'RejectCount',
                    'ResolveCount',
                    'Operator',
                ],
                where: {
                    Operator: operatorList,
                    Date: {
                        [Op.between]: [
                            moment.unix(queryBeginTime).toDate(),
                            moment.unix(queryEndTime).toDate()
                        ],
                    },
                },
                order: [['Date', 'ASC']],
                raw: true
            })

            rows = parseJSON(rows)

            // 5. 数据处理
            rows = rows.map((row) => {
                row.RejectCount = parseInt(row.RejectCount) || 0
                row.ResolveCount = parseInt(row.ResolveCount) || 0
                return row
            })

            // 6. 根据Step处理日期分组
            const result = processDataByStep(rows, operatorList, Step)

            return this.cb(0, { DataSet: result })
        } catch (e) {
            console.log(e)
            if (e instanceof Error) {
                return self.eh({
                    err: e,
                    code: 67118,
                })
            }
            self.eh(e)
        }
    }
}

// 根据Step处理数据分组
function processDataByStep(rows, operatorList, Step) {
    let result = {}

    switch (Step) {
        case 'DAY':
            // 按日分组数据
            const dateGroups = _.groupBy(rows, 'Date')
            Object.keys(dateGroups).forEach(date => {
                result[date] = generatePeriodData(dateGroups[date], operatorList)
            })
            break

        case 'WEEK':
            // 按周分组数据
            const weekGroups = {}
            rows.forEach(row => {
                const weekStart = moment(row.Date).startOf('week').format('YYYY-MM-DD')
                if (!weekGroups[weekStart]) {
                    weekGroups[weekStart] = []
                }
                weekGroups[weekStart].push(row)
            })

            Object.keys(weekGroups).forEach(weekStart => {
                result[weekStart] = generatePeriodData(weekGroups[weekStart], operatorList)
            })
            break

        case 'MONTH':
            // 按月分组数据
            const monthGroups = {}
            rows.forEach(row => {
                const monthStart = moment(row.Date).startOf('month').format('YYYY-MM-DD')
                if (!monthGroups[monthStart]) {
                    monthGroups[monthStart] = []
                }
                monthGroups[monthStart].push(row)
            })

            Object.keys(monthGroups).forEach(monthStart => {
                result[monthStart] = generatePeriodData(monthGroups[monthStart], operatorList)
            })
            break

        case 'YEAR':
            // 按年分组数据
            const yearGroups = {}
            rows.forEach(row => {
                const yearStart = moment(row.Date).startOf('year').format('YYYY-MM-DD')
                if (!yearGroups[yearStart]) {
                    yearGroups[yearStart] = []
                }
                yearGroups[yearStart].push(row)
            })

            Object.keys(yearGroups).forEach(yearStart => {
                result[yearStart] = generatePeriodData(yearGroups[yearStart], operatorList)
            })
            break

        default:
            throw {
                err: new Error('不支持的步长类型'),
                code: 67121,
            }
    }

    return result
}

// 生成时间段数据（通用函数，适用于日/周/月/年）
function generatePeriodData(periodRows, operatorList) {
    const result = {}

    // 为每个审核员生成数据
    operatorList.forEach(operator => {
        // 按审核员分组并汇总该时间段的数据
        const operatorRows = periodRows.filter(row => row.Operator === operator)
        const resolveCount = operatorRows.reduce((sum, row) => sum + row.ResolveCount, 0)
        const rejectCount = operatorRows.reduce((sum, row) => sum + row.RejectCount, 0)
        const totalCount = resolveCount + rejectCount

        result[operator] = {
            ResolveCount: resolveCount,
            RejectCount: rejectCount,
            TotalCount: totalCount,
            Rate: totalCount > 0 ? resolveCount / totalCount : 0
        }
    })

    // 生成ALL汇总数据
    const totalResolveCount = periodRows.reduce((sum, row) => sum + row.ResolveCount, 0)
    const totalRejectCount = periodRows.reduce((sum, row) => sum + row.RejectCount, 0)
    const totalCount = totalResolveCount + totalRejectCount

    result['ALL'] = {
        ResolveCount: totalResolveCount,
        RejectCount: totalRejectCount,
        TotalCount: totalCount,
        Rate: totalCount > 0 ? totalResolveCount / totalCount : 0
    }

    return result
}

// 时间验证函数
function validateTimeParams(BeginTime, EndTime) {
    // 验证BeginTime是否早于2025年7月29日 和李田沟通，之前的通过率他们有记录，从2025年7月29日开始算起即可
    const august2025 = moment('2025-07-29').unix()
    if (BeginTime < august2025) {
        throw {
            err: new Error('历史数据不能查询，开始时间不能早于2025年7月29日'),
            code: 67119,
        }
    }

    // 验证BeginTime不能是当天或大于当天
    const today = moment().startOf('day').unix()
    if (BeginTime >= today) {
        throw {
            err: new Error('开始时间不能是当天或大于当天时间'),
            code: 67120,
        }
    }

    // 基本时间验证
    if (BeginTime > EndTime) {
        throw {
            err: new Error('开始时间不能大于结束时间'),
            code: 67112,
        }
    }
}

// 根据Step调整查询时间范围
function adjustTimeRangeByStep(BeginTime, EndTime, Step) {
    let queryBeginTime = BeginTime
    let queryEndTime = EndTime

    const beginMoment = moment.unix(BeginTime)
    const endMoment = moment.unix(EndTime)

    switch (Step) {
        case 'DAY':
            // 日查询不需要调整
            break
        case 'WEEK':
            // 周查询：从BeginTime所在周的开始到EndTime所在周的结束
            queryBeginTime = beginMoment.startOf('week').unix()
            queryEndTime = endMoment.endOf('week').unix()
            break
        case 'MONTH':
            // 月查询：从BeginTime所在月的开始到EndTime所在月的结束
            queryBeginTime = beginMoment.startOf('month').unix()
            queryEndTime = endMoment.endOf('month').unix()
            break
        case 'YEAR':
            // 年查询：从BeginTime所在年的开始到EndTime所在年的结束
            queryBeginTime = beginMoment.startOf('year').unix()
            queryEndTime = endMoment.endOf('year').unix()
            break
        default:
            throw {
                err: new Error('不支持的步长类型'),
                code: 67121,
            }
    }

    return { queryBeginTime, queryEndTime }
}
