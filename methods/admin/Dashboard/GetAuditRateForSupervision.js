'use strict'
const Method = require('../../../libs/method')
const { parseJSON } = require('../../../fns/kits')
const { Op, fn, col } = require('sequelize')
const _ = require('lodash')
const { model: dashAuditModel } = require('../../../models/t_dash_audit')
const {
    unificationDate,
    timeBaseCheck,
} = require('../../../fns/dashboard_fns/index')

const operatorList = global.CONFIG.operatorList // 目前需要查询的审核员

// 根据入参查询审核员到管局的审核通过率
// 限制，除非年维度的查询，否则时间跨度最长为1年(366gd )
module.exports = class GetAuditRateForSupervision extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({
        BeginTime, // 查询的开始时间
        EndTime, // 查询的结束时间
        Step, // 步长
    }) {
        let self = this
        try {
            timeBaseCheck(BeginTime, EndTime, Step)

            let rows = await dashAuditModel.findAll({
                attributes: [
                    [fn('Substring', col(`date`), 1, 10), 'Date'],
                    [fn('SUM', col(`reject_count`)), 'RejectCount'],
                    [fn('SUM', col(`resolve_count`)), 'ResolveCount'],
                    'Operator',
                ],
                group: [[fn(Step, col(`date`))], 'operator'],

                where: {
                    Operator: operatorList, // 暂时代码中取，长期更新可以考虑从Redis中取
                    Date: {
                        [Op.between]: [BeginTime * 1000, EndTime * 1000],
                    },
                },
            })

            rows = parseJSON(rows)
            // 数据日期取第一次出现的时间，可能不是一周、一月的开始时间，因此需要统一

            rows = rows.map((row) => {
                row.RejectCount = parseInt(row.RejectCount) || 0
                row.ResolveCount = parseInt(row.ResolveCount) || 0
                return row
            })
            rows = unificationDate(rows, Step)
            console.log(rows)
            // 按日期分组,先构造结构
            let result = {}
            let dataList = _.map(rows, 'Date')
            for (const dataListElement of dataList) {
                result[dataListElement] = {}
            }

            _.forEach(result, function (value, key) {
                // 生成各审核员的数据
                for (const operatorElement of operatorList) {
                    // 统计2项取值
                    let thisOperatorCount = _.find(rows, {
                        Operator: operatorElement,
                        Date: key,
                    })
                    console.log(thisOperatorCount, operatorElement, key)
                    let thisOperatorResolveCount =
                        thisOperatorCount?.ResolveCount || 0
                    let thisOperatorRejectCount =
                        thisOperatorCount?.RejectCount || 0
                    // 汇总
                    result[key][operatorElement] = {
                        ResolveCount: thisOperatorResolveCount,
                        RejectCount: thisOperatorRejectCount,
                        TotalCount:
                            thisOperatorRejectCount + thisOperatorResolveCount,
                        Rate:
                            thisOperatorResolveCount /
                                (thisOperatorRejectCount +
                                    thisOperatorResolveCount) || 0,
                    }
                }
                // 完成通过与驳回的合计数据
                let thisALLCount = _.filter(rows, {
                    Date: key,
                })

                let thisALLResolveCount = _.sum(
                    _.map(thisALLCount, 'ResolveCount')
                )

                let thisAllRejectCount = _.sum(
                    _.map(thisALLCount, 'RejectCount')
                )

                result[key]['ALL'] = {
                    ResolveCount: thisALLResolveCount,
                    RejectCount: thisAllRejectCount,
                    TotalCount: thisAllRejectCount + thisALLResolveCount,
                    Rate:
                        thisALLResolveCount /
                            (thisALLResolveCount + thisAllRejectCount) || 0,
                }
            })

            return this.cb(0, { DataSet: result })
        } catch (e) {
            console.log(e)
            if (e instanceof Error) {
                return self.eh({
                    err: e,
                    code: 67118,
                })
            }
            self.eh(e)
        }
    }
}
