/*
 * @Date: 2023-04-27 14:34:35
 * @LastEditors: l<PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2023-04-28 09:57:58
 * @FilePath: /newicp/methods/admin/Dashboard/GetOrderCount.js
 */
'use strict'
const Method = require('../../../libs/method')
const { parseJSON } = require('../../../fns/kits')
const {
    OrderModel,
    OrderStatusEnum,
    OrderTypeEnum,
    OrderHistoryModel,
} = require('../../../models')
const { Op } = require('sequelize')

// 返回备案相关的各中信息分类的计数
module.exports = class GetOrderCount extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({ BeginTime, EndTime } = {}) {
        let self = this
        let redis = this.redis.get()
        try {
            let rangeInfo = await OrderHistoryModel.findAll({
                attributes: ['Id', 'Status', 'Operator'],
                where: {
                    Action: 'modify_order',
                    Status: {
                        [Op.in]: [
                            OrderStatusEnum.ReAuditPass,
                            OrderStatusEnum.AuditRefuse,
                            OrderStatusEnum.GovReturnCache,
                            OrderStatusEnum.GovAuditPass,
                        ],
                    }, // ucloud审核通过记录
                    OperationTime: {
                        [Op.in]: [BeginTime, EndTime],
                    },
                },
            })
            rangeInfo = parseJSON(rangeInfo)
            let Audit = { Total: 0 }, // ucloud审核总计数 包含通过和打回
                AuditRefuse = { Total: 0 }, // ucloud审核拒绝计数，并记录审核员详细
                ReAuditPass = { Total: 0 }, // ucloud审核通过计数，并记录审核员详细
                GovReturnCache = { Total: 0 }, // 管局退回暂存计数
                GovAuditPass = { Total: 0 } // 管局审核通过计数
            let GovAuditPassRate = '0%', // 管局审核通过率 = 管局通过数量/（管局通过数量+管局退回数量）*100% （不够整出四舍五入保留2位）指定时间周期内，取管局通过率平均值。
                AuditRate = '0%' // UCloud审核通过率 = UCloud通过次数/（UCloud通过次数+UCloud退回次数）*100%   （不够整出四舍五入保留2位，管局审核通过后的订单才可计算）。指定时间周期内，取全部管局通过订单的订单通过率平均值。
            rangeInfo.forEach((info) => {
                if (info.Status === OrderStatusEnum.ReAuditPass) {
                    // ucloud 审核通过
                    // 总审核量计数
                    if (Audit[info.Operator] === undefined) {
                        Audit[info.Operator] = 0
                    }
                    Audit.Total += 1
                    Audit[info.Operator] += 1

                    // 审核通过量计数
                    if (ReAuditPass[info.Operator] === undefined) {
                        ReAuditPass[info.Operator] = 0
                    }
                    ReAuditPass[info.Operator] += 1
                    ReAuditPass.Total += 1
                }
                if (info.Status === OrderStatusEnum.AuditRefuse) {
                    // ucloud审核拒绝
                    if (Audit[info.Operator] === undefined) {
                        Audit[info.Operator] = 0
                    }
                    Audit.Total += 1
                    Audit[info.Operator] += 1

                    // 审核拒绝量计数
                    if (AuditRefuse[info.Operator] === undefined) {
                        AuditRefuse[info.Operator] = 0
                    }
                    AuditRefuse[info.Operator] += 1
                    AuditRefuse.Total += 1
                }
                if (info.Status === OrderStatusEnum.GovAuditPass) {
                    // 管局审核通过订单
                    GovAuditPass.Total += 1
                }
                if (info.Status === OrderStatusEnum.GovReturnCache) {
                    // 管局退回暂存
                    GovReturnCache.Total += 1
                }
            })
            GovAuditPassRate =
                (
                    GovAuditPass.Total /
                    (GovAuditPass.Total + GovReturnCache.Total)
                ).toFixed(2) * 100 || 0
            GovAuditPassRate = `${GovAuditPassRate}%`
            AuditRate = (ReAuditPass.Total / Audit.Total).toFixed(2) * 100 || 0
            AuditRate = `${AuditRate}%`
            return this.cb(0, {
                Audit,
                AuditRefuse,
                ReAuditPass,
                GovReturnCache,
                GovAuditPass,
                GovAuditPassRate,
                AuditRate,
            })
        } catch (e) {
            console.log(e)
            if (e instanceof Error) {
                return self.eh({
                    err: e,
                    code: 67119,
                })
            }
            self.eh(e)
        }
    }
}
