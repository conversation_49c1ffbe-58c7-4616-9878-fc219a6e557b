const Method = require('../../../libs/method')
const { getTableModel, parseJSON } = require('../../../fns/kits')
const { Op } = require('sequelize')
const axios = require('../../../libs/axiosApi')
const _ = require('lodash')

/**
 * 同步CID到恒安
 * 主要是针对变更和注销类
 * 变更备案 变更接入 变更主体 变更网站
 * 取消接入 注销网站
 * 不支持 新增 主体 和新增网站 和新增接入使用 因为新增类的  恒安不验证CID 是审核通过后才会生成 并同步CID
 */

module.exports = class SyncCIDToHengan extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({ OrderNo, ICPMainNo } = {}) {
        try {
            let icpDatabase = this.db.get('icp')
            let ICPModel = getTableModel('t_icp', icpDatabase)
            let ICPWebModel = getTableModel('t_web', icpDatabase)
            let OrderModel = getTableModel('t_order', icpDatabase)
            let OrderWebModel = getTableModel('t_order_web', icpDatabase)

            const [OrderInfo, OrderWebInfo] = await CheckAndGetOrder(
                OrderModel,
                OrderWebModel,
                OrderNo,
                ICPMainNo
            )

            const [ICPInfo, ICPWebInfo] = await CheckAndGetICP(
                ICPModel,
                ICPWebModel,
                ICPMainNo
            )

            // 查看是否备案中的CID 有-1的， 若有，重新取值，并同步到恒安 若无，取现有的值，同步到恒安
            /**CIDChange结构
             * {
             * change: true  // true说明有变化  主体和网站任一变化都为 true
             * ICP: {ICPMainNo: CMainId}  //如果ICP = ''说明主体没变化
             * ICPWeb: {ICPWebNo: CWebsiteId, ICPMainNo: CWebsiteId} //如果没有变更 ICPWeb数组为空
             * }
             */
            const [CIDChange, RequestParams] =
                await CheckCIDChangeAndGetRequestParams(
                    OrderWebModel,
                    ICPInfo,
                    ICPWebInfo,
                    OrderInfo,
                    OrderWebInfo
                )

            // 调用内部封装的恒安接口请求 同步CID
            const options = {
                method: 'POST',
                url: global.CONFIG.verifyAPI.epicEntPersonRelationUrl,
                headers: {
                    'Content-Type': 'application/json',
                },
                data: RequestParams,
                timeout: 50 * 1000,
            }
            const result = await axios(options)

            if (result.data?.RetCode === 0 && CIDChange.change) {
                //同步到恒安成功，更新本地的 备案以及 订单中的CID
                await UpdateCID(
                    ICPModel,
                    ICPWebModel,
                    OrderModel,
                    OrderWebModel,
                    OrderInfo,
                    OrderWebInfo,
                    ICPInfo,
                    ICPWebInfo,
                    CIDChange
                )
            } else if (CIDChange.change === false) {
                return this.cb(0, { Message: 'no change' })
            } else if (result.data?.RetCode !== 0) {
                return this.cb(result.data?.RetCode, {
                    Message: result.data?.Message,
                })
            }
            return this.cb(0)
        } catch (e) {
            console.log(e)
            if (e instanceof Error) {
                return this.eh({
                    err: e,
                    code: 32704,
                })
            }
            this.eh(e)
        }
    }
}

async function CheckAndGetOrder(OrderModel, OrderWebModel, OrderNo, ICPMainNo) {
    let [OrderInfo, OrderWebInfo] = await Promise.all([
        OrderModel.findAll({
            where: {
                OrderNo,
                IsDeleted: 0,
            },
        }),
        OrderWebModel.findAll({
            where: {
                OrderNo,
                IsDeleted: 0,
            },
        }),
    ])
    OrderInfo = parseJSON(OrderInfo)
    OrderWebInfo = parseJSON(OrderWebInfo)

    // 判断订单是否存在
    if (OrderInfo.length === 0) {
        let error = new Error('not found this Order,OrderNo=' + OrderNo)
        error.code = 32701
        throw error
    }
    // 检查订单类型，不支持新增类
    if (OrderInfo[0].Type < 4) {
        let error = new Error(
            'This type is not supported。type: ' + OrderInfo[0].Type
        )
        error.code = 32702
        throw error
    }
    return [OrderInfo, OrderWebInfo]
}

async function CheckAndGetICP(ICPModel, ICPWebModel, ICPMainNo) {
    let ICPInfo = await ICPModel.findAll({
        where: {
            ICPMainNo,
            Status: {
                [Op.notIn]: [1, 20], //主体 状态不是 注销 或者删除
            },
        },
    })

    ICPInfo = parseJSON(ICPInfo)

    if (ICPInfo.length === 0) {
        let error = new Error('icp not exit')
        error.code = 32703
        throw error
    }

    let ICPWebInfo = await ICPWebModel.findAll({
        where: {
            MainId: ICPInfo[0].Id,
            Status: {
                [Op.notIn]: [1, 2], //不要 已注销  或者已取消接入的网站
            },
        },
    })

    ICPWebInfo = parseJSON(ICPWebInfo)

    return [ICPInfo, ICPWebInfo]
}

async function CheckCIDChangeAndGetRequestParams(
    OrderWebModel,
    ICPInfo,
    ICPWebInfo,
    OrderInfo,
    OrderWebInfo
) {
    const RequestParams = {
        Action: 'SyncSetCId',
        ICPMainNo: ICPInfo[0].ICPMainNo,
        CMainId: '',
        Websites: [],
    }

    // 记录变化
    let CIDChange = {
        change: false,
        ICP: {},
        ICPWeb: {},
    }
    if (ICPInfo[0].CMainId <= 0) {
        // 不管什么类型的订单 都取 t_order的Id
        RequestParams.CMainId = OrderInfo[0].Id

        //  记录变化
        CIDChange.change = true
        CIDChange.ICP[ICPInfo[0].ICPMainNo] = OrderInfo[0].Id
    } else {
        if (ICPInfo[0].CMainId !== OrderInfo[0].CMainId) {
            // 对应 备案信息和订单信息中不一致的情况，以主体的为准
            CIDChange.change = true
            CIDChange.ICP[ICPInfo[0].ICPMainNo] = ICPInfo[0].CMainId
        }
        RequestParams.CMainId = ICPInfo[0].CMainId
    }

    ICPWebInfo.forEach(async (ICPWeb) => {
        if (ICPWeb.CWebsiteId <= 0) {
            // 如果没有CWebsiteId，根据订单类型 获取合适的 CWebsiteId 若订单不是该网站，则新建一个Id
            let insertInfo, NewCWebsiteId
            switch (OrderInfo[0].Type) {
                case 4:
                case 8:
                    //注销主体 变更主体 没有可用的ID   需要创建网站订单，获取一个id，然后再删除订单。。。 获取一个不重复的Id 作为CWebsiteId
                    insertInfo = await OrderWebModel.create({
                        ICPWebNo: ICPWeb.ICPWebNo,
                    })

                    NewCWebsiteId = parseJSON(insertInfo).Id

                    //记录变化
                    // 用完后删除。。。
                    await OrderWebModel.destroy({
                        where: {
                            Id: NewCWebsiteId,
                        },
                    })
                    break
                case 5:
                case 6:
                    //注销网站 取消接入 --- 只有t_order,没有t_order_web 所以，使用的是t_order中的id来更新，order中的c_website_id
                    if (OrderInfo.ICPWebNo === ICPWeb.ICPWebNo) {
                        //说明是 该正在注销网站的订单 需要重新获取 CWebsiteId
                        NewCWebsiteId = OrderInfo[0].Id
                    } else {
                        // 是其他 的网站 需要同步CID 那么就需要重新生成了 。。。
                        insertInfo = await OrderWebModel.create({
                            ICPWebNo: ICPWeb.ICPWebNo,
                        })

                        NewCWebsiteId = parseJSON(insertInfo).Id

                        // 用完后删除。。。
                        await OrderWebModel.destroy({
                            where: {
                                Id: NewCWebsiteId,
                            },
                        })
                    }
                    break
                case 7:
                    //变更备案
                    NewCWebsiteId = _.filter(OrderWebInfo, {
                        ICPWebNo: ICPWeb.ICPWebNo,
                    })[0].Id

                    break
                case 9:
                case 10:
                    //变更网站 --只同步网站的 所以CMainId 拿原来的
                    if (OrderWebInfo[0].ICPWebNo === ICPWeb.ICPWebNo) {
                        NewCWebsiteId = OrderWebInfo[0].Id
                    } else {
                        // 如果不是该网站，那么就只能创建 获取id 再删除了
                        insertInfo = await OrderWebModel.create({
                            ICPWebNo: ICPWeb.ICPWebNo,
                        })

                        NewCWebsiteId = parseJSON(insertInfo).Id
                        // 用完后删除。。。
                        await OrderWebModel.destroy({
                            where: {
                                Id: NewCWebsiteId,
                            },
                        })
                    }
                    break
            }

            RequestParams.Websites.push({
                ICPWebNo: ICPWeb.ICPWebNo,
                CWebsiteId: NewCWebsiteId,
                CConnectId: NewCWebsiteId * 100,
            })

            // 记录变化
            CIDChange.change = true
            CIDChange.ICPWeb[ICPWeb.ICPWebNo] = NewCWebsiteId
        } else {
            //处理订单和备案的cid不一致情况，以备案为准 不管order 还是order_web只要有cid不一致，就标记该备案号，按照备案记录中的id来更新
            const order = _.filter(OrderInfo, { ICPWebNo: ICPWeb.ICPWebNo })
            const orderweb = _.filter(OrderWebInfo, {
                ICPWebNo: ICPWeb.ICPWebNo,
            })
            if (
                (order.length !== 0 &&
                    order[0].CWebsiteId !== ICPWeb.CWebsiteId) ||
                (orderweb.length !== 0 &&
                    orderweb[0].CWebsiteId !== ICPWeb.CWebsiteId)
            ) {
                CIDChange.change = true
                CIDChange.ICPWeb[ICPWeb.ICPWebNo] = ICPWeb.CWebsiteId
            }

            RequestParams.Websites.push({
                ICPWebNo: ICPWeb.ICPWebNo,
                CWebsiteId: ICPWeb.CWebsiteId,
                CConnectId: ICPWeb.CWebsiteId * 100,
            })
        }
    })

    return [CIDChange, RequestParams]
}

/**更新订单和备案中的CID
 * CIDChange结构
 * {
 * change: true  // true说明有变化  主体和网站任一变化都为 true
 * ICP: {ICPMainNo: CMainId}  //如果ICP = ''说明主体没变化
 * ICPWeb:{ICPWebNo: CWebsiteId, ICPMainNo: CWebsiteId} //如果没有变更 ICPWeb数组为空
 * }
 */
async function UpdateCID(
    ICPModel,
    ICPWebModel,
    OrderModel,
    OrderWebModel,
    OrderInfo,
    OrderWebInfo,
    ICPInfo,
    ICPWebInfo,
    CIDChange
) {
    let ICPMainNo, CMainId
    if (JSON.stringify(CIDChange.ICP) !== '{}') {
        // 更新 ICP ICPWeb 和 Order Order_web
        ICPMainNo = Object.keys(CIDChange.ICP)[0]
        CMainId = Object.values(CIDChange.ICP)[0]
        //更新主体的CID  不用考虑 只要主体CID变更了 该order表中 一定要变更
        let icp = _.filter(ICPInfo, { ICPMainNo })
        let order = _.filter(OrderInfo, { ICPMainNo })
        await Promise.all([
            ICPModel.update(
                { CMainId, IsSyncedCid: 1 },
                {
                    where: {
                        Id: icp[0].Id,
                    },
                }
            ),
            order.length > 0
                ? OrderModel.update(
                      {
                          CMainId,
                      },
                      {
                          where: {
                              Id: order[0].Id,
                          },
                      }
                  )
                : Promise.resolve(1),
        ])
    }
    // 网站这里就比较复杂了，，，，因为取消接入 和 注销网站 类型的订单 的CWebsiteId  在t_order表中， 取消接入 和 注销网站不生成t_order_web表
    let ICPWebNos
    if (JSON.stringify(CIDChange.ICPWeb) !== '{}') {
        const queryArray = []
        ICPWebNos = Object.keys(CIDChange.ICPWeb)

        ICPWebNos.forEach((ICPWebNo) => {
            // 只要网站有更改就一定要  修改icp_web
            let icpweb = _.filter(ICPWebInfo, { ICPWebNo })
            queryArray.push(
                ICPWebModel.update(
                    { CWebsiteId: CIDChange.ICPWeb[ICPWebNo], IsSyncedCid: 1 },
                    {
                        where: {
                            Id: icpweb[0].Id,
                        },
                    }
                )
            )

            // 修改t_order 或者t_order_web 根据订单来决定
            // 判断当前订单 是否涉及了 该备案号
            let order = _.filter(OrderInfo, { ICPWebNo })
            if (order.length > 0) {
                // 说明在t_order中有记录，更新t_order
                queryArray.push(
                    OrderModel.update(
                        { CWebsiteId: CIDChange.ICPWeb[ICPWebNo] },
                        {
                            where: {
                                Id: order[0].Id,
                            },
                        }
                    )
                )
            }
            let orderWeb = _.filter(OrderWebInfo, { ICPWebNo })
            if (orderWeb.length > 0) {
                // 说明在t_order_web中有使用， 更新t_order_web
                queryArray.push(
                    OrderWebModel.update(
                        { CWebsiteId: CIDChange.ICPWeb[ICPWebNo] },
                        {
                            where: {
                                Id: orderWeb[0].Id,
                            },
                        }
                    )
                )
            }
        })

        await Promise.all(queryArray)
    }
}
