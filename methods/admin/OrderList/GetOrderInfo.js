/*
 * @Date: 2022-10-17 15:59:08
 * @LastEditors: l<PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2023-03-28 10:33:27
 * @FilePath: /newicp/methods/admin/OrderList/GetOrderInfo.js
 */
'use strict'

const Method = require('../../../libs/method')
const { parseJSON } = require('../../../fns/kits')
const _ = require('lodash')
const {
    OrderModel,
    OrderTypeEnum,
    OrderWebModel,
    OrderWebTwoLineModel,
    CurtainModel,
    OrderHistoryModel,
    DomainWhiteListModel,
    ProxyCompanyWhiteDomainModel,
} = require('../../../models')
const { Op } = require('sequelize')
const { OrderWebPlatInfoModel } = require('../../../mongoModels/icp')

/**
 * 查看订单信息API
 */
module.exports = class GetOrderInfo extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec(params) {
        let self = this

        try {
            const where = {
                OrderNo: params.OrderNo,
            }
            const [orders, orderWebs, curtains, history] = await Promise.all([
                OrderModel.findAll({
                    where,
                }),
                OrderWebModel.findAll({
                    where,
                    include: [
                        {
                            model: OrderWebTwoLineModel,
                            as: 'TwoLineIPs',
                            attributes: ['IP'],
                        },
                    ],
                }),
                CurtainModel.findAll({
                    where,
                }),
                OrderHistoryModel.findAll({
                    where,
                }),
            ])

            if (orders.length === 0) {
                return self.eh({
                    err: new Error('找不到对应的订单信息'),
                    code: 32801,
                })
            }
            let Order = parseJSON(orders[0])
            Order.Website = parseJSON(orderWebs) || []
            Order.Curtain = parseJSON(curtains)[0]
            Order.History = parseJSON(history)
            // Order.Website.forEach((website) => {
            //     _.remove(website.IP, (ip) => {
            //         return ip.trim() === global.CONFIG.RepliceTwoIP.trim()
            //     })
            //     if (Order.Status !== 1 && website.TwoLineIPs?.IP?.length > 0) {
            //         website.IP = website.IP.concat(website.TwoLineIPs?.IP)
            //     }
            // })
            if (Order.Website.length === 0) {
                return self.cb(0, { Order })
            }
            // 返回为APP备案类型时对应的字段返回
            for (let website of Order.Website) {
                if (website.InternetServiceType == 6) {
                    let platInfo = await OrderWebPlatInfoModel.findOne({
                        OrderWebId: website.Id,
                    })
                    website.AppPlatformInformationList =
                        platInfo?.AppPlatformInformationList || []
                }
            }
            if (Order.Type === OrderTypeEnum.AddConn) {
                let domains = Order?.Website[0]?.Domain.map((d) => d.Domain)
                let [DomainWhiteInfos, PorxyCompanyInfos] = await Promise.all([
                    DomainWhiteListModel.findAll({
                        attributes: [
                            'Id',
                            'Domain',
                            'ExpiredTime',
                            'Connected',
                            'Sealed',
                            'IsDeleted',
                        ],
                        where: {
                            Domain: {
                                [Op.in]: domains,
                            },
                        },
                    }),
                    ProxyCompanyWhiteDomainModel.findAll({
                        attributes: ['Id', 'Domain'],
                        where: {
                            Domain: {
                                [Op.in]: domains,
                            },
                        },
                    }),
                ])
                DomainWhiteInfos = parseJSON(DomainWhiteInfos)
                PorxyCompanyInfos = parseJSON(PorxyCompanyInfos)

                Order.Website[0].Domain = Order.Website[0].Domain.map(
                    (dInfo) => {
                        dInfo.DomainWhiteInfo =
                            DomainWhiteInfos.filter(
                                (info) => info.Domain === dInfo.Domain
                            )?.[0] ??
                            PorxyCompanyInfos.filter(
                                (info) => info.Domain === dInfo.Domain
                            ).length > 0
                                ? { PorxyCompany: true }
                                : {}
                        return dInfo
                    }
                )
            }
            return this.cb(0, { Order })
        } catch (e) {
            console.log(e)
            if (e instanceof Error) {
                return self.eh({
                    err: e,
                    code: 32800,
                })
            }
            self.eh(e)
        }
    }
}
