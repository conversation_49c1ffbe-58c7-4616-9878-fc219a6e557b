/*
 * @Date: 2022-08-11 17:59:40
 * @LastEditors: l<PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2022-08-12 18:14:32
 * @FilePath: /newicp/methods/admin/OrderList/ModifyWebsite.js
 */
/**
 * 修改（单个字段）的网站信息
 */
const { parseJSON } = require('../../../fns/kits')
const Method = require('../../../libs/method')
const {
    OrderModel,
    OrderWebModel,
    OrderStatusEnum,
    OrderHistoryModel,
} = require('../../../models')
const { OrderWebPlatInfoModel } = require('../../../mongoModels/icp')

module.exports = class ModifyWebsite extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({ Id, Name, Value, __ssoUser } = {}) {
        let self = this
        try {
            let orderWeb = await OrderWebModel.findOne({
                where: {
                    Id,
                },
            })
            orderWeb = parseJSON(orderWeb)
            if (!orderWeb) {
                // 订单不存在
                // 订单不存在
                let err = new Error('订单不存在')
                err.code = 32801
                return this.err(err)
            }

            let orderNo = orderWeb.OrderNo

            let order = await OrderModel.findOne({
                where: {
                    OrderNo: orderNo,
                },
            })

            order = parseJSON(order)

            if (!order) {
                // 订单不存在
                let err = new Error('订单不存在')
                err.code = 32801
                return this.err(err)
            }
            if (
                [
                    OrderStatusEnum.GovAuditing,
                    OrderStatusEnum.GovAuditPass,
                ].includes(order.Status)
            ) {
                // 该订单不支持修改
                let err = new Error('订单不支持修改')
                err.code = 32802
                return this.err(err)
            }
            if (order.IsDeleted === 1) {
                // 订单已删除
                let err = new Error('订单已删除')
                err.code = 32803
                return this.err(err)
            }
            let updateJson = {
                [Name]: Value,
            }
            console.log(updateJson, 9999)
            await OrderWebModel.update(updateJson, {
                where: {
                    Id,
                },
            })
            if (
                orderWeb.InternetServiceType == 6 &&
                Name === 'AppPlatformInformationList'
            ) {
                // 此处使用set是 防止AppPlatformInformationList 结构变化引起的问题
                await OrderWebPlatInfoModel.findOneAndUpdate(
                    {
                        OrderWebId: Id,
                    },
                    {
                        $set: {
                            AppPlatformInformationList: Value,
                        },
                    },
                    { upsert: true }
                )
            }
            let historylog = {
                OrderNo: orderNo,
                Status: order.Status,
                Operator: __ssoUser,
                OperationTime: new Date(),
                Action: 'modify_order_web_params',
                Info: JSON.stringify({
                    [Name]: {
                        new: Value,
                        old: orderWeb[Name],
                    },
                }),
            }
            await OrderHistoryModel.create(historylog)

            self.cb(0)
        } catch (e) {
            e.code = 11003
            self.err(e)
        }
    }
}
