/*
 * @Author: william.qian <EMAIL>
 * @Date: 2023-07-11 10:18:45
 * @LastEditors: william.qian <EMAIL>
 * @LastEditTime: 2023-07-11 11:11:26
 * @FilePath: /newicp/methods/admin/OrderList/AddOrderHistory.js
 * @Description: 增加日志功能
 */
const Method = require('../../../libs/method')
const { OrderHistoryModel } = require('../../../models')

module.exports = class AddOrderHistory extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({
        OrderNo,
        Status,
        Operator = 'System.Auto',
        UserAction = 'modify_order',
        Info,
    }) {
        let self = this

        try {
            // 执行查询
            try {
                await OrderHistoryModel.create({
                    OrderNo,
                    Status,
                    Operator,
                    Action: UserAction,
                    Info,
                })
            } catch (error) {
                throw new this.Err(error, 31104)
            }
            return self.cb(0)
        } catch (e) {
            self.eh(e)
        }
    }
}
