/*
 * @Author: william.qian <EMAIL>
 * @Date: 2022-08-22 16:16:03
 * @LastEditors: william.qian <EMAIL>
 * @LastEditTime: 2023-05-16 11:22:49
 * @FilePath: /newicp/methods/admin/OrderList/DescribeVerbalTricks.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
/**
 * @file API DescribeVerbalTricks 展示话术，支持查询指定话术类型
 * <AUTHOR>
 */
const Method = require('../../../libs/method')
const _ = require('lodash')
const VERBAL_TYPE_DICT = {
    Common: '通用',
    Main: '主体',
    Web: '网站',
}
const VERBAL_TYPE_LIST = Object.keys(VERBAL_TYPE_DICT)
module.exports = class DescribeVerbalTricks extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({ VerbalType }) {
        let self = this
        let redis = this.redis.get()

        let Result = [],
            queryType

        try {
            let typeList = JSON.parse(JSON.stringify(VERBAL_TYPE_LIST))
            // 对比，入参类型多过标准类型，就有问题
            if (_.difference(VerbalType, typeList).length !== 0) {
                throw new this.Err(new Error('无效的驳回类型'), 65701)
            }
            queryType = VerbalType.length === 0 ? typeList : VerbalType
            // 执行查询
            try {
                // 获取Redis结果
                let queryRows = []
                queryRows = await redis.hmget('icp_verbal_tricks', queryType)

                if (queryRows.length === 0) {
                    return self.cb(0, { VerbalTricks: [] })
                }

                for (let i = 0; i < queryRows.length; i++) {
                    if (queryRows[i] !== null) {
                        // 格式化
                        queryRows[i] = JSON.parse(queryRows[i])
                        // 推送结果集
                        for (const queryRow of queryRows[i]) {
                            Result.push({
                                VerbalTrick: queryRow,
                                VerbalType: queryType[i],
                            })
                        }
                    }
                }
            } catch (error) {
                throw new this.Err(error, 65702)
            }
            self.cb(0, { VerbalTricks: Result })
        } catch (e) {
            self.eh(e)
        }
    }
}
