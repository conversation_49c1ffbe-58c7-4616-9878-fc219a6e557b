/* eslint-disable no-duplicate-case */
/**
 * @file API UpdateStatus,更新订单、已备案完成的状态
 * <AUTHOR>
 * @return { } 返回符合成功与否
 * {
	"RetCode": 0
	}
 * 错误码:
 * "30201": "更新配置时发生其它异常",
 * "30202": "新建配置请输入配置名!",
 * "30203": "线上配置不允许更新",
 */

const Method = require('../../../libs/method')
const { getTableModel } = require('../../../fns/kits')

module.exports = class UpdateStatus extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({ Type, Key, Status } = {}) {
        let self = this

        let icpDatabase = this.db.get('icp')
        let Order = getTableModel('t_order', icpDatabase)
        let ICP = getTableModel('t_icp', icpDatabase)
        let Web = getTableModel('t_web', icpDatabase)

        try {
            let recordCount
            switch (Type) {
                case 'Order':
                    // 如果是订单，验证订单是否存在
                    recordCount = await Order.count({
                        where: {
                            OrderNo: Key,
                        },
                    })
                    break
                case 'ICP':
                    // 如果是已备案主体，验证已备案是否存在
                    recordCount = await ICP.count({
                        where: {
                            ICPMainNo: Key,
                        },
                    })
                    break
                case 'Web':
                    // 如果是已备案网站，验证已备案是否存在
                    recordCount = await Web.count({
                        where: {
                            ICPWebNo: Key,
                        },
                    })
                    break

                default:
                    await Promise.reject(
                        new self.Err(new Error('未知的更新类型'), 31101)
                    )
                    break
            }

            if (recordCount !== 1) {
                await Promise.reject(
                    new self.Err(new Error('无可更新的记录'), 31102)
                )
            }

            switch (Type) {
                case 'Order':
                    // 如果是订单，验证订单是否存在
                    Order.update(
                        { Status },
                        {
                            where: {
                                OrderNo: Key,
                            },
                        }
                    )
                    break
                case 'ICP':
                    // 如果是已备案主体，验证已备案是否存在
                    ICP.update(
                        { Status },
                        {
                            where: {
                                ICPMainNo: Key,
                            },
                        }
                    )
                    break
                case 'Web':
                    // 如果是已备案网站，验证已备案是否存在
                    Web.update(
                        { Status },
                        {
                            where: {
                                ICPWebNo: Key,
                            },
                        }
                    )
                    break

                default:
                    await Promise.reject(
                        new self.Err(new Error('未知的更新类型'), 31103)
                    )
                    break
            }

            return this.cb(0)
        } catch (e) {
            console.log(e)
            if (e instanceof Error) {
                return self.eh({
                    err: e,
                    code: 30201,
                })
            }
            self.eh(e)
        }
    }
}
