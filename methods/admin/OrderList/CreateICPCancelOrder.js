'use strict'

const Method = require('../../../libs/method')
const {
    ICPModel,
    ICPStatusEnum,
    ICPWebModel,
    WebStatusEnum,
    OrderModel,
    OrderTypeEnum,
    OrderStatusEnum,
} = require('../../../models')
const { generateOrderNo, parseJSON } = require('../../../fns/kits')
const { Op } = require('sequelize')
const _ = require('lodash')
/**
 * 创建取消订单信息API,支持批量取消
 */
module.exports = class CreateICPCancelOrder extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec(params) {
        let self = this
        params.Msgs = []
        if (params.ICPMainNo) {
            if (typeof params.ICPMainNo === 'string') {
                params.Msgs.push(params.ICPMainNo)
            } else {
                params.Msgs = Array.from(new Set(params.ICPMainNo))
            }
        }
        if (params.ICPWebNo) {
            if (typeof params.ICPWebNo === 'string') {
                params.Msgs.push(params.ICPWebNo)
            } else {
                params.Msgs = Array.from(new Set(params.ICPWebNo))
            }
        }
        let Err = {}
        let Success = {}
        let OrderInsertArr = []
        let ICPUpdateIdArr = []
        let ICPWebUpdateIdArr = []
        try {
            for (let ICPNo of params.Msgs) {
                let info = {}
                let OrderNo = generateOrderNo()
                info.OrderNo = OrderNo
                info.InnerDisplay = 1
                info.Type = params.Type
                info.Auditor = params.__ssoUser
                info.ICPMainPassword = params.ICPMainPassword
                info.Status = params.Status
                info.ReasonId = params.ReasonId || true
                info.Remark = params.Remark || ''
                if (info.Type === OrderTypeEnum.CancelMain) {
                    info.ICPMainNo = ICPNo
                } else {
                    info.ICPWebNo = ICPNo
                }
                // 参数校验
                let checkResult = checkParams(info)

                if (checkResult.type !== 0) {
                    Err[ICPNo] = '参数校验错误'
                    continue
                }
                let icpInfo
                switch (info.Type) {
                    case OrderTypeEnum.CancelMain:
                        icpInfo = await ICPModel.findOne({
                            attributes: [
                                'Id',
                                'CMainId',
                                'Status',
                                'OrganizerName',
                                'PICMainLicenseId',
                                'PICMainMobile',
                            ],
                            where: {
                                ICPMainNo: ICPNo,
                            },
                        })
                        if (icpInfo === undefined) {
                            Err[ICPNo] = '没有发现该备案信息'
                            continue
                        }
                        icpInfo = parseJSON(icpInfo)
                        if (icpInfo.Status !== ICPStatusEnum.Normal) {
                            if (
                                [
                                    ICPStatusEnum.Logout,
                                    ICPStatusEnum.Isdeleted,
                                ].includes(icpInfo.Status)
                            ) {
                                Err[ICPNo] = '该主体已注销或已删除'
                                continue
                            } else {
                                Err[ICPNo] =
                                    '该备案主体不支持操作，存在流程中订单'
                                continue
                            }
                        }
                        info.ICPId = icpInfo.Id
                        info.CMainId = icpInfo.CMainId
                        info.OrganizerName = icpInfo.OrganizerName
                        info.PICMainLicenseId = icpInfo.PICMainLicenseId
                        info.PICMainMobile = icpInfo.PICMainMobile || ''
                        break
                    case OrderTypeEnum.CancelWeb:
                    case OrderTypeEnum.CancelConn:
                        icpInfo = await ICPModel.findAll({
                            attributes: [
                                'Id',
                                'CompanyId',
                                'OrganizerName',
                                'ICPMainNo',
                                'Status'
                            ],
                            include: [
                                {
                                    model: ICPWebModel,
                                    as: 'Website',
                                    where: {
                                        ICPWebNo: ICPNo,
                                    },
                                    attributes: [
                                        'Status',
                                        'Id',
                                        'CWebsiteId',
                                        'Name',
                                        'Domain',
                                    ],
                                },
                            ],
                        })
                        icpInfo = parseJSON(icpInfo)
                        icpInfo = icpInfo[0]
                        if (icpInfo === undefined) {
                            Err[ICPNo] = '没有发现该备案信息'
                            continue
                        }
                        if (icpInfo.Status !== ICPStatusEnum.Normal) {
                            if (
                                [
                                    ICPStatusEnum.Logout,
                                    ICPStatusEnum.Isdeleted,
                                ].includes(icpInfo.Status)
                            ) {
                                Err[ICPNo] = '该主体已注销或已删除'
                                continue
                            } else {
                                Err[ICPNo] =
                                    '该备案主体不支持操作，存在流程中订单'
                                continue
                            }
                        }
                        if (
                            icpInfo.Website[0]?.Status !== WebStatusEnum.Normal
                        ) {
                            if (
                                icpInfo.Website[0]?.Status ===
                                WebStatusEnum.Weblogout
                            ) {
                                Err[ICPNo] = '该网站已注销'
                                continue
                            } else {
                                Err[ICPNo] = '该网站不支持操作，存在流程中订单'
                                continue
                            }
                        }
                        info.Details = [
                            {
                                Domain: icpInfo.Website[0].Domain,
                                WebName: icpInfo.Website[0].Name,
                            },
                        ]
                        info.ICPWebId = icpInfo.Website[0].Id
                        info.CWebsiteId = icpInfo.Website[0].CWebsiteId
                        info.OrganizerName = icpInfo.OrganizerName
                        info.ICPMainNo = icpInfo.ICPMainNo
                        break
                }
                info.CompanyId = icpInfo.CompanyId
                //创建订单
                OrderInsertArr.push(info)
                // 更新 备案信息
                if (params.Type === OrderTypeEnum.CancelMain) {
                    ICPUpdateIdArr.push(info.ICPId)
                }

                if (
                    [
                        OrderTypeEnum.CancelWeb,
                        OrderTypeEnum.CancelConn,
                    ].includes(params.Type)
                ) {
                    ICPWebUpdateIdArr.push(info.ICPWebId)
                }
            }
            let insertInfos = []
            if (OrderInsertArr.length > 0) {
                insertInfos = await OrderModel.bulkCreate(OrderInsertArr)
                insertInfos = parseJSON(insertInfos)
            }
            if (ICPUpdateIdArr.length > 0) {
                await ICPModel.update(
                    {
                        Status: params.Type,
                    },
                    {
                        where: {
                            Id: {
                                [Op.in]: ICPUpdateIdArr,
                            },
                        },
                    }
                )
            }
            if (ICPWebUpdateIdArr.length > 0) {
                await ICPWebModel.update(
                    { Status: params.Type },
                    {
                        where: {
                            Id: {
                                [Op.in]: ICPWebUpdateIdArr,
                            },
                        },
                    }
                )
            }
            if (insertInfos.length > 0) {
                _.forEach(insertInfos, (val) => {
                    if (val.Type === OrderTypeEnum.CancelMain) {
                        Success[val.ICPMainNo] = val.OrderNo
                    } else {
                        Success[val.ICPWebNo] = val.OrderNo
                    }
                })
            }
            return this.cb(0, {
                Success,
                Error: Err,
            })
        } catch (err) {
            self.err(err)
        }
    }
}

// 检查参数
function checkParams(params) {
    //得到对于输入值设定的规则列表
    let arr = Object.keys(cancelCheckMap)
    //type的状态如果在{4，5，6}中
    if (arr.indexOf(params.Type.toString()) !== -1) {
        //得到该类型所需要的规范
        let common = cancelCheckMap[params.Type.toString()].common
        let hasAllData = true
        //对于每一条需要值，查看是否已输入
        for (let i = 0; i < common.length; i++) {
            if (params[common[i]] === undefined) {
                hasAllData = false
                break
            }
        }
        if (hasAllData === false) {
            return { type: 1, input: '该类型的输入值没有输入完全' }
        } else {
            return { type: 0, input: '' }
        }
    }
    //如果该状态不在{4，5，6}中，则退出;
    else {
        return { type: 2, input: '输入的类型还没有被定义' }
    }
}

// 对不同注销类型的订单类型，进行参数校验
const cancelCheckMap = {
    4: {
        common: ['ICPMainNo', 'ICPMainPassword'],
    },
    5: {
        common: ['ICPWebNo'],
    },
    6: {
        common: ['ICPWebNo'],
    },
}
