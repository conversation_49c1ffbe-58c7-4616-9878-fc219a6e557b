/**
 * @file API ClearExpiredOrder,清楚删除订单，释放关联主体的状态
 * <AUTHOR>
 * @return {} 成功与否
 * todo 确定是否增加权限管制
 */

const Method = require('../../../libs/method')
const ObjectId = require('mongodb').ObjectId
const {
    ClearOrderBatchModel,
    ClearBatchStatusEnum,
    ClearOrderRecordModel,
    ClearRecordStatusEnum,
} = require('../../../models')
const { Op } = require('sequelize')
const { getTableModel, parseJSON } = require('../../../fns/kits')
const _ = require('lodash')

module.exports = class ClearExpiredOrder extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({ BatchId, __ssoUser } = {}) {
        let self = this
        let batchInfo, records
        let producer = this.producer
        let redis = this.redis.get()
        try {
            // 确定订单是否存在，与订单的细节情况
            ;[batchInfo, records] = await Promise.all([
                ClearOrderBatchModel.findOne({
                    where: {
                        Id: BatchId,
                        Status: ClearBatchStatusEnum.notified,
                    },
                }),
                ClearOrderRecordModel.findAll({
                    where: {
                        BatchId,
                    },
                    attribures: ['Id', 'Order'],
                }),
            ])

            batchInfo = parseJSON(batchInfo)
            if (!batchInfo) {
                // 该批次无法清理 请确认批次状态
                let err = new Error('该批次无法清理 请确认批次状态')
                err.code = 67070
                return this.err(err)
            }
            if (global.CONFIG.env !== 'production') {
                records = [records[0]]
            }
            records = records.map((record) => JSON.stringify(record))
            await redis.sadd(`ClearOrder_${BatchId}`, records)
            await ClearOrderBatchModel.update(
                {
                    Status: ClearBatchStatusEnum.clearing,
                    Operator: __ssoUser,
                },
                {
                    where: {
                        Id: BatchId,
                    },
                }
            )
            // 获取所有订单 推送查询 和删除  根据节点数量，暂定并发为4
            let promiseArr = []
            for (let i = 0; i < 4; i++) {
                promiseArr.push(
                    producer.send({
                        type: 'icp',
                        topic: 'CheckAndClearOrder',
                        data: {
                            BatchId,
                        },
                    })
                )
            }
            await Promise.all(promiseArr)
            return this.cb(0)
        } catch (e) {
            self.err(e)
        }
    }
}
