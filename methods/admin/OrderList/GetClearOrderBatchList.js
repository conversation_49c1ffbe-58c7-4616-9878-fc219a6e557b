/*
 * @Date: 2023-05-18 15:39:43
 * @LastEditors: l<PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2023-05-18 16:21:15
 * @FilePath: /newicp/methods/admin/OrderList/GetClearOrderBatchList.js
 */
const Method = require('../../../libs/method')
const { Op } = require('sequelize')
const _ = require('lodash')
const { ClearOrderBatchModel } = require('../../../models')
module.exports = class GetClearOrderBatchList extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({ Offset, Limit } = {}) {
        try {
            let { rows: Rows, count: TotalCount } =
                await ClearOrderBatchModel.findAndCountAll({
                    order: [['id', 'desc']],
                    offset: Offset,
                    limit: Limit,
                })
            // 返回最终的结果
            return this.cb(0, { Rows, TotalCount })
        } catch (e) {
            let err = new Error('查看批次记录出错')
            err.code = 11000
            this.err(err)
        }
    }
}
