/*
 * @Date: 2022-12-27 16:00:09
 * @LastEditors: l<PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2023-01-11 10:13:51
 * @FilePath: /newicp/methods/admin/OrderList/UploadPicture.js
 */
const { parseJSON } = require('../../../fns/kits')
const { uploadFile } = require('../../../fns/uflieFuns')
const Method = require('../../../libs/method')
const { OrderModel, PictureModel } = require('../../../models')

module.exports = class UploadPicture extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({ OrderNo, Picture }) {
        let self = this
        try {
            let orders = await OrderModel.findAll({
                where: {
                    OrderNo,
                },
            })

            orders = parseJSON(orders)

            if (orders.length === 0) {
                // 没有发现该订单
                let err = new Error('订单不存在')
                err.code = 32151
                throw err
            }
            let filename
            try {
                filename = await uploadFile(Picture)
            } catch (err) {
                // 创建失败
                let e = new Error('上传图片失败' + err)
                e.code = 32156
                throw e
            }

            // 创建图片记录
            await PictureModel.create({
                CompanyId: orders[0].CompanyId,
                Url: filename,
            })
            return this.cb(0, {
                Filename: filename,
            })
        } catch (err) {
            self.err(err)
        }
    }
}
