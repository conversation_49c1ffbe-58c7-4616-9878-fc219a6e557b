/**
 * @file API AddVerbalTrick 增加展示话术
 * <AUTHOR>
 */
const Method = require('../../../libs/method')
module.exports = class AddVerbalTrick extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({ VerbalType, VerbalTrick }) {
        let self = this
        let redis = this.redis.get()

        try {
            // 执行查询
            try {
                // 获取Redis结果
                let queryRows = await redis.hmget('icp_verbal_tricks', [
                    VerbalType,
                ])
                // 转换格式化
                if (queryRows.length === 0 || queryRows[0] === null) {
                    queryRows = []
                } else {
                    queryRows = JSON.parse(queryRows[0])
                }
                queryRows.push(VerbalTrick)
                await redis.hmset('icp_verbal_tricks', {
                    [VerbalType]: JSON.stringify(queryRows),
                })
            } catch (error) {
                throw new this.Err(error, 65702)
            }
            self.cb(0)
        } catch (e) {
            self.eh(e)
        }
    }
}
