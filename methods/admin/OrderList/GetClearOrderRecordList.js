/*
 * @Date: 2023-05-18 15:39:43
 * @LastEditors: l<PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2023-05-18 17:16:23
 * @FilePath: /newicp/methods/admin/OrderList/GetClearOrderRecordList.js
 */
const Method = require('../../../libs/method')
const { Op } = require('sequelize')
const _ = require('lodash')
const { ClearOrderRecordModel } = require('../../../models')
module.exports = class GetClearOrderBatchList extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({ BatchId, Offset, Limit, GetAll } = {}) {
        try {
            let condition = {
                where: {
                    BatchId,
                },
            }
            if (!GetAll) {
                condition.offset = Offset || 0
                condition.limit = Limit || 20
            }
            let { rows: Rows, count: TotalCount } =
                await ClearOrderRecordModel.findAndCountAll(condition)
            // 返回最终的结果
            return this.cb(0, { Rows, TotalCount })
        } catch (e) {
            let err = new Error('查看批次记录详情出错')
            err.code = 11000
            this.err(err)
        }
    }
}
