const Method = require('../../../libs/method')
const verify_type_cn = require('../../../configs/common/verify_api_type_cn.json')

const { getTableModel, parseJSON } = require('../../../fns/kits')

module.exports = class GetOrderVerifyLog extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({ OrderNo } = {}) {
        let result
        let self = this

        let icpDatabase = this.db.get('icp')
        let LogModel = getTableModel('t_verify_log', icpDatabase)

        try {
            result = await LogModel.findAll({
                where: {
                    OrderNo: OrderNo,
                },
                order: [['Id', 'DESC']],
            })

            result = parseJSON(result)

            // Type类型换成中文
            result.map((record) => {
                let getKey = Object.keys(verify_type_cn).filter(function (x) {
                    return verify_type_cn[x] == record.Type
                })
                if (getKey.length === 1) {
                    record.Type = getKey[0]
                }
            })

            return self.cb(0, { VerifyLogList: result })
        } catch (e) {
            if (e instanceof Error) {
                return self.eh({
                    err: e,
                    code: 30801,
                })
            }
            self.eh(e)
        }
    }
}
