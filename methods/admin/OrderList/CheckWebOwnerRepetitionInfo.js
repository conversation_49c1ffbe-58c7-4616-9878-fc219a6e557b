const Method = require('../../../libs/method')
const { getTableModel, parseJSON } = require('../../../fns/kits')
const _ = require('lodash')
const { Op } = require('sequelize')
// 检查订单中的主体负责的信息，是否与其它订单或者已完成的备案重复

module.exports = class CheckWebOwnerRepetitionInfo extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({ OrderNo } = {}) {
        try {
            let self = this
            let icpDatabase = this.db.get('icp')
            let OrderWebModel = getTableModel('t_order_web', icpDatabase)
            let WebModel = getTableModel('t_web', icpDatabase)
            let OrderModel = getTableModel('t_order', icpDatabase)
            let ICPModel = getTableModel('t_icp', icpDatabase)

            // 获取订单的主体信息
            let orderWebInfo = await OrderWebModel.findAll({
                attributes: ['Mobile', 'LicenseId', 'EmergencyPhone', 'Email'],
                where: {
                    OrderNo,
                },
            })

            orderWebInfo = parseJSON(orderWebInfo)
            // 找出4项中存在值的情况

            if (orderWebInfo.length === 0) {
                return self.cb(0, {
                    OrderList: [],
                    ICPList: [],
                })
            }

            // 网站中Map出需要的结构
            // 手机
            let mobileList = _.map(orderWebInfo, 'Mobile')
            let lienseIdList = _.map(orderWebInfo, 'LicenseId')
            let emergencyPhoneList = _.map(orderWebInfo, 'EmergencyPhone')
            let emailList = _.map(orderWebInfo, 'Email')

            let selectJSON = []

            // 生成查询条件
            if (mobileList.length !== 0) {
                selectJSON.push({ Mobile: mobileList })
            }

            if (lienseIdList.length !== 0) {
                selectJSON.push({ LicenseId: lienseIdList })
            }

            if (emergencyPhoneList.length !== 0) {
                selectJSON.push({ EmergencyPhone: emergencyPhoneList })
            }

            if (emailList.length !== 0) {
                selectJSON.push({ Email: emailList })
            }

            let orderList = await OrderWebModel.findAll({
                attributes: [
                    'Mobile',
                    'LicenseId',
                    'EmergencyPhone',
                    'Email',
                    'OrderNo',
                ],
                where: {
                    [Op.or]: selectJSON,
                    IsDeleted: 0,
                },
            })

            let icpList = await WebModel.findAll({
                attributes: [
                    'Mobile',
                    'LicenseId',
                    'EmergencyPhone',
                    'Email',
                    'ICPWebNo',
                    'MainId',
                ],
                where: {
                    [Op.or]: selectJSON,
                    Status: { [Op.ne]: 1 },
                },
            })
            // 取出
            orderList = parseJSON(orderList)
            icpList = parseJSON(icpList)

            // 查询订单状态，已删除的记录去掉
            let orderNoList = [],
                icpIdList = []

            orderNoList = await OrderModel.findAll({
                attributes: ['OrderNo'],
                where: {
                    OrderNo: _.map(orderList, 'OrderNo'),
                    IsDeleted: 0,
                },
            })
            orderNoList = parseJSON(orderNoList)
            orderNoList = _.map(orderNoList, 'OrderNo')

            // 从orderList取出有效的记录
            orderList = _.filter(orderList, function (o) {
                return _.indexOf(orderNoList, o.OrderNo) != -1
            })

            // 查询主体状态，已删除的记录去掉```````````````````

            icpIdList = await ICPModel.findAll({
                attributes: ['Id'],
                where: {
                    Id: _.map(icpList, 'MainId'),
                    IsDeleted: 0,
                },
            })
            icpIdList = parseJSON(icpIdList)
            icpIdList = _.map(icpIdList, 'Id')

            // 从orderList取出有效的记录
            icpList = _.filter(icpList, function (o) {
                return _.indexOf(icpIdList, o.MainId) != -1
            })

            let OrderList = {}
            let ICPList = {}
            // 结构体
            // OrderList = {
            //     "O202003172329170033": {
            //         'PICMainMobile': true, 'PICMainLicenseId': true, 'EmergencyPhone': true, 'PICMainEmail': true
            //     }
            // }

            if (orderList.length !== 0) {
                orderList.forEach((order) => {
                    OrderList[order.OrderNo] = {}
                    selectJSON.forEach((element) => {
                        _.forEach(element, function (value, key) {
                            if (_.indexOf(value, order[key]) != -1) {
                                OrderList[order.OrderNo][key] = true
                            }
                        })
                    })
                })
            }

            if (icpList.length !== 0) {
                icpList.forEach((icp) => {
                    ICPList[icp.ICPWebNo] = {}
                    selectJSON.forEach((element) => {
                        _.forEach(element, function (value, key) {
                            if (_.indexOf(value, icp[key]) != -1) {
                                ICPList[icp.ICPWebNo][key] = true
                            }
                        })
                    })
                })
            }

            delete OrderList[OrderNo]

            return this.cb(0, {
                OrderList,
                ICPList,
            })
        } catch (e) {
            console.log(e)
            if (e instanceof Error) {
                return this.eh({
                    err: e,
                    code: 67061,
                })
            }
            this.eh(e)
        }
    }
}
