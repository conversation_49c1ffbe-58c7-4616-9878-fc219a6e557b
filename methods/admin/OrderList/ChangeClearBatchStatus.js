/*
 * @Date: 2023-05-18 15:39:43
 * @LastEditors: l<PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2023-05-18 16:21:43
 * @FilePath: /newicp/methods/admin/OrderList/ChangeClearBatchStatus.js
 */
const Method = require('../../../libs/method')
const _ = require('lodash')
const { ClearOrderBatchModel } = require('../../../models')
module.exports = class ChangeClearBatchStatus extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({ Id, Status } = {}) {
        try {
            await ClearOrderBatchModel.update(
                {
                    Status,
                },
                {
                    where: {
                        Id,
                    },
                }
            )
            // 返回最终的结果
            return this.cb(0)
        } catch (e) {
            let err = new Error('更新批次状态出错')
            err.code = 11002
            this.err(err)
        }
    }
}
