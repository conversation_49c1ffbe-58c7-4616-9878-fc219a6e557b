/**
 * 接收验证完成后的回调
 * 1.成功的话 将最佳成像照上传到us3 ，调用FinishH5 完成验证
 * 2.失败的话 只更新日志
 */
const Method = require('../../../libs/method')
const { getTableModel, parseJSON } = require('../../../fns/kits')
const { VerifyLogModel, TokenModel, PictureModel } = require('../../../models')
const { uploadFile } = require('../../../fns/uflieFuns')
const { FinishH5 } = require('../../../fns/FinishH5')
const moment = require('moment')
module.exports = class ReciveFaceIdResult extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({ Token, UUID, Picture, ReturnObject } = {}) {
        let self = this
        let PictureName = ''
        let CompanyId, LicenseId, OrderNo
        try {
            // 根据token获取信息
            let TokenInfo = await TokenModel.findOne({
                where: {
                    Token,
                },
            })
            if (!TokenInfo) return self.cb(69007, 'token无效')
            TokenInfo = parseJSON(TokenInfo)

            CompanyId = TokenInfo.CompanyId
            LicenseId = TokenInfo.LicenseId
            OrderNo = TokenInfo.OrderNo

            // 接收回调结果 记录日志
            if (ReturnObject.RetCode === 0 && ReturnObject.Status && Picture) {
                // 验证成功
                // 将base64上传到 US3
                PictureName = await uploadFile(Picture)

                // 插入最佳成像到数据库
                await PictureModel.create({
                    CompanyId,
                    Url: PictureName,
                })
                // CompanyId, Timeout, OrderNo, LicenseId, Picture, UUID, Token
                // 调用FinishH5完成识别
                await FinishH5({
                    CompanyId,
                    OrderNo,
                    LicenseId,
                    Token,
                    UUID,
                    Picture: PictureName,
                    Timeout: 0,
                })
            }
            // 日志记录， 根据OrderNo 和UUID找到记录 并更新
            let log = await VerifyLogModel.findOne({
                where: { OrderNo, UUID },
            })
            log = parseJSON(log)
            if (log?.Id) {
                let IsMatch =
                    ReturnObject.RetCode === 0 && ReturnObject.Status
                        ? true
                        : false
                await VerifyLogModel.update(
                    {
                        Result: ReturnObject.RetCode === 0 ? 0 : 1,
                        UpdateTime: moment().format('X'),
                        Response: {
                            ...ReturnObject, IsMatch
                        },
                        ReturnObject: { ...ReturnObject, IsMatch },
                    },
                    { where: { Id: log.Id } }
                )
            }
            return self.cb(0)
        } catch (e) {
            if (e instanceof Error) {
                return self.eh({
                    err: e,
                    code: 67076,
                })
            }
            self.eh(e)
        }
    }
}
