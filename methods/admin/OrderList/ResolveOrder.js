/**
 * @file API ResolveOrder,替换旧ICP备案的接口，订单状态置为通过
 * <AUTHOR>
 * @return
 */

const Method = require('../../../libs/method')
const { getTableModel, parseJSON } = require('../../../fns/kits')
const email = require('../../../libs/email')
const {
    getNextStatus,
} = require('../../../fns/admin_order_fns/orderStatusFuns.js')
const henganApi = require('../../../libs/henganApiPromise')
const ICPSyncFromAodun = require('../../../methods/common/ICPSyncFromAodun')
const {
    cancelRecordSuccess,
    changeRecordSuccess,
    addRecordSuccess,
    updateOrderNextStatus,
} = require('../../../fns/admin_order_fns/icpAndOrderSync.js')
const { OrderWebPlatInfoModel } = require('../../../mongoModels/icp')
const _ = require('lodash')
const moment = require('moment')
module.exports = class ResolveOrder extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec(params) {
        let self = this
        const redis = this.redis.get()
        let icpDatabase = self.db.get('icp')
        let OrderModel = getTableModel('t_order', icpDatabase)
        let OrderWebModel = getTableModel('t_order_web', icpDatabase)
        let CurtainModel = getTableModel('t_curtain', icpDatabase)
        let ICPModel = getTableModel('t_icp', icpDatabase)
        let WebModel = getTableModel('t_web', icpDatabase)
        let logger = this.logger
        params.remoteUser = params.staff_name_en || params['__ssoUser']
        if (params.Website && params.Website.length === 0) {
            delete params.Website
        }

        const OrderNo = params.OrderNo
        let orderInfo, nextStatus, webInfo, ICPMainNo
        try {
            try {
                // 获取订单信息
                ;[orderInfo, webInfo] = await Promise.all([
                    OrderModel.findAll({
                        where: {
                            OrderNo,
                            IsDeleted: 0,
                        },
                    }),
                    OrderWebModel.findAll({
                        where: {
                            OrderNo,
                            IsDeleted: 0,
                        },
                    }),
                ])
                orderInfo = parseJSON(orderInfo)
                webInfo = parseJSON(webInfo)
            } catch (error) {
                await Promise.reject(new self.Err(error, 11000))
            }

            // 长度判断
            if (orderInfo.length === 0) {
                await Promise.reject(
                    new self.Err(new Error('审核订单通过，此订单不存在'), 32502)
                )
            }

            orderInfo = orderInfo[0]
            if (webInfo.length !== 0) {
                orderInfo.Website = webInfo
            }
            ICPMainNo = params.ICPMainNo || orderInfo.ICPMainNo

            // 看下是否网站信息中包含APP备案 若有 需要赋值到orderInfo.Website中
            if (orderInfo.Website) {
                for (let website of orderInfo.Website) {
                    if (website.InternetServiceType == 6) {
                        let platInfo = await OrderWebPlatInfoModel.findOne({
                            OrderWebId: website.Id,
                        })
                        website.AppPlatformInformationList =
                            platInfo?.AppPlatformInformationList || []
                    }
                }
            }

            // 取得下一个状态
            nextStatus = getNextStatus(
                orderInfo.Status,
                'resolve',
                orderInfo.Type,
                orderInfo.Version
            )
            if (!nextStatus)
                await Promise.reject(
                    new self.Err(
                        new Error('审核订单通过，无法获取下个状态'),
                        32503
                    )
                )

            // promise调用链，后面公用的部分
            let promiseChain

            if (
                nextStatus === 12 &&
                _.indexOf([4, 5, 6], orderInfo.Type) !== -1
            ) {
                // 注销类订单，管局审核通过
                // 执行注销
                promiseChain = cancelRecordSuccess(
                    // 对已备案记录t_icp与t_web做操作
                    orderInfo,
                    ICPModel,
                    WebModel
                )
                if (orderInfo.Type === 6) {
                    let domains = orderInfo.Details[0]?.Domain?.map(
                        (d) => d.Domain
                    )
                    // 取消接入 处理域名状态为未接入
                    this.producer.send({
                        type: 'icp',
                        topic: 'DelDomainFromWhiteList',
                        data: {
                            Domains: domains,
                            RetryTime: 0,
                        },
                    })
                }
            } else if (
                nextStatus == 12 &&
                _.indexOf([7, 8, 9, 10], orderInfo.Type) !== -1
            ) {
                // 变更类审核成功
                // 主要动作，从订单拉取图片，信息更新到网站上
                promiseChain = changeRecordSuccess(
                    // 对已备案记录t_icp与t_web做操作
                    orderInfo,
                    ICPModel,
                    WebModel,
                    OrderModel
                )
            } else if (nextStatus === 12) {
                // 其它更新操作，新增类
                // 有主体备案号 从待分配里面清除这个数据，原因是 管局回调顺序很奇怪，可能不先告诉我们审核通过，而是告诉我们有个备案来了，这个时候这个备案它没有cid，无归宿，。，，
                await redis.hdel('AwaitDistributeICP', ICPMainNo)
                promiseChain = addRecordSuccess(
                    // 对已备案记录t_icp与t_web做操作
                    orderInfo,
                    params,
                    ICPModel,
                    WebModel,
                    OrderModel
                )
                // 如果是新增接入 审核通过
                if (orderInfo.Type === 3) {
                    //新增接入
                    let domains = orderInfo.Website[0]?.Domain?.map(
                        (d) => d.Domain
                    )
                    this.producer.send({
                        type: 'icp',
                        topic: 'AuditPassAddDomainToWhiteList',
                        data: {
                            Domains: domains,
                            RetryTime: 0,
                        },
                    })
                }
            } else if (
                nextStatus == 8 &&
                _.indexOf([4, 5, 6], orderInfo.Type) === -1
            ) {
                promiseChain = Promise.resolve(0)
            } else {
                // 其它审核阶段，无特别操作，更新状态就行
                promiseChain = Promise.resolve(0)
            }

            // 通用操作
            return (
                promiseChain
                    .then(() => {
                        // 对订单做更新操作
                        return updateOrderNextStatus(
                            {
                                OrderNo,
                                nextStatus,
                                remoteUser: params.remoteUser,
                            },
                            OrderModel
                        )
                    })
                    .then(() => self.cb(0))
                    .then(() => {
                        return Promise.all([
                            OrderModel.findAll({
                                where: { OrderNo: orderInfo.OrderNo },
                            }),
                            OrderWebModel.findAll({
                                where: { OrderNo: orderInfo.OrderNo },
                            }),
                            CurtainModel.findAll({
                                where: { OrderNo: orderInfo.OrderNo },
                            }),
                        ]).then(([orders, websites, curtains]) => {
                            let order = parseJSON(orders)[0]
                            order.Website = parseJSON(websites)
                            order.Curtain = parseJSON(curtains)[0]
                            email.send(order)
                        })
                    })
                    // 此处先注释掉 以免产生CID不一致影响 因为傲盾cid会影响进行中的订单
                    // .then(() => {
                    //     // 如果是变更主体与三合一，完成一次Cid同步,暂时关掉同步看一下效果
                    //     if (
                    //         (orderInfo.Type === 7 || orderInfo.Type === 8) &&
                    //         nextStatus === 8 &&
                    //         global.CONFIG.env === 'production'
                    //     ) {
                    //         return henganApi('SyncSetCId', {
                    //             ICPMainNo: orderInfo.ICPMainNo,
                    //             CMainId: orderInfo.CMainId,
                    //             Websites: orderInfo.Website.map((website) => {
                    //                 return {
                    //                     ICPWebNo: website.ICPWebNo,
                    //                     CWebsiteId: website.CWebsiteId,
                    //                     CConnectId: website.CWebsiteId * 100,
                    //                 }
                    //             }),
                    //         })
                    //     }
                    // })
                    .then(() => {
                        // 如果是订单审核完成，执行一次同步, 主要是记录该备案由于我司订单审核通过更新，实际前面已经做过数据更新了
                        if (nextStatus === 12 && ICPMainNo) {
                            // 有备案号的情况下情况一次
                            let method = new ICPSyncFromAodun(
                                (RetCode, data) => {
                                    if (RetCode !== 0)
                                        logger
                                            .getLogger('error')
                                            .error(
                                                'ResolveOrder 同步傲盾备案信息失败 ' +
                                                    data?.Message || ''
                                            )
                                }
                            )
                            return method.exec({
                                ICPMainNo,
                                ResolveOrder: true,
                                __ssoUser: 'ResolveOrder Operator',
                            })
                        }
                    })
                    .then(() => {
                        // 如果是订单审核完成，执行一次同步
                        if (nextStatus === 12) {
                            // 推送 二线IP处理
                            self.producer.send({
                                type: 'icp',
                                topic: 'UpdateTwoLineIPsToICP',
                                data: {
                                    OrderNo,
                                    OrderType: orderInfo.Type,
                                },
                            })
                            // 有备案号的情况下情况一次
                            return self.producer.send({
                                type: 'icp',
                                topic: 'ResolveHook',
                                data: { OrderNo },
                            })
                        }
                    })
                    .catch((err) => {
                        return self.eh({
                            err,
                            code: 32504,
                        })
                    })
            )
        } catch (e) {
            if (e instanceof Error) {
                return self.eh({
                    err: e,
                    code: 32504,
                })
            }
            self.eh(e)
        }
    }
}
