const Method = require('../../../libs/method')
const {
    getOrderInfo,
    countOrderInfo,
    getOrderWebInfo,
} = require('../../../fns/order/OrderService')
const { Op } = require('sequelize')
const { getCurtainInfo } = require('../../../fns/order/CurtainService')
const _ = require('lodash')
const { OrderModel, OrderWebModel, CurtainModel } = require('../../../models')
const { OrderWebPlatInfoModel } = require('../../../mongoModels/icp')

module.exports = class GetOrderList extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec(params) {
        try {
            // 通过 网站信息/ 幕布状态 查询
            // -----------------------通过 网站和幕布状态 的查询 主要还是为了得到订单号
            let [webs, curtains] = await Promise.all([
                getOrderWebInfo(OrderWebModel, {
                    Domain: params.Domain,
                    IP: params.IP,
                    LicenseId: params.LicenseId,
                }),
                getCurtainInfo(CurtainModel, {
                    CurtainStatus: params.CurtainStatus,
                }),
            ])
            let cond = {}

            // 长度为0 表示传入筛选条件但是筛选结果为空
            if (webs || curtains) {
                if (
                    (webs && webs.length === 0) ||
                    (curtains && curtains.length === 0)
                ) {
                    return this.cb(0, {
                        TotalCount: 0,
                        Orders: [],
                    })
                }

                let webOrderNos = _.uniq(_.map(webs, (web) => web.OrderNo))
                let curtainOrderNos = _.map(
                    curtains,
                    (curtain) => curtain.OrderNo
                )
                let orderNos

                if (!webs) {
                    orderNos = curtainOrderNos
                } else if (!curtains) {
                    orderNos = webOrderNos
                } else {
                    orderNos = _.intersection(webOrderNos, curtainOrderNos)
                }

                // 没有同时符合幕布和网站筛选条件的订单
                if (_.isEmpty(orderNos)) {
                    return this.cb(0, {
                        TotalCount: 0,
                        Orders: [],
                    })
                }

                // 如果传 订单号 则从 网站和幕布状态 查询出的订单号 集合中 查找该订单号，如果不包含该 订单号  则返回没有找到
                if (params.OrderNo) {
                    if (!_.includes(orderNos, params.OrderNo)) {
                        return this.cb(0, {
                            TotalCount: 0,
                            Orders: [],
                        })
                    } else {
                        cond.OrderNo = params.OrderNo
                    }
                } else {
                    cond.OrderNo = orderNos
                }
            } else if (params.OrderNo) {
                cond.OrderNo = params.OrderNo
            }
            // ------------------------上面都是在获取订单号
            // 过滤出 查询的参数
            let filterBy = [
                'AreaId',
                'StartTime',
                'EndTime',
                'OrganizerName',
                'Type',
                'Status',
                'OrganizerLicenseId',
                'InnerDisplay',
                'OrganizerLicenseType',
                'ICPMainNo',
                'Auditor',
                'CompanyId',
                'IsDeleted',
                'PICMainLicenseId',
                'Channel',
            ]

            filterBy.forEach((filter) => {
                if (params[filter] !== undefined) {
                    cond[filter] = params[filter]
                }
            })

            if (params.Owner === 'MINE') {
                cond.Auditor = params['__ssoUser']
            } else if (params.Owner === 'NONE') {
                cond.Auditor = ''
            }
            const order = [['update_time', 'asc']]
            // ------------------------以上内容 都是在准备查询条件 为下面做铺垫
            // 获取订单 信息 和 计数
            let [Orders, TotalCount] = await Promise.all([
                getOrderInfo(
                    OrderModel,
                    cond,
                    params.Offset || 0,
                    params.Limit || 10,
                    order
                ),
                countOrderInfo(OrderModel, cond),
            ])

            // 判断是否查询到订单 没有 则直接返回
            if (!Orders || Orders.length === 0) {
                return this.cb(0, {
                    TotalCount: 0,
                    Orders: [],
                })
            }
            //获取到 查询到的所有订单号
            const orderNos = Orders.map((order) => order.OrderNo)
            // 循环找到该订单下 的 网站
            const websites = await getOrderWebInfo(OrderWebModel, {
                OrderNos: orderNos,
            })
            // 处理APP备案 平台信息的返回
            for (let website of websites) {
                if (website.InternetServiceType == 6) {
                    let platInfo = await OrderWebPlatInfoModel.findOne({
                        OrderWebId: website.Id
                    })
                    website.AppPlatformInformationList = platInfo?.AppPlatformInformationList || []
                }
            }
            // 将查询到的websites 信息 通过orderNo的统一 放置到Order下
            Orders.forEach((order) => {
                const orderNo = order.OrderNo
                order.WebSite = websites.filter(
                    (website) => website.OrderNo === orderNo
                )
            })
            // 返回最终的结果
            return this.cb(0, { Orders, TotalCount })
        } catch (e) {
            if (e instanceof Error) {
                return this.eh({
                    err: e,
                    code: 68001,
                })
            }
            this.eh(e)
        }
    }
}
