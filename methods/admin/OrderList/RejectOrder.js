/*
 * @Date: 2022-09-05 10:33:26
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2023-07-10 15:54:35
 * @FilePath: /newicp/methods/admin/OrderList/RejectOrder.js
 *
 * @Description: 这份代码用于处理管理员拒绝订单的请求，包括验证订单状态，更新订单状态，以及发送相关邮件通知等操作
 */
const Method = require('../../../libs/method')
const {
    OrderModel,
    OrderStatusEnum,
    CurtainStatusEnum,
    OrderWebModel,
    OrderHistoryModel,
} = require('../../../models')
const {
    getNextStatus,
} = require('../../../fns/admin_order_fns/orderStatusFuns')
const { parseJSON } = require('../../../fns/kits')
const _ = require('lodash')
const { Op } = require('sequelize')
const moment = require('moment')
const email = require('../../../libs/email')

// 定义RejectOrder类，继承自Method
module.exports = class RejectOrder extends Method {
    constructor(cb) {
        super(cb)
    }
    // 定义exec方法，处理订单拒绝逻辑
    async exec(params) {
        let self = this
        try {
            // 获取订单号
            let OrderNo = params.OrderNo
            // 查询订单
            let order = await OrderModel.findOne({
                where: {
                    OrderNo,
                    IsDeleted: 0,
                },
            })

            order = parseJSON(order)

            // 如果没有找到订单，抛出错误
            if (!order) {
                let err = new Error('没有发现该订单')
                err.code = 32151
                throw err
            }

            // 获取下一个订单状态
            let nextStatus = getNextStatus(
                order.Status,
                'reject',
                order.Type,
                order.Version
            )
            // 如果没有下一个状态，抛出错误
            if (!nextStatus) {
                let err = new Error('订单状态异常')
                err.code = 32154
                throw err
            }

            // 定义更新的数据
            let updateJson = {
                RejectReason: params.RejectReason,
                Status: nextStatus,
                Error: params.Error,
                Auditor: params['__ssoUser'],
            }
            // 如果有幕布状态，并且幕布状态包含0，则更新幕布状态为等待更改
            if (
                params.CurtainListStatus &&
                params.CurtainListStatus?.length !== 0 &&
                _.indexOf(params.CurtainListStatus, 0) !== -1
            ) {
                updateJson.CurtainStatus = CurtainStatusEnum.awaitChange
            }

            // 更新订单
            await OrderModel.update(updateJson, {
                where: {
                    OrderNo,
                },
            })
            // 查询订单的所有网站
            let websites = await OrderWebModel.findAll({
                where: {
                    OrderNo,
                },
            })
            websites = parseJSON(websites)

            let websiteUpdateIds = []
            if (websites.length > 0) {
                // 处理幕布状态
                if (params.CurtainListStatus?.length !== websites.length) {
                    let err = new Error('图片压缩失败')
                    err.code = 32155
                    throw err
                }
                for (let i = 0; i < websites.length; i++) {
                    if (params.CurtainListStatus[i] === 0) {
                        websiteUpdateIds.push(websites[i].Id)
                    }
                }
            }
            // 收集打回信息, 并处理新旧对应，后面写入日志
            let errInfo = params.Error
            let errLog = {}
            let existWebInfoErr = false

            for (let key in errInfo) {
                if (
                    key === 'Website' &&
                    JSON.stringify(errInfo['Website']) !== '{}'
                ) {
                    existWebInfoErr = true
                    continue
                }
                errLog[key] = {
                    reason: errInfo[key],
                    old: order[key],
                }
            }

            if (existWebInfoErr && websites.length > 0) {
                errLog['Website'] = []
                for (let Id in errInfo['Website']) {
                    let oldWeb = _.filter(websites, { Id: parseInt(Id) })
                    if (oldWeb?.length > 0) {
                        let newWebErr = {}
                        let webErrInfo = errInfo['Website'][Id]
                        newWebErr['Id'] = Id
                        for (let webKey in webErrInfo) {
                            newWebErr[webKey] = {
                                reason: webErrInfo[webKey],
                                ord: oldWeb[0][webKey],
                            }
                        }
                        errLog['Website'].push(newWebErr)
                    }
                }
            }
            // 如果有需要更新的网站，更新幕布状态
            if (websiteUpdateIds.length > 0) {
                await OrderWebModel.update(
                    {
                        CurtainStatus: 0,
                    },
                    {
                        where: {
                            Id: {
                                [Op.in]: websiteUpdateIds,
                            },
                        },
                    }
                )
            }

            // 将打回信息写入日志
            await OrderHistoryModel.create({
                OrderNo,
                Status: nextStatus, //此处记录打回的状态
                OperationTime: new Date(),
                Operator: params['__ssoUser'],
                Action: 'modify_order',
                Info: JSON.stringify(errLog),
            })
            // 发送邮件
            email
                .send({
                    ...order,
                    ...updateJson,
                    Website: websites,
                })
                .catch((err) =>
                    self.logger.getLogger('error').error(err.message)
                )
            // 返回成功
            return this.cb(0)
        } catch (e) {
            // 错误处理
            self.err(e)
        }
    }
}
