/*
 * @Date: 2022-09-05 10:33:26
 * @LastEditors: william.qian <EMAIL>
 * @LastEditTime: 2023-06-28 17:27:53
 * @FilePath: /newicp/methods/admin/OrderList/ModifyOrder.js
 * @Description: 审核侧的后台的订单修改接口。部分状态有权限限制
 */
const Method = require('../../../libs/method')
const { OrderModel, OrderStatusEnum } = require('../../../models')
const { parseJSON, isAdmin } = require('../../../fns/kits')

module.exports = class ModifyOrder extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({ OrderNo, Name, Value, __ssoUser }) {
        let self = this
        try {
            // 返回最终的结果
            let order = await OrderModel.findOne({
                where: {
                    OrderNo: OrderNo,
                },
            })

            order = parseJSON(order)

            if (!order) {
                // 没有发现该订单
                let err = new Error('没有发现该订单')
                err.code = 32151
                throw err
            }
            // 2023年06月28日17:27:07 增加，如果是特权管理员，可以修改
            if (
                [
                    OrderStatusEnum.GovAuditing,
                    OrderStatusEnum.GovAuditPass,
                ].includes(order.Status) &&
                !isAdmin(__ssoUser)
            ) {
                // 管局审核中 和审核通过状态 不可以修改
                let err = new Error('该订单状态不支持修改')
                err.code = 32153
                throw err
            }
            if (order.IsDeleted) {
                // 该订单已删除
                let err = new Error('订单已删除')
                err.code = 32152
                throw err
            }

            let updateJson = {
                [Name]: Value,
            }

            await OrderModel.update(updateJson, {
                where: {
                    OrderNo: OrderNo,
                },
            })

            return this.cb(0)
        } catch (e) {
            self.err(e)
        }
    }
}
