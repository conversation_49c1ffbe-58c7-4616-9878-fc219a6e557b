const Method = require('../../../libs/method')
const _ = require('lodash')
const { getTableModel, parseJSON } = require('../../../fns/kits')
const { Op } = require('sequelize')
const { getKeyAsync } = require('../../../fns/redisFuns')
const typeStatusCreatingMapping = require('../../../configs/common/order_type_creating_status.json')
let Order, OrderWeb, ICP, ICPWeb
const { checkOrder } = require('../../../fns/checkOrder')
const { verifyAPI } = require('../../../fns/verifyFun')

// 此接口作用为，为无法通过前端接口审核的客户操作的客户，由后台审核员手工提交
module.exports = class SubmitOrderByAuditor extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec(params = {}) {
        try {
            let self = this
            let icpDatabase = this.db.get('icp')
            let redis = this.redis.get()

            let redisCheck

            Order = getTableModel('t_order', icpDatabase)
            OrderWeb = getTableModel('t_order_web', icpDatabase)
            ICP = getTableModel('t_icp', icpDatabase)
            ICPWeb = getTableModel('t_web', icpDatabase)

            try {
                redisCheck = await getKeyAsync(redis, 'blockRule')
                redisCheck = JSON.parse(redisCheck)
            } catch (error) {
                return await Promise.reject(new self.Err(error, 30101))
            }

            // 取订单信息
            let oldDatas = await Promise.all([
                Order.findAll({
                    where: {
                        OrderNo: params.OrderNo,
                        IsDeleted: 0,
                    },
                }),
                OrderWeb.findAll({
                    where: {
                        OrderNo: params.OrderNo,
                    },
                }),
            ])
            // 未查到订单，可能是不在这个公司下
            if (oldDatas[0].length === 0)
                throw new this.Err(new Error('There is no such order'), 67007)

            // 结构处理
            let order = parseJSON(oldDatas[0][0])

            order.Website = parseJSON(oldDatas[1])

            console.log(order, 331)

            // 静态检查
            let statusInfo
            // 如果跳过验证，则不验状态锁
            if (order.IsExtractType != 0) {
                statusInfo = await CheckStatus(order, redisCheck)
                if (statusInfo.CanOperated === false) {
                    throw new this.Err(new Error(statusInfo.Message), 67080)
                }
            }

            // 检查类型是否允许此状态
            if (!checkTypeStatusCreating(order)) {
                throw new this.Err(
                    new Error('Creating status or type illegal'),
                    67003
                )
            }

            let checkRes = await checkOrder(redis, order)

            if (checkRes) {
                throw new this.Err(new Error(checkRes), 67004)
            }

            // 人的二要素检查

            // 取人与去重
            let personList = []
            if (order.PICMainLicenseType === 2) {
                personList.push({
                    PersoneName: order.PICMainName,
                    PersoneLicenseId: order.PICMainLicenseId,
                })
            }

            order.Website.forEach((element) => {
                if (
                    element.LicenseType === 2 &&
                    _.findIndex(personList, {
                        PersoneLicenseId: element.LicenseId,
                    }) === -1
                ) {
                    personList.push({
                        PersoneName: element.PICName,
                        PersoneLicenseId: element.LicenseId,
                    })
                }
            })

            let promiseList = []

            let NotMatchList = []
            console.log(personList)
            personList.forEach((element) => {
                let options = {
                    Name: element.PersoneName,
                    Id: element.PersoneLicenseId,
                    CompanyId: order.CompanyId,
                    Backend: 'IdAuth',
                    Action: 'VerifyBankCard2',
                    Source: 'ICP',
                    OrderNo: params.OrderNo,
                    Operator: params.Operator,
                }
                promiseList.push(
                    verifyAPI({
                        Action: 'VerifyBankCard2',
                        body: options,
                    }).then((returnBody) => {
                        console.log(returnBody)
                        if (
                            returnBody.Error === true ||
                            !returnBody.Response ||
                            returnBody.Response.IsMatch === 0
                        ) {
                            NotMatchList.push(element.PersoneName)
                        }
                    })
                )
            })

            await Promise.all(promiseList)

            if (NotMatchList.length !== 0) {
                throw new this.Err(
                    new Error(
                        '存在未通过二要素审核的人员' + NotMatchList.join('|')
                    ),
                    31201
                )
            }

            // 以上都过，更新状态
            try {
                await Order.update(
                    // 如果是变更网站或者变更主体，使状态做加法。其它类型的+0不变化
                    { Status: 2 },
                    {
                        where: {
                            OrderNo: params.OrderNo,
                        },
                    }
                )
            } catch (error) {
                throw new this.Err(error, 31201)
            }

            self.cb(0)
        } catch (e) {
            console.log(e, 333)
            if (e instanceof Error) {
                return self.eh({
                    err: e,
                    code: 67061,
                })
            }
            this.eh(e)
        }
    }
}
async function checkTypeStatusCreating(params) {
    return (
        typeStatusCreatingMapping[params.type] &&
        typeStatusCreatingMapping[params.type][params.status]
    )
}
async function CheckStatus(params, redisCheck) {
    // 核验已完成备案类型，是否可操作
    // todo 注销类的验证
    // params.ICPWebNo = params.ICPWebNo
    let Type = params.Type.toString()
    // 确定类型是否存在

    try {
        if (redisCheck[Type] && redisCheck[Type].length !== 0) {
            // 取省简称
            let referredPro = params.ICPMainNo[0]

            if (_.indexOf(redisCheck[Type], referredPro) !== -1) {
                return {
                    CanOperated: false,
                    Message:
                        '因该省管局系统升级，暂停提交该备案类型。恢复时间请参考专区公告。',
                }
            }
        }
    } catch (error) {
        console.log(error)
    }

    if (
        !params.ICPWebNo &&
        params.Website &&
        params.Website.length !== 0 &&
        params.Website[0].ICPWebNo
    ) {
        params.ICPWebNo = params.Website[0].ICPWebNo
    }

    let isReduplicate, orderCount, icpCount, webCount, webInfo
    switch (params.Type) {
        // 状态1呢？重写时发现没有Case1
        case 2: {
            // 取域名，老代码可能有问题,不过有问题的域名应该在验证时就拦截了 "Domain":"jdapi.com.cn", `%${params.Domain}%`
            // let domain = params.Website[0].Domain

            isReduplicate = await checkOrderWeb({
                Domain: { [Op.like]: `%"Domain":"${params.Domain}"%` },
                IsDeleted: 0,
                // fuzzy: `domain like '%"${params.Domain}"%' and  is_deleted  = 0 `	旧版代码
            })

            if (!isReduplicate) {
                return {
                    CanOperated: false,
                    Message: '该域名已存在流程中的订单，请勿重复提交',
                }
            }

            // 确定主体状态是否正常，是否在注销中，变更中（可能是已注销的）
            icpCount = await ICP.count({
                where: {
                    ICPMainNo: params.ICPMainNo,
                    Status: {
                        [Op.in]: [4, 7, 8],
                    },
                },
                // fuzzy: '(status = 4 or status = 7 or status = 8) '
            })

            if (icpCount != 0) {
                return {
                    CanOperated: false,
                    Message: '该主体下存在流程中的订单，请等待流程结束后再提交',
                }
            }

            break
        }
        case 3: {
            // 新增接入
            // 此互联网接入服务是否已存在
            // 是否有状态正常的备案号
            isReduplicate = await checkOrderWeb({
                ICPWebNo: params.ICPWebNo,
                IsDeleted: 0,
            })

            if (!isReduplicate) {
                return {
                    CanOperated: false,
                    Message: '该网站已存在流程中的订单，请勿重复提交',
                }
            }

            // 确定主体是否在流程中的，通过主体备案号，类型，注销与变更
            icpCount = await ICP.count({
                where: {
                    ICPMainNo: params.ICPMainNo,
                    Status: {
                        [Op.in]: [4, 7],
                        // fuzzy: ' (status = 4 or status = 7 ) '
                    },
                },
            })

            if (icpCount !== 0) {
                return { CanOperated: false, Message: '该主体已无法新增接入' }
            }

            // 确定是否存在有效接入
            webCount = await ICPWeb.count({
                where: { ICPWebNo: params.ICPWebNo, Status: 0 },
            })
            if (webCount != 0) {
                return {
                    CanOperated: false,
                    Message: '该网站在我司已存在备案，请勿重复提交',
                }
            }

            break
        }
        case 4: {
            // 注销主体
            // 全部不允许

            // 确定是否有在流程中，（注销中）
            orderCount = await Order.count({
                where: {
                    ICPMainNo: params.ICPMainNo,
                    Status: {
                        [Op.ne]: 12,
                    },
                    // fuzzy: '(status != 12) ',
                    IsDeleted: 0,
                },
            })
            if (orderCount != 0) {
                return {
                    CanOperated: false,
                    Message: '该主体已存在流程中的订单，请勿重复提交',
                }
            }

            // 确定此主体的状态是否正常
            icpCount = await ICP.count({
                where: { ICPMainNo: params.ICPMainNo, Status: 0 },
            })

            if (icpCount === 0) {
                return {
                    CanOperated: false,
                    Message: '该主体已无法执行注销功能，请勿重复提交',
                }
            }

            break
        }
        case 5: {
            // 注销网站
            // 通过网站备案号确定此网站是否有流程中的订单（变更）
            isReduplicate = await checkOrderWeb({
                ICPWebNo: params.ICPWebNo,
                IsDeleted: 0,
            })

            if (!isReduplicate) {
                return {
                    CanOperated: false,
                    Message: '该网站已存在流程中的订单，请勿重复提交',
                }
            }

            // 确定是否有在流程中，（注销中）
            orderCount = await Order.count({
                where: {
                    ICPWebNo: params.ICPWebNo,
                    Status: {
                        [Op.ne]: 12,
                    },
                    IsDeleted: 0,
                    // fuzzy: '(status != 12) ', IsDeleted: 0
                },
            })
            if (orderCount != 0) {
                return {
                    CanOperated: false,
                    Message: '该网站已存在流程中的订单，请勿重复提交',
                }
            }
            break
        }
        case 6: {
            // 注销接入
            // 通过网站备案号确定此网站是否有流程中的订单（变更）
            isReduplicate = await checkOrderWeb({
                ICPWebNo: params.ICPWebNo,
                IsDeleted: 0,
            })

            if (!isReduplicate) {
                return {
                    CanOperated: false,
                    Message: '该网站已存在流程中的订单，请勿重复提交',
                }
            }

            // 确定是否有在流程中，（注销中）
            orderCount = await Order.count({
                where: {
                    ICPWebNo: params.ICPWebNo,
                    Status: {
                        [Op.ne]: 12,
                    },
                    // fuzzy: '(status != 12) ',
                    IsDeleted: 0,
                },
            })
            if (orderCount != 0) {
                return {
                    CanOperated: false,
                    Message: '该网站已存在流程中的订单，请勿重复提交',
                }
            }
            break
        }
        case 7: {
            if (params.ICPId === -1) {
                return { CanOperated: false, Message: '无效已备案记录记录' }
            }

            let icpRecord = await ICP.findAll({
                where: { Id: params.ICPId },
            })

            let webRecords = await ICPWeb.findAll({
                where: {
                    MainId: params.ICPId,
                    ICPWebNo: {
                        [Op.in]: _.map(params.Website, 'ICPWebNo'),
                    },
                    // 'IN': {
                    // 	key: 'icp_web_no',
                    // 	value: _.map(params.Website, 'ICPWebNo')
                    // }
                },
            })
            if (icpRecord.length !== 1 || icpRecord[0].Status !== 0) {
                return {
                    CanOperated: false,
                    Message:
                        '当前主体状态下，无法变更，请回到已备案列表确定状态',
                }
            }

            if (
                webRecords.length === 0 ||
                _.findIndex(webRecords, function (o) {
                    return o.Status !== 0
                }) !== -1
            ) {
                return {
                    CanOperated: false,
                    Message:
                        '当前操作的网站存在无法变更的情况，请回到已备案列表确定状态',
                }
            }
            break
        }
        case 8: {
            // "8": "变更主体",
            // 非变更的流程中的订单是否存在
            // 确定是否有在流程中，（变更类不包括）
            orderCount = await Order.count({
                where: {
                    ICPMainNo: params.ICPMainNo,
                    // fuzzy: '(status != 12 and (type !=9 and type !=10 ) ) ',
                    [Op.and]: [
                        { Status: { [Op.ne]: 12 } },
                        { Type: { [Op.notIn]: [9, 10] } },
                    ],
                    IsDeleted: 0,
                },
            })
            if (orderCount != 0) {
                return {
                    CanOperated: false,
                    Message: '该主体已存在流程中的订单，请勿重复提交',
                }
            }

            // 确定主体是否正常
            icpCount = await ICP.count({
                where: { ICPMainNo: params.ICPMainNo, Status: 0 },
            })
            if (icpCount != 1) {
                return { CanOperated: false, Message: '该主体无法变更' }
            }
            break
        }
        case 9: {
            // 	"9": "变更网站",
            // 流程中  注销主体  不允许
            // 确定是否有在流程中，（注销主体）

            orderCount = await Order.count({
                where: {
                    // fuzzy: `(status != 12)  and (( type=  4	and icp_main_no='${params.ICPMainNo}'	)   or ( icp_web_no ='${params.ICPWebNo}'  and type in (9,5,6))) `,
                    [Op.and]: [
                        { Status: { [Op.ne]: 12 } },
                        {
                            [Op.or]: [
                                { Type: 4, ICPMainNo: params.ICPMainNo },
                                {
                                    ICPWeb: params.ICPWebNo,
                                    Type: { [Op.in]: [5, 6, 9] },
                                },
                            ],
                        },
                    ],
                    IsDeleted: 0,
                },
            })

            if (orderCount != 0) {
                return {
                    CanOperated: false,
                    Message: '该网站已存在流程中的订单，请勿重复提交',
                }
            }

            // 确定此网站的状态是否正常
            webInfo = await ICPWeb.findAll({
                where: {
                    ICPWebNo: params.ICPWebNo,
                    Status: { [Op.in]: [0, 10] },
                    // 'IN': {
                    // 	key: 'status',
                    // 	value: [10, 0]
                    // }
                },
            })
            // 如果没查到，且状态不是10
            if (webInfo.length === 0) {
                return {
                    CanOperated: false,
                    Message: '该网站已无法执行注销功能，请勿重复提交',
                }
            }
            return { CanOperated: true, oldStatus: webInfo[0].Status }
        }
        case 10: {
            // 	"9": "变更接入",
            // 流程中  注销主体  不允许

            // 确定是否有在流程中，（注销主体）
            orderCount = await Order.count({
                where: {
                    // fuzzy: `(status != 12)  and (( type=  4	and icp_main_no='${params.ICPMainNo}'	)   or ( icp_web_no ='${params.ICPWebNo}'  and type in (10,5,6))) `,
                    [Op.or]: [
                        { Type: 4, ICPMainNo: params.ICPMainNo },
                        {
                            ICPWebNo: params.ICPWebNo,
                            Type: { [Op.in]: [5, 6, 10] },
                        },
                    ],
                    Status: { [Op.ne]: 12 },
                    IsDeleted: 0,
                },
            })

            if (orderCount != 0) {
                return {
                    CanOperated: false,
                    Message: '该网站已存在流程中的订单，请勿重复提交',
                }
            }

            // 确定此网站的状态是否正常
            webInfo = await ICPWeb.findAll({
                where: {
                    ICPWebNo: params.ICPWebNo,
                    Status: {
                        [Op.in]: [9, 0],
                    },
                },
            })
            if (webInfo.length === 0) {
                return {
                    CanOperated: false,
                    Message: '该网站已无法执行变更功能，请勿重复提交',
                }
            }
            return { CanOperated: true, oldStatus: webInfo[0].Status }
        }
        default:
            return { CanOperated: true }
    }
    return { CanOperated: true }
}

async function checkOrderWeb(condition) {
    // 检查订单网站中，是否有符合条件的订单
    let orderWebInfo = await OrderWeb.findAll({ where: condition })
    if (orderWebInfo.length != 0) {
        // 如果存在，确定订单状态
        let orderInfo = await Order.findAll(
            {
                where: {
                    Status: {
                        [Op.ne]: 12,
                    },
                    IsDeleted: 0,
                    OrderNo: {
                        [Op.in]: _.map(orderWebInfo, 'OrderNo'),
                    },
                },
            }
            //旧版代码 {
            // 	fuzzy: `Status != 12 `,
            // 	IsDeleted: 0,
            // 	'IN': {
            // 		key: 'order_no',
            // 		value: _.map(orderWebInfo, 'OrderNo')
            // 	}
            // }
        )
        if (orderInfo.length !== 0) {
            return false
        }
    }
    return true
}
