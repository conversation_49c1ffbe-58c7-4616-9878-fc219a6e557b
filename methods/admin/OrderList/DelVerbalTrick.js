/**
 * @file API DelVerbalTrick 删除展示话术，支持查询指定话术类型
 * <AUTHOR>
 */
const Method = require('../../../libs/method')
const _ = require('lodash')
const VERBAL_TYPE_DICT = {
    Common: '通用',
    Main: '主体',
    Web: '网站',
}
const VERBAL_TYPE_LIST = Object.keys(VERBAL_TYPE_DICT)
module.exports = class DelVerbalTrick extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({ VerbalType, VerbalTrick }) {
        let self = this
        let redis = this.redis.get()

        try {
            // 执行查询
            try {
                // 获取Redis结果
                let queryRows = await redis.hmget('icp_verbal_tricks', [
                    VerbalType,
                ])

                if (queryRows.length === 0 || queryRows[0] === null) {
                    return self.cb(0)
                } else {
                    queryRows = JSON.parse(queryRows[0])
                }
                // 删除
                let vIndex = queryRows.indexOf(VerbalTrick)
                if (vIndex === -1) {
                    return self.cb(0)
                } else {
                    delete queryRows[vIndex]
                    queryRows = _.filter(queryRows, function (o) {
                        return o !== undefined
                    })
                }
                await redis.hmset('icp_verbal_tricks', {
                    [VerbalType]: JSON.stringify(queryRows),
                })
            } catch (error) {
                throw new this.Err(error, 65702)
            }
            self.cb(0)
        } catch (e) {
            self.eh(e)
        }
    }
}
