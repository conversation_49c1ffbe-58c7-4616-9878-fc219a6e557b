/**
 * @file API DeleteOrder,删除订单，释放关联主体的状态
 * <AUTHOR>
 * @return {} 成功与否
 * todo 确定是否增加权限管制
 */
const henganApiPromise = require('../../../libs/henganApiPromise')

const Method = require('../../../libs/method')
const ObjectId = require('mongodb').ObjectId
const {
    OrderModel,
    OrderWebModel,
    OrderStatusEnum,
    OrderHistoryModel,
    ICPModel,
    ICPWebModel,
} = require('../../../models')
const { Op } = require('sequelize')
const { getTableModel, parseJSON } = require('../../../fns/kits')
const _ = require('lodash')

module.exports = class DeleteOrder extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({ OrderNo, __ssoUser } = {}) {
        let self = this
        let orderList, orderInfo, orderWeb
        try {
            // 确定订单是否存在，与订单的细节情况
            ;[orderList, orderWeb] = await Promise.all([
                OrderModel.findAll({
                    where: {
                        OrderNo,
                        IsDeleted: 0,
                    },
                }),
                // 获取网站的备案号
                OrderWebModel.findAll({
                    where: {
                        OrderNo,
                    },
                }),
            ])
            orderList = parseJSON(orderList)
            orderWeb = parseJSON(orderWeb)
            // 长度
            if (orderList.length === 0) {
                let err = new Error('删除订单时时，此订单不存在')
                err.code = 32114
                throw err
            }

            orderInfo = orderList[0]
            // 开始删除
            // 订单与网站的是否删除置为1
            try {
                await Promise.all([
                    OrderModel.update(
                        { IsDeleted: 1 },
                        {
                            where: {
                                OrderNo,
                            },
                        }
                    ),
                    OrderWebModel.update(
                        { IsDeleted: 1 },
                        {
                            where: {
                                OrderNo,
                            },
                        }
                    ),
                ])
            } catch (error) {
                console.log(error)
                let err = new Error(
                    '删除订单时时，删除过程出错' + error.message
                )
                err.code = 32116
                throw err
            }

            // 完成关联已备案主体的信息
            // 三合一变更,已备案恢复
            try {
                if (orderInfo.Type === 7) {
                    await ICPModel.update(
                        { Status: 0 },
                        {
                            where: {
                                Status: 7,
                                Id: orderInfo.ICPId,
                            },
                        }
                    )

                    await ICPWebModel.update(
                        { Status: 0 },
                        {
                            where: {
                                MainId: orderInfo.ICPId,
                                IsDeleted: 0,
                                Status: 7,
                            },
                        }
                    )
                }
                // 变更、注销主体 新增同时变更主体
                if (
                    orderInfo.Type === 8 ||
                    orderInfo.Type === 4 ||
                    ((orderInfo.Type === 2 || orderInfo.Type === 3) &&
                        orderInfo.NeedModifyIcp)
                ) {
                    // 判断是否还存在变更订单 或者新增+变更的订单 若存在则不更新
                    let orderCount = await OrderModel.count({
                        where: {
                            ICPMainNo: orderInfo.ICPMainNo,
                            IsDeleted: 0,
                            Status: {
                                [Op.ne]: 12,
                            },
                            [Op.or]: [
                                { Type: 8 },
                                {
                                    Type: {
                                        [Op.in]: [2, 3],
                                    },
                                    NeedModifyIcp: 1,
                                },
                            ],
                        },
                    })
                    // 没有上述类型订单 或者删除的这个订单是注销主体的 就恢复主体状态
                    if (orderCount == 0 || orderInfo.Type === 4) {
                        await ICPModel.update(
                            { Status: 0 },
                            {
                                where: {
                                    ICPMainNo: orderInfo.ICPMainNo,
                                    Status: {
                                        [Op.in]: [4, 8],
                                    },
                                },
                            }
                        )
                    }
                }
                // 新增的变更类订单，网站
                // 考虑网站与主体存在兼容的情况
                if (
                    orderInfo.Type === 9 ||
                    orderInfo.Type === 10 ||
                    orderInfo.Type === 5 ||
                    orderInfo.Type === 6
                ) {
                    // 查网站的状态
                    let webData = await ICPWebModel.findOne({
                        where: {
                            ICPWebNo:
                                orderWeb[0]?.ICPWebNo &&
                                orderWeb[0]?.ICPWebNo.trim() !== ''
                                    ? orderWeb[0].ICPWebNo
                                    : orderInfo.ICPWebNo,
                        },
                    })
                    webData = parseJSON(webData)
                    if (
                        webData.Status === 9 ||
                        webData.Status === 10 ||
                        webData.Status === 5 ||
                        webData.Status === 6 ||
                        webData.Status === 19
                    ) {
                        ICPWebModel.update(
                            { Status: 0 },
                            {
                                where: {
                                    ICPWebNo: webData.ICPWebNo,
                                    Status: {
                                        [Op.in]: [9, 10, 5, 6, 19],
                                    },
                                },
                            }
                        )
                    }
                }
                await OrderHistoryModel.create({
                    OrderNo: OrderNo,
                    OperationTime: new Date(),
                    Status: orderInfo.Status,
                    Operator: __ssoUser,
                    Action: 'delete_order',
                })
            } catch (error) {
                let err = new Error(
                    '删除订单时时，恢复已备案的过程出错' + error.message
                )
                err.code = 32117
                throw err
            }
            // 编辑状态的删除 就没比较提交到傲顿了
            if (orderInfo.Status !== 1) {
                // 将删除操作同步到傲顿
                // 将删除同步到傲顿
                let henganReqConf = {}
                // 备案类型
                //     1.	新增备案
                //     2.	新增网站
                //     3.	新增接入
                //     4.	变更备案
                //     5.	变更主体
                //     6.	变更网站
                //     7.	变更接入
                //     8.	注销备案
                //     9.	注销网站
                //     10.	注销接入

                //新增备案、变更备案、变更主体、注销备案时为主体cid；
                //新增网站、变更网站、注销网站时为网站cid；
                //新增接入、变更接入、注销接入时为接入cid
                switch (orderInfo.Type) {
                    case 1:
                        // 主体订单id为cmainid
                        henganReqConf.Cid = orderInfo.Id
                        henganReqConf.Type = 1
                        break
                    case 2:
                        // 新增网站 网站订单中的id 作为cid
                        henganReqConf.Cid = orderWeb[0].Id
                        henganReqConf.Type = 2
                        break
                    case 3:
                        // 新增接入 网站订单中的id * 100
                        henganReqConf.Cid = orderWeb[0].Id * 100
                        henganReqConf.Type = 2

                        break
                    case 4:
                        henganReqConf.Cid = orderInfo.CMainId
                        henganReqConf.Type = 8
                        break
                    case 7:
                        henganReqConf.Cid = orderInfo.CMainId
                        henganReqConf.Type = 4
                        break
                    case 8:
                        // 注销主体 变更备案 变更主体 都是主体订单中的CMainId
                        henganReqConf.Type = 5
                        henganReqConf.Cid = orderInfo.CMainId
                        break
                    case 5:
                        // 注销网站 信息只有一条记录在order表 故取order表中的CWebsiteId
                        henganReqConf.Cid = orderInfo.CWebsiteId
                        henganReqConf.Type = 9
                        break
                    case 6:
                        // 取消接入 信息只有一条记录在order表 故取订单中的CWebsiteId * 100
                        henganReqConf.Cid = orderInfo.CWebsiteId * 100
                        henganReqConf.Type = 10

                        break
                    case 9:
                        // 变更网站 网站订单中的CWebsiteId 作为cid
                        henganReqConf.Cid = orderWeb[0].CWebsiteId
                        henganReqConf.Type = 6

                        break
                    case 10:
                        // 变更接入 网站中的CWebsiteId * 100
                        henganReqConf.Type = 7
                        henganReqConf.Cid = orderWeb[0].CWebsiteId * 100
                        break
                }

                try {
                    let res = await henganApiPromise(
                        'DeleteOrderAction',
                        henganReqConf
                    )
                    if (res.RetCode !== 0) {
                        return self.cb(32116, { Message: res.msg })
                    }
                } catch (err) {
                    return self.cb(32116, { Message: err.message })
                }
            }
            return this.cb(0)
        } catch (e) {
            self.err(e)
        }
    }
}
