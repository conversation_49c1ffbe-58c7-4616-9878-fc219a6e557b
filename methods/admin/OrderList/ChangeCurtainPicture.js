'use strict'

const Method = require('../../../libs/method')
const { getTableModel, parseJSON } = require('../../../fns/kits')
const producer = require('../../../libs/producer')
const logger = require('../../../libs/logger')
const { uploadHit, copyFile, headerFile } = require('../../../fns/uflieFuns')
const uuid = require('uuid')
module.exports = class ChangeCurtainPicture extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({ OrderNo, LicenseId, Picture } = {}) {
        let self = this
        try {
            let icpDatabase = this.db.get('icp')
            let OrderModel = getTableModel('t_order', icpDatabase)
            let OrderWebModel = getTableModel('t_order_web', icpDatabase)
            let PictureModel = getTableModel('t_picture', icpDatabase)
            let params = {
                OrderNo,
                LicenseId,
                Picture,
                UUID: uuid.v4(),
            }
            // log 中type 为4的是 CheckVideo的日志
            let [OrderInfos, OrderWebInfos] = await Promise.all([
                OrderModel.findAll({
                    where: {
                        OrderNo,
                    },
                }),
                OrderWebModel.findAll({
                    where: {
                        OrderNo,
                        LicenseId,
                    },
                }),
            ])

            OrderInfos = parseJSON(OrderInfos)
            OrderWebInfos = parseJSON(OrderWebInfos)
            if (OrderInfos.length === 0 || OrderWebInfos.length === 0) {
                let error = new Error('not found this order')
                error.code = 35002
                throw error
            }

            if (OrderInfos[0].Status === 12 || OrderInfos[0].Status === 11) {
                let error = new Error(
                    'this status cannot change this curtain picture'
                )
                error.code = 35003
                throw error
            }

            logger
                .getLogger('access')
                .info(
                    '[' + params.UUID + ']',
                    'before change curtain picture',
                    OrderWebInfos[0]
                )
            logger
                .getLogger('access')
                .info(
                    '[' + params.UUID + ']',
                    'change curtain picture start',
                    params
                )
            if (OrderWebInfos[0].CurtainPicture !== Picture) {
                //如果是从临时文件夹拿的文件，将从临时目录中拿取的图片插入到长期存储的文件夹下
                const filename = Picture.split(
                    `${global.CONFIG.tmpUS3PictureDir}/`
                )
                if (filename.length > 1) {
                    // 为新文件换名字  截取之前的目录就好
                    params.Picture = filename[1]

                    // 获取已存在的文件的Etag FileType 等信息
                    const PicInfoRes = await headerFile(Picture)
                    const picHeaders = PicInfoRes.headers
                    // 利用已存在文件 秒传新文件  返回的etag中带了两层引号 所以要去除
                    // await uploadHit(
                    //     picHeaders.etag.split('"').join(''),
                    //     params.Picture,
                    //     picHeaders['content-length'],
                    //     picHeaders['content-type']
                    // )
                    await copyFile(
                        Picture,
                        params.Picture,
                        picHeaders['content-type']
                    )
                    //新文件名截取，上传至根目录下
                }
                // 更新原有的picture在t_picture表中
                await PictureModel.update(
                    {
                        Url: params.Picture,
                    },
                    {
                        where: {
                            CompanyId: OrderInfos[0].CompanyId,
                            Url: OrderWebInfos[0].CurtainPicture,
                        },
                    }
                )
                logger
                    .getLogger('access')
                    .info(
                        '[' + params.UUID + ']',
                        'send save picture task to mq',
                        params
                    )
                // 发送修改幕布照的 mq 因为mq可以批量修改该订单下的 同一个网站负责人的所有幕布照
                await producer.send({
                    type: 'icp',
                    topic: 'SavePicture',
                    data: params,
                })
            }

            return this.cb(0)
        } catch (e) {
            if (e instanceof Error) {
                return self.eh({
                    err: e,
                    code: 35004,
                })
            }
            self.eh(e)
        }
    }
}
