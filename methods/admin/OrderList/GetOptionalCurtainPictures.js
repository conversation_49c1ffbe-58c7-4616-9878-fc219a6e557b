const Method = require('../../../libs/method')
const { getTableModel, parseJSON } = require('../../../fns/kits')
const { getPictureFromPrefix } = require('../../../fns/uflieFuns')
const LogType = require('../../../configs/common/verify_api_type.json')
const { Op } = require('sequelize')
const _ = require('lodash')
module.exports = class GetOptionalCurtainPictures extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({ OrderNo, LicenseId } = {}) {
        let self = this
        try {
            let icpDatabase = this.db.get('icp')
            let LogModel = getTableModel('t_verify_log', icpDatabase)

            let result = {
                Pictures: [],
            }

            // 取最新的验证视频的日志
            let logInfos = await LogModel.findAll({
                attributes: [
                    'UUID',
                    'Type',
                    'DataContent',
                    'Result',
                    'Response',
                ],
                where: {
                    OrderNo,
                    Type: {
                        [Op.in]: [
                            LogType.CheckVideoAndGetBestPicture,
                            LogType.ValidateVideo,
                        ],
                    },
                },
                order: [['create_time', 'desc']],
            })

            logInfos = parseJSON(logInfos)

            if (logInfos.length === 0) {
                // 没有查询到信息 或者 Result 不为0  都认为 不通过
                result.Message = '没有提交过视频验证，没有可替换的照片'
                return this.cb(0, result)
            }

            // 过滤出 该负责人的 验证记录  只取最近的一条
            let getBestPictureInfos = _.filter(logInfos, {
                Type: LogType.CheckVideoAndGetBestPicture,
                DataContent: {
                    LicenseId: LicenseId,
                },
            })

            if (
                getBestPictureInfos.length === 0 ||
                getBestPictureInfos[0]?.Result !== 0
            ) {
                //没有查询到 或者结果为失败都认为是 最后一次没有成功
                result.Message =
                    '最新一次视频换脸与光线过暗验证没有成功，没有可替换的照片'
                return this.cb(0, result)
            }

            // 通过UUID关联 验证视频的记录 ---UUID可能有多个 取最后一条
            // 这种方法其实也不是没有漏洞，，，，如果多次使用二维码提交，  UUID一样，最新一次请求失败，还没有来得及记录，，， 就会取到上一条的了，，，不过概率不大，暂时忽略吧。。。
            let checkVideoInfos = _.filter(logInfos, {
                Type: LogType.ValidateVideo,
                UUID: getBestPictureInfos[0]?.UUID,
            })

            if (
                checkVideoInfos.length === 0 ||
                checkVideoInfos[0]?.Result !== 0 ||
                checkVideoInfos[0]?.Response.RetCode !== 0
            ) {
                //没有查询到 或者结果为失败都认为是 最后一次没有成功
                result.Message = '最新一次视频验证没有通过，没有可替换的照片'
                return this.cb(0, result)
            }

            let checkVideoInfo = checkVideoInfos[0]

            // 一系列验证结束，，，， 说明最近一次提交视频验证  成功

            // 获取最近一次验证成功的 图片
            result.Pictures.push(checkVideoInfo.Response.FileName)

            /**如果 需要从 视频中取照片
             * 1.开启 上传视频时，的照片存储  ，位置/fns/verifyFun.js  ffmpegParseVideo()
             * 2.开启下面代码，从拉取从视频中截图的图片
             * 3.视频名称 从验证视频的文件中取出
             *
             * checkvedio 后面更改了 存储日志的格式 此处做新老的兼容
             * 原来是存file(文件名) 后来是寸 vedio（在ufile中的连接）
             * file 格式  asdfadfa.png
             * vedio 格式  Video":"http://icp.cn-bj.ufileos.com/e83cc303c8f4ccbd7c4ae089cc3f1059f2686feb.mp4?UCloudPublicKey=4E9UU0VhDy6mp5pBg6YhGi5V%2B6Ag2NjoOnUSLDUl6INJ%
             * 都需要截取 名字
             */
            let vedioName = checkVideoInfo?.DataContent?.File
                ? checkVideoInfo?.DataContent?.File.split('.')[0]
                : checkVideoInfo?.DataContent?.Video
                ? checkVideoInfo?.DataContent?.Video.split(
                      global.CONFIG.ufile.target + '/'
                  )[1]
                      .split('?')[0]
                      .split('.')[0]
                : false
            // 从ufile的临时目录中 查找 前缀 为 vedioName_  的文件
            let pictures
            if (vedioName) {
                pictures = await getPictureFromPrefix(
                    `${global.CONFIG.tmpUS3PictureDir}/${vedioName}_`
                )
            }

            if (pictures.length !== 0) {
                result.Pictures.push(...pictures)
            }

            if (result.Pictures.length === 0) {
                result.Message = '没有可替换的照片'
            }

            return this.cb(0, result)
        } catch (e) {
            if (e instanceof Error) {
                return self.eh({
                    err: e,
                    code: 35001,
                })
            }
            self.eh(e)
        }
    }
}
