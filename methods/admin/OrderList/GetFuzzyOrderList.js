/*
 * @Date: 2022-10-17 15:59:08
 * @LastEditors: william.qian <EMAIL>
 * @LastEditTime: 2023-09-27 15:31:30
 * @FilePath: /newicp/methods/admin/OrderList/GetFuzzyOrderList.js
 */
'use strict'

const Method = require('../../../libs/method')
const { parseJSON } = require('../../../fns/kits')
const _ = require('lodash')
const { OrderModel, OrderWebModel } = require('../../../models')
const { Op } = require('sequelize')
/**
 * 查看订单信息模糊查询API
 */
module.exports = class GetFuzzyOrderList extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({
        OrderNo,
        OrganizerName,
        ICPMainNo,
        Domain,
        ICPWebNo,
        Offset,
        Limit,
        IsDeleted = false, // 新增的可选入参，用来确定订单是否删除
    } = {}) {
        let self = this

        try {
            if (
                !OrderNo &&
                !OrganizerName &&
                !ICPMainNo &&
                !Domain &&
                !ICPWebNo
            ) {
                return this.eh({
                    err: new Error(
                        '至少需要提供一个查询参数：OrderNo, OrganizerName, ICPMainNo, Domain, ICPWebNo'
                    ),
                    code: 32804,
                })
            }

            let orderCondition = [],
                orderWebCondition = []
            if (OrderNo) {
                orderCondition.push({
                    OrderNo: {
                        [Op.like]: `%${OrderNo}%`,
                    },
                })
            }
            if (OrganizerName) {
                orderCondition.push({
                    OrganizerName: {
                        [Op.like]: `%${OrganizerName}%`,
                    },
                })
            }
            if (ICPMainNo) {
                orderCondition.push({
                    ICPMainNo: {
                        [Op.like]: `${ICPMainNo}%`,
                    },
                })
            }
            if (Domain) {
                orderWebCondition.push({
                    Domain: {
                        [Op.like]: `%"${Domain}"%`,
                    },
                })
            }
            if (ICPWebNo) {
                orderCondition.push({ ICPWebNo })
                orderWebCondition.push({ ICPWebNo })
            }

            let [orders, orderWebs] = await Promise.all([
                OrderModel.findAll({
                    where: {
                        IsDeleted,
                        [Op.or]: orderCondition,
                    },
                    attributes: ['OrderNo'],
                }),
                OrderWebModel.findAll({
                    where: {
                        [Op.or]: orderWebCondition,
                    },
                    attributes: ['OrderNo'],
                }),
            ])
            orders = parseJSON(orders)
            orderWebs = parseJSON(orderWebs)
            let orderNos = new Set()
            _.forEach(orders, (order) => orderNos.add(order.OrderNo))
            _.forEach(orderWebs, (orderweb) => orderNos.add(orderweb.OrderNo))
            orderNos = Array.from(orderNos)
            if (orderNos.length === 0) {
                return this.cb(0, { Orders: [], TotalCount: 0 })
            } else {
                let { rows: Orders, count: TotalCount } =
                    await OrderModel.findAndCountAll({
                        where: {
                            OrderNo: {
                                [Op.in]: orderNos,
                            },
                        },
                        distinct: true,
                        include: [
                            {
                                model: OrderWebModel,
                                as: 'Website',
                                required: false,
                            },
                        ],
                        offset: Offset,
                        limit: Limit,
                    })
                Orders = parseJSON(Orders)
                return this.cb(0, { Orders, TotalCount })
            }
        } catch (e) {
            self.err(e)
        }
    }
}
