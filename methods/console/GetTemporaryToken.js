/**
 * @file API GetTemporaryToken 生成TokenId,tokenId存数据库，实际对应着账号STS的临时用户密钥
 * <AUTHOR>
 * @param {CompanyId} 公司Id 必选参数,由网关传入条件
 * @return {Object} 返回符合条件的记录,格式如下
 * 错误码:
 * 67036: 签名过出错
 * 67011: 调用生成STS Token出错
 * 67038: Token加密出错
 */

const Method = require('../../libs/method')
const CryptoJS = require('crypto-js')
const base64url = require('base64-url')
const uuid = require('uuid/v4')
const { getTableModel, parseJSON } = require('../../fns/kits')
const { getSTSToken } = require('../../fns/getSTSToken')

module.exports = class GetTemporaryToken extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({
        CompanyId,
        Channel,
        organization_id,
        LicenseId,
        LicenseType,
        OrderNo,
    } = {}) {
        let self = this
        try {
            let icpDatabase = this.db.get('icp')
            const redis = this.redis.get()
            const TokenModel = getTableModel('t_token', icpDatabase)
            const OrderWebModel = getTableModel('t_order_web', icpDatabase)
            // 获取按照分流数据
            let V2CompanyIds = await redis.smembers('V2CompanyIds')
            // 确定此订单是否已经有Token了，无论是否有。都取出来
            let Version =  V2CompanyIds &&
            (V2CompanyIds.includes('ALLIN') ||
                V2CompanyIds.includes(CompanyId.toString())) ? 'v2' : 'v1'
            let tokenInfo, session
            
            if (+LicenseType !== 2) {
                Version = 'v1'
            }
            tokenInfo = await TokenModel.findAll({
                order: [['Id', 'DESC']],
                where: { CompanyId, LicenseId, LicenseType, OrderNo },
            })

            tokenInfo = parseJSON(tokenInfo)

            if (tokenInfo && tokenInfo.length > 0) {
                return this.cb(0, { Token: tokenInfo[0].Token, Version })
            }

            // 确定数据库中没有，生成新的
            let { RetCode, Data, Message } = await getSTSToken(CompanyId, self)

            if (RetCode === 0 && Message === 'success') {
                // 得到的请求，做AES对称加密（其实最好是非对称，但前端支持的库少）
                Data = CryptoJS.AES.encrypt(
                    JSON.stringify(Data),
                    global.CONFIG.SessionKey
                )

                Data = base64url.encode(Data.toString())
                session = Data
            } else {
                await Promise.reject(new self.Err(new Error(Message), 67038))
            }

            // 保存结果
            let token = uuid()
            TokenModel.create({
                Channel,
                CompanyId, //网关传入
                OrganizationId: organization_id,
                Token: token,
                LicenseId,
                LicenseType,
                OrderNo,
                Session: session,
            })
                .then(() => {
                    // todo 过期控制
                    return OrderWebModel.update(
                        { CurtainUUID: token },
                        {
                            where: {
                                LicenseId,
                                LicenseType,
                                OrderNo,
                            },
                        }
                    )
                })
                .then(() => {
                    return this.cb(0, { Token: token, Version })
                })
                .catch((e) => {
                    return Promise.reject(new self.Err(e, 67038))
                })
        } catch (e) {
            if (e instanceof Error) {
                return self.eh({
                    err: e,
                    code: 67076,
                })
            }
            self.eh(e)
        }
    }
}
