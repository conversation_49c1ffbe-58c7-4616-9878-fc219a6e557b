const Method = require('../../libs/method')
const _ = require('lodash')
const { getTableModel, parseJSON } = require('../../fns/kits')
const { Op } = require('sequelize')
const { OrderStatusEnum } = require('../../models')
const { OrderWebPlatInfoModel } = require('../../mongoModels/icp')

// 更新网站，单个网站-》多个网站-》单个  吐了
// 2023年04月06日16:01:22 增加功能，验证网站的结构体中域名证书的情况

module.exports = class ModifyOrderWeb extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({ Website, CompanyId, OrderNo, Step, EditStatus } = {}) {
        let self = this
        let icpDatabase = this.db.get('icp')

        const Order = getTableModel('t_order', icpDatabase)
        const OrderWeb = getTableModel('t_order_web', icpDatabase)

        let webSeleltJSON = {
            attributes: ['Id'],
            where: { OrderNo, IsDeleted: 0 },
        }

        try {
            var [orderList, orderWebIdList] = await Promise.all([
                // 查此订单是否存在
                Order.findAll({
                    attributes: ['Type', 'Id'],
                    where: {
                        CompanyId,
                        OrderNo,
                        Status: {
                            [Op.in]: [
                                OrderStatusEnum.Editing,
                                OrderStatusEnum.AuditRefuse,
                                OrderStatusEnum.ReAuditRefuse,
                                OrderStatusEnum.GovAuditRefuse,
                            ],
                        },
                    },
                }),
                // 查网站情况
                OrderWeb.findAll(webSeleltJSON),
            ])

            // 如果结果为1，确定订单在此公司下
            if (orderList.length !== 1) {
                throw new self.Err(
                    new Error('在对应公司下找不到可编辑的订单'),
                    67060
                )
            }

            orderList = parseJSON(orderList)
            orderList = orderList[0]
            orderWebIdList = parseJSON(orderWebIdList)

            // 检查网站ID是否存在于订单网站ID列表中
            if (
                Website.Id &&
                !orderWebIdList.some((orderWeb) => orderWeb.Id === Website.Id)
            ) {
                // 如果不存在，抛出错误
                throw new self.Err(
                    new Error('找不到可编辑的网站，请确定是否已删除'),
                    67060
                )
            }

            // 检查订单类型和网站ID，如果订单类型为2或3（新增网站或新增接入），并且已有网站存在，抛出错误
            if (
                !Website.Id &&
                [2, 3].includes(parseInt(orderList.Type)) &&
                orderWebIdList.length > 0
            ) {
                throw new self.Err(
                    new Error('订单类型为新增网站或新增接入时，仅允许一个网站'),
                    67082
                )
            }

            if (Website.NullPreAppoval == 1) Website.PreAppoval = []
            // 处理数组置空问题，因为控制台穿不过来空数组，目前ModifyOrderWeb接收完整的参数 故若参数未传就置空
            if (!Website.OperatingPlatformType)
                Website.OperatingPlatformType = []
            if (!Website.UseThirdPartySdkServiceDetails)
                Website.UseThirdPartySdkServiceDetails = []
            delete Website.NullPreAppoval
            delete Website.Index
            delete Website.IPType
            Website.OrderNo = OrderNo
            let promiseList = []

            if (Website.Id) {
                try {
                    promiseList.push(
                        OrderWeb.update(Website, {
                            where: { OrderNo, Id: Website.Id },
                        })
                    )
                } catch (error) {
                    throw new self.Err(error, 67062)
                }
            } else {
                try {
                    // todo 增加检查，如果是类型2、类型3.不可以创建
                    promiseList.push(OrderWeb.create(Website))
                } catch (error) {
                    throw new self.Err(error, 67063)
                }
            }

            let orderUpdateJSON = {}
            // if (Step) {
            // 	orderUpdateJSON.Step = Step
            // }

            orderUpdateJSON.Step = Step || 2

            if (EditStatus) {
                orderUpdateJSON.EditStatus = EditStatus
            }

            if (JSON.stringify(orderUpdateJSON) !== '{}') {
                promiseList.push(
                    Order.update(orderUpdateJSON, {
                        where: { Id: orderList.Id },
                    })
                )
            }

            let [WebInfo, OrderInfo] = await Promise.all(promiseList)
            WebInfo = parseJSON(WebInfo)
            // 判断是否是APP备案 若是 判断是否传参 AppPlatformInformationList
            if (Website.InternetServiceType == 6) {
                await OrderWebPlatInfoModel.findOneAndUpdate(
                    { OrderWebId: Website.Id || WebInfo.Id },
                    {
                        $set: {
                            AppPlatformInformationList:
                                Website.AppPlatformInformationList || [],
                        },
                    },
                    { upsert: true }
                )
            }
            return this.cb(0, {
                Id: Website.Id || WebInfo.Id,
            })
        } catch (e) {
            console.log(e, 333)
            if (e instanceof Error) {
                return self.eh({
                    err: e,
                    code: 67061,
                })
            }
            this.eh(e)
        }
    }
}
