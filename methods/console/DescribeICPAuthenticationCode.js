const Method = require('../../libs/method')
const { getTableModel, parseJSON } = require('../../fns/kits')
const { Op, Error: SequelizeError } = require('sequelize')
const _ = require('lodash')
// 获取公司下IP对应的授权码信息
module.exports = class DescribeICPAuthenticationCode extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec(params = {}) {
        let self = this
        let icpDatabase = this.db.get('icp')

        const AuthCodeModel = getTableModel(
            't_authentication_code',
            icpDatabase
        )
        const OrderWeb = getTableModel('t_order_web', icpDatabase)
        try {
            let codeInfo = await AuthCodeModel.findAll({
                where: {
                    CompanyId: params.CompanyId,
                    IP: params.IP,
                },
            })
            codeInfo = parseJSON(codeInfo)
            let isUse = await OrderWeb.findAll({
                where: {
                    AuthenticationCode: {
                        [Op.in]: _.map(codeInfo, 'Code'),
                    },
                },
            })
            isUse = parseJSON(isUse)
            for (let index = 0; index < codeInfo.length; index++) {
                _.findIndex(isUse, [
                    'AuthenticationCode',
                    codeInfo[index].Code,
                ]) === -1
                    ? (codeInfo[index].Status = '未使用')
                    : (codeInfo[index].Status = '已使用')

                if (codeInfo[index].IsBlock === 1) {
                    codeInfo[index].Status = '已失效'
                }
                codeInfo[index] = _.pick(codeInfo[index], [
                    'Code',
                    'TargetEmail',
                    'Status',
                ])
            }
            return this.cb(0, {
                CodeList: codeInfo,
            })
        } catch (e) {
            if (e instanceof SequelizeError) {
                let err = new Error(e.message)
                err.code = 67068
                self.err(err)
            } else {
                let err = new Error(e.message)
                err.code = 67039
                self.err(err)
            }
        }
    }
}
