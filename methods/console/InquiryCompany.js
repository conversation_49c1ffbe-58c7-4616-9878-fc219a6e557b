const Method = require('../../libs/method')
const _ = require('lodash')
const { getTableModel, parseJSON } = require('../../fns/kits')
const ucloudinternalapi = require('../../libs/ucloudinternalapi')
const { Op } = require('sequelize')
const orderResults = require('../../configs/common/order_result')

//查看公司是否为代理，如果不是则需要公司名与实名信息一致 ---- 接口下架了，后面的都没有兼容哦 若弃用要检查哦
module.exports = class InquiryCompany extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec(params = {}) {
        let self = this
        let icpDatabase = this.db.get('icp')

        const ProxyCompanyModel = getTableModel(
            't_proxy_company_list',
            icpDatabase
        )
        try {
            let data = await ProxyCompanyModel.findAll({
                where: {
                    CompanyId: params.CompanyId,
                },
            })
            data = parseJSON(data)

            if (data.length > 0) {
                // 代理
                this.cb(0, {
                    isBelong: true,
                })
            } else {
                let options = {
                    Backend: 'UAccount',
                    Action: 'IGetUserAuthInfo',
                    CompanyId: params.CompanyId,
                    AuthType: params.AuthType,
                    Target: 'CRM',
                }
                try {
                    let body = await ucloudinternalapi(options)
                    if (!body.AuthInfo || body.AuthInfo.length === 0) {
                        return this.cb(0, {
                            Tip: '不存在该公司信息',
                            IsMatch: false,
                        })
                    }
                    if (
                        params.AuthType === 1 &&
                        body.AuthInfo[0].UserName !== params.CompanyName
                    ) {
                        return this.cb(0, {
                            Tip: '主办单位名称不一致',
                            IsMatch: false,
                        })
                    }
                    if (
                        params.AuthType === 0 &&
                        body.AuthInfo[0].CompanyName !== params.CompanyName
                    ) {
                        return this.cb(0, {
                            Tip: '主办单位名称不一致',
                            IsMatch: false,
                        })
                    }
                    if (body.AuthInfo[0].AuditState !== '已认证') {
                        return this.cb(0, {
                            Tip: '未通过实名认证',
                            IsMatch: false,
                        })
                    }
                    return this.cb(0, {
                        IsMatch: true,
                    })
                } catch (err) {
                    let error = new Error('请求内部接口报错')
                    error.code = 67047
                    return self.err(error)
                }
            }
        } catch (e) {
            let err = new Error(e.message)
            err.code = 67039
            self.err(err)
        }
    }
}
