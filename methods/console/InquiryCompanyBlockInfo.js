/*
 * @Author: william.qian <EMAIL>
 * @Date: 2022-11-15 15:45:19
 * @LastEditors: william.qian <EMAIL>
 * @LastEditTime: 2023-05-05 15:22:31
 * @FilePath: /newicp/methods/console/InquiryCompanyBlockInfo.js
 * @Description: 查询封禁库，确定此公司是否是封禁中
 */
const Method = require('../../libs/method')
const { checkCompanyOrderPermissions } = require('../../fns/order/OrderService')
module.exports = class InquiryCompanyBlockInfo extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({ CompanyId } = {}) {
        let self = this

        try {
            let IsBlock = await checkCompanyOrderPermissions(CompanyId)

            return this.cb(0, {
                IsBlock,
            })
        } catch (e) {
            let error = new Error(e.message)
            error.code = 67039
            self.err(error)
        }
    }
}
