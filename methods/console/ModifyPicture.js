const Method = require('../../libs/method')
const order_status = require('../../configs/common/order_status.json')
const _ = require('lodash')
const { getTableModel, parseJSON } = require('../../fns/kits')
const { Op } = require('sequelize')
const { getPictureMeta, getPictureUrls } = require('../../fns/uflieFuns')
const { OrderStatusEnum } = require('../../models')

// 接受图片，更新至图片表同时更新至订单
module.exports = class ModifyPicture extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({
        Pictures,
        CompanyId,
        OrderNo,
        Step,
        EditStatus,
        FrontPictureError,
    } = {}) {
        console.log(JSON.stringify(Pictures))
        let self = this
        let icpDatabase = this.db.get('icp')

        const Order = getTableModel('t_order', icpDatabase)
        const OrderWeb = getTableModel('t_order_web', icpDatabase)
        const OrderPicture = getTableModel('t_order_picture', icpDatabase)

        try {
            let [orderInfo, orderWebInfoList] = await Promise.all([
                // 查此订单是否存在
                Order.findAll({
                    where: {
                        CompanyId,
                        OrderNo,
                        Status: {
                            [Op.in]: [
                                OrderStatusEnum.Editing,
                                OrderStatusEnum.AuditRefuse,
                                OrderStatusEnum.ReAuditRefuse,
                                OrderStatusEnum.GovAuditRefuse,
                            ],
                        },
                    },
                }),
                // 查网站情况
                OrderWeb.findAll({
                    where: { OrderNo, IsDeleted: 0 },
                }),
            ])

            // 如果结果为1，确定订单在此公司下
            if (orderInfo.length !== 1) {
                throw new self.Err(
                    new Error('在对应公司下找不到编辑的订单'),
                    67060
                )
            }
            orderInfo = parseJSON(orderInfo[0])
            orderWebInfoList = JSON.parse(JSON.stringify(orderWebInfoList))
            let promiseList = []

            // 判断是否有PictureName
            Pictures.forEach((element) => {
                if (!element.PictureName) {
                    element.PictureName = []
                }
            })

            // 主体的
            let orderUpdateJSON = {}

            // 取主体负责人的信息
            let PICLicensePictureId = _.findIndex(Pictures, {
                LicenseId: orderInfo.PICMainLicenseId,
                PictureType: 'PICMainLicensePicture',
            })

            if (PICLicensePictureId !== -1) {
                orderUpdateJSON.PICMainLicensePicture =
                    Pictures[PICLicensePictureId].PictureName
                orderUpdateJSON.PICMainLicenseDate =
                    Pictures[PICLicensePictureId].PICMainLicenseDate
            }
            //主办单位证件
            let OrganizerLicensePictureId = _.findIndex(Pictures, {
                LicenseId: orderInfo.OrganizerLicenseId,
                PictureType: 'OrganizerLicensePicture',
            })

            if (OrganizerLicensePictureId !== -1) {
                orderUpdateJSON.OrganizerLicensePicture =
                    Pictures[OrganizerLicensePictureId].PictureName
                // 存在 主办单位证件 校验证件大小 和像素 是否符合要求
                if (
                    ![2, 7, 11, 14, 30, 41, 42].includes(
                        parseInt(orderInfo.OrganizerLicenseType)
                    )
                ) {
                    let urls = Object.values(
                        getPictureUrls(orderUpdateJSON.OrganizerLicensePicture)
                    )
                    let metas = await Promise.all(
                        urls.map((url) => getPictureMeta(url))
                    )
                    let moreLimit = false
                    metas.forEach((meta) => {
                        let tmpArr = [meta?.Width, meta?.Height]
                        if (_.max(tmpArr) < 1500 || _.min(tmpArr) < 1100) {
                            moreLimit = true
                        }
                    })
                    if (moreLimit) {
                        let err = new Error(
                            `主办单位证件像素不符合要求，要求像素不低于1500*1100`
                        )
                        err.code = 67064
                        return self.err(err)
                    }
                }
            }
            //承诺书图片  只有广东省支持 自己上传承诺书图片
            let PromiseLetterPictureId = _.findIndex(Pictures, {
                PictureType: 'PromiseLetterPicture',
            })
            if (PromiseLetterPictureId !== -1) {
                if (Pictures[PromiseLetterPictureId].PictureName.length !== 0) {
                    // 440100 - 445381 是广东省的 地区id地址
                    if (
                        orderInfo.AreaId < 440100 ||
                        orderInfo.AreaId > 445381
                    ) {
                        //报错
                        Pictures = Pictures.filter(
                            (p) => p.PictureType !== 'PromiseLetterPicture'
                        )
                    } else {
                        orderUpdateJSON.PromiseLetterPicture =
                            Pictures[PromiseLetterPictureId].PictureName
                    }
                }
            }

            orderWebInfoList = JSON.parse(JSON.stringify(orderWebInfoList))

            for (let index = 0; index < orderWebInfoList.length; index++) {
                const element = orderWebInfoList[index]
                try {
                    let webLicensePictureId = _.findIndex(Pictures, {
                        LicenseId: element.LicenseId,
                        PictureType: 'PersonLicensePicture',
                    })
                    let CurtainPictureId = _.findIndex(Pictures, {
                        LicenseId: element.LicenseId,
                        PictureType: 'CurtainPicture',
                    })
                    let FetchInfoId = _.findIndex(Pictures, {
                        LicenseId: element.LicenseId,
                        PictureType: 'FetchInfo',
                    })
                    let RelevantPromiseLetterId = _.findIndex(Pictures, {
                        LicenseId: element.LicenseId,
                        PictureType: 'RelevantPromiseLetter',
                    })
                    let OtherPictureId = _.findIndex(Pictures, {
                        PictureType: 'Others',
                    })

                    let updateJson = {}

                    if (webLicensePictureId !== -1) {
                        updateJson.LicensePicture =
                            Pictures[webLicensePictureId].PictureName
                        updateJson.LicenseDate =
                            Pictures[webLicensePictureId].LicenseDate
                    }
                    if (FetchInfoId !== -1) {
                        updateJson.FetchInfo = Pictures[FetchInfoId].PictureName
                    }

                    if (CurtainPictureId !== -1) {
                        updateJson.CurtainPicture =
                            Pictures[CurtainPictureId].PictureName
                        if (Pictures[CurtainPictureId].UUID) {
                            updateJson.CurtainUUID =
                                Pictures[CurtainPictureId].UUID
                        }
                    }
                    if (RelevantPromiseLetterId !== -1) {
                        updateJson.RelevantPromiseLetter =
                            Pictures[webLicensePictureId].PictureName
                    }
                    if (OtherPictureId !== -1) {
                        if (
                            Pictures[OtherPictureId]['PictureName'][index] &&
                            JSON.stringify(
                                Pictures[OtherPictureId]['PictureName'][index]
                            ) !== '{}'
                        ) {
                            if (
                                Pictures[OtherPictureId]['PictureName'][index]
                                    .OtherPicture
                            ) {
                                updateJson.OtherPicture =
                                    Pictures[OtherPictureId]['PictureName'][
                                        index
                                    ].OtherPicture
                            }
                            if (
                                Pictures[OtherPictureId]['PictureName'][index]
                                    .AuthoriztionPicture
                            ) {
                                updateJson.AuthoriztionPicture =
                                    Pictures[OtherPictureId]['PictureName'][
                                        index
                                    ].AuthoriztionPicture
                            }
                        }
                    }

                    if (JSON.stringify(updateJson) !== '{}') {
                        promiseList.push(
                            OrderWeb.update(updateJson, {
                                where: { Id: element.Id },
                            })
                        )
                    }
                } catch (error) {
                    throw new self.Err(error, 67062)
                }
            }

            // Map取主体图片信息
            let OrderPictureMap = {}

            Pictures.map((picture) => {
                picture.OrderNo = OrderNo
                picture.CompanyId = CompanyId
                picture.LicenseType = picture.PictureType
                picture.LicensePicture = picture.PictureName

                if (
                    picture.PictureType !== 'PersonLicensePicture' &&
                    picture.PictureType !== 'Others'
                ) {
                    OrderPictureMap[picture.PictureType] = picture.PictureName
                }
            })
            // 获取主办单位证件图片内容
            OrderPictureMap = _.pick(OrderPictureMap, [
                'OrganizerLicensePicture',
                'OrganizerResidencePermitPicture',
                'OtherPicture',
            ])

            orderUpdateJSON = Object.assign(orderUpdateJSON, OrderPictureMap)

            if (Step) {
                orderUpdateJSON.Step = Step
            }

            if (EditStatus) {
                orderUpdateJSON.EditStatus = EditStatus
            }

            if (FrontPictureError) {
                orderUpdateJSON.FrontPictureError = FrontPictureError
            } else {
                orderUpdateJSON.FrontPictureError = {}
            }

            promiseList.push(
                Order.update(orderUpdateJSON, { where: { Id: orderInfo.Id } })
            )

            Pictures = _.filter(Pictures, function (o) {
                return o.PictureName && o.PictureName?.length !== 0
            })

            await Promise.all(promiseList)

            let PictureList = []
            Pictures.map((picture) => {
                // 拆图片的结构，把图片文件名放到一个数组里PictureList
                // 结构为[a.jpg,b.jpg,c.jpg]
                if (picture.PictureType === 'Others') {
                    // 拆Others的结果
                    picture.PictureName.forEach((element) => {
                        if (element) {
                            if (element.OtherPicture) {
                                PictureList.push(element.OtherPicture)
                            }
                            if (element.AuthoriztionPicture) {
                                PictureList.push(element.AuthoriztionPicture)
                            }
                        }
                    })
                }
                if (picture.PictureType === 'FetchInfo') {
                    // 数据示例
                    // "PictureName": { "NotificationPicture": [], "CommitmentPicture": [], "CommitmentVedio": [] }
                    // 拆FetchInfo的结果，不同省份如果需要用到这个字段，需要注意格式
                    let pictureArr = []
                    _.forEach(picture.PictureName, function (value, key) {
                        pictureArr.push(value)
                    })

                    // 拆数组
                    pictureArr = _.flattenDeep(pictureArr)
                    PictureList = _.concat(PictureList, pictureArr)
                } else {
                    PictureList.push(picture.PictureName)
                }
            })
            let result = []
            // 得到图片名
            PictureList = _.flattenDeep(PictureList)
            for (let index = 0; index < PictureList.length; index++) {
                //  深度解析，如果是object，取Value
                const element = PictureList[index]
                if (_.isObject(element) && !_.isArray(element)) {
                    // 防止数组引入object的判断
                    _.forEach(element, function (value, key) {
                        result.push(value)
                    })
                } else {
                    result.push(element)
                }
            }
            PictureList = _.flattenDeep(result)

            PictureList = _.uniq(PictureList)
            PictureList = getPictureUrls(PictureList)

            return this.cb(0, { Pictures: PictureList })
        } catch (e) {
            console.log(e, 333)
            if (e instanceof Error) {
                return self.eh({
                    err: e,
                    code: 67061,
                })
            }
            this.eh(e)
        }
    }
}
