const Method = require('../../libs/method')
const { getTableModel, parseJSON } = require('../../fns/kits')
const { Op } = require('sequelize')
const { OrderModel:Order, OrderWebModel:OrderWeb } = require('../../models')

// 导出一个描述ICP简化订单的类，继承自Method
module.exports = class DescribeICPSimplifiedOrder extends Method {
    // 构造函数，接收一个回调函数作为参数
    constructor(cb) {
        super(cb)
    }
    // 执行函数，接收一个参数对象
    async exec(params = {}) {
        let self = this
        // 获取icp数据库
 
        try {
            // 从参数中解构出公司ID和订单号
            const { CompanyId, OrderNo } = params;

            // 定义查询条件
            let cond = {
                CompanyId,
                InnerDisplay: 0,
                IsDeleted: 0,
            }
            if (OrderNo) {
                cond.OrderNo = OrderNo
            }

            // 查询订单表，获取订单数量和订单列表
            let { count: totalCount, rows: orders } = await Order.findAndCountAll({
                where: cond,
                attributes: ['Status', 'CurtainStatus', 'Error', 'OrderNo']
            })

            // 解析订单列表
            orders = parseJSON(orders)

            // 获取所有订单的订单号
            let orderNos = orders.map(order => order.OrderNo)

            // 查询网站订单表，获取所有订单的网站信息
            let orderWebsites = await OrderWeb.findAll({
                where: {
                    OrderNo: {
                        [Op.in]: orderNos
                    }
                },
                attributes: ['Domain', 'CurtainPicture', 'Id', 'OrderNo']
            })

            // 解析网站订单信息
            orderWebsites = parseJSON(orderWebsites)

            // 初始化结果对象
            let result = {
                TotalCount: totalCount,
                Order: [],
            }

            // 遍历订单列表，将每个订单的信息和对应的网站信息添加到结果中
            for (let order of orders) {
                let websites = orderWebsites.filter(website => website.OrderNo === order.OrderNo)

                let orderResult = {
                    Status: order.Status,
                    OrderNo: order.OrderNo, 
                    CurtainStatus: order.CurtainStatus,
                    Error: order.Error,
                    Website: websites.map((website) => {
                        return {
                            Domain: website.Domain,
                            IsMain: website.IsMain,
                            CurtainPicture: website.CurtainPicture,
                            Id: website.Id,
                        }
                    }),
                }

                result.Order.push(orderResult)
            }

            // 返回结果
            return this.cb(0,result)
        } catch (e) {
            // 错误处理
            if (e instanceof Error) {
                return self.eh({
                    err: e,
                    code: 67062,
                })
            }
            self.eh(e)
        }
    }
}
