/*
 * @Author: william.qian <EMAIL>
 * @Date: 2022-02-16 11:11:48
 * @LastEditors: william.qian <EMAIL>
 * @LastEditTime: 2023-05-18 14:50:53
 * @FilePath: /newicp/methods/console/GetOrderPicture.js
 * @Description: 传入订单号，得到对应的图片链接或文件地址
 */
const Method = require('../../libs/method')
const { getTableModel } = require('../../fns/kits')
const { getPictureUrls } = require('../../fns/uflieFuns')
const { getPictures } = require('../../fns/checkOrder')

module.exports = class GetOrderPicture extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({ CompanyId, OrderNo, Resize, OrderInfo } = {}) {
        let self = this
        let icpDatabase = this.db.get('icp')

        const Order = getTableModel('t_order', icpDatabase)
        const OrderWeb = getTableModel('t_order_web', icpDatabase)

        try {
            let orderInfo = {}

            if (OrderNo) {
                let [orderList, orderWebIdList] = await Promise.all([
                    // 查此订单是否存在
                    Order.findAll({
                        where: {
                            CompanyId,
                            OrderNo,
                        },
                    }),
                    // 查网站情况
                    OrderWeb.findAll({
                        where: { OrderNo, IsDeleted: 0 },
                    }),
                ])

                // 如果结果为1，确定订单在此公司下
                if (orderList.length !== 1) {
                    throw new self.Err(
                        new Error('在对应公司下找不到相关的订单'),
                        67060
                    )
                }

                // 之前代码中的attributes是为了减少返回，后续需要继续Pick,减少返回的空字符串
                // 2层Pick

                orderInfo = JSON.parse(JSON.stringify(orderList[0]))

                orderInfo.Website = JSON.parse(JSON.stringify(orderWebIdList))
            } else {
                orderInfo = OrderInfo
            }

            orderInfo = getPictures(orderInfo, Resize)

            let pictureList = getPictureUrls(orderInfo)

            return this.cb(0, { Pictures: pictureList })
        } catch (e) {
            if (e instanceof Error) {
                return self.eh({
                    err: e,
                    code: 67061,
                })
            }
            this.eh(e)
        }
    }
}
