/**
 * @file API 保存授权动作
 * <AUTHOR>
 * @return {} 返回符合条件的记录,格式如下
 */

const Method = require('../../libs/method')

module.exports = class SaveAuthorizedInformationGather extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({ CompanyId } = {}) {
        let self = this
        let redis = this.redis.get()

        try {
            // 将配置发布到Redis
            await redis.sadd('authorized_company_list', CompanyId)

            return this.cb(0)
        } catch (e) {
            if (e instanceof Error) {
                return self.eh({
                    err: e,
                    code: 67011,
                })
            }
            self.eh(e)
        }
    }
}
