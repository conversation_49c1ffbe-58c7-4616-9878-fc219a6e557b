const Method = require('../../libs/method')
const _ = require('lodash')
const { parseJSON } = require('../../fns/kits')
const { model: OrderModel } = require('../../models/t_order')
const { model: OrderNoteModel } = require('../../models/t_order_note')
const moment = require('moment')
// 修改订单标记
module.exports = class ModifyOrderNote extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({ CompanyId, OrderNo, Note } = {}) {
        let self = this
        try {
            // 检查订单是否存在
            let orderInfo = await OrderModel.findOne({
                where: {
                    CompanyId,
                    OrderNo,
                },
            })
            orderInfo = parseJSON(orderInfo)
            if (!orderInfo) {
                // 订单不存在
                let err = new Error('There is no such order')
                err.code = 67007
                throw err
            }
            // 存在更新 不存在添加
            await OrderNoteModel.upsert({
                OrderNo,
                CompanyId,
                Note,
                UpdateTime: moment().format('X'),
            })
            // 不出执行出错返回
            return self.cb(0)
        } catch (e) {
            if (e.code === 67007) {
                return self.err(e)
            } else {
                let err = new Error('更新订单标记出错' + e)
                err.code = 67010
                return self.err(err)
            }
        }
    }
}
