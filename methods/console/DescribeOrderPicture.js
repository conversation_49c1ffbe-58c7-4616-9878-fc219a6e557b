const Method = require('../../libs/method')
const order_status = require('../../configs/common/order_status.json')
const _ = require('lodash')
const { getTableModel } = require('../../fns/kits')
const { Op } = require('sequelize')
const { signature } = require('../../fns/uflieFuns')

// 更新网站
module.exports = class DescribeOrderPicture extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({ CompanyId, OrderNo, LicenseId } = {}) {
        let self = this
        let icpDatabase = this.db.get('icp')

        const Order = getTableModel('t_order', icpDatabase)
        const OrderPicture = getTableModel('t_order_picture', icpDatabase)

        try {
            var orderCount = await Order.count({
                where: {
                    CompanyId,
                    OrderNo,
                },
            })

            // 如果结果为1，确定订单在此公司下
            if (orderCount !== 1) {
                throw new self.Err(
                    new Error('在对应公司下找不到编辑的订单'),
                    67060
                )
            }

            let selectJson = {
                CompanyId,
                OrderNo,
                LicenseType: 'CurtainPicture',
            }

            if (LicenseId) {
                selectJson.LicenseId = LicenseId
            }

            let OrderPictureList = await OrderPicture.findAll({
                where: selectJson,
            })

            let returnObject = []

            OrderPictureList.map((picture) => {
                picture = _.pick(picture, [
                    'LicenseId',
                    'LicensePicture',
                    'UUID',
                ])
                picture.Name = picture.LicensePicture[0]
                delete picture.LicensePicture

                if (!picture.Name) {
                    throw new Error('picture.Name不能为空')
                }

                picture.URL =
                    global.CONFIG.ufile.target +
                    '/' +
                    picture.Name +
                    signature(picture.Name, Math.floor(Date.now() / 1000) + 600)

                returnObject.push(picture)
            })

            return this.cb(0, { Pictures: returnObject })
        } catch (e) {
            if (e instanceof Error) {
                return self.eh({
                    err: e,
                    code: 67061,
                })
            }
            this.eh(e)
        }
    }
}
