const Method = require('../../libs/method')
const _ = require('lodash')
const { getTableModel, parseJSON } = require('../../fns/kits')
const { Op } = require('sequelize')
const orderResults = require('../../configs/common/order_result')
const {
    OrderNoteModel,
    OrderWebTwoLineModel,
    OrderWebModel,
} = require('../../models')
const { OrderWebPlatInfoModel } = require('../../mongoModels/icp')

// 获取订单列表
module.exports = class DescribeICPOrder extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec(params = {}) {
        let self = this
        let icpDatabase = this.db.get('icp')

        const Order = getTableModel('t_order', icpDatabase)
        const OrderWeb = getTableModel('t_order_web', icpDatabase)
        const Curtain = getTableModel('t_curtain', icpDatabase)

        try {
            let cond = {
                CompanyId: params.CompanyId || parseInt(params.company_id, 10),
                // InnerDisplay内部订单不在此展示
                InnerDisplay: 0,
                IsDeleted: 0,
                // Limit: params.Limit ? (params.Limit > 1000 ? 1000 : params.Limit) : 10,
                // Offset: params.Offset || 0
            }

            params.Limit = params.Limit
                ? params.Limit > 1000
                    ? 1000
                    : params.Limit
                : 10
            params.Offset = params.Offset || 0

            if (params.OrderNo) {
                cond.OrderNo = params.OrderNo
            }
            if (params.Type) {
                cond.Type = params.Type
            }

            if (params.Status) {
                cond.Status = params.Status
            }

            if (params.CurtainStatus) {
                cond.CurtainStatus = { [Op.in]: params.CurtainStatus }
            }

            if (params.StartTime && params.EndTime) {
                params.StartTime = _.floor(params.StartTime / 1000)
                params.EndTime = _.ceil(params.EndTime / 1000)
                // cond.fuzzy = ` update_time BETWEEN ${params.StartTime} AND  ${params.EndTime}`
                cond.UpdateTime = {
                    [Op.between]: [params.StartTime, params.EndTime],
                }
            }

            if (params.OrganizerName) {
                cond.OrganizerName = {
                    [Op.like]: `%${params.OrganizerName}%`,
                }
            }

            // let OrderNoList = []
            // 通过域名查找订单号 Domain

            if (params.Domain) {
                let websites = await OrderWeb.findAll({
                    where: {
                        Domain: {
                            [Op.like]: `%"${params.Domain}"%`,
                        },
                    },
                })

                websites = parseJSON(websites)

                if (websites || websites.length > 0) {
                    cond.OrderNo = { [Op.in]: _.map(websites, 'OrderNo') }
                    // OrderNoList = _.map(websites, 'OrderNo')
                } else {
                    throw new this.Err(new Error('Err Domain'), 67073)
                }
            }

            //通过 标记搜索
            if (params.Note) {
                let notes = await OrderNoteModel.findAll({
                    where: {
                        CompanyId: params.CompanyId,
                        Note: {
                            [Op.like]: `%${params.Note}%`,
                        },
                    },
                })
                notes = parseJSON(notes)
                if (notes.length > 0) {
                    cond.OrderNo = { [Op.in]: _.map(notes, 'OrderNo') }
                } else {
                    return this.cb(0, { TotalCount: 0, Order: [] })
                }
            }
            let [count, data] = await Promise.all([
                Order.count({ where: cond }),
                Order.findAll({
                    offset: params.Offset,
                    limit: params.Limit,
                    order: [['UpdateTime', 'DESC']],
                    where: cond,
                }).then((orders) => {
                    if (orders.length === 0) {
                        return Promise.resolve([[], [], []])
                    }

                    let orderNos = []

                    orders = orders.map((order) => {
                        //统计所有orderNo
                        orderNos.push(order.OrderNo)
                        // 用脏的方法处理 Detail
                        order.Details =
                            typeof order.Details === 'string'
                                ? JSON.parse(order.Details)[0]
                                : order.Details[0]
                        order.UpdateContent =
                            order.UpdateContent !== null
                                ? order.UpdateContent[0]
                                : {}
                        return order
                    })
                    return Promise.all([
                        // 根据 Order，获取所有 Website 信息
                        OrderWeb.findAll({
                            where: {
                                OrderNo: { [Op.in]: orderNos },
                            },
                            include: [
                                {
                                    model: OrderWebTwoLineModel,
                                    as: 'TwoLineIPs',
                                    attributes: ['IP'],
                                },
                            ],
                        }),

                        // 根据 Order，获取所有幕布信息
                        Curtain.findAll({
                            where: { OrderNo: { [Op.in]: orderNos } },
                        }),
                        // 订单
                        orders,
                        OrderNoteModel.findAll({
                            where: {
                                OrderNo: { [Op.in]: orderNos },
                            },
                        }),
                    ])
                }),
            ])
            let Orders = parseJSON(data[2])
            let OrderWebs = parseJSON(data[0])
            let OrderWebIds = OrderWebs.map((orderWeb) => orderWeb.Id)
            // 查询OrderWebPlatInfoModel 表所有 OrderWebId 在OrderWebIds 中的数据
            let platInfoLists = await OrderWebPlatInfoModel.find({
                OrderWebId: { $in: OrderWebIds },
            })
            platInfoLists = parseJSON(platInfoLists)
            return this.cb(0, {
                TotalCount: count,
                Order: Orders.map((order) => {
                    // 没有这个类型的订单
                    if (!orderResults[order.Type]) return {}

                    let orderRes = orderResults[order.Type].concat(
                        orderResults.common
                    )

                    order = _.pick(order, orderRes)
                    // 返回订单标记
                    order.Note =
                        data[3].filter(
                            (data) => data.OrderNo === order.OrderNo
                        )[0]?.Note || ''
                    // 需要展示 Website 时，拼接 Website 内容
                    if (_.indexOf(orderResults[order.Type], 'Website') > -1) {
                        order.Website = OrderWebs.filter(
                            (website) => website.OrderNo === order.OrderNo
                        )
                        // 将platInfoLists 中和Website的id符合的数据挑出来
                        order.Website.forEach((website) => {
                            website.AppPlatformInformationList =
                                platInfoLists.filter(
                                    (platInfoList) =>
                                        platInfoList.OrderWebId === website.Id
                                )[0]?.AppPlatformInformationList || []
                        })
                        // 将三线IP 写入网站IP字段 只有在 Ucloud审核通过之后 才这么展示，（因为在之前 还没有替换）
                        if (order.Status !== 1) {
                            order.Website?.forEach((website) => {
                                website.IP = website.IP.concat(
                                    website.TwoLineIPs?.IP?.length > 0
                                        ? website.TwoLineIPs?.IP
                                        : []
                                )
                                _.remove(website.IP, (ip) => {
                                    return ip === global.CONFIG.RepliceTwoIP
                                })
                            })
                        }
                    }

                    if (params.FetchPicture) {
                        order.Picture = {
                            OrganizerLicensePicture:
                                order.OrganizerLicensePicture,
                            PICMainLicensePicture: order.PICMainLicensePicture,
                            AuthVerificationPicture:
                                order.AuthVerificationPicture,
                            CurtainPicture: order.CurtainPicture,
                            OrganizerResidencePermitPicture:
                                order.OrganizerResidencePermitPicture,
                            PICWebLicensePicture: order.PICWebLicensePicture,
                            PromiseLetterPicture: order.PromiseLetterPicture,
                        }

                        if (order.Website && _.isArray(order.Website)) {
                            order.Picture.Website = {}
                            order.Website.forEach((website) => {
                                order.Picture.Website[
                                    website.LicenseId + '_LicensePicture'
                                ] = website.LicensePicture
                                order.Picture.Website[
                                    website.LicenseId + '_CurtainPicture'
                                ] = website.CurtainPicture
                                // fetchinfo中的图片处理
                                // let pictureArr = []
                                // _.forEach(website.FetchInfo, function (value, key) {
                                // 	pictureArr.push(value)
                                // });

                                // 拆数组
                                order.Picture.Website[
                                    website.LicenseId + '_FetchInfo'
                                ] = website.FetchInfo
                            })
                        }
                    }

                    // 需要展示快递信息时，拼接快递信息内容
                    if (_.indexOf(orderResults[order.Type], 'Recipient') > -1) {
                        let recipient = data[1].filter(
                            (curtain) => curtain.OrderNo === order.OrderNo
                        )

                        if (recipient.length > 0) {
                            order = _.extend(
                                order,
                                _.pick(recipient[0], [
                                    'Recipient',
                                    'RecipientMobile',
                                    'RecipientAddress',
                                    'ExpressNo',
                                    'ExpressCompany',
                                ])
                            )
                        }
                    }

                    return order
                }),
            })
        } catch (e) {
            console.log(e, 333)
            if (e instanceof Error) {
                return self.eh({
                    err: e,
                    code: 67061,
                })
            }
            this.eh(e)
        }
    }
}
