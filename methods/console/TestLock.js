const Method = require('../../libs/method')
const { lock, unLock } = require('../../fns/lockFuns')
const Redlock = require('redlock')

// 增加与傲盾的同步解封
// 为兼容前端，不改传参
module.exports = class TestLock extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({} = {}) {
        let redis = this.redis.get()
        let self = this

        try {
            // 检查mongo
            const redlock = new Redlock([redis])
            let lockInstance = await lock(redlock, 'ressss', 20 * 1000)

            await sleep(5000)

            try {
                await unLock(redlock, lockInstance)
            } catch (error) {
                // throw new this.Err(new Error('数据库查询长度为0'), 30001)
                return await Promise.reject(new self.Err(error, 30903))
            }

            return this.cb(0)
        } catch (e) {
            console.log(e, 333)
            if (e instanceof Error) {
                return self.eh({
                    err: e,
                    code: 30201,
                })
            }
            self.eh(e)
        }
    }
}

const sleep = (millseconds = 3000) => {
    return new Promise((rs) => {
        setTimeout(() => rs(), millseconds)
    })
}
