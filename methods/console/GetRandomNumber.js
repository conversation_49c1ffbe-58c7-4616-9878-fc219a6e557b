// 获取活体视频中需要的随机数

const Method = require('../../libs/method')
const verify_type = require('../../configs/common/verify_api_type.json')
const {
    insertLog,
    getVerifyLog,
    updateLog,
    getFileName,
    verifyAPI,
    getUrl,
} = require('../../fns/verifyFun')
const { getTableModel } = require('../../fns/kits')

module.exports = class GetRandomNumber extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({ UUID, CompanyId, OrderNo, UseLipLanguage } = {}) {
        let logId, result, multiplex
        // 字段是否复用
        result = {}
        multiplex = 1
        let self = this

        let icpDatabase = this.db.get('icp')
        let LogModel = getTableModel('t_verify_log', icpDatabase)

        try {
            let params = {
                UUID,
                Backend: 'IdAuth',
                CompanyId,
                OrderNo,
                Action: 'GetRandomNumber',
                Source: 'ICP',
            }
            // 如果使用唇语
            if (UseLipLanguage) {
                params.ActionIndex = 1
            }
            params['Type'] = verify_type.GetRandomNumber

            // 执行日志
            logId = await insertLog(params, LogModel)
            try {
                result = await verifyAPI({
                    Action: 'ValidatePersonalInfo',
                    body: params,
                })
            } catch (error) {
                await Promise.reject(new self.Err(error, 67052))
            }

            await updateLog({ ReturnObject: result }, logId, LogModel)

            if (result.Response.RandomNumber === undefined) {
                result.RandomNumber = ''
            }
            if (result.Response.TokenRandomNumber === undefined) {
                result.TokenRandomNumber = ''
            }

            return self.cb(0, {
                RandomNumber: result.Response.RandomNumber,
                TokenRandomNumber: result.Response.TokenRandomNumber,
            })
        } catch (e) {
            if (e instanceof Error) {
                return self.eh({
                    err: e,
                    code: 67042,
                })
            }
            self.eh(e)
        }
    }
}
