const Method = require('../../libs/method')
const { getTableModel, parseJSON } = require('../../fns/kits')
const _ = require('lodash')
const { Op } = require('sequelize')

//查看订单历史
module.exports = class DescribeICPOrderHistory extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec(params = {}) {
        let self = this
        let icpDatabase = this.db.get('icp')
        const OrderModel = getTableModel('t_order', icpDatabase)
        const OrderHistoryModel = getTableModel('t_order_history', icpDatabase)

        try {
            let orders = await OrderModel.findAll({
                where: {
                    OrderNo: params.OrderNo,
                    CompanyId: params.CompanyId,
                },
            })
            orders = parseJSON(orders)
            // 如果没有这个订单则报错
            if (orders.length === 0) {
                let err = new Error('没有找到该订单')
                err.code = 68001
                return this.err(err)
            }
            // 获取订单历史信息
            let histories = await OrderHistoryModel.findAll({
                where: {
                    OrderNo: orders[0].OrderNo,
                },
            })
            histories = parseJSON(histories)
            return this.cb(0, {
                History: histories.map((h) => {
                    return _.pickBy(h, (value, key) => {
                        return _.indexOf(['Status', 'OperationTime'], key) > -1
                    })
                }),
            })
        } catch (e) {
            let error = new Error(e.message)
            error.code = 67039
            self.err(error)
        }
    }
}
