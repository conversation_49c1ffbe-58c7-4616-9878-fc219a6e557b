const Method = require('../../libs/method')
const { getTableModel, parseJSON } = require('../../fns/kits')
const ip = require('ip')
const _ = require('lodash')
const { uploadFile } = require('../../fns/uflieFuns')
const { Error: SequelizeError } = require('sequelize')

//创建备案图片
module.exports = class CreateICPPicture extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec(params = {}) {
        let self = this
        let icpDatabase = this.db.get('icp')

        const PictureModel = getTableModel('t_picture', icpDatabase)
        try {
            // 判断图片类型 看看是name 还是base64 若是base64 则上传US3 并get到name
            if (params.Picture.indexOf('data:') === 0 && params.Picture.indexOf(';base64,') > 0){
                params.Picture = await uploadFile(params.Picture)
            } else {
                // 非base64 是提供给外部访问的 需要验证外部ip 是否加白
                let isInWhiteList =
                    _.findIndex(global.CONFIG.IPWhiteList, (whiteIP) => {
                        return ip.cidrSubnet(whiteIP).contains(params.client_ip)
                    }) >= 0
                if (!isInWhiteList) {
                    throw new Error('There is no picture right of this ip')
                }
            }
            let filename = params.Picture

            await PictureModel.create(
                _.extend(params, {
                    Url: filename,
                })
            )

            return this.cb(0, { Filename: filename })
        } catch (e) {
            if (e instanceof SequelizeError) {
                let err = new Error(e.message)
                err.code = 67068
                self.err(err)
            } else {
                let err = new Error(e.message)
                err.code = 67057
                self.err(err)
            }
        }
    }
}
