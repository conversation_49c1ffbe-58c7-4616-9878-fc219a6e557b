/**
 * @file API ResetToken 重置Token,对应刷新二维码，先验证Token是否有效，有效则不刷新，无效则重新生成，写入数据库
 * <AUTHOR>
 * @param {CompanyId} 公司Id 必选参数,由网关传入条件
 * @return {Object} 返回符合条件的记录,格式如下
 * 错误码:
 * 67036: 签名过出错
 * 67011: 调用生成STS Token出错
 * 67038: Token加密出错
 */

const Method = require('../../libs/method')
const CryptoJS = require('crypto-js')
const base64url = require('base64-url')
const uuid = require('uuid/v4')
const { getTableModel } = require('../../fns/kits')
const { getSTSToken } = require('../../fns/getSTSToken')

module.exports = class ResetToken extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({
        Token,
        LicenseId,
        LicenseType,
        OrderNo,
        CompanyId,
        Channel,
        organization_id,
    } = {}) {
        let self = this
        let icpDatabase = this.db.get('icp')
        let oldToken = Token
        const TokenModel = getTableModel('t_token', icpDatabase)
        const OrderWebModel = getTableModel('t_order_web', icpDatabase)
        const redis = this.redis.get()
        // 获取按照分流数据
        let V2CompanyIds = await redis.smembers('V2CompanyIds')
        let Version =
            V2CompanyIds &&
            (V2CompanyIds.includes('ALLIN') ||
                V2CompanyIds.includes(CompanyId.toString()))
                ? 'v2'
                : 'v1'
        let session
        // 请求STS,生成密钥
        if (+LicenseType !== 2) {
            Version = 'v1'
        }

        try {
            let { RetCode, Data, Message } = await getSTSToken(CompanyId, self)

            if (RetCode === 0 && Message === 'success') {
                // 得到的请求，做AES对称加密（其实最好是非对称，但前端支持的库少）
                Data = CryptoJS.AES.encrypt(
                    JSON.stringify(Data),
                    global.CONFIG.SessionKey
                )

                Data = base64url.encode(Data.toString())
                session = Data
                let newToken = uuid()

                // 生成新的Toekn记录
                TokenModel.create({
                    Channel,
                    CompanyId: CompanyId, //网关传入
                    OrganizationId: organization_id,
                    Token: newToken,
                    LicenseId,
                    LicenseType,
                    OrderNo,
                    Session: session,
                })
                    .then(() => {
                        return Promise.all(
                            // 新Token更新至订单
                            [
                                OrderWebModel.update(
                                    { CurtainUUID: newToken },
                                    {
                                        where: {
                                            LicenseId,
                                            LicenseType,
                                            OrderNo,
                                        },
                                    }
                                ),
                                // 老Token注销掉
                                TokenModel.update(
                                    { Status: 1 },
                                    {
                                        where: {
                                            Token: oldToken,
                                            LicenseId,
                                            LicenseType,
                                            OrderNo,
                                        },
                                    }
                                ),
                            ]
                        )
                    })
                    .then(() => {
                        return this.cb(0, {
                            Token: newToken,
                            Version,
                        })
                    })
                    .catch((e) => {
                        return Promise.reject(new self.Err(e, 67038))
                    })
            } else {
                await Promise.reject(new self.Err(new Error(Message), 67038))
                // return fn(67038, { Message })
            }
        } catch (e) {
            if (e instanceof Error) {
                return self.eh({
                    err: e,
                    code: 67075,
                })
            }
            self.eh(e)
        }
    }
}
