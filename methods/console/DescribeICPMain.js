const Method = require('../../libs/method')
const { getTableModel, parseJSON } = require('../../fns/kits')
const {
    OrderModel,
    ICPModel,
    ICPStatusEnum,
    ICPWebModel,
    ICPWebTwoLineModel,
} = require('../../models')
const { ICPWebPlatInfoModel } = require('../../mongoModels/icp')
const { Op } = require('sequelize')
const _ = require('lodash')

module.exports = class DescribeICPMain extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec(params = {}) {
        let self = this
        try {
            //过滤数据基础
            // 根据公司Id，获取公司下所有备案主体
            let ICPQuery = {
                CompanyId: params.CompanyId || parseInt(params.company_id, 10),
                IsDeleted: 0,
            }
            if (params.Status !== undefined) {
                if (params.Status === 8) {
                    ICPQuery.Status = {
                        [Op.in]: [
                            ICPStatusEnum.Mainchanging,
                            ICPStatusEnum.Icpchanging,
                        ],
                    }
                } else {
                    ICPQuery.Status = params.Status
                }
            }
            let websites, orders
            if (params.Domain || params.ICPWebNo || params.Name) {
                // 要通过域名 或者 网站备案号 进行搜索
                let webQuery = {
                    IsDeleted: 0,
                    Status: { [Op.notIn]: [1, 2] },
                }
                if (params.Domain) {
                    webQuery.Domain = {
                        [Op.like]: `%${params.Domain}%`,
                    }
                }
                if (params.ICPWebNo) {
                    webQuery.ICPWebNo = params.ICPWebNo
                }
                if (params.Name) {
                    webQuery.Name = params.Name
                }
                websites = await ICPWebModel.findAll({ where: webQuery })

                websites = parseJSON(websites)

                if (websites.length === 0) {
                    return this.cb(0, { TotalCount: 0, ICP: [] })
                }

                // 若搜索到网站信息，则统计该信息中的 主体id MainId
                ICPQuery.Id = { [Op.in]: _.map(websites, 'MainId') }
            } else if (params.ICPMainNo || params.OrganizerName) {
                if (params.ICPMainNo) {
                    ICPQuery.ICPMainNo = params.ICPMainNo
                }

                if (params.OrganizerName) {
                    ICPQuery.OrganizerName = params.OrganizerName
                }
            }
            if (params.Id !== undefined && params.Id !== '') {
                ICPQuery.Id = params.Id
            }
            // 查找主体信息
            let [icps, count] = await Promise.all([
                // 不得超过1000，默认50
                ICPModel.findAll({
                    where: ICPQuery,
                    limit: params.Limit
                        ? params.Limit > 1000
                            ? 1000
                            : parseInt(params.Limit)
                        : 50,
                    offset: parseInt(params.Offset) || 0,
                    order: [['id', 'desc']],
                }),
                ICPModel.count({ where: ICPQuery }),
            ])

            icps = parseJSON(icps)
            count = parseJSON(count)

            if (count.length === 0) {
                return this.cb(0, { TotalCount: 0, ICP: [] })
            }

            let promiseArray = []

            promiseArray.push(
                ICPWebModel.findAll({
                    where: {
                        MainId: {
                            [Op.in]: _.map(icps, 'Id'),
                        },
                        IsDeleted: 0, //不要已删除的
                    },
                    include: [
                        {
                            model: ICPWebTwoLineModel,
                            as: 'TwoLineIPs',
                            attributes: ['IP'],
                        },
                    ],
                })
            )

            // 不要审核通过 和已删除的订单， 查询订单是为了判断当前状态 是否可以进行变更 和注销 等操作
            promiseArray.push(
                OrderModel.findAll({
                    where: {
                        CompanyId: params.CompanyId,
                        ICPMainNo: {
                            [Op.in]: _.map(icps, 'ICPMainNo'),
                        },
                        Status: {
                            [Op.ne]: 12,
                        },
                        IsDeleted: 0,
                    },
                })
            )
            ;[websites, orders] = await Promise.all(promiseArray)
            websites = parseJSON(websites)
            orders = parseJSON(orders)

            icps.map((icp) => {
                // 确定主体是否可以执行操作
                // 有任何关于 该主体备案号的订单 在进行 都不可以提交变更备案
                //变更备案 -----三合一的
                icp.CanOperated =
                    _.findIndex(orders, function (o) {
                        return o.ICPMainNo === icp.ICPMainNo
                    }) === -1 && icp.Status !== 1

                //注销主体  和变更备案一致 若不一致 则注意 先分开
                icp.DeleteMain = icp.CanOperated

                //变更主体 -----单一的  // icp.status web.status的 7都是三合一的变更
                // 变更主体 只能有该主体备案号的 变更网站 和变更接入的流程中订单 其他都不允许
                icp.CanChange =
                    _.findIndex(orders, function (o) {
                        return (
                            [2, 3, 4, 5, 6, 7, 8].includes(o.Type) &&
                            o.ICPMainNo === icp.ICPMainNo
                        )
                    }) === -1 && icp.Status !== 1
                return icp
            })
            // 网站状态判断
            for (let website of websites) {
                // 判断网站类型为APP时 如果是APP类型 则查询mongo的ICPWebPlatInfoModel 表ICPWebId为website.Id的数据
                if (website.InternetServiceType == 6) {
                    let platInfoList = await ICPWebPlatInfoModel.findOne(
                        { ICPWebId: website.Id },
                        { _id: 0, ICPWebId: 0 }
                    )
                    website.AppPlatformInformationList =
                        platInfoList?.AppPlatformInformationList || []
                }
                //该网站所属的主体
                const icp = _.find(icps, { Id: website.MainId })
                //处理二线IP展示
                if (website.TwoLineIPs?.IP?.length > 0 && website.IP) {
                    _.remove(website.IP, (ip) => {
                        return ip.trim() === global.CONFIG.RepliceTwoIP.trim()
                    })
                    website.IP = website.IP.concat(website.TwoLineIPs?.IP)
                }
                // 变更接入时 不能有 变更备案 注销主体的订单存在 也不可以有该网站的订单存在 此处与变更网站 逻辑一样
                website.ConnectCanModify =
                    website.Status === 0 && [0, 8].includes(icp.Status)
                // 变更网站 与变更接入一致
                website.CanChange = website.ConnectCanModify

                // 取消接入 不可以【变更备案 注销主体】=》主体状态只能为0，8  【注销网站 取消接入 变更网站 变更接入】=》网站状态要为0
                website.CancelConnect =
                    website.Status === 0 && [0, 8].includes(icp.Status)
                // 注销网站 和取消接入一致
                website.DeleteWeb = website.CancelConnect

                // 删除网站中空的前置审批照，与域名证书照片
                // 网站中的域名Map
                website.Domain.map((domain) => {
                    if (
                        !domain.CerificationPicture ||
                        domain.CerificationPicture[0] === '1.jpg'
                    ) {
                        domain.CerificationPicture = []
                    }
                })

                // 网站中的前置审批Map
                if (
                    _.isArray(website.PreAppoval) &&
                    website.PreAppoval.length !== 0
                ) {
                    website.PreAppoval.map((preAppoval) => {
                        if (
                            preAppoval.Picture[0] === 'x.jpg' ||
                            preAppoval.Picture[0] === '1.jpg'
                        ) {
                            preAppoval.Picture = []
                        }
                    })
                }
            }

            this.cb(0, {
                TotalCount: count,
                ICP: icps.map((icp) => {
                    icp.Website = websites.filter(
                        (website) => website.MainId === icp.Id
                    )
                    return icp
                }),
            })
        } catch (e) {
            let err = new Error(e.message)
            err.code = 67009
            self.err(err)
        }
    }
}
