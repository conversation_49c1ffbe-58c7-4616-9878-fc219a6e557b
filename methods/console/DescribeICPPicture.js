const Method = require('../../libs/method')
const { getTableModel, parseJSON } = require('../../fns/kits')
const _ = require('lodash')
const { Op } = require('sequelize')
const { signature } = require('../../fns/uflieFuns')

//获取 账号下图片的临时链接
module.exports = class DescribeICPPicture extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec(params = {}) {
        let self = this
        let icpDatabase = this.db.get('icp')
        const PictureModel = getTableModel('t_picture', icpDatabase)

        try {
            const picture = params.Picture
            let pics = await PictureModel.findAll({
                where: {
                    CompanyId: params.CompanyId,
                    Url: picture,
                },
            })

            pics = parseJSON(pics)

            if (pics.length === 0 || !picture) {
                throw new Error('No such picture')
            } else {
                this.cb(0, {
                    PictureInfo:
                        global.CONFIG.ufile.target +
                        '/' +
                        picture +
                        signature(
                            picture,
                            Math.floor(Date.now() / 1000) + 600
                        ) +
                        (params.Resize
                            ? `&iopcmd=convert&q=${params.Resize}&dst=jpg`
                            : ''),
                })
            }
        } catch (e) {
            let error = new Error(e.message)
            error.code = 67006
            self.err(error)
        }
    }
}
