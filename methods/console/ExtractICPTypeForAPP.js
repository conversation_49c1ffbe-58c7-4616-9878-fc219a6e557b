const Method = require('../../libs/method')
const _ = require('lodash')
const henganApi = require('../../libs/henganApiPromise')
const { getTableModel } = require('../../fns/kits')
const { ICPModel, ICPWebModel, LogModel } = require('../../models')
const GetDomainPublicICP = require('../../methods/common/GetDomainPublicICP')

var request = require('request')
const {
    getMsgFromWebService,
    getIcpAndWebMsgService,
} = require('../../fns/getMsgFromTabelService')
const { Op } = require('sequelize')
// 临时改需求的该打
module.exports = class ExtractICPTypeForAPP extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({
        OrganizerLicenseType,
        OrganizerLicenseId,
        APPName,
        CompanyId,
    }) {
        let self = this

        let icpInUCloudByOrganizerLicenseId, // 主体证件号在我司是否有备案
            icpInMIITByOrganizerLicenseId, // 主体证件号在工信部(其他供应商)是否有备案
            icpInUCloudByAPPName, // APP名在我司是否有备案
            icpInMIITByAPPName, // APP名在工信部(其他供应商)是否有备案
            ICPWebNo,
            ICPMainNo
        const ServiceType = 'APP'

        try {
            // 查询我司数据库
            // 主体证件号在我司是否有备案
            icpInUCloudByOrganizerLicenseId = henganApi('SelectICPInterface', {
                KeyWordType: 3,
                CertificateType: parseInt(OrganizerLicenseType),
                Keyword: OrganizerLicenseId,
            }).catch((err) => {
                // 查询我司数据库
                return getICPInfoByDomain({
                    OrganizerLicenseId,
                })
            })
            // 主体证件号在工信部(其他供应商)是否有备案
            let methodICP = new GetDomainPublicICP((RetCode, data) => {
                icpInMIITByOrganizerLicenseId = {
                    ...data,
                    RetCode,
                }
                if (RetCode === 0) {
                    icpInMIITByOrganizerLicenseId = data
                }
            })
            let icpInfoExec = methodICP.exec({
                OrganizerLicenseType: OrganizerLicenseType,
                OrganizerLicenseId: OrganizerLicenseId,
            })

            // APP名在我司是否有备案
            // icpInUCloudByAPPName = henganApi('SelectICPInterface', {
            //     KeyWordType: 4,
            //     Keyword: APPDomain,
            // }).catch((err) => {
            //     // 查询我司数据库
            //     return getICPInfoByDomain({
            //         Name: APPName,
            //     })
            // })

            // APP名在工信部(其他供应商)是否有备案
            let methodAPPName = new GetDomainPublicICP((RetCode, data) => {
                icpInMIITByAPPName = {
                    ...data,
                    RetCode,
                }
                if (RetCode === 0) {
                    icpInMIITByAPPName = data
                }
            })
            let appNameInfoExec = methodAPPName.exec({
                OrganizerLicenseType: 104,
                OrganizerLicenseId,
                AdditionalQueryCondition: APPName,
                ServiceType,
            })

            let pList = [
                icpInUCloudByOrganizerLicenseId,
                icpInfoExec,
                { ICPInfos: [], RetCode: 0 },
                appNameInfoExec,
            ]
            pList.push(ICPModel.count({
                where: {
                    CompanyId,
                    OrganizerLicenseId,
                    Status: {
                        [Op.ne]: 1,
                    },
                },
            }))
            let pData = await Promise.all(pList)
            let isCompanyMatch = pData[4]
            // 处理结构
            // 从ExtractICPTypeForAPP_1.json中取mock数据

            let ICPINUCloud = pData[0].ICPInfos.length !== 0 //主体证件号在我司是否有备案
            if (ICPINUCloud) {
                let icpCount = await ICPModel.count({
                    where: { CompanyId, OrganizerLicenseId },
                })
                if (icpCount === 0) {
                    return self.cb(0, {
                        Tip: '该备案主体或备案域名已在我司系统其他账号中存在，请登录对应账号进行操作！',
                    })
                }
            }
            let ICPINOther = icpInMIITByOrganizerLicenseId.ICPInfos.length !== 0 //主体证件号在工信部是否有备案
            if (ICPINOther) {
                ICPMainNo =
                    icpInMIITByOrganizerLicenseId.ICPInfos[0].mainRecordNum
            }
            //     let APPNameINUCloud = pData[2].ICPInfos.length !== 0 //APP名称在我司是否有备案
            // APPNameINUCloud 的数据从ICPINUCloud取
            let APPNameINUCloud = false
            if (ICPINUCloud) {
                for (let website of pData[0].ICPInfos[0].websiteList) {
                    if (
                        website.websitename === APPName &&
                        website.websiteserviceTypes === 6
                    ) {
                        APPNameINUCloud = true
                        pData[2] = pData[0]
                        break
                    }
                }
            }
            console.log(icpInMIITByAPPName)
            let APPNameINOther = icpInMIITByAPPName.ICPInfos.length !== 0 //APP名称在工信部是否有备案
            if (APPNameINOther) {
                ICPWebNo = icpInMIITByAPPName.ICPInfos[0].webSiteNum
            }
            let determine = [
                pData[0].ICPInfos.length,
                icpInMIITByOrganizerLicenseId.ICPInfos.length,
                pData[2].ICPInfos.length,
                icpInMIITByAPPName.ICPInfos.length,
            ]
            // 判断标准顺序为ICPINUCloud, ICPINOther, APPNameINUCloud, APPNameINOther
            // 否否否否；新增备案
            if (
                !ICPINUCloud &&
                !ICPINOther &&
                !APPNameINUCloud &&
                !APPNameINOther
            ) {
                return this.cb(0, {
                    Type: [1],
                    Determine: determine,
                    Description: '首次备案',
                    Reason: 1,
                    Message:
                        '根据您当前填写的信息判断，证件号无备案、APP名称无备案，您的备案类型为“首次备案”。确认无误后，请点击【继续】提交备案。\n\n若您确定主体名下存在备案，\n请点击【上一步】，证件类型与证件号码请按照三证合一前，原备案接入商登记的证件类型和证件号码进行填写。企业性质证件类型选择【工商营业执照】',
                })
            }
            // 有；判断是不是和当前提的在一个公司下
            if (ICPINUCloud && isCompanyMatch === 0) {
                // 有主体 单不是当前公司的
                return this.cb(0, {
                    Determine: determine,
                    Tip: '该备案主体在我司系统已存在，请在对应账号已备案完成列表处进行操作！',
                })
            }
            // 有有否否；有主体新增网站
            if (
                ICPINUCloud &&
                ICPINOther &&
                !APPNameINUCloud &&
                !APPNameINOther
            ) {
                return this.cb(0, {
                    Type: [2],
                    Info: { IcpMainNo: ICPMainNo },
                    Description: '有主体新增网站',
                    Determine: determine,
                    Reason: 2,
                    Message:
                        '根据您当前填写的信息判断，证件号有备案、APP名称无备案，您的备案类型为“新增网站/APP”。因您的备案主体已在我司备案，请仔细核对主体信息是否发生变更，若发生变更，请按照【新增+变更】方式提交，若无变更则按照【仅新增】提交。两种方式提交后暂不可更改，只能删除订单重新创建。\n\n确认无误后，请点击【继续】提交备案。',
                })
            }

            // 否有否否；新增备案
            if (
                !ICPINUCloud &&
                ICPINOther &&
                !APPNameINUCloud &&
                !APPNameINOther
            ) {
                return this.cb(0, {
                    Type: [2],
                    Info: { IcpMainNo: ICPMainNo },
                    Description: '无主体新增网站',
                    Determine: determine,
                    Reason: 3,
                    Message:
                        '根据您当前填写的信息判断，证件号有备案、APP名称无备案，您的备案类型为“新增网站/APP”。确认无误后，请点击【继续】提交备案。请注意因您的备案主体未在我司备案，本次新增网站/APP的同时需在我司更新最新主体信息。',
                })
            }

            // 有有有有；[]
            if (
                ICPINUCloud &&
                ICPINOther &&
                APPNameINUCloud &&
                APPNameINOther
            ) {
                return this.cb(0, {
                    Type: [],
                    Description: '此APP名称已存在该主体',
                    Determine: determine,
                    Reason: 4,
                    Message:
                        '此APP名称已存在该主体，请更换APP名称。若想在此APP名称下增加域名，请在已备案列表处进行变更操作！',
                })
            }

            // 有有否有；有主体接入备案
            if (
                ICPINUCloud &&
                ICPINOther &&
                !APPNameINUCloud &&
                APPNameINOther
            ) {
                return this.cb(0, {
                    Type: [3],
                    Info: { IcpWebNo: ICPWebNo, IcpMainNo: ICPMainNo },
                    Description: '有主体接入备案',
                    Determine: determine,
                    Reason: 5,
                    Message:
                        '根据您当前填写的信息判断，证件号有备案、APP名称有备案，您的备案类型为“接入备案”。确认无误后，请点击【继续】提交备案。',
                })
            }

            // 否有否有；无主体新增接入
            if (
                !ICPINUCloud &&
                ICPINOther &&
                !APPNameINUCloud &&
                APPNameINOther
            ) {
                return this.cb(0, {
                    Type: [3],
                    Info: { IcpWebNo: ICPWebNo, IcpMainNo: ICPMainNo },
                    Description: '无主体新增接入',
                    Determine: determine,
                    Reason: 6,
                    Message:
                        '根据您当前填写的信息判断，证件号有备案、APP名称有备案，您的备案类型为“新增接入”。确认无误后，请点击【继续】提交备案。',
                })
            }

            return this.cb(0, {
                Type: [0],
                // Info: { Determine: determine },
            })
        } catch (e) {
            console.log(e, 1314)
            if (e instanceof Error) {
                return self.eh({
                    err: e,
                    code: 67061,
                })
            }
            this.eh(e)
        }
    }
}

/**
 * @description: 传入证域名，得到备案信息
 * @param {*} Domain
 * @return {*}
 */
async function getICPInfoByDomain({
    Domain,
    OrganizerLicenseId,
    OrganizerLicenseType,
}) {
    let mainIds = []
    let generalJson = {}
    if (Domain) {
        mainIds = await getMsgFromWebService('console', ICPWebModel, { Domain })
        if (mainIds.length === 0) {
            return Promise.resolve(noICPInUCloud)
        } else {
            generalJson.Id = { [Op.in]: mainIds }
        }
    }
    if (OrganizerLicenseId) {
        generalJson.OrganizerLicenseId = OrganizerLicenseId
    }

    let getIcpAndWebMsg = await getIcpAndWebMsgService(
        'console',
        ICPModel,
        ICPWebModel,
        generalJson,
        0,
        10,
        LogModel
    )

    if (getIcpAndWebMsg.TotalCount === 0) {
        return Promise.resolve(noICPInUCloud)
    }

    // 处理结构

    return Promise.resolve({
        ICPInfos: [
            {
                phylicnum: getIcpAndWebMsg.ICP[0].ICPMainNo,
            },
        ],
        RetCode: 0,
    })
}

const noICPInUCloud = {
    ICPInfos: [],
    Message: '无对应的ICP备案信息',
    RetCode: 0,
}
