const Method = require('../../libs/method')
const { getTableModel, parseJSON } = require('../../fns/kits')

//创建订单快递记录？
module.exports = class ApplyICPCurtain extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec(params = {}) {
        let self = this
        let icpDatabase = this.db.get('icp')
        const Order = getTableModel('t_order', icpDatabase)
        const Curtain = getTableModel('t_curtain', icpDatabase)

        try {
            let orders = await Order.findAll({
                where: {
                    OrderNo: params.OrderNo,
                    CompanyId: params.CompanyId,
                },
            })
            orders = parseJSON(orders)

            if (orders.length === 0) {
                let error = new Error('没有找到该订单')
                error.code = 67007
                this.err(error)
            } else {
                await Curtain.create(params)
                this.cb(0)
            }
        } catch (e) {
            // 此接口暂时 不使用，若使用 则更改下列报错的code
            let error = new Error(e.message)
            error.code = 67018
            self.err(error)
        }
    }
}
