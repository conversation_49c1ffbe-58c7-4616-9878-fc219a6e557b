// 检查视频口型或者声音是否正确

const Method = require('../../libs/method')
const verify_type = require('../../configs/common/verify_api_type.json')
const {
    insertLog,
    getVerifyLog,
    updateLog,
    verifyAPI,
    getFileName,
    getUrl,
} = require('../../fns/verifyFun')
const _ = require('lodash')

const { getTableModel } = require('../../fns/kits')

module.exports = class CheckVideo extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({
        Token,
        Url, // 文件URL
        File, // 文件名
        CompanyId,
        ForceSelect = 1,
        UUID,
        Session,
        RandomNumber,
        FileType,
        OrderNo,
        __ssoUser,
        UseLipLanguage,
    } = {}) {
        let Operator, logId, result, multiplex
        // 字段是否复用

        result = {}
        multiplex = 1
        Operator = __ssoUser
        let self = this

        let icpDatabase = this.db.get('icp')
        let LogModel = getTableModel('t_verify_log', icpDatabase)
        try {
            Url = getUrl(Url, File, null)

            // 取出文件名，为性能加速
            if (!File) {
                File = getFileName(Url)
            }

            let params = {
                Action: 'CheckVideo',
                body: {
                    Video: Url,
                    UUID: UUID,
                    Token: Token,
                    CompanyId: CompanyId,
                    OrderNo,
                    Session: Session,
                    Backend: 'IdAuth',
                    Action: 'CheckVideo',
                    Source: 'ICP',
                },
            }

            // 增加唇语支持，ActionIndex 1为使用证通接口,解决华为等手机声画不同步的问题
            if (UseLipLanguage) {
                params.body.ActionIndex = 1
            }
            // 如果有随机数，同时随机数是数字类型
            if (RandomNumber && _.isNumber(RandomNumber)) {
                RandomNumber = RandomNumber.toString()
                params.body.Number = RandomNumber
            }
            // if (FileType) {
            //     params.body.Type = FileType
            // }

            params.body['Type'] = verify_type.ValidateVideo

            // 执行日志
            logId = await insertLog(params.body, LogModel)

            // 如果没有强制验证，则查询旧记录
            try {
                if (!ForceSelect) {
                    // 验证是否此参数是否验证过,此处实际不需要，URL会更新
                    result = await getVerifyLog(
                        {
                            Type: verify_type.ValidateVideo,
                            Video: File,
                            Number: RandomNumber,
                        },
                        logId,
                        LogModel
                    )
                }
            } catch (error) {
                await Promise.reject(new self.Err(error, 67045))
            }

            try {
                // 如果未取到，或者之前的请求为出错，重试
                if (
                    result === null ||
                    JSON.stringify(result) === '{}' ||
                    result.Error === true
                ) {
                    params.body.Type = FileType
                    result = await verifyAPI(params)
                    multiplex = 0
                }
            } catch (error) {
                await Promise.reject(new self.Err(error, 67054))
            }

            // 更新结果到数据库
            result.Multiplex = multiplex
            try {
                await updateLog(result, logId, LogModel)
            } catch (error) {
                await Promise.reject(new self.Err(error, 67045))
            }

            let returnObject = {}
            let returnCode = 0
            // 结果匹配

            if (result.Error) {
                returnCode = 67054
                returnObject = {
                    IsMatch: false,
                    Message: result.Response.Message,
                }
            } else {
                if (!result.Response.Message) {
                    returnCode = 0
                    returnObject = { IsMatch: true }
                } else {
                    returnCode = 0
                    returnObject = {
                        IsMatch: false,
                        Message: result.Response.Message,
                    }
                }
            }

            await updateLog({ ReturnObject: returnObject }, logId, LogModel)

            this.cb(returnCode, returnObject)
        } catch (e) {
            if (e instanceof Error) {
                return self.eh({
                    err: e,
                    code: 67044,
                })
            }
            self.eh(e)
        }
    }
}
