const Method = require('../../libs/method')
const { checkIPisMatch } = require('../../fns/checkIPisMatch')
const { getTableModel } = require('../../fns/kits')
const _ = require('lodash')
//检查IP是否在本账户下,增加逻辑如果是白名单中的IP+公司，直接返回正确
//1判断是否在白名单中，如在，直接返回
//获取公司下的组织ID List
//获取IP对应的组织ID,与上List是否匹配（基于托管云可能有误差的情况）
module.exports = class InquiryIPBelong extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec(params = {}) {
        let self = this
        try {
            let IPList = params.IP.split(';')

            IPList = _.uniq(IPList)
            let icpDatabase = this.db.get('icp')
            const IpWhiteModel = getTableModel('t_ip_white_list', icpDatabase)
            await checkIPisMatch(
                IpWhiteModel,
                params.CompanyId,
                IPList,
                (code, message) => {
                    return self.cb(code, {
                        ...message,
                    })
                }
            )
        } catch (e) {
            let err = new Error(e.message)
            err.code = 67038
            self.err(err)
        }
    }
}
