// 获取客户的实名信息

const Method = require('../../libs/method')
const { getAuthInfo } = require('../../fns/authFuns')

module.exports = class GetICPUserAuthInfo extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({ CompanyId, Channel } = {}) {
        let self = this
        try {
            let authInfo = await getAuthInfo(CompanyId, Channel)
            console.log(authInfo, 'authInfo')
            if (
                authInfo &&
                authInfo.AuthType === '个人认证' &&
                authInfo.CertificateType === ''
            ) {
                authInfo.CertificateType = '身份证'
            }
            return self.cb(0, { AuthInfo: authInfo })
        } catch (e) {
            if (e instanceof Error) {
                return self.eh({
                    err: e,
                    code: 67029,
                })
            }
            self.eh(e)
        }
    }
}
