/**
 * @file API GetTemporaryToken 生成TokenId,tokenId存数据库，实际对应着账号STS的临时用户密钥
 * <AUTHOR>
 * @param {CompanyId} 公司Id 必选参数,由网关传入条件
 * @return {Object} 返回符合条件的记录,格式如下
 * 错误码:
 * 67036: 签名过出错
 * 67011: 调用生成STS Token出错
 * 67038: Token加密出错
 */

const Method = require('../../libs/method')
const { getTableModel, parseJSON } = require('../../fns/kits')

module.exports = class DescribeTemporaryToken extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({ CompanyId, LicenseId, LicenseType, OrderNo } = {}) {
        let self = this
        let icpDatabase = this.db.get('icp')
        const TokenModel = getTableModel('t_token', icpDatabase)

        let tokenInfo

        try {
            tokenInfo = await TokenModel.findOne({
                order: [['Id', 'DESC']],
                where: { CompanyId, LicenseId, LicenseType, OrderNo },
            })

            tokenInfo = parseJSON(tokenInfo)

            if (!tokenInfo) {
                await Promise.reject(
                    new self.Err(new Error('未查到有效授权，刷新确认'), 67079)
                )
            }
            return this.cb(0, { Token: tokenInfo.Token })
        } catch (e) {
            if (e instanceof Error) {
                return self.eh({
                    err: e,
                    code: 67076,
                })
            }
            self.eh(e)
        }
    }
}

const sleep = (millseconds = 3000) => {
    return new Promise((rs) => {
        setTimeout(() => rs(), millseconds)
    })
}
