/**
 * @file API 刷新公司名下最近的网站负责人
 * <AUTHOR>
 * @param {PICName} 人名 可选参数
 * @return {Object} 返回符合条件的记录,格式如下
 */

const Method = require('../../libs/method')
const { getTableModel } = require('../../fns/kits')
const { QueryTypes } = require('sequelize')
const moment = require('moment')
const _ = require('lodash')
const { model: ICPModel } = require('../../models/t_icp')
const { model: ICPWebModel } = require('../../models/t_web')

module.exports = class GetRecentWebOwnerInfo extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({ PICName, CompanyId } = {}) {
        let self = this
        let icpDatabase = this.db.get('icp')
        const Order = getTableModel('t_order', icpDatabase)
        const OrderWeb = getTableModel('t_order_web', icpDatabase)

        try {
            let PCINameQuery = ''

            if (PICName) {
                PCINameQuery = `AND t_order_web.pic_name ="${PICName}"`
            }

            let PICList
            PICList = await icpDatabase.query(
                `SELECT
                t_order_web.pic_name AS PICName,
                t_order_web.license_type AS LicenseType,
                t_order_web.license_id AS LicenseId,
                t_order_web.license_date AS LicenseDate,
                t_order_web.mobile AS Mobile,
                t_order_web.emergency_phone AS EmergencyPhone,
                t_order_web.email AS Email,
                t_order_web.license_picture AS LicensePicture,
                t_order_web.create_time AS CreateTime,
                t_order_web.curtain_picture AS CurtainPicture
            FROM  
              t_order_web AS t_order_web
              LEFT OUTER JOIN t_order AS orderA ON t_order_web.order_no = orderA.order_no
            WHERE
              orderA.status > 2
              AND orderA.type IN (1, 2, 3, 7, 9) 
              AND orderA.update_time > ${parseInt(
                  moment().subtract(30, 'day').format('X')
              )}
              AND orderA.company_id = ${CompanyId}
              AND orderA.is_deleted = 0 ${PCINameQuery}    
            ORDER BY  t_order_web.id DESC
            LIMIT  0, 20;`,
                {
                    type: QueryTypes.SELECT,
                }
            )
            // 取出需要的数据
            let NeedPick = [
                'PICName',
                'LicenseType',
                'LicenseId',
                'LicenseDate',
                'Mobile',
                'EmergencyPhone',
                'Email',
                'LicensePicture',
                'CurtainPicture',
            ]

            PICList = PICList.map((record) => {
                record.LicensePicture =
                    record.LicensePicture === ''
                        ? []
                        : record.LicensePicture.split('|')
                record.CurtainPicture =
                    record.CurtainPicture === ''
                        ? []
                        : record.CurtainPicture.split('|')
                return _.pick(record, NeedPick)
            })

            return this.cb(0, { PICList })
        } catch (e) {
            console.log(e)
            if (e instanceof Error) {
                return self.eh({
                    err: e,
                    code: 67025,
                })
            }
            self.eh(e)
        }
    }
}
