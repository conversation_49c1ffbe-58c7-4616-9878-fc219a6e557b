/**
 * @file API GetInfoByUUID 通过公司与UUID查询记录日志
 * <AUTHOR>
 * @param {CompanyId，UUID} 公司Id 必选参数,由网关传入条件
 * @return {Object} 返回符合条件的记录,格式如下
 */

const Method = require('../../libs/method')
const { Op } = require('sequelize')
const { getTableModel, parseJSON } = require('../../fns/kits')
const moment = require('moment')
const verify_type = require('../../configs/common/verify_api_type.json')

// 获取API密钥24小时内有效。

module.exports = class GetInfoByUUID extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({ CompanyId, UUID } = {}) {
        let self = this
        let icpDatabase = this.db.get('icp')

        let LogTable = getTableModel('t_verify_log', icpDatabase)
        let log
        try {
            // 获取Token,如果没有，出错
            log = await LogTable.findAll({
                where: { CompanyId, UUID },
            })
            log = parseJSON(log)

            if (log.length === 0) {
                throw new Error('查询不到对应的UUID')
            }

            // 拼出需要的结果
            let typeArr = Object.values(verify_type)
            let logArr = {}
            for (let i = 0; i < typeArr.length; i++) {
                let body = _.filter(log, { Type: typeArr[i] })
                if (body.length === 0) continue
                body = _.sortBy(body, ['CreateTime'])
                //修改数据格式，生成符合要求的数据
                let rightBody = verifyFun.parseLog(body)
                if (Object.keys(rightBody.value).length === 0) continue
                logArr[rightBody.key] = rightBody.value
            }

            return self.cb(67043, logArr)
        } catch (e) {
            if (e instanceof Error) {
                return self.eh({
                    err: e,
                    code: 67078,
                })
            }
            self.eh(e)
        }
    }
}
