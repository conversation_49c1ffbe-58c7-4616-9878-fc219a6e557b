const Method = require('../../libs/method')
const moment = require('moment')
const { countAsync } = require('../../fns/mongoFuns')

// 增加与傲盾的同步解封
// 为兼容前端，不改传参
module.exports = class HeathCheck extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({ Time } = {}) {
        let mongo = this.mongo.get('icp-config')
        let redis = this.redis.get()
        const area = mongo.collection('area')
        if (!Time) {
            Time = moment().format('')
        }
        try {
            // 检查mongo

            let areaLeath = await countAsync(area, {})

            if (areaLeath === 0) {
                throw new this.Err(new Error('数据库查询长度为0'), 30001)
            }

            // 检查Redis
            let redisCheck = await new Promise((resolve, reject) => {
                redis
                    .pipeline()
                    .set('heathCheckTime', Time)
                    .get('heathCheckTime', function (err, result) {
                        if (err) {
                            reject(new this.Err(err, 30002))
                        }
                        if (result !== Time) {
                            reject(new this.Err(err, 30003))
                        }
                        console.log(result, 1)
                        resolve(true)
                    })
                    .exec(function (err, result) {
                        if (err) {
                            reject(new this.Err(err, 30004))
                        }
                        console.log(result, 2)
                    })
            })

            if (redisCheck) {
                return this.cb(0)
            }
        } catch (e) {
            console.log(e)
            this.eh(e)
        }
    }
}

/*
 *	Mongo数据库查询
 *
 * @params params   syslog, StartTime, EndTime, Region
 *
 */
function mongoHeathCheck(areaCollection) {
    return new Promise((resolve, reject) => {
        areaCollection
            .find({})
            .setReadPreference('secondaryPreferred')
            .count(function (err, docs) {
                err ? reject(err) : resolve(docs)
            })
    })
}
