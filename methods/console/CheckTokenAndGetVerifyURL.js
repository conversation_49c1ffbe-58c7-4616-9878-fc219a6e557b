/**
 * 检查token有效期24小时，并获取faceId token，返回验证url
 */
const Method = require('../../libs/method')
const uuid = require('uuid/v4')
const { getPictureBase64 } = require('../../fns/uflieFuns')
const { OrderWebModel, VerifyLogModel, TokenModel } = require('../../models')
const { insertLog } = require('../../fns/verifyFun')
const ucloudinternalapi = require('../../libs/ucloudinternalapi')
const verify_type = require('../../configs/common/verify_api_type.json')
const InquiryToken = require('./InquiryToken')
const { parseJSON } = require('../../fns/kits')
const moment = require('moment')
module.exports = class CheckTokenAndGetVerifyURL extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({ Token, request_uuid } = {}) {
        let self = this
        let UUID = request_uuid || uuid() // 此次验证的唯一ID 用来记录验证唯一，日志更新
        let redis = this.redis.get()
        let OrderNo, LicenseId, LicenseName, LicensePicture, CompanyId, Channel
        try {
            // 根据token获取信息
            let TokenInfo = await TokenModel.findOne({
                where: {
                    Token,
                },
            })
            if (!TokenInfo) return self.cb(69007, { Message: 'token无效' })
            TokenInfo = parseJSON(TokenInfo)

            OrderNo = TokenInfo.OrderNo
            LicenseId = TokenInfo.LicenseId
            CompanyId = TokenInfo.CompanyId
            Channel = TokenInfo.Channel
            let limit = await redis.get('CheckTokenAndGetVerifyURL-Limit')
            let isMember = await redis.sismember('SSSCompanyId', CompanyId)
            let cur = await redis.incr(
                `${moment().format('YYYY-MM-DD')}-${CompanyId}`
            )
            if (cur == 1)
                await redis.expire(
                    `${moment().format('YYYY-MM-DD')}-${CompanyId}`,
                    60 * 60 * 24
                ) // 24小时过期
            // 设置大客户不受limit的限制
            if (cur > parseInt(limit) && !isMember)
                return self.cb(69008, { Message: '今日验证次数已达上限' })
            // token有效期校验
            let res = {
                retCode: 0,
                data: {
                    Effective: false,
                },
            }
            let method = new InquiryToken((retCode, data) => {
                res.retCode = retCode
                res.data = { ...data }
            })
            await method.exec({ Token, CompanyId })
            if (res.retCode !== 0 || !res.data.Effective) {
                return self.cb(69007, res.data)
            }

            // token有效， 根据orderNo获取到证件图片,和网站负责人名称 用于后面的ocr
            let OrderWeb = await OrderWebModel.findOne({
                attributes: ['LicensePicture', 'PICName'],
                where: {
                    OrderNo,
                    LicenseId,
                },
            })
            if (!OrderWeb) {
                return self.cb(69003, { Message: '信息验证时，缺失负责人证件' })
            }

            OrderWeb = parseJSON(OrderWeb)
            if (
                !OrderWeb.LicensePicture ||
                OrderWeb.LicensePicture.length === 0
            ) {
                return self.cb(69004, {
                    Message: '信息验证时，缺失负责人证件图片',
                })
            }

            LicenseName = OrderWeb.PICName
            LicensePicture = OrderWeb.LicensePicture[0]
            let pictureBase64 = await getPictureBase64({
                pictureName: LicensePicture,
            })
            console.log('pictureBase64', pictureBase64)
            let resData = await ucloudinternalapi({
                Backend: 'NewIdAuth',
                Action: 'StartKYCH5Auth',
                Source: 'ICP',
                CompanyId,
                channel: Channel,
                IdInfoType: 'paramsUpload',
                admin: 1,
                ExtraData: {
                    IdNumber: LicenseId,
                    Name: LicenseName,
                    OrderNo,
                    PictureName: LicensePicture,
                    IdPicture: pictureBase64.split(';base64,')[1],
                    UUID,
                    Token, // 24小时内有效的token，完成后注销token
                },
            })
            if (resData.RetCode !== 0)
                return self.cb(69005, {
                    Message: resData.Message || '获取授权码失败',
                })
            // 只有获取token成功才记录日志，失败就不记录了，
            await insertLog(
                {
                    UUID,
                    CompanyId,
                    OrderNo,
                    Token,
                    LicenseId,
                    LicenseName,
                    Type: verify_type['FaceIdVerify'],
                    PictureName: LicensePicture,
                    URL: resData.Url,
                },
                VerifyLogModel
            )
            return self.cb(0, { URL: resData.Url })
        } catch (e) {
            let err = new Error(e.message)
            err.code = 69006
            self.err(err)
        }
    }
}
