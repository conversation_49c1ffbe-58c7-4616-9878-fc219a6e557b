/**
 * @file API GetTemporarySession 通过生成的Id,获取对应的Session，另判断，如果Session超时，重新更新
 * <AUTHOR>
 * @param {CompanyId} 公司Id 必选参数,由网关传入条件
 * @return {Object} 返回符合条件的记录,格式如下
 * 错误码:
 * 67071: 授权码已过有效期，请在控制台刷新
 * 67072: 调用STS接口出错
 */

const Method = require('../../libs/method')
const { getTableModel } = require('../../fns/kits')
const moment = require('moment')
const CryptoJS = require('crypto-js')
const { getSTSToken } = require('../../fns/getSTSToken')
const base64url = require('base64-url')

// 获取API密钥24小时内有效。

module.exports = class GetTemporarySession extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({ Token } = {}) {
        // let redis = this.redis.get()
        let self = this
        let icpDatabase = this.db.get('icp')
        let now, session
        now = parseInt(moment().format('X'))

        const TokenTable = getTableModel('t_token', icpDatabase)

        try {
            // 获取Token,如果没有，出错
            let tokenInfo = await TokenTable.findOne({
                where: {
                    Token: Token,
                },
            })
            tokenInfo = JSON.parse(JSON.stringify(tokenInfo))
            if (!tokenInfo) {
                new self.Err(new Error('未知的授权'), 67071)
            }
            // 判断是否过有效期，24小时
            if (
                tokenInfo.CreateTime + 24 * 60 * 60 < now ||
                tokenInfo.Status === 1
            ) {
                // 创建时间后的24小时小于现在，说明有效期内
                await Promise.reject(
                    new self.Err(
                        new Error('授权码已失效，请在控制台刷新'),
                        67071
                    )
                )
            }

            // 确定当前的update时间是否在半小时前，如果是，生成新的更新
            if (now - tokenInfo.UpdateTime > 30 * 60) {
                // 如果更新时间在半小时前，重新刷新下二维码
                let { RetCode, Data, Message } = await getSTSToken(
                    tokenInfo.CompanyId,
                    self
                )

                if (RetCode === 0 && Message === 'success') {
                    // 得到的请求，做AES对称加密（其实最好是非对称，但前端支持的库少）
                    Data = CryptoJS.AES.encrypt(
                        JSON.stringify(Data),
                        global.CONFIG.SessionKey
                    )

                    Data = base64url.encode(Data.toString())
                    session = Data
                } else {
                    await Promise.reject(
                        new self.Err(new Error(Message), 67072)
                    )
                    // return fn(67038, { Message })
                }
                await TokenTable.update(
                    // 如果是变更网站或者变更主体，使状态做加法。其它类型的+0不变化
                    { Session: session },
                    {
                        where: {
                            Token: Token,
                        },
                    }
                )
                    .then(() => {
                        // todo 过期控制
                        return this.cb(0, { Token: session })
                    })
                    .catch((e) => {
                        return Promise.reject(new self.Err(e, 67038))
                    })
            } else {
                return this.cb(0, { Session: tokenInfo.Session })
            }
        } catch (e) {
            console.log(e, 333)
            if (e instanceof Error) {
                return self.eh({
                    err: e,
                    code: 67074,
                })
            }
            self.eh(e)
        }
    }
}
