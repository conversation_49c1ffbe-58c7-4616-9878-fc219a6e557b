const Method = require('../../libs/method')
const _ = require('lodash')
const { getTableModel, parseJSON } = require('../../fns/kits')
const { checkOrder } = require('../../fns/checkOrder')
const { Op } = require('sequelize')
const typeStatusCreatingMapping = require('../../configs/common/order_type_creating_status')
const { getKeyAsync } = require('../../fns/redisFuns')

let Order, OrderWeb, ICP, ICPWeb

// 创建订单
module.exports = class CheckICPOrder extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec(params = {}) {
        let self = this
        let icpDatabase = this.db.get('icp')
        let redis = this.redis.get()
        // 引入表格Model
        Order = getTableModel('t_order', icpDatabase)
        OrderWeb = getTableModel('t_order_web', icpDatabase)
        ICP = getTableModel('t_icp', icpDatabase)
        ICPWeb = getTableModel('t_web', icpDatabase)

        // 变更类可能会引入完成的ID,删除
        delete params.Id

        try {
            // 状态锁检查
            // 版本检查，晚于人名信息的订单，标记为New
            // let afterVersionForUserMork = parseInt(moment().format('X')) > global.CONFIG.versionTimeForUserMork
            let redisCheck
            try {
                redisCheck = await getKeyAsync(redis, 'blockRule')
                redisCheck = JSON.parse(redisCheck)
            } catch (error) {
                return await Promise.reject(new self.Err(error, 30101))
            }

            let statusInfo
            // 如果跳过验证，则不验状态锁
            if (params.IsExtractType != 0) {
                statusInfo = await CheckStatus(params, redisCheck)
                if (statusInfo.CanOperated === false) {
                    throw new this.Err(new Error(statusInfo.Message), 67080)
                }
            }

            // 检查类型是否允许此状态
            if (!checkTypeStatusCreating(params)) {
                throw new this.Err(
                    new Error('Creating status or type illegal'),
                    67003
                )
            }
            if (params.CreateCheck && params.Status == 1) {
                return self.cb(0)
            }
            let checkRes = await checkOrder(redis, params)
            if (checkRes) {
                throw new this.Err(new Error(checkRes), 67004)
            }
            return self.cb(0)
        } catch (e) {
            console.log(e)
            if (e instanceof Error) {
                return self.eh({
                    err: e,
                    code: 67061,
                })
            }
            this.eh(e)
        }
    }
}

async function checkTypeStatusCreating(params) {
    return (
        typeStatusCreatingMapping[params.type] &&
        typeStatusCreatingMapping[params.type][params.status]
    )
}

async function CheckStatus(params, redisCheck) {
    // 核验已完成备案类型，是否可操作
    // todo 注销类的验证
    // params.ICPWebNo = params.ICPWebNo
    let Type = params.Type.toString()
    // 确定类型是否存在

    try {
        if (redisCheck[Type] && redisCheck[Type].length !== 0) {
            // 取省简称
            let referredPro = params.ICPMainNo[0]

            if (_.indexOf(redisCheck[Type], referredPro) !== -1) {
                return {
                    CanOperated: false,
                    Message:
                        '因该省管局系统升级，暂停提交该备案类型。恢复时间请参考专区公告。',
                }
            }
        }
    } catch (error) {
        console.log(error)
    }

    if (
        !params.ICPWebNo &&
        params.Website &&
        params.Website.length !== 0 &&
        params.Website[0].ICPWebNo
    ) {
        params.ICPWebNo = params.Website[0].ICPWebNo
    }

    let isReduplicate, orderCount, icpCount, webCount, webInfo
    switch (params.Type) {
        case 1: {
            icpCount = await ICP.count({
                where: {
                    OrganizerLicenseId: params.OrganizerLicenseId,
                    Status: {
                        [Op.notIn]: [1, 20],
                    },
                },
            })

            // 如果备案信息中有 该主办单位证件号的 并且状态不是 已注销 或者已删除的订单
            // 此处主要是验证 是否已经有已备案的主体
            if (icpCount !== 0) {
                return {
                    CanOperated: false,
                    Message: '该证件在我司已存在有效主体，请勿重复提交新增主体',
                }
            }

            // 此处主要是验证 是否已经存在 新增备案进程中的订单（无主体新增接入和新增网站 是可以放行的）
            isReduplicate = await Order.findAll({
                where: {
                    OrganizerLicenseId: params.OrganizerLicenseId,
                    IsDeleted: 0,
                    Status: {
                        [Op.ne]: 12,
                    },
                },
            })

            if (isReduplicate.length > 0) {
                return {
                    CanOperated: false,
                    Message: '该主体下存在流程中的订单，请等待流程结束后再提交',
                }
            }
            break
        }
        case 2: {
            // 取域名，老代码可能有问题,不过有问题的域名应该在验证时就拦截了 "Domain":"jdapi.com.cn", `%${params.Domain}%`
            // let domain = params.Website[0].Domain
            ;[isReduplicate, orderCount] = await Promise.all([
                checkOrderWeb({
                    Domain: { [Op.like]: `%"Domain":"${params.Domain}"%` },
                    IsDeleted: 0,
                }),
                Order.count({
                    where: {
                        ICPMainNo: params.ICPMainNo,
                        IsDeleted: 0,
                        Type: {
                            [Op.in]: [4, 7],
                        },
                        Status: {
                            [Op.ne]: 12,
                        },
                    },
                }),
            ])
            // 判断域名是否已经在提交备案了 防止重复提交
            if (!isReduplicate) {
                return {
                    CanOperated: false,
                    Message: '该域名已存在流程中的订单，请勿重复提交',
                }
            }
            // 判断是否有 主体注销 、变更备案类型的订单 这两种存在 不允许新增
            if (orderCount != 0) {
                return {
                    CanOperated: false,
                    Message: '主体注销或变更备案状态下，不允许新增网站',
                }
            }
            break
        }
        case 3: {
            // 新增接入
            ;[webCount, isReduplicate, orderCount] = await Promise.all([
                ICPWeb.count({
                    where: {
                        ICPWebNo: params.ICPWebNo,
                        Status: {
                            [Op.ne]: 1,
                        },
                    },
                }),
                checkOrderWeb({
                    ICPWebNo: params.ICPWebNo,
                    IsDeleted: 0,
                }),
                Order.count({
                    where: {
                        ICPMainNo: params.ICPMainNo,
                        Type: {
                            [Op.in]: [4, 7],
                        },
                        IsDeleted: 0,
                        Status: {
                            [Op.ne]: 12,
                        },
                    },
                }),
            ])
            // 判断该网站备案号是否已经在我司有备案了
            if (webCount != 0) {
                return {
                    CanOperated: false,
                    Message: '该网站在我司已存在备案，请勿重复提交',
                }
            }
            // 判断该网站备案号 已经有在处理中的订单了
            if (!isReduplicate) {
                return {
                    CanOperated: false,
                    Message: '该网站已存在流程中的订单，请勿重复提交',
                }
            }
            // 判断是否有 主体注销、变更备案类型的订单 这两种存在 不允许新增接入
            if (orderCount != 0) {
                return {
                    CanOperated: false,
                    Message: '主体注销或变更备案状态下，不允许新增接入',
                }
            }
            break
        }
        case 4: {
            // 注销主体
            // 全部不允许
            ;[orderCount, icpCount] = await Promise.all([
                Order.count({
                    where: {
                        ICPMainNo: params.ICPMainNo,
                        Status: {
                            [Op.ne]: 12,
                        },
                        IsDeleted: 0,
                    },
                }),
                ICP.count({
                    where: {
                        ICPMainNo: params.ICPMainNo,
                        CompanyId: params.CompanyId,
                        Status: 0,
                    },
                }),
            ])
            // 确定此主体的状态是否正常
            if (icpCount === 0) {
                return {
                    CanOperated: false,
                    Message:
                        '该主体可能存在流程中的订单申请或主体不存在，无法进行注销操作',
                }
            }
            // 确定是否有在流程中，（注销中）
            if (orderCount != 0) {
                return {
                    CanOperated: false,
                    Message: '该主体已存在流程中的订单，无法进行主体注销',
                }
            }
            break
        }
        case 5:
        case 6: {
            // 取消接入 注销网站
            // 判断是否有 变更备案 注销主体 注销该网站 取消该网站接入 变更该网站 变更该网站接入 相关订单
            // 那其实就是 有这个网站的备案 且网站状态不为0
            params.ICPMainNo = params.ICPMainNo ?? params.ICPWebNo.split('-')[0]
            let [icpInfo, webInfo] = await Promise.all([
                ICP.findAll({
                    where: {
                        ICPMainNo: params.ICPMainNo,
                        CompanyId: params.CompanyId,
                    },
                }),
                ICPWeb.findAll({
                    where: {
                        ICPWebNo: params.ICPWebNo,
                    },
                    attributes: ['Id', 'Status'],
                }),
            ])
            icpInfo = parseJSON(icpInfo)
            webInfo = parseJSON(webInfo)
            if (icpInfo.length === 0) {
                return {
                    CanOperated: false,
                    Message: '您账号下未发现该网站在我司的备案信息，无法注销',
                }
            }
            if (webInfo.length === 0) {
                return {
                    CanOperated: false,
                    Message: '您账号下未发现该网站在我司的备案信息，无法注销',
                }
            }
            if (webInfo[0].Status !== 0) {
                return {
                    CanOperated: false,
                    Message: '该网站已存在流程中的订单，请勿重复提交',
                }
            }
            // 确定是否有变更备案 注销主体相关的订单
            orderCount = await Order.count({
                where: {
                    ICPMainNo: params.ICPMainNo,
                    Type: {
                        [Op.in]: [4, 7],
                    },
                    IsDeleted: 0,
                    Status: {
                        [Op.ne]: 12,
                    },
                },
            })
            if (orderCount != 0) {
                return {
                    CanOperated: false,
                    Message:
                        '该网站所属主体正在进行变更备案或注销，无法注销网站',
                }
            }
            break
        }
        case 7: {
            // 变更备案 存在任何类型和该主体 或该主体下网站有关的操作 都不可以提交变更
            if (params.ICPId === -1) {
                return { CanOperated: false, Message: '无效已备案记录' }
            }
            // 主体或者网站状态 非正常状态
            let [icpRecord, webRecords, orderCount] = await Promise.all([
                ICP.findAll({
                    where: { Id: params.ICPId, CompanyId: params.CompanyId },
                }),
                ICPWeb.findAll({
                    where: {
                        MainId: params.ICPId,
                        ICPWebNo: {
                            [Op.in]: _.map(params.Website, 'ICPWebNo'),
                        },
                    },
                }),
                Order.count({
                    where: {
                        ICPMainNo: params.ICPMainNo,
                        Status: {
                            [Op.ne]: 12,
                        },
                        IsDeleted: 0,
                    },
                }),
            ])
            icpRecord = parseJSON(icpRecord)
            webRecords = parseJSON(webRecords)
            if (icpRecord.length !== 1 || icpRecord[0].Status !== 0) {
                return {
                    CanOperated: false,
                    Message:
                        '当前主体状态下，无法变更，请回到已备案列表确定状态',
                }
            }

            if (
                webRecords.length === 0 ||
                _.findIndex(webRecords, function (o) {
                    return o.Status !== 0
                }) !== -1
            ) {
                return {
                    CanOperated: false,
                    Message:
                        '当前操作的网站存在无法变更的情况，请回到已备案列表确定状态',
                }
            }
            // 确定是否有在流程中，（注销中）
            if (orderCount != 0) {
                return {
                    CanOperated: false,
                    Message: '该主体已存在流程中的订单，无法进行变更备案操作',
                }
            }
            break
        }
        case 8: {
            // "8": "变更主体",
            // 非变更的流程中的订单是否存在
            // 确定是否有在流程中，（变更类不包括）
            // 变更主体时， 只能够 变更网站 和变更接入 其他不允许
            // 变更主体时，主体的状态不能是 已删除 、注销中、已注销
            ;[orderCount, icpCount] = await Promise.all([
                Order.count({
                    where: {
                        ICPMainNo: params.ICPMainNo,
                        [Op.and]: [
                            { Status: { [Op.ne]: 12 } },
                            { Type: { [Op.notIn]: [9, 10] } },
                        ],
                        IsDeleted: 0,
                    },
                }),
                ICP.count({
                    where: {
                        ICPMainNo: params.ICPMainNo,
                        Status: {
                            [Op.in]: [1, 4, 20],
                        },
                    },
                }),
            ])
            if (orderCount != 0) {
                return {
                    CanOperated: false,
                    Message: '该主体已存在流程中的订单，请勿重复提交',
                }
            }
            if (icpCount != 0) {
                return {
                    CanOperated: false,
                    Message: '该主体已注销或者在注销中，无法操作',
                }
            }
            return { CanOperated: true }
        }
        case 9:
        case 10: {
            // 	"10": "变更接入",
            ;[icpCount, webCount] = await Promise.all([
                // 在主体注销中 变更备案中 主体已删除 不允许变更接入
                ICP.count({
                    where: {
                        ICPMainNo: params.ICPMainNo,
                        Status: {
                            [Op.in]: [4, 7, 20],
                        },
                    },
                }),
                // 通过查订单需要查两个表
                // 所以 根据网站备案状态来判断 是否未在 变更 或注销
                ICPWeb.count({
                    where: {
                        ICPWebNo: params.ICPWebNo,
                        Status: 0,
                    },
                }),
            ])

            if (icpCount != 0 || webCount == 0) {
                return {
                    CanOperated: false,
                    Message: '该网站已存在流程中的订单，请勿重复提交',
                }
            }

            webInfo = await ICPWeb.findAll({
                where: {
                    ICPWebNo: params.ICPWebNo,
                },
            })
            webInfo = parseJSON(webInfo)

            // 2023年06月02日15:24:24  省份限制逻辑调整为统一的逻辑
            return { CanOperated: true, oldStatus: webInfo[0].Status }
        }
        default:
            return { CanOperated: true }
    }
    return { CanOperated: true }
}

async function checkOrderWeb(condition) {
    // 检查订单网站中，是否有符合条件的订单
    let orderWebInfo = await OrderWeb.findAll({ where: condition })
    if (orderWebInfo.length != 0) {
        // 如果存在，确定订单状态
        let orderInfo = await Order.findAll({
            where: {
                Status: {
                    [Op.ne]: 12,
                },
                IsDeleted: 0,
                OrderNo: {
                    [Op.in]: _.map(orderWebInfo, 'OrderNo'),
                },
            },
        })
        if (orderInfo.length !== 0) {
            return false
        }
    }
    return true
}
