const Method = require('../../libs/method')
const { pickMainDomain, getDomainInfo } = require('../../fns/kits')
const logger = require("../../libs/logger");

// 检查域名是否在我司
module.exports = class CheckICPDomainInUcloud extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec(params = {}) {
        const { Domain } = params
        let self = this
        try {
            let redis = this.redis.get()
            let ICPConfig = await redis.get('DescribeICPConfig')
            ICPConfig = JSON.parse(ICPConfig)
            let mainDomain = pickMainDomain(Domain, ICPConfig.EffectiveDomain)
            let domainInfo = await getDomainInfo(mainDomain)
            return this.cb(0, { CheckResult: domainInfo.Data.length !== 0 })
        } catch (err) {
            logger.getLogger('error').error('[' + params.UUID + ']', 'CheckICPDomainInUcloud failed, the err is: ',err)
            // 此接口暂时 不使用，若使用 则更改下列报错的code
            let error = new Error(err.message)
            error.code = 67092
            self.err(error)
        }
    }
}
