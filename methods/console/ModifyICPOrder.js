const Method = require('../../libs/method')
const _ = require('lodash')
const { getTableModel, parseJSON } = require('../../fns/kits')
const { checkEipUhybrid } = require('../../fns/checkEipUhybrid')
const { checkOrder, checkPicture } = require('../../fns/checkOrder')
const { Op } = require('sequelize')
const orderUpdateMapping = require('../../configs/common/order_update.json')
const { changeCurtainStatus } = require('../../fns/FinishH5')
const { checkCompanyOrderPermissions } = require('../../fns/order/OrderService')
const { OrderWebPlatInfoModel } = require('../../mongoModels/icp')
const InternalApiReq = require('../../libs/ucloudinternalapi')

let {
    OrderModel: Order,
    OrderTypeEnum,
    OrderStatusEnum,
    OrderWebModel: OrderWeb,
    PictureModel: Picture,
    AuthenticationCodeModel: Authentication,
    IPWhiteListModel: IPWhiteList,
} = require('../../models')
const logger = require("../../libs/logger");
// 修改订单
module.exports = class ModifyICPOrder extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec(params = {}) {
        let self = this
        let redis = this.redis.get()
        let lock = await redis.incr(`lock_${params.OrderNo}`)
        if (parseInt(lock) > 1) {
            return this.cb(67080, {
                CanOperated: false,
                Message: '订单变更中，请勿频繁重复提交',
            })
        }
        await redis.expire(`lock_${params.OrderNo}`, 10)

        let IsBlock = await checkCompanyOrderPermissions(params.CompanyId)
        if (IsBlock) {
            return self.cb(67018, {
                Message:
                    '当前账号因存在违规备案行为被禁止操作备案。',
            })
        }
        // 删除Id,无论什么情况下，这个值都是不需要更新的
        delete params.Id
        try {
            // 更新操作，先获取旧订单数据
            let oldDatas = await Promise.all([
                Order.findAll({
                    where: {
                        // Id: params.Id,
                        OrderNo: params.OrderNo,
                        CompanyId: params.CompanyId,
                        IsDeleted: 0,
                    },
                }),
                OrderWeb.findAll({
                    where: {
                        OrderNo: params.OrderNo,
                        IsDeleted: 0,
                    },
                }),
            ])

            // 未查到订单，可能是不在这个公司下
            if (oldDatas[0].length === 0)
                throw new this.Err(new Error('There is no such order'), 67007)

            // 结构处理
            let order = parseJSON(oldDatas[0][0])
            let oldOrderStatus = order.Status
            order.Website = parseJSON(oldDatas[1])
            // 存储老数据避免domain更新
            let oldWebsites = parseJSON(oldDatas[1])
            // 获取Website的所有 App平台信息列表 并未order.Website.AppPlatformInformationList 赋值 作为初始值来判断变更的更新
            for (let orderWebInfo of order.Website) {
                if (orderWebInfo.InternetServiceType == 6) {
                    let platInfo = await OrderWebPlatInfoModel.findOne({
                        OrderWebId: orderWebInfo.Id,
                    })
                    orderWebInfo.AppPlatformInformationList =
                        platInfo?.AppPlatformInformationList || []
                }
            }
            if (
                Object.hasOwnProperty.call(params, 'Type') &&
                +params.Type !== order.Type
            ) {
                throw new this.Err(new Error("Can't change order type"), 67066)
            }

            // 授权码、座机处理逻辑，脏的部分
            if (params.EmergencyPhone) {
                // 如果应急联系电话更新，则同步更新手机
                params.PICMainOfficePhone = params.EmergencyPhone
            }

            // Error字段处理
            if (params.Status === 2 || params.Status === 7 || !params.FrontError) {
                // 只要提交，就清除前端记录的错误
                params.FrontError = {}
            }

            // 为前端方便提供的功能，不是很喜欢
            if (params.Picture) {
                params.Picture = Buffer.from(params.Picture, 'base64').toString(
                    'ascii'
                )

                try {
                    params.Picture = JSON.parse(params.Picture)

                    if (params.Picture.OrganizerLicensePicture) {
                        params.OrganizerLicensePicture =
                            params.Picture.OrganizerLicensePicture
                    }

                    if (params.Picture.PromiseLetterPicture) {
                        // 440100 - 445381 是广东省的 地区id地址
                        if (params.Picture.PromiseLetterPicture.length !== 0) {
                            if (
                                order.AreaId < 440100 ||
                                order.AreaId > 445381
                            ) {
                                //报错  如果不是广东省 并且有图片 则报错
                                delete params.Picture.PromiseLetterPicture
                            } else {
                                params.PromiseLetterPicture =
                                    params.Picture.PromiseLetterPicture
                            }
                        }
                    }
                    if (params.Picture.OrganizerResidencePermitPicture) {
                        params.OrganizerResidencePermitPicture =
                            params.Picture.OrganizerResidencePermitPicture
                    }

                    if (params.Picture.PICMainLicensePicture) {
                        params.PICMainLicensePicture =
                            params.Picture.PICMainLicensePicture
                    }

                    if (params.Picture.OtherPicture) {
                        params.OtherPicture = params.Picture.OtherPicture
                    }

                    _.forEach(params.Picture.Website, function (value, key) {
                        // 定义证件号与图片类型
                        let [LNumber, PType] = key.split('_')
                        // 循环网站,更新订单中的网站，而且不是仅仅入参的网站。为了防止旧的没有复用
                        params.Website.forEach((element) => {
                            if (element.LicenseId === LNumber) {
                                if (order.Version == '4.6') {
                                    element[PType] = value
                                } else if (
                                    PType !== 'CurtainPicture' &&
                                    PType !== 'OtherPicture' &&
                                    PType !== 'AuthoriztionPicture'
                                ) {
                                    element[PType] = value
                                }
                            }
                        })
                    })

                    if (params.Picture.Others) {
                        for (
                            let index = 0;
                            index < params.Picture.Others.length;
                            index++
                        ) {
                            const element = params.Picture.Others[index]
                            if (JSON.stringify(element) !== '{}') {
                                // 增加对网站的判断，
                                if (
                                    element.OtherPicture &&
                                    params.Website[index]
                                ) {
                                    params.Website[index].OtherPicture =
                                        element.OtherPicture
                                }
                                if (
                                    element.AuthoriztionPicture &&
                                    params.Website[index]
                                ) {
                                    params.Website[index].AuthoriztionPicture =
                                        element.AuthoriztionPicture
                                }
                            }
                        }
                    }
                } catch (error) {
                    console.log(error)
                    throw new this.Err(new Error('图片处理失败'), 67065)
                }
            }
            // 调整对网站需要处理的数据位置的调整
            if (params.Website) {
                for (let website of params.Website) {
                    if (website.NullPreAppoval === 1) {
                        website.PreAppoval = []
                        delete website.NullPreAppoval
                    }
                }
            }
            if (params.Status !== 1 && params.Status !== order.Status) {
                // 提交审核的订单，根据订单类型，判断输入参数是不是完
                let websites = [].concat(order.Website || [])
                let APPs = []
                if (params.Website) {
                    // 取授权码信息
                    for (let i = 0, l = params.Website.length; i < l; i++) {
                        // 网站检查
                        if (params.Website[i].EmergencyPhone) {
                            // 如果应急联系电话更新，则同步更新座机
                            params.Website[i].Phone =
                                params.Website[i].EmergencyPhone
                        }

                        // 如果有授权码，则替换成IP  ,确定是否用授权码，且是提交状态
                        if (
                            params.Website[i].AuthenticationCode !==
                                undefined &&
                            params.Website[i].AuthenticationCode !== '' &&
                            [2, 7, 11].indexOf(params.Status) >= 0
                        ) {
                            try {
                                // 如果有授权码，则替换成IP
                                let codeInfo = await Authentication.findAll({
                                    where: {
                                        TargetCompanyId: params.CompanyId,
                                        Code: params.Website[i]
                                            .AuthenticationCode,
                                        IsBlock: 0,
                                    },
                                })
                                codeInfo = parseJSON(codeInfo)
                                // 二次检查授权是否匹配
                                if (codeInfo.length === 0) {
                                    throw new this.Err(
                                        new Error('未查到有效授权'),
                                        67028
                                    )
                                    // return fn(67028, {
                                    // 	Message: '未查询到有效授权'
                                    // })
                                }
                                // IP可以为多个
                                params.Website[i].IP = [codeInfo[0].IP]
                                // 验证IP是否为客户的
                            } catch (error) {
                                throw new this.Err(
                                    new Error('验证授权码验证备案过程出错出错'),
                                    67027
                                )
                                // return fn(67024, {
                                // 	Message: '验证授权码过程出错'
                                // })
                            }
                        }

                        const website = params.Website[i]
                        if (website.Id) {
                            const j = _.findIndex(
                                websites,
                                (_website) => _website.Id === +website.Id
                            )
                            if (j < 0) {
                                throw new this.Err(
                                    new Error(
                                        'There is no such website: ${website.Id}'
                                    ),
                                    67063
                                )
                            } else {
                                websites[j] = _.extend({}, websites[j], website)
                            }
                        } else {
                            websites.push(website)
                        }
                    }
                    APPs =
                        params.Website.filter(
                            (web) => web.InternetServiceType == 6
                        ) || []
                    APPs = APPs.map((app) => app.Name)
                }
                if (APPs.length > 1 && _.uniq(APPs).length !== APPs.length) {
                    throw new this.Err(new Error('App名称不能重复'), 67004)
                }
                let checkRes = await checkOrder(
                    redis,
                    _.extend({}, order, params, {
                        Website: websites,
                    })
                )
                if (checkRes) throw new this.Err(new Error(checkRes), 67004)

                // 根据当前状态和Error重的数据，找出能更新或新增的字段
            }
            let info = getUpdateParams(order, params)

            if (!info)
                throw new this.Err(
                    new Error(
                        "There is no params need to update or this order can't be modified"
                    ),
                    67019
                )

            //检查IP是否符合
            //update 2 增加授权码功能
            // 存各网站授权码，不能重复
            // let authCodeList = []		功能已下线

            if (params.Website !== undefined) {
                for (let value of params.Website) {
                    // 是否有授权码，有验证授权码是否被使用，没有验证IP与公司的匹配
                    // IPType不存在是IP，存在的话，确定值，1为IP
                    // 判断这个网站是否用授权码，用了则不去验证IP与公司的匹配

                    value.IP = _.uniq(value.IP)
                    let checkOk = await new Promise((rs, rj) => {
                        checkEipUhybrid(
                            IPWhiteList,
                            params.company_id || params.CompanyId,
                            value.IP,
                            (code, message) => rs(message)
                        )
                    })

                    if (!(checkOk.IsBelong !== false))
                        throw new this.Err(new Error(checkOk.Tip), 67021)

                    if (
                        params.Picture &&
                        params.Picture.Website &&
                        params.Picture.Website[value.LicenseId] !== undefined
                    ) {
                        value.LicensePicture =
                            params.Picture.Website[value.LicenseId]
                    }
                }
            }

            if (params.Status == 2 || params.Status == 7) {
                try {
                    let pictureCheck = await checkPicture(Picture, params)
                    if (pictureCheck !== 0) {
                        {
                            throw new this.Err(new Error(pictureCheck), 67034)
                        }
                    }
                } catch (error) {
                    return self.cb(67034, {
                        Message:
                            '更新的图片存在异常请重新上传:' + error.message,
                    })
                }
            }
            // 图片检查

            // 数据更新部分
            // 更新订单
            if (
                Array.isArray(params.OtherPicture) &&
                Array.isArray(info.needToUpdate.OtherPicture)
            ) {
                info.needToUpdate.OtherPicture = params.OtherPicture.filter(
                    (pic) => /^[a-zA-Z0-9.]+$/.test(pic)
                )
            }

            let promises = []

            // 更新网站
            if (info.needToUpdate.Website) {
                info.needToUpdate.Website.forEach((website) => {
                    //过滤掉不合规
                    if (Array.isArray(website.OtherPicture)) {
                        website.OtherPicture = website.OtherPicture.filter(
                            (pic) => /^[a-zA-Z0-9.]+$/.test(pic)
                        )
                    }

                    // 前端出错的暂存区域，只在编辑时可用，提交审核时，内容清空
                    if (
                        website.FrontError === undefined ||
                        params.Status == 2 ||
                        params.Status == 7
                    ) {
                        website.FrontError = {}
                    }
                    if (website.AppPlatformInformationList) {
                        // 更新AppPlatformInformationList 数据
                        promises.push(
                            OrderWebPlatInfoModel.findOneAndUpdate(
                                { OrderWebId: website.Id },
                                {
                                    $set: {
                                        AppPlatformInformationList:
                                            website.AppPlatformInformationList,
                                    },
                                },
                                {
                                    upsert: true,
                                }
                            )
                        )
                    }
                    // 扩展校验
                    if (!checkDomainChangeRight(oldWebsites, website, params.Type, website.InternetServiceType)) {
                        throw new this.Err(
                            new Error(
                                "Changing the website's domain is not allowed"
                            ),
                            67093
                        )
                    }
                    if (website.Id) {
                        promises.push(
                            OrderWeb.update(website, {
                                where: {
                                    Id: website.Id, //这里不用提前查找ID是否存在是因为，如果不存在 也更新不了 影响不大
                                },
                            })
                        )
                    }
                })
            }

            // 新增网站
            if (info.addWeb && Array.isArray(params.Website)) {
                params.Website.filter(
                    (website) => website.Id === undefined
                ).forEach((website) => {
                    website.OrderNo = params.OrderNo
                    promises.push(
                        OrderWeb.create(website).then(async (res) => {
                            res = parseJSON(res)
                            if (
                                res.InternetServiceType == 6 &&
                                res.AppPlatformInformationList
                            ) {
                                // 创建AppPlatformInformationList数据
                                await new OrderWebPlatInfoModel({
                                    OrderWebId: res.Id,
                                    AppPlatformInformationList:
                                        res.AppPlatformInformationList,
                                }).save()
                            }
                        })
                    )
                })
            }

            try {
                await Order.update(info.needToUpdate, {
                    where: {
                        OrderNo: params.OrderNo,
                        CompanyId: params.CompanyId,
                    },
                })
                await Promise.all(promises)
                await changeCurtainStatus(
                    Order,
                    OrderWeb,
                    params.OrderNo,
                    params.CompanyId
                )
            } catch (error) {
                console.log(error)
                throw new this.Err(new Error(error), 67064)
            }
            if (
                parseInt(order.Type) === OrderTypeEnum.AddConn &&
                parseInt(params.Status) === OrderStatusEnum.Auditing
            ) {
                // 新增接入订单提交审核，校验并加白等操作 ,只加白在我司接入的域名
                let domains = []
                if (order.Website[0].InternetServiceType == 6) {
                    order.Website[0]?.Domain?.forEach((d) => {
                        if (d.BelongToUs) {
                            domains.push(d.Domain)
                        }
                    })
                } else {
                    domains = order.Website[0]?.Domain?.map((d) => d.Domain)
                }
                if (domains?.length > 0) {
                    // 域名加入白名单
                    await this.producer.send({
                        type: 'icp',
                        topic: 'AddDomainToWhiteList',
                        data: {
                            Domains: domains,
                            RetryTime: 0,
                        },
                    })
                }
            }
            if (+oldOrderStatus === OrderStatusEnum.Editing && +params.Status === OrderStatusEnum.Auditing) {
                // 首次提交到审核 触发自动化审核, 异步 不阻塞
                InternalApiReq({
                    Action: 'CreateAutoAudit',
                    Backend: 'ICPV4',
                    __ssoUser: 'modify order first audit',
                    OrderNo: params.OrderNo,
                    AuditType: "FullAudit"
                }).catch(err => {
                    console.log('ModifyICPOrder FullAudit CreateAutoAudit error:' + err.message)
                })
            } else if (+oldOrderStatus === OrderStatusEnum.AuditRefuse && +params.Status === OrderStatusEnum.Auditing ||
                    +oldOrderStatus === OrderStatusEnum.GovAuditRefuse && +params.Status === OrderStatusEnum.Auditing) {
                // ucloud审核退回或者管局审核退回，再到ucloud审核中触发对打回字段的检查
                InternalApiReq({
                    Action: 'CreateAutoAudit',
                    Backend: 'ICPV4',
                    __ssoUser: 'modify order increment audit',
                    OrderNo: params.OrderNo,
                    AuditType: "IncrementAudit"
                }).catch(err => {
                    console.log('ModifyICPOrder IncrementAudit CreateAutoAudit error:' + err.message)
                })
            }
            if (+params.Status === OrderStatusEnum.Auditing) {
                // 订单提交到审核中 检查二线IP
                this.producer.send({
                    type: 'icp',
                    topic: 'CheckIPIsTwoLine',
                    data: {
                        OrderNo: params.OrderNo,
                    },
                })
            }
            // 不出执行出错返回
            await redis.del(`lock_${params.OrderNo}`)
            self.cb(0, {
                OrderNo: params.OrderNo,
            })
        } catch (e) {
            console.log(e)
            if (e instanceof Error) {
                return self.eh({
                    err: e,
                    code: 67061,
                })
            }
            this.eh(e)
        }
    }
}

function getUpdateParams(origin, params) {
    // 判断是否允许如此更新
    if (
        !orderUpdateMapping[origin.Type] ||
        !orderUpdateMapping[origin.Type][origin.Status] ||
        !orderUpdateMapping[origin.Type][origin.Status][params.Status]
    )
        return false

    // 获取对应可更新规则
    const mapping =
        orderUpdateMapping[origin.Type][origin.Status][params.Status]

    let addWeb = false

    // 根据规则获得要修改的信息
    mapping.forEach((rule) => {
        // 如果是特定的某个字段，则直接修改

        if (rule.substr(0, 1) !== '@' && params[rule])
            return (origin[rule] = params[rule])

        // 处理每个Website下新增的部分
        if (rule.substr(0, 8) === '@Website') {
            origin.Website.map((website) => {
                let key = rule.split('.')[1]

                // 传入的Id类型不定，当是做兼容
                let currentWebsite = _.find(params.Website, function (o) {
                    return o.Id == website.Id.toString()
                })
                if (currentWebsite) {
                    if (currentWebsite[key] === undefined) return
                    website[key] = currentWebsite[key]
                }
            })
        }

        // todo 要过滤出能修改的部分
        // 统一return  todo
        switch (rule) {
            case '@All': {
                addWeb = true
                return (origin = _.extend(origin, params))
            }
            case '@Error': {
                // 主体部分的 Error
                let putAwayWebsite = parseJSON(origin.Error)
                delete putAwayWebsite.Website
                origin = _.extend(
                    origin,
                    _.pick(params, _.keys(putAwayWebsite))
                )

                if (origin.Error.AddWeb) addWeb = true

                // 每个网站的 Error
                origin.Website.map((website) => {
                    let currentWebsite = (params.Website || []).filter(
                        (web) => web.Id === website.Id
                    )
                    // 从原始的Error信息中，取出对应网站的出错放入website.Error。
                    try {
                        website.Error = origin.Error.Website[website.Id]
                    } catch (e) {
                        return
                    }
                    if (currentWebsite.length === 0) return
                    currentWebsite = currentWebsite[0]

                    website = _.extend(
                        website,
                        _.pick(currentWebsite, _.keys(website.Error))
                    )

                    if (website.Error && website.Error.AddWeb) addWeb = true
                })
                break
            }
            case '@AddWeb': {
                return (addWeb = true)
            }
        }
    })

    // 设置将要更新到的状态
    origin.Status = params.Status
    return {
        needToUpdate: origin,
        addWeb: addWeb,
    }
}

function checkDomainChangeRight(oldWebsites, newWebsite, type, internetServiceType) {
    logger.getLogger('access').info(`the type is ${type}, the internetServiceType is ${internetServiceType}`)
    // 定义允许直接通过的备案类型
    const allowedTypes = new Set([1, 4, 5, 6, 8])
    if (allowedTypes.has(type)) return true

    // 获取对应的旧网站信息
    const oldWebsite = oldWebsites.find(old => old.Id === newWebsite.Id)
    if (!newWebsite.Domain?.length || !oldWebsite?.Domain?.length) return true

    const newDomainArray = newWebsite.Domain.map(d => d.Domain);
    const oldDomainArray = oldWebsite.Domain.map(d => d.Domain);
    const differentDomain = _.difference(newDomainArray, oldDomainArray)
    logger.getLogger('access').info(`the newDomainArray is ${JSON.stringify(newDomainArray)}`)
    logger.getLogger('access').info(`the oldDomainArray is ${JSON.stringify(oldDomainArray)}`)
    // 单独处理APP
    if (internetServiceType === 6) {
        // APP类型，如果有新增的，或者新的domain中相对老的有改变的直接返回false
        if (type === 10 && (newDomainArray.length > oldDomainArray.length || differentDomain.length > 0)) {
            return false
        }
        return true
    }

    switch (type) {
        case 2:
        case 3:
            // 新增互联网信息服务和新增接入，第一个域名不能更改
            if (newDomainArray[0] !== oldDomainArray[0]) {
                return false
            }
            break
        case 7:
        case 9:
            // 变更备案信息和变更互联网信息服务，第一个域名不允许更改且只能删除也不能修改原来的
            if (newDomainArray[0] !== oldDomainArray[0] || newDomainArray.length > oldDomainArray.length || differentDomain.length > 0) {
                return false
            }
            break
        case 10:
            // 网站类型的变更接入：必须完全一致
            if (newDomainArray.length !== oldDomainArray.length) return false

            for (let i = 0; i<newDomainArray.length; i++) {
                if (newDomainArray[i] !== oldDomainArray[i]) {
                    return false
                }
            }
            break
        default:
            break;
    }
    return true;
}
