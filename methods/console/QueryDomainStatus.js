const Method = require('../../libs/method')
const ucloudApiPromise = require('../../fns/ucloudApiPromise')

/**
 * 查看域名备案情况
 */
module.exports = class QueryDomainStatus extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec(params = {}) {
        let self = this
        try {
            let { RetCode, IsICP } = await ucloudApiPromise({
                options: { category: 'TplCrd', action: 'ICPQueryDomainStatus' },
                data: {
                    Domain: params.Domain,
                },
            })

            if (RetCode !== 0) {
                return this.cb(67047, {
                    Tip: '请求时网络失败',
                })
            }
            let responseData = { IsICP: IsICP === 1 }
            if (!responseData.IsICP) {
                responseData.Tip = '此域名在工信部查询未存在备案记录'
            }

            return this.cb(0, responseData)
        } catch (e) {
            let err = new Error(e.message)
            err.code = 67009
            self.err(err)
        }
    }
}
