/**
 * @file API GetTemporarySession 通过生成的Id,获取对应的Session，另判断，如果Session超时，重新更新
 * <AUTHOR>
 * @param {CompanyId} 公司Id 必选参数,由网关传入条件
 * @return {Object} 返回符合条件的记录,格式如下
 * 错误码:
 * 67036: 签名过出错
 * 67011: 调用生成STS Token出错
 * 67038: Token加密出错
 */

const Method = require('../../libs/method')
const { Op } = require('sequelize')
const { getTableModel } = require('../../fns/kits')
const moment = require('moment')

// 获取API密钥24小时内有效。

module.exports = class InquiryToken extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({ Token, CompanyId } = {}) {
        // let redis = this.redis.get()
        let self = this
        let icpDatabase = this.db.get('icp')
        let now, session
        now = parseInt(moment().format('X'))

        const TokenTable = getTableModel('t_token', icpDatabase)

        try {
            // 获取Token,如果没有，出错
            let tokenInfo = await TokenTable.findOne({
                where: {
                    Token,
                    CompanyId,
                },
            })
            tokenInfo = JSON.parse(JSON.stringify(tokenInfo))

            if (!tokenInfo) {
                return this.cb(0, {
                    Effective: false,
                    Message: '未查到有效授权，刷新确认',
                })
            }

            // 判断是否过有效期，24小时
            if (tokenInfo.CreateTime + 24 * 60 * 60 < now) {
                // 创建时间后的24小时小于现在，说明有效期内
                return this.cb(0, {
                    Effective: false,
                    Message: '授权码已过有效期，请在控制台刷新',
                })
            } else if (tokenInfo.Status === 1) {
                return this.cb(0, {
                    Effective: false,
                    Message: '授权码已失效，请在控制台刷新',
                })
            } else {
                return this.cb(0, { Effective: true })
            }
        } catch (e) {
            console.log(e, 333)
            if (e instanceof Error) {
                return self.eh({
                    err: e,
                    code: 67078,
                })
            }
            self.eh(e)
        }
    }
}
