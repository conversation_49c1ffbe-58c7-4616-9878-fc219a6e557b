const Method = require('../../libs/method')
const { getTableModel, parseJSON } = require('../../fns/kits')
const _ = require('lodash')
const { Op } = require('sequelize')
const henganApiPromise = require('../../libs/henganApiPromise')
//删除订单
module.exports = class DeleteICPOrder extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec(params = {}) {
        let self = this
        let icpDatabase = this.db.get('icp')
        const ICPModel = getTableModel('t_icp', icpDatabase)
        const ICPWebModel = getTableModel('t_web', icpDatabase)
        const OrderModel = getTableModel('t_order', icpDatabase)
        const OrderWebModel = getTableModel('t_order_web', icpDatabase)
        const OrderHistoryModel = getTableModel('t_order_history', icpDatabase)

        try {
            params.OrderNo =
                params.OrderNo instanceof Array
                    ? params.OrderNo
                    : [params.OrderNo]
            
            let [orders, orderWebs] = await Promise.all([
                OrderModel.findAll({
                    where: {
                        OrderNo: {
                            [Op.in]: params.OrderNo,
                        },
                        CompanyId: params.CompanyId,
                    },
                }),
                OrderWebModel.findAll({
                    where: {
                        OrderNo: { [Op.in]: params.OrderNo },
                    },
                }),
            ])
            orderWebs = parseJSON(orderWebs)
            orders = parseJSON(orders)
            if (orders.length === 0) {
                let error = new Error('没有找到该订单')
                error.code = 67007
                return self.err(error)
            } else {
                for (let order of orders) {
                    if (_.indexOf([11, 12], order.Status) !== -1) {
                        let error = new Error('该状态的订单 不允许删除')
                        error.code = 67015
                        return self.err(error)
                    }
                    let orderWeb = orderWebs.filter(o => o.OrderNo === order.OrderNo)
                    // 编辑状态下的删除 就没必要同步到傲顿了 因为还没提过去
                    if (order.Status !== 1) {
                        // 将删除同步到傲顿
                        let henganReqConf = {
                            Type: order.Type,
                        }
                        //新增备案、变更备案、变更主体、注销备案时为主体cid；
                        //新增网站、变更网站、注销网站时为网站cid；
                        //新增接入、变更接入、注销接入时为接入cid
                        switch (order.Type) {
                            case 1:
                                // 主体订单id为cmainid
                                henganReqConf.Cid = order.Id
                                henganReqConf.Type = 1
                                break
                            case 2:
                                // 新增网站 网站订单中的id 作为cid
                                henganReqConf.Cid = orderWeb[0].Id
                                henganReqConf.Type = 2
                                break
                            case 3:
                                // 新增接入 网站订单中的id * 100
                                henganReqConf.Cid = orderWeb[0].Id * 100
                                henganReqConf.Type = 2

                                break
                            case 4:
                                henganReqConf.Cid = order.CMainId
                                henganReqConf.Type = 8
                                break
                            case 7:
                                henganReqConf.Cid = order.CMainId
                                henganReqConf.Type = 4
                                break
                            case 8:
                                // 注销主体 变更备案 变更主体 都是主体订单中的CMainId
                                henganReqConf.Type = 5
                                henganReqConf.Cid = order.CMainId
                                break
                            case 5:
                                // 注销网站 信息只有一条记录在order表 故取order表中的CWebsiteId
                                henganReqConf.Cid = order.CWebsiteId
                                henganReqConf.Type = 9
                                break
                            case 6:
                                // 取消接入 信息只有一条记录在order表 故取订单中的CWebsiteId * 100
                                henganReqConf.Cid = order.CWebsiteId * 100
                                henganReqConf.Type = 10

                                break
                            case 9:
                                // 变更网站 网站订单中的CWebsiteId 作为cid
                                henganReqConf.Cid = orderWeb[0].CWebsiteId
                                henganReqConf.Type = 6

                                break
                            case 10:
                                // 变更接入 网站中的CWebsiteId * 100
                                henganReqConf.Type = 7
                                henganReqConf.Cid = orderWeb[0].CWebsiteId * 100
                                break
                        }

                        try {
                            let res = await henganApiPromise(
                                'DeleteOrderAction',
                                henganReqConf
                            )
                            if (res.RetCode !== 0) {
                                return self.cb(32116, {
                                    Message:
                                        res.msg + `，OrderNo: ${order.OrderNo}`,
                                })
                            }
                        } catch (err) {
                            return self.cb(32116, {
                                Message:
                                    err.message + `，OrderNo: ${order.OrderNo}`,
                            })
                        }
                    }
                    // 更新 t_order 和 t_order_web 的订单状态为 删除
                    await Promise.all([
                        OrderModel.update(
                            { IsDeleted: 1 },
                            {
                                where: {
                                    CompanyId: order.CompanyId,
                                    OrderNo: order.OrderNo,
                                },
                            }
                        ),
                        OrderWebModel.update(
                            { IsDeleted: 1, AuthenticationCode: '' },
                            {
                                where: {
                                    OrderNo: order.OrderNo,
                                },
                            }
                        ),
                    ])

                    // 一下开始修改 备案记录中的状态
                    if (order.Type === 7) {
                        // 变更备案类型的订单
                        await Promise.all([
                            ICPModel.update(
                                { Status: 0 },
                                {
                                    where: {
                                        Status: 7,
                                        Id: order.ICPId,
                                    },
                                }
                            ),
                            ICPWebModel.update(
                                { Status: 0 },
                                {
                                    where: {
                                        MainId: order.ICPId,
                                        IsDeleted: 0,
                                        Status: 7,
                                    },
                                }
                            ),
                        ])
                    }
                    // 变更、注销主体 新增同时变更主体
                    if (
                        order.Type === 8 ||
                        order.Type === 4 ||
                        ((order.Type === 2 || order.Type === 3) &&
                            order.NeedModifyIcp)
                    ) {
                        // 判断是否还存在变更订单 或者新增+变更的订单 若存在则不更新
                        let orderCount = await OrderModel.count({
                            where: {
                                ICPMainNo: order.ICPMainNo,
                                IsDeleted: 0,
                                Status: {
                                    [Op.ne]: 12,
                                },
                                [Op.or]: [
                                    { Type: 8 },
                                    {
                                        Type: {
                                            [Op.in]: [2, 3],
                                        },
                                        NeedModifyIcp: 1,
                                    },
                                ],
                            },
                        })
                        // 没有上述类型订单 或者删除的这个订单是注销主体的 就恢复主体状态
                        if (orderCount == 0 || order.Type === 4) {
                            await ICPModel.update(
                                { Status: 0 },
                                {
                                    where: {
                                        ICPMainNo: order.ICPMainNo,
                                        Status: {
                                            [Op.in]: [4, 8],
                                        },
                                    },
                                }
                            )
                        }
                    }
                    // 新增的变更类订单，网站
                    // 考虑网站与主体存在兼容的情况
                    if (
                        order.Type === 9 ||
                        order.Type === 10 ||
                        order.Type === 5 ||
                        order.Type === 6
                    ) {
                        // 查网站的状态
                        let webData = await ICPWebModel.findAll({
                            where: {
                                ICPWebNo:
                                    orderWeb[0]?.ICPWebNo &&
                                    orderWeb[0]?.ICPWebNo.trim() !== ''
                                        ? orderWeb[0]?.ICPWebNo
                                        : order.ICPWebNo,
                            },
                        })
                        webData = parseJSON(webData)
                        if (
                            webData[0].Status === 9 ||
                            webData[0].Status === 10 ||
                            webData[0].Status === 5 ||
                            webData[0].Status === 6
                        ) {
                            await ICPWebModel.update(
                                { Status: 0 },
                                {
                                    where: {
                                        ICPWebNo: webData[0].ICPWebNo,
                                        Status: {
                                            [Op.in]: [9, 10, 5, 6],
                                        },
                                    },
                                }
                            )
                        } else if (webData[0].Status === 19) {
                            await ICPWebModel.update(
                                { Status: 19 - webData[0].Status },
                                {
                                    where: {
                                        ICPWebNo: webData[0].ICPWebNo,
                                    },
                                }
                            )
                        }
                    }
                    await OrderHistoryModel.create({
                        OrderNo: order.OrderNo,
                        OperationTime: new Date(),
                        Status: order.Status,
                        Operator: 'user',
                        Action: 'delete_order',
                    })
                }
                return this.cb(0)
            }
        } catch (e) {
            let error = new Error(e.message)
            error.code = 67039
            self.err(error)
        }
    }
}
