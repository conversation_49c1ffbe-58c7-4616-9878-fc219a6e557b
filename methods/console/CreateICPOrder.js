const Method = require('../../libs/method')
const _ = require('lodash')
const {
    getTableModel,
    parseJSON,
    getICPMainNoByICPWebNo,
    generateOrderNo,
} = require('../../fns/kits')
const { checkPicture } = require('../../fns/checkOrder')
const { Op } = require('sequelize')
const { checkCompanyOrderPermissions } = require('../../fns/order/OrderService')
const { OrderWebPlatInfoModel } = require('../../mongoModels/icp')
const CheckICPOrder = require('./CheckICPOrder')
const InternalApiReq = require('../../libs/ucloudinternalapi')

let Order, OrderWeb, ICP, ICPWeb, Picture, Authentication

// 创建订单
module.exports = class CreateICPOrder extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec(params = {}) {
        let self = this
        let icpDatabase = this.db.get('icp')
        let redis = this.redis.get()

        let lock = await redis.incr(`lock_${params.OrganizerLicenseId}`)
        if (parseInt(lock) > 1) {
            return this.cb(67080, {
                CanOperated: false,
                Message: '订单创建中，请勿频繁重复提交',
            })
        }
        await redis.expire(`lock_${params.OrganizerLicenseId}`, 10)

        let IsBlock = await checkCompanyOrderPermissions(params.CompanyId)
        if (IsBlock) {
            return self.cb(67018, {
                Message:
                    '当前账号因存在违规备案行为被禁止操作备案。',
            })
        }
        // 引入表格Model
        Order = getTableModel('t_order', icpDatabase)
        OrderWeb = getTableModel('t_order_web', icpDatabase)
        ICP = getTableModel('t_icp', icpDatabase)
        ICPWeb = getTableModel('t_web', icpDatabase)
        Picture = getTableModel('t_picture', icpDatabase)
        Authentication = getTableModel('t_authentication_code', icpDatabase)

        // 变更类可能会引入完成的ID,删除
        delete params.Id

        // 新增互联网信息服务检查数据库中是否已经存在备案了
        if ((params.Type === 2 || params.Type === 3) && params.ICPMainNo) {
            let checkRecords = await ICP.findAll({
                attributes: [ 'ICPMainNo', 'Status', 'CompanyId'],
                where: { ICPMainNo: params.ICPMainNo },
            })
            let icps = parseJSON(checkRecords)
            if (icps && icps.length > 0 && icps[0].Status !== 1 && icps[0].CompanyId !== params.CompanyId) {
                return self.cb(67091, {
                    Message: '该备案主体在我司系统已存在，请在对应账号已备案完成列表处进行操作',
                })
            }
        }

        try {
            // 状态锁检查
            // 版本检查，晚于人名信息的订单，标记为New
            // let afterVersionForUserMork = parseInt(moment().format('X')) > global.CONFIG.versionTimeForUserMork
            if (parseInt(params.Type) > 3 && !params.ICPMainNo) {
                // 如果非新增类，同时没有传入ICPMainNo，使用网站备案号处理后替换
                params.ICPMainNo = getICPMainNoByICPWebNo(params.ICPWebNo)
            }
            // 验证参数
            let checkRes = {}
            let checkMethod = new CheckICPOrder((RetCode, data) => {
                checkRes = {
                    RetCode,
                    ...data,
                }
            })
            await checkMethod.exec({ ...params, CreateCheck: true })

            if (checkRes.RetCode !== 0) {
                return self.cb(checkRes.RetCode, { Message: checkRes.Message })
            }

            if (params.Status == 2 || params.Status == 7) {
                try {
                    let pictureCheck = await checkPicture(Picture, params)
                    if (pictureCheck !== 0) {
                        {
                            throw new this.Err(new Error(pictureCheck), 67034)
                        }
                    }
                } catch (error) {
                    return self.cb(67034, {
                        Message:
                            '更新的图片存在异常请重新上传:' + error.message,
                    })
                }
            }

            // 订单号与各字段脏活步骤
            params.OrderNo = generateOrderNo()
            // 更新细节字段，注销类会使用
            params.Details = params.Details === undefined ? [] : params.Details
            params.PICMainOfficePhone = params.EmergencyPhone || ''

            try {
                // 如果是有主体新增 网站/接入 + 变更主体相关， 锁定主体变更状态
                if (
                    (params.Type === 2 || params.Type === 3) &&
                    params.NeedModifyIcp
                ) {
                    await ICP.update(
                        { Status: 8 },
                        {
                            where: {
                                OrganizerLicenseId: params.OrganizerLicenseId,
                                Status: {
                                    [Op.ne]: 1,
                                },
                            },
                        }
                    )
                }
                // 如果是注销主体  主体相关

                if (
                    params.Type === 4 || // 注销主体
                    params.Type === 7 || // 变更备案信息
                    params.Type === 8 // 变更主体
                ) {
                    await ICP.update(
                        { Status: params.Type },
                        {
                            where: {
                                Id: params.ICPId,
                            },
                        }
                    )
                }
                // 如果是注销主体、接入  网站相关
                if (
                    params.Type === 5 || // 注销互联网信息服务
                    params.Type === 6 || // 取消接入
                    params.Type === 7 || // 变更备案信息
                    params.Type === 9 || // 变更互联网信息服务
                    params.Type === 10 // 变更接入
                ) {
                    // 状态取值与类型一致，需要区分下
                    let ICPWebIds =
                        params.Type === 7
                            ? _.map(params.Website, 'Id')
                            : [params.ICPWebId || params.Website[0].Id] // 下线7类型
                    if (ICPWebIds.length > 0) {
                        await ICPWeb.update(
                            {
                                Status: params.Type,
                            },
                            {
                                where: {
                                    Id: {
                                        [Op.in]: ICPWebIds,
                                    },
                                },
                            }
                        )
                    }
                }
            } catch (error) {
                return self.cb(67034, {
                    Message: '锁定备案状态过程出错',
                })
            }

            // 为没有AreaId的网站添加AreaId,不在Update取旧数据的结果的原因是，Sequlize的Update retuπrning:true,只在PG上支持

            if (!params.AreaId) {
                let areaPrefix
                if (params.ICPWebNo) {
                    areaPrefix = params.ICPWebNo.charAt(0)
                } else if (params.ICPMainNo) {
                    areaPrefix = params.ICPMainNo.charAt(0)
                }
                if (areaPrefix) {
                    let areaInfo =
                        require('../../configs/common/area.json').find(
                            (area) => area.Refer === areaPrefix
                        )
                    if (areaInfo) {
                        params.AreaId = areaInfo.Id
                    }
                }
            }

            await Order.create(params)

            if (params.Status === 2) {
                // 提交到审核 预审
                InternalApiReq({
                    Action: 'CreateAutoAudit',
                    Backend: 'ICPV4',
                    __ssoUser: 'modify order first audit',
                    OrderNo: params.OrderNo,
                    AuditType: "FullAudit"
                }).catch(err => {
                    console.log('CreateICPOrder CreateAutoAudit error:' + err.message)
                })
            }
            // 已经完成备案后的操作，锁死状态
            let insertPlatInfoArr = []
            let promises = []
            // !==8 是因为变更主体 不需要网站的信息
            if (params.Website && params.Type !== 8) {
                // 确定授权码是否重复
                let AuthenticationCodeIsUniq = uniqCheck(
                    'AuthenticationCode',
                    params.Website
                )

                if (AuthenticationCodeIsUniq) {
                    // 授权码本订单内是否重复，重复则抛错
                    promises.push(
                        Promise.reject({
                            Code: 67025,
                            Message: '请确定网站中是否使用重复的授权码',
                        })
                    )
                } else {
                    // 不重复则执行
                    params.Website.forEach((website) => {
                        website.OrderNo = params.OrderNo
                        website.Phone = website.EmergencyPhone || ''

                        delete website.Status
                        delete website.Id
                        if (
                            website.AuthenticationCode !== undefined &&
                            website.AuthenticationCode !== '' &&
                            params.Status === 2
                        ) {
                            promises.push(
                                new Promise((resolve, reject) => {
                                    Authentication.findAll({
                                        where: {
                                            TargetCompanyId: params.CompanyId,
                                            Code: website.AuthenticationCode,
                                            IsBlock: 0,
                                        },
                                    }).then((codeInfo) => {
                                        codeInfo = parseJSON(codeInfo)
                                        if (codeInfo.length === 0) {
                                            return reject({
                                                Code: 67028,
                                                Message:
                                                    '网站授权，未查询到有效授权',
                                            })
                                        }
                                        website.IP = [codeInfo[0].IP]
                                        resolve(website)
                                    })
                                })
                            )
                        } else {
                            promises.push(Promise.resolve(website))
                        }
                    })
                }
                await Promise.all(promises)
                    .then((webList) => {
                        let webInsert = []
                        webList.forEach((website) => {
                            if (params.Type === 3) {
                                delete website.PreAppoval
                            }
                            webInsert.push(OrderWeb.create(website))
                        })
                        return Promise.all(webInsert)
                    })
                    .then(async (orderWebList) => {
                        orderWebList.forEach((orderWebInfo, key) => {
                            orderWebInfo = parseJSON(orderWebInfo)
                            // 如果订单类型是 APP类型 且传了AppPlatformInformationList APP平台信息列表 就在mongo中插入该数据
                            if (
                                orderWebInfo.InternetServiceType == 6 &&
                                params.Website[key].AppPlatformInformationList
                            ) {
                                // 获取到入参Website数组中
                                insertPlatInfoArr.push({
                                    OrderWebId: orderWebInfo.Id,
                                    AppPlatformInformationList:
                                        params.Website[key]
                                            .AppPlatformInformationList,
                                })
                            }
                        })
                        await OrderWebPlatInfoModel.insertMany(
                            insertPlatInfoArr
                        )
                    })
                    .catch((err) => {
                        // 如果网站创建出错 则删除网站订单信息 用户需要重新新建网站信息
                        return OrderWeb.update(
                            { IsDeleted: 1 },
                            {
                                where: {
                                    OrderNo: params.OrderNo,
                                },
                            }
                        ).then(() => {
                            return Promise.reject(
                                new Error('网站创建失败' + err.message)
                            )
                        })
                    })
            }
            await redis.del(`lock_${params.OrganizerLicenseId}`)

            return self.cb(0, {
                OrderNo: params.OrderNo,
            })
        } catch (e) {
            console.log(e)
            if (e instanceof Error) {
                return self.eh({
                    err: e,
                    code: 67061,
                })
            }
            this.eh(e)
        }
    }
}

function uniqCheck(key, params) {
    let valueMap = _.filter(params, function (o) {
        return o[key] !== undefined && o[key] !== ''
    })
    valueMap = _.map(valueMap, key)

    // 如果去重后的长度与去重前不同，则为true 存在重复
    return valueMap.length !== _.uniq(valueMap).length
}
