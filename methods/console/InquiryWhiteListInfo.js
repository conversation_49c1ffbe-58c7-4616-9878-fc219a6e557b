const Method = require('../../libs/method')
const { getTableModel, parseJSON } = require('../../fns/kits')

//查看block库，此公司ID是否被禁用
module.exports = class InquiryWhiteListInfo extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec(params = {}) {
        let self = this
        let icpDatabase = this.db.get('icp')

        const WhiteListCompanyModel = getTableModel(
            't_company_white_list',
            icpDatabase
        )
        try {
            let data = await WhiteListCompanyModel.findAll({
                where: {
                    CompanyId: params.CompanyId,
                },
            })
            data = parseJSON(data)

            if (data.length === 0) {
                // 代理
                return this.cb(0, {
                    IsMatch: false,
                })
            } else {
                return this.cb(0, {
                    IsMatch: true,
                })
            }
        } catch (e) {
            let err = new Error('查询数据库失败')
            err.code = 67039
            self.err(err)
        }
    }
}
