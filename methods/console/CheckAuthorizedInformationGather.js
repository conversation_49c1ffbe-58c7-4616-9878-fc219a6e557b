/**
 * @file API CheckAuthorizedInformationGather,检查此公司是否有历史授权记录
 * <AUTHOR>
 * @return {} 返回是否有授权
 */

const Method = require('../../libs/method')

module.exports = class CheckAuthorizedInformationGather extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({ CompanyId } = {}) {
        let self = this
        let redis = this.redis.get()

        let redisResult
        try {
            // 将配置发布到Redis
            redisResult = await redis.sismember(
                'authorized_company_list',
                CompanyId
            )

            return this.cb(0, { IsAuthorized: redisResult === 1 })
        } catch (e) {
            if (e instanceof Error) {
                return self.eh({
                    err: e,
                    code: 67011,
                })
            }
            self.eh(e)
        }
    }
}
