const Method = require('../../libs/method')
const moment = require('moment')
// const Sequelize = require('sequelize');
// const SequelizeInstance = require('../../config/db'); // sequelize的实例
// const User = require('../../models/user')(SequelizeInstance, Sequelize)

// 增加与傲盾的同步解封
// 为兼容前端，不改传参
module.exports = class DBCheck extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({ Time } = {}) {
        let icpDatabase = this.db.get('icp')
        const ICP = require('../../models/t_icp')(icpDatabase)

        try {
            var icpRecord = await ICP.findAll({
                where: {
                    id: 12,
                },
            })
            return this.cb(0, { icpRecord })
        } catch (e) {
            console.log(e)
            this.eh(e)
        }
    }
}

/*
 *	Mongo数据库查询
 *
 * @params params   syslog, StartTime, EndTime, Region
 *
 */
function mongoHeathCheck(areaCollection) {
    return new Promise((resolve, reject) => {
        areaCollection
            .find({})
            .setReadPreference('secondaryPreferred')
            .count(function (err, docs) {
                err ? reject(err) : resolve(docs)
            })
    })
}
