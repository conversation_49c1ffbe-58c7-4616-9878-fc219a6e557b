const Method = require('../../libs/method')
const { getTableModel, parseJSON } = require('../../fns/kits')

module.exports = class DescribeWeb extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec(params = {}) {
        let self = this
        try {
            const icpDatabase = this.db.get('icp')
            const ICPWebModel = getTableModel('t_web', icpDatabase)

            const results = await ICPWebModel.findAndCountAll({
                where: {
                    MainId: params.IcpId,
                    Id: params.Id,
                },
            })

            let { count, rows } = parseJSON(results)

            if (count === 0) {
                return this.cb(0, {
                    Web: [],
                    Count: 0,
                })
            }
            rows = rows.map((row) => {
                row.BaseManageprovince =
                    row.BaseManageprovince === null
                        ? '5'
                        : row.BaseManageprovince
                row.ConnectType = row.ConnectType === null ? 5 : row.ConnectType
                return row
            })

            return this.cb(0, {
                Web: rows,
                Count: count,
            })
        } catch (e) {
            let err = new Error(e.message)
            err.code = 67009
            self.err(err)
        }
    }
}
