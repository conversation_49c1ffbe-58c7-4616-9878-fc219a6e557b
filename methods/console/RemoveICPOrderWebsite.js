const Method = require('../../libs/method')
const { getTableModel, parseJSON } = require('../../fns/kits')
const _ = require('lodash')
const { Error: SequelizeError } = require('sequelize')

/**
 * 客户删除备案中的网站
 * 新增主体时，删除网站
 * 若订单已经提交到审核，并且该网站若有被打回的错误的信息，
 * 则需要把订单中 主体信息中记录的网站的报错删除。
 * 若没有就算了
 */
module.exports = class RemoveICPOrderWebsite extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec(params = {}) {
        let self = this
        let icpDatabase = this.db.get('icp')
        const OrderModel = getTableModel('t_order', icpDatabase)
        const OrderWebModel = getTableModel('t_order_web', icpDatabase)

        try {
            let [orders, orderWebs] = await Promise.all([
                OrderModel.findAll({
                    where: {
                        CompanyId: params.CompanyId,
                        OrderNo: params.OrderNo,
                    },
                }),
                OrderWebModel.findAll({
                    where: {
                        OrderNo: params.OrderNo,
                        IsDeleted: 0,
                    },
                }),
            ])
            orders = parseJSON(orders)
            orderWebs = parseJSON(orderWebs)
            if (orders.length === 0) {
                let error = new Error(
                    `没有找到该订单,OrderNo: ${params.OrderNo}`
                )
                error.code = 67007
                throw error
            }

            let order = orders[0]
            if (_.indexOf([1, 4, 9, 13, 15], order.Status) === -1) {
                let error = new Error('该状态的订单 不允许删除')
                error.code = 67015
                throw error
            }

            if (order.Type === 7) {
                let error = new Error('变更类订单不能删除网站')
                error.code = 67014
                throw error
            }

            if (orderWebs.length <= 1) {
                let error = new Error('订单下只有小于一个网站，不允许删除')
                error.code = 67015
                throw error
            }
            // 更新 t_order 和 t_order_web 的订单状态为 删除
            let promiseArray = []
            promiseArray.push(
                OrderWebModel.destroy({
                    where: {
                        OrderNo: order.OrderNo,
                        Id: params.WebsiteId,
                    },
                })
            )
            //查看订单的出错日志中是否有该网站相关的信息，若有 则删除该信息
            if (
                order.Error &&
                order.Error.Website &&
                order.Error.Website[params.WebsiteId.toString()]
            ) {
                let errorInfo = order.Error
                delete errorInfo.Website[params.WebsiteId.toString()]
                if (JSON.stringify(errorInfo.Website) === '{}') {
                    delete errorInfo.Website
                }

                promiseArray.push(
                    OrderModel.update(
                        { Error: errorInfo },
                        {
                            where: { OrderNo: params.OrderNo },
                        }
                    )
                )
            }

            await Promise.all(promiseArray)

            return this.cb(0)
        } catch (e) {
            if (e instanceof SequelizeError) {
                // 数据库报错
                let err = new Error(`服务器内部错误,${e}`)
                err.code = 67038
                self.err(err)
            } else {
                self.err(e)
            }
        }
    }
}
