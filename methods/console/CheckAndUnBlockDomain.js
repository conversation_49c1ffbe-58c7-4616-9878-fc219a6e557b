/**
 * 提供回调接口，供备案完成的域名调用，我司核查在管局的备案情况与违规情况，符合情况做解封
 */

const Method = require('../../libs/method')

const GetDomainPublicICP = require('../common/GetDomainPublicICP')
const ucloudApiPromise = require('../../fns/ucloudApiPromise')

module.exports = class CheckAndUnBlockDomain extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec(params = {}) {
        let self = this
        let redis = self.redis.get()

        let companyId = params.CompanyId,
            domain = params.Domain
        console.log(companyId)
        console.log(domain)

        // 检查是否为允许的公司
        const allowedCompanyIds = [
            55969781, 66119258, 56012604, 10413, 66134896, 37479, 55904570,
            50901521,
        ]
        if (!allowedCompanyIds.includes(companyId)) {
            return self.eh({
                err: new Error('CompanyId is not allowed.'),
                code: 67090,
            })
        }

        // 检查在工信部的备案情况；增加缓存domain_icp_in_other_map
        try {
            let icpRes
            let method = new GetDomainPublicICP((RetCode, data) => {
                icpRes = {
                    RetCode,
                    ...data,
                }
            })
            await method.exec({ Domain: domain })

            if (icpRes.RetCode === 0 && icpRes.ICPInfos?.length > 0) {
                try {
                    let redisInsertResult = {}
                    redisInsertResult[domain] = icpRes.ICPInfos[0]?.webSiteNum
                    let cacheXinNetDomain = await redis.hmset(
                        'domain_icp_in_other_map',
                        redisInsertResult
                    )
                    console.log(
                        domain +
                            ' 缓存于 domain_icp_in_other_map: ' +
                            cacheXinNetDomain
                    )
                } catch (err) {
                    console.log(domain + ' 缓存于 domain_icp_in_other_map 报错')
                    console.log(err)
                }
            } else {
                return self.eh({
                    err: new Error('ICP Info is not existed.'), //查询工信部，备案信息不存在
                    code: 67091,
                })
            }
        } catch (err) {
            return self.eh({
                err: new Error('Query ICP Info Err.'), //查询工信部，接口报错
                code: 67092,
            })
        }

        // 检查是否因存在违规内容
        try {
            let { RetCode, CanUnBlock } = await ucloudApiPromise({
                options: {
                    category: 'HEGUI',
                    action: 'GetDominHaveIllegalAndRegisteredRecordFromNotify',
                },
                data: {
                    Domain: domain,
                    Source: 'newicp',
                },
            })
            if (RetCode !== 0) {
                console.log('Query illegal info Err: ' + RetCode)
                return self.eh({
                    err: new Error('Query illegal info Err.'), //查询违规内容，接口报错
                    code: 67094,
                })
            }
            if (!CanUnBlock) {
                return self.eh({
                    err: new Error('Domain exists illegal record.'), //经查询，存在违规内容
                    code: 67093,
                })
            }
        } catch (e) {
            return self.eh({
                err: new Error('Query illegal info Err.'), //查询违规内容，接口报错
                code: 67094,
            })
        }

        // 检查是否存在拦截（奥顿系统封堵）
        try {
            let { RetCode, IsBlock } = await ucloudApiPromise({
                options: {
                    category: 'HEGUI',
                    action: 'GetDominBlockInfoInAudun',
                },
                data: {
                    Domain: domain,
                    Source: 'newicp',
                },
            })
            if (RetCode !== 0) {
                console.log('Query domain if blocked info Err: ' + RetCode)
                return self.eh({
                    err: new Error('Query domain if blocked info Err.'), //查询是否拦截，接口报错
                    code: 67096,
                })
            }
            if (!IsBlock) {
                return self.eh({
                    err: new Error('Domain is not blocked'), //经查询，没被拦截
                    code: 67095,
                })
            }
        } catch (e) {
            return self.eh({
                err: new Error('Query domain if blocked info Err.'), //查询是否拦截，接口报错
                code: 67096,
            })
        }

        // 执行解封
        try {
            let { RetCode } = await ucloudApiPromise({
                options: {
                    category: 'HEGUI',
                    action: 'UnBlockRecordDomain',
                },
                data: {
                    Domain: domain,
                    Source: 'newicp',
                },
            })
            if (RetCode !== 0) {
                console.log('Unblock domain Err: ' + RetCode)
                return self.eh({
                    err: new Error('Unblock domain Err.'), //执行解封接口报错
                    code: 67096,
                })
            }
        } catch (e) {
            return self.eh({
                err: new Error('Unblock domain Err.'), //执行解封接口报错
                code: 67096,
            })
        }

        // 解封后再次查询是否存在拦截
        try {
            let { RetCode, IsBlock } = await ucloudApiPromise({
                options: {
                    category: 'HEGUI',
                    action: 'GetDominBlockInfoInAudun',
                },
                data: {
                    Domain: domain,
                    Source: 'newicp',
                },
            })
            if (RetCode !== 0) {
                console.log('Query domain if blocked info Err: ' + RetCode)
                return self.eh({
                    err: new Error('Query domain if blocked info Err.'), //查询是否拦截，接口报错
                    code: 67096,
                })
            }
            if (IsBlock) {
                return self.eh({
                    err: new Error('Domain is still blocked.'), //经查询，解封后仍被拦截
                    code: 67097,
                })
            }
            if (!IsBlock) {
                //解封成功，返回成功
                return this.cb(0, {
                    Domain: domain,
                    IsBlock,
                })
            }
        } catch (e) {
            return self.eh({
                err: new Error('Query domain if blocked info Err.'), //查询是否拦截，接口报错
                code: 67096,
            })
        }
    }
}
