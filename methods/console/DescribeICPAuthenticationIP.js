const Method = require('../../libs/method')
const { getTableModel, parseJSON } = require('../../fns/kits')
const { Error: SequelizeError } = require('sequelize')

// 获取公司下所有的IP 以及IP对应的授权码信息
module.exports = class DescribeICPAuthenticationIP extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec(params = {}) {
        let self = this
        let icpDatabase = this.db.get('icp')

        const AuthCodeModel = getTableModel(
            't_authentication_code',
            icpDatabase
        )

        try {
            let codeList = await AuthCodeModel.findAll({
                where: {
                    CompanyId: params.CompanyId,
                },
            })

            let AuthenticationList = {}
            codeList = parseJSON(codeList)
            codeList.forEach((element) => {
                if (AuthenticationList[element.IP]) {
                    // 如果列表中有原IP，则把新授权码推入数组
                    AuthenticationList[element.IP].push(element.Code)
                } else {
                    // 如果没有，新建
                    AuthenticationList[element.IP] = [element.Code]
                }
            })

            let IPList = Object.keys(AuthenticationList)

            return this.cb(0, {
                IPList,
                AuthenticationList,
            })
        } catch (e) {
            if (e instanceof SequelizeError) {
                let err = new Error(e.message)
                err.code = 67068
                self.err(err)
            } else {
                let err = new Error(e.message)
                err.code = 67039
                self.err(err)
            }
        }
    }
}
