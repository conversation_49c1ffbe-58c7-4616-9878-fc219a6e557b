const Method = require('../../libs/method')
const verify_type = require('../../configs/common/verify_api_type.json')
const {
    insertLog,
    getVerifyLog,
    updateLog,
    ffmpegParseVideo,
    getUrl,
} = require('../../fns/verifyFun')
const { getTableModel } = require('../../fns/kits')
const { parseURI } = require('uri-parse-lib')
const _ = require('lodash')
const producer = require('../../libs/producer')
module.exports = class CheckVideoAndGetBestPicture extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec({
        VideoURL,
        OrderNo,
        CompanyId,
        ForceSelect = 1,
        UUID,
        VideoName,
        Token,
        LicenseId,
    } = {}) {
        let Operator, logId, result, multiplex, BestPictureName
        // 字段是否复用
        result = {}
        multiplex = 1
        let self = this

        let icpDatabase = this.db.get('icp')
        let LogModel = getTableModel('t_verify_log', icpDatabase)
        let PictureModel = getTableModel('t_picture', icpDatabase)

        if (VideoURL) {
            VideoName = parseURI(VideoURL)
            VideoName = VideoName.replace('/')
        } else {
            VideoURL = getUrl(undefined, VideoName)
        }

        try {
            let params = {
                VideoName: VideoName,
                CompanyId: CompanyId,
                // Backend: 'IdAuth',
                // Action: 'GetIdenityOCRInfo',
                // Source: 'ICP',
                LicenseId,
                OrderNo,
                Operator,
            }

            if (UUID) {
                params.UUID = UUID
            }
            params['Type'] = verify_type.CheckVideoAndGetBestPicture

            // 执行日志
            logId = await insertLog(params, LogModel)

            // 如果没有强制验证，则查询旧记录
            try {
                if (!ForceSelect) {
                    // 验证是否此参数是否验证过,此处实际不需要，URL会更新

                    result = await getVerifyLog(
                        {
                            VideoName: params.VideoName,
                            Type: verify_type.CheckVideoAndGetBestPicture,
                        },
                        logId,
                        LogModel
                    )
                    console.log(result, 3113)
                }

                // 如果未取到，或者之前的请求为出错，重试
                if (
                    result === null ||
                    JSON.stringify(result) === '{}' ||
                    result.Error === true ||
                    JSON.stringify(result.Response) === '{}'
                ) {
                    // params.IdPhoto = getUrl(Url, PictureName)
                    ;[result, BestPictureName] = await ffmpegParseVideo(
                        VideoURL
                    )
                    BestPictureName = BestPictureName[0]
                    result.Response.BestPictureName = BestPictureName
                    // 插入最佳成像到数据库
                    await PictureModel.create({
                        CompanyId,
                        Url: BestPictureName,
                    })
                    multiplex = 0
                } else {
                    BestPictureName = result.Response.BestPictureName
                }
            } catch (error) {
                console.log(error)
                await Promise.reject(new self.Err(error, 31001))
            }

            // 更新结果到数据库
            result.Multiplex = multiplex

            let returnObject = {
                IsMatch: true,
                Multiplex: multiplex,
                BestPictureName: BestPictureName,
            }

            let returnCode = 0
            // 结果匹配

            _.extend(returnObject, result.Response)
            if (
                result.Error ||
                JSON.stringify(result.Response) === '{}' ||
                result.Response.error_message
            ) {
                returnCode = 31003
                returnObject.Message =
                    result.Response.error_message || '请求出错'
                returnObject.IsMatch = false
            } else {
                // 判断是否为同一人
                try {
                    ;['1', '2', '3'].forEach((element) => {
                        console.log(result.Response, element)

                        if (
                            result.Response['result_ref' + element].confidence <
                            75
                        ) {
                            returnObject.IsMatch = false
                            returnObject.Message =
                                '人脸获取异常，请保持脸部无遮挡，并保持同一人完成核验。'
                            delete result.Response.BestPictureName
                        }
                    })
                } catch (error) {
                    console.log(error)
                    returnObject.IsMatch = false
                    returnObject.Message = '视频中检查出错'
                }
            }

            if (returnObject.Message) {
                delete result.Response.BestPictureName
                delete returnObject.BestPictureName
            }

            await updateLog(
                {
                    ReturnObject: returnObject,
                    Response: result.Response,
                    Multiplex: multiplex,
                },
                logId,
                LogModel
            )

            if (returnCode === 0) {
                const data = {
                    CompanyId,
                    UUID,
                    Token,
                    LicenseId,
                    OrderNo,
                    VideoName,
                    Picture: returnObject.BestPictureName,
                }
                // 检查其它API是否执行成功，成功后，执行后面异步操作
                producer.send({
                    type: 'icp',
                    topic: 'CheckOtherAPIStatus',
                    data: data,
                })
            }

            this.cb(returnCode, returnObject)
        } catch (e) {
            console.log(e)
            if (e instanceof Error) {
                return self.eh({
                    err: e,
                    code: 31002,
                })
            }
            self.eh(e)
        }
    }
}
