/*
 * @Author: william.qian <EMAIL>
 * @Date: 2022-02-22 15:59:34
 * @LastEditors: william.qian <EMAIL>
 * @LastEditTime: 2023-10-07 18:19:27
 * @FilePath: /newicp/methods/console/ExtractICPType.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
const Method = require('../../libs/method')
const _ = require('lodash')
const henganApi = require('../../libs/henganApiPromise')
const { getTableModel } = require('../../fns/kits')
const { ICPModel, ICPWebModel, LogModel } = require('../../models')
const GetDomainPublicICP = require('../../methods/common/GetDomainPublicICP')

var request = require('request')
const {
    getMsgFromWebService,
    getIcpAndWebMsgService,
} = require('../../fns/getMsgFromTabelService')
const { Op } = require('sequelize')

// 通过输入信息，给出备案类型的建议
module.exports = class ExtractICPType extends Method {
    constructor(cb) {
        super(cb)
    }
    async exec(params = {}) {
        let self = this
        // 变更类可能会引入完成的ID,删除

        try {
            // 调恒安接口，是否在我司有备案

            let icpInfoInUcloud = henganApi('SelectICPInterface', {
                KeyWordType: 3,
                CertificateType: parseInt(params.OrganizerLicenseType),
                Keyword: params.OrganizerLicenseId,
            }).catch((err) => {
                // 查询我司数据库
                return getICPInfoByDomain({
                    OrganizerLicenseId: params.OrganizerLicenseId,
                })
            })
            let icpInfoInOther
            // 备案不在我们这
            let method = new GetDomainPublicICP((RetCode, data) => {
                icpInfoInOther = {
                    ...data,
                    RetCode,
                }
                if (RetCode === 0) {
                    icpInfoInOther = data
                }
            })
            let icpInfoExec = method.exec({
                OrganizerLicenseType: params.OrganizerLicenseType,
                OrganizerLicenseId: params.OrganizerLicenseId,
            })

            // 查傲顿备案服务库
            let webInfoInUcloud = henganApi('SelectICPInterface', {
                KeyWordType: 4,
                Keyword: params.Domain,
            }).catch((err) => {
                // 查询我司数据库
                return getICPInfoByDomain({
                    Domain: params.Domain,
                })
            })

            // 网站是否备案
            let webInfoInOther
            let method1 = new GetDomainPublicICP((RetCode, data) => {
                webInfoInOther = { ...data, RetCode }
            })
            let webInfoExec = method1.exec({
                Domain: params.Domain,
            })

            let pList = [
                icpInfoInUcloud,
                webInfoInUcloud,
                icpInfoExec,
                webInfoExec,
            ]

            let pData = await Promise.all(pList)

            icpInfoInUcloud = pData[0]
            webInfoInUcloud = pData[1]

            if (
                _.findIndex(
                    [
                        icpInfoInUcloud,
                        webInfoInUcloud,
                        icpInfoInOther,
                        webInfoInOther,
                    ],
                    function (o) {
                        return o.RetCode !== 0
                    }
                ) !== -1
            ) {
                // 验证接口返回码
                return this.cb(67027, {
                    Message: '验证备案过程出错',
                })
            }
            // 由于傲顿未区分备案 所以要过滤APP的
            let ICPINUCloud = icpInfoInUcloud.ICPInfos.length !== 0
            let ICPINOther = icpInfoInOther.ICPInfos.length !== 0
            let WEBINUCloud =
                webInfoInUcloud.ICPInfos.length !== 0 &&
                webInfoInUcloud.ICPInfos[0].websiteList?.filter(
                    (web) =>
                        web.websiteserviceTypes != 6 &&
                        web.domainList.findIndex(
                            (v) => v.topdomain == params.Domain
                        ) !== -1
                )?.length > 0

            let WEBINOther = webInfoInOther.ICPInfos.length !== 0

            let determine = [
                icpInfoInUcloud.ICPInfos.length,
                icpInfoInOther.ICPInfos.length,
                webInfoInUcloud.ICPInfos.length &&
                webInfoInUcloud.ICPInfos[0].websiteList?.filter(
                    (web) =>
                        web.websiteserviceTypes != 6 &&
                        web.domainList.findIndex(
                            (v) => v.topdomain == params.Domain
                        ) !== -1
                )?.length > 0
                    ? 1
                    : 0,
                webInfoInOther.ICPInfos.length,
            ]

            //2否否否否
            if (!ICPINUCloud && !WEBINUCloud && !ICPINOther && !WEBINOther) {
                return this.cb(0, {
                    Determine: determine,
                    Reason: 1,
                    Type: [1],
                    Info: {},
                })
            }

            // 有有否否
            if (ICPINUCloud && ICPINOther && !WEBINUCloud && !WEBINOther) {
                //  有主体新增网站，确定记录是否在当前公司下
                // let isCompanyMatch = await ICPModel.count({ CompanyId: params.CompanyId, OrganizerLicenseId: params.OrganizerLicenseId, fuzzy: 'status !=1' })
                let isCompanyMatch = await ICPModel.count({
                    where: {
                        CompanyId: params.CompanyId,
                        OrganizerLicenseId: params.OrganizerLicenseId,
                        Status: {
                            [Op.ne]: 1,
                        },
                    },
                })
                if (isCompanyMatch !== 0) {
                    return this.cb(0, {
                        Type: [2],
                        Reason: 2,
                        Determine: determine,

                        Info: {
                            IcpMainNo: icpInfoInUcloud.ICPInfos[0]['phylicnum'],
                        },
                    })
                } else {
                    return this.cb(0, {
                        Determine: determine,
                        Tip: '该备案主体在我司系统已存在，请在对应账号已备案完成列表处进行操作！',
                    })
                }
            }

            //3否有否否 无主体新增网站
            if (!ICPINUCloud && ICPINOther && !WEBINUCloud && !WEBINOther) {
                return this.cb(0, {
                    Type: [2],
                    Reason: 3,
                    Determine: determine,
                    Info: {
                        IcpMainNo: icpInfoInOther.ICPInfos[0]['mainRecordNum'],
                    },
                })
            }

            //5有有否有
            if (ICPINUCloud && ICPINOther && !WEBINUCloud && WEBINOther) {
                //  有主体新增接入，确定记录是否在当前公司下

                if (
                    icpInfoInUcloud.ICPInfos[0].phylicnum ===
                    webInfoInOther.ICPInfos[0].mainRecordNum
                ) {
                    // let isCompanyMatch = await ICPModel.count({ CompanyId: params.CompanyId, OrganizerLicenseId: params.OrganizerLicenseId, fuzzy: 'status !=1' })
                    let isCompanyMatch = await ICPModel.count({
                        where: {
                            CompanyId: params.CompanyId,
                            OrganizerLicenseId: params.OrganizerLicenseId,
                            Status: {
                                [Op.ne]: 1,
                            },
                        },
                    })

                    if (isCompanyMatch !== 0) {
                        return this.cb(0, {
                            Type: [3],
                            Determine: determine,
                            Reason: 4,
                            Info: {
                                ICPInUCloud: true,
                                IcpMainNo:
                                    icpInfoInOther.ICPInfos[0]['mainRecordNum'],
                                IcpWebNo:
                                    webInfoInOther.ICPInfos[0]['webSiteNum'] ||
                                    '',
                                WebName:
                                    webInfoInOther.ICPInfos[0]['webSiteName'] ||
                                    '',
                            },
                        })
                    } else {
                        return this.cb(0, {
                            Reason: 5,
                            Determine: determine,
                            Tip: '该备案信息在系统中已存在，请在对应账号已备案完成列表处进行操作！',
                        })
                    }
                } else {
                    return this.cb(0, {
                        Determine: determine,
                        Reason: 11,
                        Tip: '主体与网站的备案号不一致，无法进行操作，请重新输入！',
                    })
                }
            }
            // mainRecordNum
            //6否有否有 无主体新增接入  b=d
            if (
                !ICPINUCloud &&
                ICPINOther &&
                !WEBINUCloud &&
                WEBINOther &&
                icpInfoInOther.ICPInfos[0].mainRecordNum ===
                    webInfoInOther.ICPInfos[0].mainRecordNum
            ) {
                return this.cb(0, {
                    Type: [3],
                    Determine: determine,
                    Reason: 5,
                    Info: {
                        ICPInUCloud: false,
                        IcpMainNo: icpInfoInOther.ICPInfos[0]['mainRecordNum'],
                        IcpWebNo:
                            webInfoInOther.ICPInfos[0]['webSiteNum'] || '',
                        WebName:
                            webInfoInOther.ICPInfos[0]['webSiteName'] || '',
                    },
                })
            }

            // 7 8  10 11有有有有
            if (ICPINUCloud && ICPINOther && WEBINUCloud && WEBINOther) {
                if (
                    icpInfoInUcloud.ICPInfos[0].phylicnum !==
                        icpInfoInOther.ICPInfos[0]['mainRecordNum'] ||
                    webInfoInUcloud.ICPInfos[0].phylicnum !==
                        icpInfoInOther.ICPInfos[0].mainRecordNum
                ) {
                    // 11 10
                    return this.cb(0, {
                        Reason: 9,
                        Determine: determine,
                        Tip: '主体与网站的备案号不一致，无法进行操作，请重新输入！',
                    })
                } else {
                    // 7\8
                    // 确定情况7还是8
                    // let isCompanyMatch = await ICPModel.count({ CompanyId: params.CompanyId, OrganizerLicenseId: params.OrganizerLicenseId, fuzzy: 'status !=1' })
                    let isCompanyMatch = await ICPModel.count({
                        where: {
                            CompanyId: params.CompanyId,
                            OrganizerLicenseId: params.OrganizerLicenseId,
                            Status: {
                                [Op.ne]: 1,
                            },
                        },
                    })

                    if (isCompanyMatch !== 0) {
                        return this.cb(0, {
                            Determine: determine,
                            Reason: 6,
                            Tip: '该备案信息在系统中已存在，请在已备案完成列表处进行操作！',
                        })
                    } else {
                        return this.cb(0, {
                            Determine: determine,
                            Reason: 7,

                            Tip: '该备案主体或备案域名已在我司系统其他账号中存在，请登录对应账号进行操作！',
                        })
                    }
                }
            }

            // 9否否否有
            if (!ICPINUCloud && !ICPINOther && !WEBINUCloud && WEBINOther) {
                return this.cb(0, {
                    Determine: determine,
                    Reason: 8,
                    Tip: '证件号无备案，域名有备案，无法进行备案！ 若您确定主体名下存在备案，请点击【确定】，证件类型与证件号码请按照三证合一前，原备案接入商登记的证件类型和证件号码进行填写。',
                })
            }

            // 13否有否有
            if (!ICPINUCloud && ICPINOther && !WEBINUCloud && WEBINOther) {
                return this.cb(0, {
                    Reason: 12,
                    Determine: determine,
                    Tip: '主体与网站的备案号不一致，无法进行操作，请重新输入！',
                })
            }

            // 14否否有有
            if (!ICPINUCloud && !ICPINOther && WEBINUCloud && WEBINOther) {
                return this.cb(0, {
                    Determine: determine,
                    Reason: 13,
                    Tip: '证件号无备案，域名有备案，无法进行操作，请重新输入！',
                })
            }

            // 15否有有有
            if (!ICPINUCloud && ICPINOther && WEBINUCloud && WEBINOther) {
                return this.cb(0, {
                    Reason: 13,
                    Determine: determine,
                    Tip: '主体与网站的备案号不一致，无法进行操作，请重新输入！',
                })
            }

            return this.cb(0, {
                Type: [1],
                Determine: determine,

                Info: {},
            })
        } catch (e) {
            console.log(e, 1314)
            if (e instanceof Error) {
                return self.eh({
                    err: e,
                    code: 67061,
                })
            }
            this.eh(e)
        }
    }
}

/**
 * @description: 传入证域名，得到备案信息
 * @param {*} Domain
 * @return {*}
 */
async function getICPInfoByDomain({
    Domain,
    OrganizerLicenseId,
    OrganizerLicenseType,
}) {
    let mainIds = []
    let generalJson = {
        Status: { [Op.ne]: 1 },
    }
    if (Domain) {
        mainIds = await getMsgFromWebService('console', ICPWebModel, { Domain })
        if (mainIds.length === 0) {
            return Promise.resolve(noICPInUCloud)
        } else {
            generalJson.Id = { [Op.in]: mainIds }
        }
    }
    if (OrganizerLicenseId) {
        generalJson.OrganizerLicenseId = OrganizerLicenseId
    }

    let getIcpAndWebMsg = await getIcpAndWebMsgService(
        'console',
        ICPModel,
        ICPWebModel,
        generalJson,
        0,
        10,
        LogModel
    )

    if (getIcpAndWebMsg.TotalCount === 0) {
        return Promise.resolve(noICPInUCloud)
    }

    // 处理结构

    return Promise.resolve({
        ICPInfos: [
            {
                phylicnum: getIcpAndWebMsg.ICP[0].ICPMainNo,
            },
        ],
        RetCode: 0,
    })
}

const noICPInUCloud = {
    ICPInfos: [],
    Message: '无对应的ICP备案信息',
    RetCode: 0,
}
