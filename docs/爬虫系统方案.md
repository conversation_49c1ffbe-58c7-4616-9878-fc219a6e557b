# 爬虫系统建设与计费方案

## 名词解释

- **目标网站**：需要扫描网站的初始网址
- **遍历深度**：既请求到目标网址后，仅扫描主页为深度1，把主页的中的链接记录后继续扫描为深度2。以此类推，深度越高时间与计算成功均会指数增长
- **单网站网页总数**：大型网站，深度近乎无限，可指定单个网站的扫描网页总数。超过总数后，即使是未到指定深度也需要停止
- **关键词**：既将扫描得到的网页内容与指定的关键词匹配。
- **关键词关系**：支持单个关键词，网页中有关键词，视为符合特征,同时也支持“与”，“非”关系。
  - “与”关系：当单个网页中，A关键词与B关键词同时出现时，视为符合特征
  - “非”关系：当单个网页中，A关键词出现，视为符合特征，但网页中有B关键词则此网页不符合特征

## 功能设计

1. **网页抓取**：爬虫系统能够根据预设的规则，自动访问指定的目标网址，并下载网页的内容。它可以处理不同类型的网页，包括静态网页、动态网页和JavaScript生成的网页，同时根据指定的遍历深度，对网站做深度扫描。
2. **数据提取**：爬虫系统能够从下载的网页中提取所需的数据。它可以使用正则表达式、XPath、CSS选择器等技术来定位和提取数据。用户可以根据自己的需求定义数据提取规则（如是业务分析与内容安全，此仅需要提取文本）。
3. **数据清洗**：爬虫系统能够对提取的数据进行清洗和处理。它可以去除HTML标签、过滤无用的字符、转换数据格式等。用户可以根据自己的需求定义数据清洗规则。
4. **反爬虫处理**：爬虫系统能够应对网站的反爬虫机制。它可以模拟人类用户的行为，如随机访问间隔、模拟转跳、浏览器模拟，以降低被网站封禁的风险。
5. **特征扫描**：根据预先提供的关键词，扫描网页是否符合相关的业务特征，同时关键词支持复杂的特征分析。
6. **用户界面**：爬虫系统提供用户界面，方便用户配置和管理爬虫任务。用户可以通过界面来添加、编辑和删除任务，查看任务的执行状态和结果。

## 技术指标

- 目标网址在1万个，遍历深度为1,关键词100个，扫描速度为2小时
- 目标网址在1万个，遍历深度为2,单网站网页总数10,关键词100个，扫描速度为10小时

## 计费方案

### 任务形式

- 需求方提供目标网站、关键词、遍历深度、单网站网页总数
- 300个关键词内，最终按扫描网页总数计费，单个网页的扫描成本为0.7元
- 关键词增加不做费用增加，但会影响扫描速度
- 最终我方交付符合特征的扫描结果

### 项目建设形式

- 我方交付系统，包含独立用户界面与后端爬虫系统。用户可上传扫描网站列表与关键词列表。扫描完成可展示结果
- 初期建设费用为20万元，每年2万元维护费（维护费包含扫描系统的云主机成本）
