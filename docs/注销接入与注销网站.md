# 注销网站 / 取消接入

---

注销网站 / 取消接入 的前提是需要已完成备案

因此需要先使用DescribeICPMain取到已备案的数据。
若 DescribeICPMain 对应Website下 
"CancelConnect": true, // 则该网站可以 注销接入
"DeleteWeb": true, // 则该网站可以 注销网站
使用CreateICPOrder接口创建一个订单类型为5/6的注销订单。

----------

请求结构

字段 | 类型 | 必填 | 描述
---- | ---- | ---- | ----
Action | string | Yes | CreateICPOrder
ICPWebNo| string | Yes | 网站备案号
ICPMainNo| string | Yes | 主体备案号
OrganizerName| string | Yes | 主办单位名称
ICPWebId| string | Yes | DescribeICPMain中主体内，Website.Id
CWebsiteId| string | Yes | DescribeICPMain中主体内，Website.CWebsiteId
Type| string | Yes | 注销接入时为6,注销网站时为5
Status| string | Yes | 状态，固定为8
ReasonId| string | Yes | 原因，固定为1,
Domain| array | Yes | 域名信息
Details| array | Yes | 详情

-----

# 注销网站 / 取消接入 CreateICPOrder

请求结构
```
{
    "ICPWebNo": "苏ICP备xxxxxxx号-1",//取网站备案号
    "ICPMainNo": "苏ICP备xxxxxxx号",//取主体备案号
    "OrganizerName": "钱xx烨", //取主办单位名称
    "Domain.0.Domain": "qiaxnye.com",取值为Website.Domain
    "Domain.0.IsMain": 1,
    "Details.0.WebName":"", //DescribeICPMain中主体内，Website.Name
    "Details.0.Domain.0.CerificationPicture.0": "a22792e7f73250169e713dc86725xxxxxxxxxx.jpg",
    "Details.0.Domain.0.Domain": "qiaxnye.com", //Details中Domain取值为Website.Domain
    "Details.0.Domain.0.IsMain": 1,
    "Details.0.Domain.0.key": "main_domain_34",
    "ICPWebId": 000000,//DescribeICPMain中主体内，Website.Id
    "CWebsiteId": 1111111, //DescribeICPMain中主体内，Website.CWebsiteId
    "Type": 5,//注销接入时为6,注销网站时为5
    "Status": 8,//固定为8
    "ReasonId": 1,//固定为1
    "Action": "CreateICPOrder"
}

```

返回结构

```
{
   "Action": "CreateICPOrderResponse",
   "OrderNo": "O20220623105454009585",
   "RetCode": 0
}

```
