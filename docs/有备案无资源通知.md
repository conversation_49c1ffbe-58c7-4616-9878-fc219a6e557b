
# 有备案无资源通知

----------
### 上传批次    UploadUnresourceBatch

请求结构

字段 | 类型 | 必填 | 描述
---- | ---- | ---- | ----
Action | string | Yes | UploadUnresourceBatch
File   | string | Yes | 上传文件的base64编码，文件内容只包含一列，网站备案号
FileName | string | Yes | 上传的文件名

返回结构
| 名称    | 类型   | 必返回 | 描述     |
| ------- | ------ | ------ | -------- |
| RetCode | int    | Yes    | 返回码   |
| Action  | string | Yes    | UploadUnresourceBatchResponse   |


例如：
```javascript

{
    "RetCode": 0,
    "Action": "UploadUnresourceBatchResponse",
}

```


----------
### 生成批次    GenerateUnresourceBatch

请求结构

字段 | 类型 | 必填 | 描述
---- | ---- | ---- | ----
Action | string | Yes | GenerateBatch
CompanyId| int | No | 公司Id

返回结构
| 名称    | 类型   | 必返回 | 描述     |
| ------- | ------ | ------ | -------- |
| RetCode | int    | Yes    | 返回码   |
| Action  | string | Yes    | GenerateBatchResponse   |


例如：
```javascript

{
    "RetCode": 0,
    "Action": "GenerateBatchResponse",
}

```


----------
### 获取有备案无资源批次列表 GetUnResourcesBatchList

请求结构

字段 | 类型 | 必填 | 描述
---- | ---- | ---- | ----
Action | string | Yes | GetUnResourcesBatchList
CompanyId| int | No | 公司Id
BatchId | int | No | 批次Id
Domain | string | No | 域名
StartTime | int | No | 开始时间
EndTime | int | No | 结束时间
Offset | int | Yes | 偏移量
Limit | int | Yes | 限制数量

返回结构
| 名称    | 类型   | 必返回 | 描述     |
| ------- | ------ | ------ | -------- |
| RetCode | int    | Yes    | 返回码   |
| Action  | string | Yes    | Action   |
| BatchList | object [] | Yes | 批次内容列表 |
| TotalCount | int | Yes    | 批次总数 |


例如：
```javascript

{
    "RetCode": 0,
    "Action": "GetUnResourcesBatchListResponse",
    "BatchList": [
        {
            Id: 21321, //批次id
            Remark: 取消接入2022-3-7, //批次备注
            Status: 1, //批次状态, 0:数据获取中，1：数据解析中，2：已解析，3：发送中，4：发送完成，5：已完成
            CreateTime: *********, //创建时间
        }
    ],
    "TotalCount": 1232
}

```

----------
### 查看批次详情 DescribeUnResourcesBatch

请求结构

字段    |   类型  |   必填  | 描述
----    |   ----  |   ----  | ----
Action  | string | Yes | DescribeUnResourcesBatch
Id      | int    | No  | 记录Id 用于处理记录详情时使用
BatchId | int | Yes | 批次Id
CompanyId| int | No | 公司Id
CompanyName| int | No | 公司Id
Domain  | string | No | 域名
Status | int | No | 发送状态
Offset | int | No | 偏移量
Limit | int | No | 限制数量 
GetAll | boolean | No | 获取全量数据，true:获取全量, false:分页查询

返回结构
| 名称    | 类型   | 必返回 | 描述     |
| ------- | ------ | ------ | -------- |
| RetCode | int    | Yes    | 返回码   |
| Action  | string | Yes    | Action   |
| RecordList | object [] | Yes | 批次详情列表 |
| TotalCount | int | Yes    | 批次记录总数 |


例如：
```javascript

{
    "RetCode": 0,
    "Action": "GetUnResourcesBatchListResponse",
    "BatchStatus": 1, //批次状态, 0:数据获取中，1：数据解析中，2：已解析，3：发送中，4：发送完成，5：已完成
    "Notified": 1, //1:通知过， 0：未通知过
    "RecordList": [
        {
            Id: 21321, //记录id
            CompanyId: 34278, //公司Id
            CompanyName: 广州凡科, //公司Id
            Level: S, //客户等级
            Manager: san.zhang, //客户经理
            BU: IBU, //所属BU
            Status: 1, //发送状态0：待发送，1：发送中，2：发送成功，3：发送失败，4：超时未响应
            SmsContent: "fasdfasdfas", //短信预览内容，只有传记录Id时有该字段
            EmailContent: "fasfdasdfas", //邮件预览内容，只有传记录Id时有该字段
            ICPInfo: [{
                OrganizerName: "广州凡科", //主办单位名称
                Websites: [{//通知的域名和网站备案号的信息 从这里面拿
                    Domain: "domain1; domain2",域名信息
                    ICPWebNo: "xxxxx", //网站备案号
                }, {
                    Domain: "domain1; domain2"
                    ICPWebNo: "xxxxx"
                }]
            }],
            NotifyDetail: [{ //只有传记录Id时有该字段
                Id: 123,//通知记录的Id,
                Contact: 17266666666,//手机号/邮箱
                SendStatus: 2 ,//消息发送状态 0:新建，1:发送中，2:已经接收, 3:发送失败，4:接收失败，5:超时未响应，6：尝试发送中（这是一个中间状态）,
                HistorySendStatus: 1, //上次发送的最终状态
                HistorySendTime: 42342342, //上次发送完成的时间
                UpdateTime: 1231312, //更新时间
            }]
        }
    ],
    "TotalCount": 1232
}

```
----------

### 异常解析数据获取    GetAbnormalUnresourceData

请求结构

字段 | 类型 | 必填 | 描述
---- | ---- | ---- | ----
Action | string | Yes | GenerateBatch
BatchId | int | Yes | 批次Id

返回结构
| 名称    | 类型   | 必返回 | 描述     |
| ------- | ------ | ------ | -------- |
| RetCode | int    | Yes    | 返回码   |
| Action  | string | Yes    | GetAbnormalUnresourceDataResponse   |
| RecordList | object [] | Yes | 异常数据列表 |
| TotalCount | int  | Yes | 数据总数 |


例如：
```javascript

{
    "RetCode": 0,
    "Action": "GetAbnormalUnresourceDataResponse",
    "RecordList": [
        {
            "CompanyId": 34278,
            "Reason": "公司信息查找出错"
        },
        {
            "CompanyId": 34279,
            "Reason": "备案信息查找出错"
        },
    ]
    "TotalCount": 1232
}

```
----------

### 重新拉取异常的数据信息 RePullAbnormalRecordInfo

请求结构

字段 | 类型 | 必填 | 描述
---- | ---- | ---- | ----
Action | string | Yes | RePullAbnormalRecordInfo
BatchId     | int | Yes | 批次Id

返回结构
| 名称    | 类型   | 必返回 | 描述     |
| ------- | ------ | ------ | -------- |
| RetCode | int    | Yes    | 返回码   |
| Action  | string | Yes    | Action   |

例如：
```javascript
{
    "RetCode": 0,
    "Action": "RePullAbnormalRecordInfoResponse",
}

```


----------

### 批量发送通知 NotifyUnResourcesBatch

请求结构

字段 | 类型 | 必填 | 描述
---- | ---- | ---- | ----
Action | string | Yes | NotifyUnResourcesBatch
BatchId| int | Yes | 批次Id

返回结构
| 名称    | 类型   | 必返回 | 描述     |
| ------- | ------ | ------ | -------- |
| RetCode | int    | Yes    | 返回码   |
| Action  | string | Yes    | Action   |


例如：
```javascript
{
    "RetCode": 0,
    "Action": "NotifyUnResourcesBatchResponse",
}

```


----------

### 完成处理 FinishNotifyBatch

请求结构

字段 | 类型 | 必填 | 描述
---- | ---- | ---- | ----
Action | string | Yes | FinishNotifyBatch
BatchId| int | Yes | 批次Id

返回结构
| 名称    | 类型   | 必返回 | 描述     |
| ------- | ------ | ------ | -------- |
| RetCode | int    | Yes    | 返回码   |
| Action  | string | Yes    | Action   |


例如：
```javascript
{
    "RetCode": 0,
    "Action": "FinishNotifyBatchResponse",
}

```


----------

### 重试发送消息 RetrySendUnResources

请求结构

字段 | 类型 | 必填 | 描述
---- | ---- | ---- | ----
Action | string | Yes | RetrySendUnResources
Id     | int | Yes | 通知Id

返回结构
| 名称    | 类型   | 必返回 | 描述     |
| ------- | ------ | ------ | -------- |
| RetCode | int    | Yes    | 返回码   |
| Action  | string | Yes    | Action   |


例如：
```javascript
{
    "RetCode": 0,
    "Action": "RetrySendUnResourcesResponse",
}

```



