# 备案系统，客户主体配额与网站配额调整API

标签： 配额


此版本在之前的功能上，加了WebsiteQuota字段，可让审核员设置网站配额

以下接口原本就存在，但调用的地址需要从https://web_api.ucloudadmin.com/api/换到https://web_api.ucloudadmin.com/newicp/


---

### GetProxyCompanyList 获取代理商公司列表

获取代理商的配额信息

#### 请求参数

| 字段      | 类型   | 必填 | 描述                |
| --------- | ------ | ---- | ------------------- |
| Action    | String | Yes  | GetProxyCompanyList |
| Offset    | Int    | NO   | 偏移量              |
| Limit     | Int    | NO   | 长度                |
| CompanyId | Int    | NO   | 公司Id,非必填       |

#### 返回参数

| 字段        | 类型   | 描述                 |
| ----------- | ------ | -------------------- |
| Action      | String | GetProxyCompanyList  |
| CompanyList | Array  | 代理商的配额信息列表 |
| Count       | String | 长度                 |
| RetCode     | Int    | 状态码               |

#### CompanyList结构

| 字段           | 类型   | 描述                           |
| -------------- | ------ | ------------------------------ |
| Note           | String | 备注                           |
| Quota          | Int    | 主体配额，使用默认限制时为null |
| Id             | Int    | 长度                           |
| CompanyId      | Int    | 公司Id                         |
| UpdateTime     | Int    | 更新时间                       |
| CompanyType    | Int    | 公司类型，0普通客户，1代理商   |
| CompanyName    | String | 公司名                         |
| `WebsiteQuota` | Int    | 网站配额，使用默认限制时为null |
| CreateTime     | Int    | 创建时间                       |
| Operator       | String | 操作人                         |

返回示例

``` javascript

{
    "CompanyList": [
        {
            "Note": "2台主机",
            "Quota": 12,
            "Id": 1584521022,
            "CompanyId": 30111,
            "UpdateTime": 1623138103,
            "CompanyType": 0,
            "CompanyName": "优刻得",
            "WebsiteQuota": null,
            "CreateTime": 1623138103,
            "Operator": "daniela.he"
        }
    ],
    "Count": 81,
    "Action": "GetProxyCompanyListResponse",
    "RetCode": 0
}

```

---

### CreateProxyCompany

创建主体与网站配额记录，
注意，如果审核员对网站或主体某项配额不做调整，此项不传既可

#### 请求参数

| 字段           | 类型   | 必填 | 描述                         |
| -------------- | ------ | ---- | ---------------------------- |
| Action         | String | Yes  | CreateProxyCompany           |
| CompanyId      | Int    | Yes  | 公司Id                       |
| CompanyName    | String | NO   | 公司名                       |
| Quota          | Int    | NO   | 主体配额                     |
| `WebsiteQuota` | Int    | NO   | 网站配额                     |
| CompanyType    | Int    | Yes  | 公司类型，0普通客户，1代理商 |
| Note           | Int    | NO   | 备注                         |

#### 返回参数

| 字段    | 类型   | 描述               |
| ------- | ------ | ------------------ |
| Action  | String | CreateProxyCompany |
| RetCode | Int    | 状态码             |

---

### UpdateProxyCompany

更新主体与网站配额记录，注意，如果审核员对网站或主体某项配额不做调整，此项不传既可

#### 请求参数

| 字段           | 类型   | 必填 | 描述                         |
| -------------- | ------ | ---- | ---------------------------- |
| Action         | String | Yes  | UpdateProxyCompany           |
| CompanyId      | Int    | Yes  | 公司Id                       |
| CompanyName    | String | NO   | 公司名                       |
| Quota          | Int    | NO   | 主体配额                     |
| `WebsiteQuota` | Int    | NO   | 网站配额                     |
| CompanyType    | Int    | Yes  | 公司类型，0普通客户，1代理商 |
| Note           | Int    | NO   | 备注                         |

#### 返回参数

| 字段    | 类型   | 描述               |
| ------- | ------ | ------------------ |
| Action  | String | UpdateProxyCompany |
| RetCode | Int    | 状态码             |


### DeleteProxyCompany

删除配额公司Id为必填

#### 请求参数

| 字段           | 类型   | 必填 | 描述                         |
| -------------- | ------ | ---- | ---------------------------- |
| Action         | String | Yes  | DeleteProxyCompany           |
| CompanyId      | Int    | Yes  | 公司Id                       |
| Note           | Int    | NO   | 备注                         |


#### 返回参数

| 字段    | 类型   | 描述               |
| ------- | ------ | ------------------ |
| Action  | String | DeleteProxyCompany |
| RetCode | Int    | 状态码             |

---
