
# 跨境报备api文档

标签（空格分隔）： 跨境报备
除特别标注的API外，API请求地址如下：
URL: https://api.ucloud.cn/
签名生成文档： https://docs.ucloud.cn/api/summary/signature
注意：所有经过网关的入参类型为数组的需转换为key[0] :xxx, key[1]:xxx格式

----------

### 获取用户当前审核状态

### GetCrossBorderApplyStatus

请求结构

| 字段   | 类型   | 必填 | 描述 |
| ----   | ----   | ---- | ---- |
| Action | string | Yes  | GetCrossBorderApplyStatus |
| CompanyId | int | Yes  | 通过网关请求会携带|

返回结构
| 名称    | 类型    | 必返回 | 描述     |
| ------- | ------  | ------ | -------- |
| RetCode | int     | Yes    | 返回码   |
| Action  | string  | Yes    | Action   |
| CanApply| boolean | Yes    | 是否可以新建申请 |
| FinishApply | boolean  | Yes    | 当前是否存在有效已完成审核订单   |

```javascript
{
    "RetCode": 0,
    "Action": "GetCrossBorderApplyStatusResponse",
    "CanApply": true, //是否可以新建申请
    "FinishApply": true, //当前是否存在有效已完成审核订单
}
```

### 获取认证信息

### GetUserAuthInfo

请求结构

| 字段   | 类型   | 必填 | 描述 |
| ----   | ----   | ---- | ---- |
| Action | string | Yes  | GetUserAuthInfo |
| CompanyId | int | Yes  | 通过网关请求会携带|
| AuditType | int | No   | 0:企业认证，1：个人认证，不传为两种认证都获取 |

返回结构
| 名称    | 类型   | 必返回 | 描述     |
| ------- | ------ | ------ | -------- |
| RetCode | int    | Yes    | 返回码   |
| Action  | string | Yes    | Action   |
| AuthInfo| object[] | Yes    | 认证信息 |

```javascript
{
    "RetCode": 0,
    "Action": "GetUserAuthInfoResponse",
    "AuthInfo": [
    { 
        "CompanyName": "xxxxx", // 公司名称
        "CompanyAddress": "xxxxx", //公司地址
        "CreditCode": "xxxx",//统一社会信用代码
        "Manager": "xxxx",  //客户经理
        "AuditState": "已认证", //认证状态
    },
    { 
        "UserName": "xxxxx", // 个人认证时用户名称
        "UserAddress": "xxxxx", //公司地址
        "IdentityNo": "xxxx",//身份证号码
        "Manager": "xxxx",  //客户经理
        "AuditState": "实名信息验证成功", //认证状态
    },
    ]
}
```

### 上传文件
### UploadFile

请求结构

请求URL https://icpupload.ucloud.cn/picture_upload/?Action=UploadFile

| 字段 | 类型 | 必填 | 描述 |
| ---- | ---- | ---- | ---- |
| Action  | string | Yes  | UploadFile|
| File    | string | Yes  | base64    |
| CompanyId | int  | Yes  | 通过网关请求会携带|

返回结构
| 名称    | 类型   | 必返回 | 描述     |
| ------- | ------ | ------ | -------- |
| RetCode | int    | Yes    | 返回码   |
| Action  | string | Yes    | Action   |
| FileName| string | Yes    | 出错原因 |


例如：
```javascript

{
    "RetCode": 0,
    "FileName": "xxxxx.png"
    "Action": "UploadFileResponse"
}

```

### 创建跨境报备申请

### CreateCrossBorderApply

请求结构

| 字段 | 类型 | 必填 | 描述 |
| ---- | ---- | ---- | ---- |
| Action | string | Yes | CreateCrossBorderApply |
| Status | string | No  | 状态，详情见 ApplyStatusEnum |
| CompanyId | int  | Yes  | 通过网关请求会携带|
| CompanyName   | string | No | 公司名称 |
| CompanyCode | string | No | 统一社会信用代码 |
| LegalEntityName   | string| No | 法人名称 |
| BusinessPlace   | string| No | 经营场所 |
| LicenseIssuingAgency   | string| No | 发证机构 |
| PostalCode   | int | No | 邮政编码 |
| BusinessLicenseBeginTime   | int | No | 营业执照有效开始时间 |
| BusinessLicenseEndTime   | int | No | 营业执照有效结束时间 |
| ManagerName   | string| No | 经办人姓名 |
| ManagerLicenseId   | int | No | 经办人证件号 |
| ManagerAddress   | string| No | 经办人地址 |
| ManagerPhone   | string| No | 经办人联系方式 |
| BusinessLicensePic   | string | No | 营业执照图片 |
| ManagerLicensePic   | string[] | No | 经办人身份证图片 |
| PromiseLetterPic   | string | No | 承诺书 |
| ProxyLetterPic   | string | No | 委托书 |
| ServiceProtocolPic   | string | No | 服务协议 |

注意：若状态为提交，则所有字段为必填项

返回结构
| 名称    | 类型   | 必返回 | 描述     |
| ------- | ------ | ------ | -------- |
| RetCode | int    | Yes    | 返回码   |
| Action  | string | Yes    | Action   |
| Id      | int    | Yes    | 申请id   |
| Message | string | No     | 出错原因 |


例如：
```javascript

{
    "RetCode": 0,
    "Message": "出错原因",
    "Id": 1,//申请id
    "Action": "CreateCrossBorderApplyResponse"
}

```

### 修改跨境报备申请

### ModifyCrossBorderApply

请求结构

字段 | 类型 | 必填 | 描述
---- | ---- | ---- | ----
| Action | string | Yes | ModifyCrossBorderApply |
| Id     | int | Yes | 记录id |
| Status | string | No  | 状态，详情见 ApplyStatusEnum |
| CompanyId | int  | Yes  | 通过网关请求会携带 |
| CompanyName   | string | No | 公司名称 |
| CompanyCode | string | No | 统一社会信用代码 |
| LegalEntityName   | string| No | 法人名称 |
| BusinessPlace   | string| No | 经营场所 |
| LicenseIssuingAgency   | string| No | 发证机构 |
| PostalCode   | int | No | 邮政编码 |
| BusinessLicenseBeginTime   | int | No | 营业执照有效开始时间 |
| BusinessLicenseEndTime   | int | No | 营业执照有效结束时间 |
| ManagerName   | string| No | 经办人姓名 |
| ManagerLicenseId   | int | No | 经办人证件号 |
| ManagerAddress   | string| No | 经办人地址 |
| ManagerPhone   | string| No | 经办人联系方式 |
| BusinessLicensePic   | string | No | 营业执照图片 |
| ManagerLicensePic   | string | No | 经办人身份证图片 |
| PromiseLetterPic   | string | No | 承诺书 |
| ProxyLetterPic   | string | No | 委托书 |
| ServiceProtocolPic   | string | No | 服务协议 |

注意：若状态为提交，则所有字段为必填项

返回结构
| 名称    | 类型   | 必返回 | 描述     |
| ------- | ------ | ------ | -------- |
| RetCode | int    | Yes    | 返回码   |
| Action  | string | Yes    | Action   |
| Message | string | No     | 出错原因 |


例如：
```javascript

{
    "RetCode": 0,
    "Message": "出错原因"
    "Action": "ModifyCrossBorderApplyResponse"
}

```

### 获取跨境报备申请列表

### GetCrossBorderApplyList

请求参数

| 字段      | 类型   | 必填 | 描述                |
| --------- | ------ | ---- | ------------------- |
| Action    | string | Yes  | GetCrossBorderApplyList |
| CompanyId | int  | Yes  | 通过网关请求会携带 |
| Id     | int  | No | 申请Id |
| Offset | int  | Yes | 偏移量 |
| Limit  | int  | Yes | 限制数 |

返回参数

| 字段    | 类型   | 描述                        |
| ------- | ------ | --------------------------- |
| RetCode | int | 返回码        |
| Action  | String | GetCrossBorderApplyListResponse |
| Rows | Object [] | 跨境报备申请列表信息 |
| TotalCount | int  | 总数据条数 |

```json
{
    "RetCode": 0,
    "Action": "GetCrossBorderApplyListResponse",
    "TotalCount": 121,
    "Rows": [
        {
            "Id": "申请id",
            "Status": 1, //审核状态
            "CompanyId": xxx,//公司id
            "CompanyName": "公司名称",//公司名称
            "CompanyCode": "xxxxxx", // 统一社会信用代码
            "LegalEntityName": "xxx", //法人名称
            "BusinessPlace": "xxx", //经营场所
            "LicenseIssuingAgency": "xxx", //发证机构
            "PostalCode": 200000, //邮政编码
            "BusinessLicenseBeginTime": "312423542345", //营业执照有效开始时间
            "BusinessLicenseEndTime": "312423542345", //营业执照有效结束时间
            "AuditTime": "xxxxxxxxxx",//审核时间
            "ManagerName": "xxx", //经办人姓名
            "ManagerLicenseId": "312423542345", //经办人身份证号
            "ManagerAddress": "xxxxxx", //经办人地址
            "ManagerPhone": "312423542345", //经办人联系电话
            "BusinessLicensePic": "xxx.png", //营业执照图片
            "ManagerLicensePic": ["xxx.png","xxx.png"], //经办人身份证照片
            "PromiseLetterPic": "xxx.png", //承诺书
            "ProxyLetterPic": "xxx.png", //委托书
            "ServiceProtocolPic": "xxx.png", //服务协议
            "Manager": "xxxx",//客户经理
            "Error": {
                "CompanyName": "公司名称错误信息",
                "CompanyCode": "统一社会信用代码错误信息",
                "LegalEntityName": "法人名称错误信息",
                "BusinessPlace": "经营场所错误信息",
                "LicenseIssuingAgency": "发证机构错误信息",
                "PostalCode": "邮政编码错误信息",
                "BusinessLicenseBeginTime": "营业执照有效开始时间错误信息",
                "BusinessLicenseEndTime": "营业执照有效结束时间错误信息",
                "ManagerName": "经办人姓名错误信息",
                "ManagerLicenseId": "经办人证件号错误信息",
                "ManagerAddress": "经办人地址错误信息",
                "ManagerPhone": "营业执照图片错误信息",
                "BusinessLicensePic": "营业执照图片错误信息",
                "ManagerLicensePic": "经办人身份证图片错误信息",
                "PromiseLetterPic": "承诺书错误信息",
                "ProxyLetterPic": "委托书错误信息",
                "ServiceProtocolPic": "服务协议错误信息",
    }
        }
    ]
}

```

### 删除申请

### DeleteCrossBorderApply

请求参数

| 字段      | 类型   | 必填 | 描述                |
| --------- | ------ | ---- | ------------------- |
| Action    | String | Yes  | DeleteCrossBorderApply |
| CompanyId | int    | Yes  | 通过网关请求会携带 |
| Id        | int    | Yes  | 申请Id |

返回结构
| 名称    | 类型   | 必返回 | 描述     |
| ------- | ------ | ------ | -------- |
| RetCode | int    | Yes    | 返回码   |
| Action  | string | Yes    | Action   |
| Message | string | No     | 出错原因 |

``` javascript

{
    "Action": "DeleteCrossBorderApply",
    "RetCode":  0,
    "Message": "错误信息",
}

```

### 身份证二要素校验

### CheckIdenityTwo

请求参数

| 字段      | 类型   | 必填 | 描述                |
| --------- | ------ | ---- | ---------------- |
| Action    | String | Yes  |  CheckIdenityTwo |
| CompanyId | int    | Yes  | 通过网关请求会携带 |
| Name      | String | Yes | 经办人姓名 |
| LicenseId | String | Yes | 经办人身份证号 |
| Source    | String | Yes | 来源，该项目为CrossBorder 固定值 |

返回结构
| 名称    | 类型   | 必返回 | 描述     |
| ------- | ------ | ------ | -------- |
| RetCode | int    | Yes    | 返回码   |
| Action  | string | Yes    | Action   |
| IsMatch | boolean| Yes    | 对比结果 |
| Message | string | No     | 对比信息 |

``` javascript

{
    "Action": "CheckIdenityTwoResponse",
    "RetCode":  0,
    "IsMatch": 1, // 0:匹配，1：不匹配
    "Message": "错误时反馈信息",
}

```

### 身份证照片校验

### GetIdenityOCRInfo

请求参数

| 字段      | 类型   | 必填 | 描述                |
| --------- | ------ | ---- | ----------------  |
| Action    | String | Yes  | GetIdenityOCRInfo |
| Name      | String | Yes | 经办人姓名 |
| IdCardNumber | String | Yes | 经办人身份证号 |
| PictureName  | String | Yes | 经办人身份证照片名称|
| Side      | String | Yes  | Front:正面，Back:反面 注：反面不校验身份信息|
| Source    | String | Yes | 来源，该项目为CrossBorder 固定值 |

返回结构
| 名称    | 类型   | 必返回 | 描述     |
| ------- | ------ | ------ | -------- |
| RetCode | int    | Yes    | 返回码   |
| Action  | string | Yes    | Action   |
| IsMatch | boolean| Yes    | 对比结果 |
| Info    | object | No     | ocr结果，出错时不返回 |
| Message | string | Yes    | 错误信息 |

``` javascript

{
    "Action": "CheckIdenityInfoResponse",
    "RetCode":  0,
    "IsMatch": true,
    "Info": {
        "Address": "身份证地址",
        "Birthday": "1996-6-16",
        "Gender": "女",
        "IdCardNumber": "身份证号",
        "Name": "李文",
        "Race": "汉",
        "Side": "front"
    }
    "Message": "认证一致(通过)",
}

```

### 营业执照校验

### CheckBusinessLicenseData

请求参数

| 字段      | 类型   | 必填 | 描述                |
| --------- | ------ | ---- | ------------------- |
| Action    | String | Yes  | CheckBusinessLicenseData |
| CompanyId | int    | Yes  | 通过网关请求会携带 |
| CompanyName| String | Yes | 公司名称 |
| CompanyCode | String | Yes | 统一社会信用代码 |
| LegalEntityName | String | No | 法人名称 |
| BusinessLicensePic| String | Yes | 营业执照图片|
| Source    | String | Yes | 来源，该项目为CrossBorder 固定值 |

返回结构
| 名称    | 类型   | 必返回 | 描述     |
| ------- | ------ | ------ | -------- |
| RetCode | int    | Yes    | 返回码   |
| Action  | string | Yes    | Action   |
| IsMatch | boolean| Yes    | 对比结果 |
| Message | string | No     | 信息    |

``` javascript

{
    "Action": "CheckBusinessLicenseData",
    "RetCode":  0,
    "IsMatch": true,
    "Message": "认证一致(通过)",
}

```

### 申请状态枚举

### ApplyStatusEnum

```javascript

const ApplyStatusEnum = {
    0: "编辑中",
    1: "审核中",
    2: "审核通过",
    3: "审核驳回",
    4: "已过期",
    5: "已作废"
}

```


