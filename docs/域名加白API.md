# 域名加白相关API

支持新增接入订单，在UCLOUD审核通过后，将订单中的域名 进行加白。
当管局审核通过后，会自动删除该加白的域名。

标签： 域名 白名单 加白

---

## 添加域名白名单    AddDomainWriteList

通过”订单号“搜索该订单是否是新增接入，并且状态是UCLOUD审核通过
通过 域名 查找该域名是否已在白名单列表中，如果在白名单中会报对应的错误

请求参数

| 字段      | 类型   | 必填 | 描述                |
| --------- | ------ | ---- | ------------------- |
| Action    | String | Yes  | AddDomainWriteList |
| OrderNo   | String | No  | 订单号              |
| CompanyId | String | Yes  | 公司ID    |
| Domain | String [] | No | 域名数组 |
| Expired | String | No | 过期时间 |
| Remark | String | No | 备注 |

传订单号时，Domain Expired Remark不传 相反 传这几个时，不需要传订单号

返回参数

| 字段    | 类型   | 描述                        |
| ------- | ------ | --------------------------- |
| Action  | String | AddDomainWriteList |
| RetCode | Int    | 状态码                      |

response

```json
{
    "RetCode": 0
}

```

---

## 更新加白信息API UpdateDomainWriteList

请求参数

| 字段      | 类型   | 必填 | 描述                |
| --------- | ------ | ---- | ------------------- |
| Action    | String | Yes  | UpdateDomainWriteList |
| Id | Int | Yes  | 白名单记录Id |
| Domain | String [] | 域名数组 |
| Expired | String | Yes | 过期时间 |
| Remark | String | Yes | 备注 |

返回参数

| 字段    | 类型   | 描述                        |
| ------- | ------ | --------------------------- |
| Action  | String | UpdateDomainWriteList |
| RetCode | Int    | 状态码                      |

response

```json
{
    "RetCode": 0
}

```

## 删除的加白信息API DeleteDomainWriteList

请求参数

| 字段      | 类型   | 必填 | 描述                |
| --------- | ------ | ---- | ------------------- |
| Action    | String | Yes  | DeleteDomainWriteList |
| Id | Int | Yes  | 白名单记录Id                  |  

返回参数

| 字段    | 类型   | 描述                        |
| ------- | ------ | --------------------------- |
| Action  | String | DeleteDomainWriteList |
| RetCode | Int    | 状态码                      |

```json
{
    "RetCode": 0
}

```

---

## 获取域名白名单列表API   GetDomainWriteList

请求参数

| 字段      | 类型   | 必填 | 描述                |
| --------- | ------ | ---- | ------------------- |
| Action    | String | Yes  | GetDomainWriteList |
| CompanyId | String | No  | 公司ID    |
| Domain | String | No | 域名 |
| OrderNo | String | No | 订单号 |
| Offset | Int | Yes | 位置 |
| Limit | Int | Yes | 条数 |

返回参数

| 字段    | 类型   | 描述                        |
| ------- | ------ | --------------------------- |
| Action  | String | GetDomainWriteList |
| Rows | Object [] | 白名单信息 |
| TotalCount | Int | 总数据条数 |

```json
{
    "RetCode": 0,
    "Action": "GetDomainWriteList",
    "TotalCount": 121,
    "Rows": [
        {
            "Id": "记录id",
            "CompanyId": "公司Id",
            "Domain": ["*.ucloud.cn","*.baidu.com"], //域名
            "Remark": "xxx原因添加白名单", //备注
            "Operator": "yuewen.li", //操作人
            "Expired": "到期时间",
            "Status": 1, //状态 1：有效，0：失效
            "UpdateTime": "312423542345", //更新时间
        }
    ]
    
}

```

---

## 获取域名加白日志API GetDomainWriteOperatorLog

请求参数

| 字段      | 类型   | 必填 | 描述                |
| --------- | ------ | ---- | ------------------- |
| Action    | String | Yes  | GetDomainWriteOperatorLog |
| Offset | Int | Yes | 位置 |
| Limit | Int | Yes | 条数 |

返回参数

| 字段    | 类型   | 描述                        |
| ------- | ------ | --------------------------- |
| Action  | String | GetDomainWriteOperatorLog |
| Logs | Object [] | 日志信息 |
| TotalCount | Int | 总数据条数 |

```json
{
    "RetCode": 0,
    "Action": "GetDomainWriteOperatorLog",
    "TotalCount": 121,
    "Logs": [
        {
            "CompanyId": 1234,
            "Action": "AddDomainWriteList",//AddDomainWriteList:添加，UpdateDomainWriteList：修改，DeleteDomainWriteList:删除
            "Operator": "yuewen.li", //操作人
            "Domain": ["*.ucloud.cn","*.baidu.com"], //域名
            "Remark": "xxx原因添加白名单", //备注
            "Expired": "到期时间",
            "CreateTime": "操作时间"
        }
    ]
}

```
