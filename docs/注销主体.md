# 注销主体

---

注销主体的前提是需要已完成备案
因此需要先使用DescribeICPMain取到已备案的数据。
若 DescribeICPMain 对应主体下 
"DeleteMain":true, //则该主体可以 执行注销主体

使用CreateICPOrder接口创建一个订单类型为4的注销主体订单。

----------

请求结构

字段 | 类型 | 必填 | 描述
---- | ---- | ---- | ----
Action | string | Yes | CreateICPOrder
ICPMainNo| string | Yes | 主体备案号
OrganizerName| string | Yes | 主办单位名称
ICPId| string | Yes | DescribeICPMain中主体内，主体的Id
CMainId| string | Yes | DescribeICPMain中主体内，主体的CMainId
PICMainLicenseId| string | Yes | DescribeICPMain中主体内，PICMainLicenseId
PICMainMobile| string | Yes | DescribeICPMain中主体内，PICMainMobile，短信核验
Type| string | Yes | 类型，固定为4
Status| string | Yes | 状态，固定为8
ReasonId| string | Yes | 原因，固定为1,
ICPMainPassword| string | Yes | 原因，固定为123456,

-----

# 注销主体CreateICPOrder

请求结构
```

{
    "ICPMainNo": "苏ICP备xxxxxxx号",
    "OrganizerName": "钱俊烨",
    "ICPId": 111,
    "PICMainLicenseId": "53011*****1234314",
    "PICMainMobile": "176*****527",
    "CMainId": 222,
    "Type": 4,
    "Status": 8,
    "ICPMainPassword": "123456",
    "ReasonId": 1,
    "Action": "CreateICPOrder"
}

```

返回结构

```
{
   "Action": "CreateICPOrderResponse",
   "OrderNo": "O20220623105454009585",
   "RetCode": 0
}

```
