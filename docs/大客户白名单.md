# 备案大客户白名单功能

标签： 白名单 接入

---

解决大客户无法做接入，同时解决代理商无法提供接口的替代方案

---

### DescribeProxyCompanyDomainList

查询代理商与大客户的白名单域名列表

请求参数

| 字段   | 类型   | 必填 | 描述             |
| ------ | ------ | ---- | ---------------- |
| Action | String | Yes  | DescribeProxyCompanyDomainList |
| CompanyId | Int | NO  | 公司Id |
| CompanyName | String | NO  | 公司名，支持模糊搜索 |
| Domain | String | NO  | 域名 |
| BeginTime | Int | NO  | 按时间查询，开始的时间 |
| EndTime | Int | NO  |按时间查询，结束的时间  |
| NeedAll | Bool | NO  |是否输出查询条件对应的全部记录，为导出文件使用，有此参数后,Offset与Limit做废  |
| Offset | Int | NO  | 偏移量，默认为0 |
| Limit  | Int | NO   | 数据返回长度，默认20，           |

返回值

| 字段    | 类型   | 描述          |
| ------- | ------ | ------------- |
| Action  | String | DescribeProxyCompanyDomainList |
| DomainList    | Array[Object] | 配置列表      |
| Count   | String | 数据总长度          |
| RetCode | Int    | 状态码        |

```JSON

{
  "Action" : "DescribeProxyCompanyDomainList",
  "DomainList" : [
    {
      "Id" : "xx1difadslkfj23",
      "CompanyId" : 34278,
      "CompanyName" : "公司名",
      "Domain" : "qianjunye.com",
      "Operator" : "操作人",
      "CreateTime" : 131111
    }
  ],
  "Count" : 25,
  "RetCode" : 0
}


```

 ---

### DescribeProxyCompanyDomainActionList

查询代理商与大客户的白名单域名的操作日志

请求参数

| 字段   | 类型   | 必填 | 描述             |
| ------ | ------ | ---- | ---------------- |
| Action | String | Yes  | DescribeProxyCompanyDomainActionList |
| CompanyId | Int | NO  | 公司Id |
| Domain | String | NO  | 域名 |
| Offset | Int | NO  | 偏移量，默认为0 |
| Limit  | Int | NO   | 数据返回长度，默认20，           |

返回值

| 字段    | 类型   | 描述          |
| ------- | ------ | ------------- |
| Action  | String | DescribeProxyCompanyDomainActionList |
| ActionList    | Array[Object] | 配置列表      |
| Count   | String | 数据总长度          |
| RetCode | Int    | 状态码        |

```JSON

{
  "Action" : "DescribeProxyCompanyDomainActionList",
  "ActionList" : [
    {
      "Id" : "xx1difadslkfj23",
      "CompanyId" : 34278,
      "ActionName" : "操作类型",
      "Domain" : "qianjunye.com",
      "Remark" : "备注",
      "Operator" : "操作人",
      "ActionTime" : 131111
    }
  ],
  "Count" : 25,
  "RetCode" : 0
}


```

---

### AddProxyCompanyDomain

增加的白名单

| 字段   | 类型   | 必填 | 描述             |
| ------ | ------ | ---- | ---------------- |
| Action | String | Yes  | AddProxyCompanyDomain |
| CompanyId | Int | Yes  | 公司Id |
| CompanyName | String | Yes  | 公司名（还是使用接口去取） |
| Domain | String | Yes  | 域名 |
| DomainList | String | Yes  | 域名列表，Base64后的CSV文件。与Domain二选一 |

返回值

| 字段    | 类型   | 描述          |
| ------- | ------ | ------------- |
| Action  | String | AddProxyCompanyDomain |
| RetCode    | String | 是否成功   |


DomainList对应的CSV文件格式，第一列为公司Id,第二列为域名。列名约定严格
| 字段    | 类型   | 描述          |
| ------- | ------ | ------------- |
| CompanyId  | String | 公司Id |
| Domain    | String | 域名   |

参考Base64:"data:text/csv;base64,77u/Q29tcGFueUlkLERvbWFpbg0KMTIzNDU2LHRlc3QuY29tDQo="


---

### DeleteProxyCompanyDomain

删除增加的白名单

| 字段   | 类型   | 必填 | 描述             |
| ------ | ------ | ---- | ---------------- |
| Action | String | Yes  | DeleteProxyCompanyDomain |
| Id | Int | Yes  | 需要删除的Id |

返回值

| 字段    | 类型   | 描述          |
| ------- | ------ | ------------- |
| Action  | String | DeleteProxyCompanyDomain |
| RetCode    | String | 是否成功   |

返回结构简单，仅有RetCode来判断操作是否成功
