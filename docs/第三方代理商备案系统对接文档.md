# 第三方代理商备案系统对接文档

标签（空格分隔）： 备案 自动化核验 H5

---

## 步骤一，生成订单信息

备案数据采集流程与 UCloud 自有流程保持一致。
代理商采集完原备案的初审信息后。使用 CreateICPOrder 提交信息，参数与之前一致。唯一的区别是 Status 字段从原来“提交 UCLloud 审核”状态值为 7 变成“编辑中”，状态值为 1。

---

## 步骤二，验证订单内容是否有出错之处

备案数据采集流程与 UCloud 自有流程保持一致。
代理商采集完原备案的初审信息后。使用 CreateICPOrder 提交信息，参数与之前一致。唯一的区别是 Status 字段从原来“提交 UCLloud 审核”状态值为 7 变成“编辑中”，状态值为 1。

#### 订单静态检查 CheckICPOrder

入参为订单号，返回目前订单静态检查。是否有出错之处

### RequestParams

| 名称    | 类型   | 必填 | 描述                  |
| ------- | ------ | ---- | --------------------- |
| Action  | string | Yes  | 方法名，CheckICPOrder |
| OrderNo | string | Yes  | 订单号                |

### ResponseParams

| 名称    | 类型   | 必返回 | 描述     |
| ------- | ------ | ------ | -------- |
| RetCode | int    | Yes    | 返回码   |
| IsTrue  | Bool   | Yes    | 是否正确 |
| Message | string | Yes    | 出错原因 |

---

## 步骤三，提取需要核验的个人信息

当客户信息保存好后，代理商根据证件类型与证件 Id,去重取出需要做活体核验的人的信息。举例说明，当前订单中有 3 个网站，其中网站 1 与网站 2 分别为同一网站负责人 A,网站 3 为负责人 B.供应商需要提取出 A 与 B 的信息，请求 2 次接口，生成 2 张二维码提供给客户扫描。

#### 获取授权 Token GetTemporaryToken

二维码授权 TokenAPI,API 使用 UCloud 公用的 API 调用方式。主要参数如下

### RequestParams

| 名称            | 类型    | 必填 | 描述                      |
| --------------- | ------- | ---- | ------------------------- |
| Action          | string  | Yes  | 方法名，GetTemporaryToken |
| LicenseType     | integer | Yes  | 证件类型                  |
| LicenseId       | string  | Yes  | 证件号                    |
| organization_id | string  | Yes  | IP 所在的项目 Id          |
| OrderNo         | string  | Yes  | 订单号                    |

### ResponseParams

| 名称    | 类型   | 必返回 | 描述   |
| ------- | ------ | ------ | ------ |
| RetCode | int    | Yes    | 返回码 |
| Token   | string | Yes    | 授权码 |

---

## 步骤四， 生成 H5 URL

```
let url = 'https://m.ucloud.cn/mobile/authentication/icp.html#/pages/'
if ('licId' in params) {
          url += `veriface/start?licId=${encodeURI(params.licId)}&licName=${encodeURI(params.licName)}`
    }
url += `&orderNo=${params.orderNo || ''}&orgType=${params.organizerType || ''}&tokenId=${res.Token}&uuid=${uuid}

```

将以上方式生成得到的 URL 提供给客户或者生成二维码，用手机浏览器或者微信打开既进入 H5 验证

## 步骤五， H5 验证完成，提交

在订单与网站中，都存在 CurtainStatus 字段。当订单中的 CurtainStatus 状态为 3，则说明整体的订单已录入完成。可以提交。
网站中 CurtainStatus 为 0 说明客户还未做 H5 核验，状态为 1，说明已做核验。
