# 新版备案后端，结合审核侧与用户侧

标签（空格分隔）： 检验 通用配置 在线 API

---

## 获取数据库中的历史通用配置列表 GetIcpCommonConfigOnlineList

查询当前 Mongo 中的配置列表
请求参数

| 字段   | 类型   | 必填 | 描述           |
| ------ | ------ | ---- | -------------- |
| Action | String | Yes  | GetIcpCommonConfigOnlineList  |
| Offset | Int    | NO   | 偏移量         |
| Limit  | String | NO   | 长度           |
| Id     | String | NO   | 配置 Id,String |
| Name   | String | NO   | 配置名         |

返回参数

| 字段    | 类型   | 描述          |
| ------- | ------ | ------------- |
| Action  | String | GetIcpCommonConfigOnlineListResponse |
| List    | Array | 配置列表      |
| Count   | String | 长度          |
| RetCode | Int    | 状态码        |

```json
{
  "List": [
    {
      "Config": {
      },
      "Name": "20200525Mmd",
      "UnixTime": 1590339094,
      "Id": "5ecaa61649188f076776af33"
    }
  ],
  "Count": 1,
  "RetCode": 0
}
```

### 查询当前线上 Redis 配置 DescribeICPConfig

查询当前线上 Redis 的配置
请求参数

| 字段   | 类型   | 必填 | 描述             |
| ------ | ------ | ---- | ---------------- |
| Action | String | Yes  | DescribeICPConfig |

返回参数

| 字段    | 类型   | 描述             |
| ------- | ------ | ---------------- |
| Action  | String | DescribeICPConfigResponse |
| Config  | Object | 配置列表         |
| RetCode | Int    | 状态码           |

```json
{
  "Action": "DescribeICPConfigResponse",
  //...
  "RetCode": 0
}
```

### 保存编辑的配置 EditCommonConfig

编辑 Mongo 中保存的配置，如无 Id 系统会新建记录，如有则会在原 Id 上更新。

`Config需要完整传入`

请求参数

| 字段   | 类型   | 必填 | 描述           |
| ------ | ------ | ---- | -------------- |
| Action | String | Yes  | EditCommonConfig   |
| Id     | String | NO   | 配置 Id,String |
| Name   | String | NO   | 配置名         |
| Config | Object | Yes  | 配置           |

返回参数

| 字段    | 类型   | 描述       |
| ------- | ------ | ---------- |
| Action  | String | EditCommonConfigResponse |
| RetCode | Int    | 状态码     |

```json
{
    "Id": "619356e0c6aa0e439c07f568",
    "Action": "EditCommonConfigResponse",
    "RetCode": 0
}
```

### 发布至线上 ApplyCommonConfig

传入 Mongo 中配置的 Id ,更新至 Redis 中。原 Redis 记录保存至 Mongo 成为历史配置，不允许更新

`Config需要完整传入`

请求参数

| 字段   | 类型   | 必填 | 描述           |
| ------ | ------ | ---- | -------------- |
| Action | String | Yes  | ApplyCommonConfigResponse     |
| Id     | String | NO   | 配置 Id,String |

返回参数

| 字段    | 类型   | 描述       |
| ------- | ------ | ---------- |
| Action  | String | ApplyCommonConfig |
| RetCode | Int    | 状态码     |

```json
{
  "RetCode": 0
}
```


