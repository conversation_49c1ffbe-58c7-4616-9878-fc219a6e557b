
# 同步CID

标签（空格分隔）： 同步，CmainId,CwebsiteId,CconnectId

----------
### SyncCIDToHengan

请求结构

字段 | 类型 | 必填 | 描述
---- | ---- | ---- | ----
Action | string | Yes | SyncCIDToHengan
OrderNo| string | No | 订单号
ICPMainNo | string | Yes | 主体备案号

注意：
要同步指定的订单时，OrderNo必填
同步CID只支持 变更备案、变更主体、变更接入、变更网站、取消接入、注销网站、 注销主体等类型的订单

返回结构
| 名称    | 类型   | 必返回 | 描述     |
| ------- | ------ | ------ | -------- |
| RetCode | int    | Yes    | 返回码   |
| Action  | string | Yes    | Action   |
| Message | string | No     | 出错原因 |


例如：
```javascript

{
    "RetCode": 0,
    "Message": "出错原因"
    "Action": "GetOptionalCurtainPicturesResponse"
}

```
