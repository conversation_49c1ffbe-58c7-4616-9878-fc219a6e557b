# 审核侧信息核验相关文档


支持批量创建验证任务，查询往期日志


标签： 信息 核验 接口 自动化 日志

---





## 查询核验日志     GetPictureMetaInfo

通过传入文件名或者URL，得到图片的像素判断与图片大小判断。
目前线上标准
最长边像素不超过3000
最小边像素不超过400
文件最大为3M


请求参数

| 字段        | 类型   | 必填 | 描述               |
| ----------- | ------ | ---- | ------------------ |
| Action      | String | Yes  | GetPictureMetaInfo |
| PictureName | String | No   | 文件名             |
| PictureURL  | String | No   | 文件URL            |
 


返回参数


| 字段            | 类型   | 描述                       |
| --------------- | ------ | -------------------------- |
| Action          | String | GetPictureMetaInfoResponse |
| PictureMetaInfo | Object | 图片Meta信息               |
| Compliance      | Bool   | 检查结果                   |
| Message         | String | 具体检查不通过的原因       |
| RetCode         | Int    | 状态码                     |


response


```json
{
    "PictureMetaInfo": {
        "Size": 3347409,
        "Width": 4032,
        "Height": 3024
    },
    "Compliance": false,
    "Message": "最长边不能超过3000像素",
    "RetCode": 0
}

```
---



## 查询核验日志     GetOrderVerifyLog

通过订单号或者备案号，查询核验相关的日志。
订单号与主体备案必须传一项

线上地址    https://icp-admin.ucloudadmin.com/newicp/?Action=GetOrderVerifyLog
测试地址    *************:6161/?Action=GetOrderVerifyLog

请求参数

| 字段      | 类型   | 必填 | 描述              |
| --------- | ------ | ---- | ----------------- |
| Action    | String | Yes  | GetOrderVerifyLog |
| OrderNo   | String | No   | 订单号            |
| ICPMainNo | String | No   | 主体备案号        |
 


返回参数


| 字段          | 类型     | 描述                      |
| ------------- | -------- | ------------------------- |
| Action        | String   | GetOrderVerifyLogResponse |
| VerifyLogList | [Object] | 日志列表数组              |
| RetCode       | Int      | 状态码                    |


response


```json

{
    "VerifyLogList": [
        {
            "DataContent": {
                "Picture": "http://icp.cn-bj.ufileos.com/89438496f168d041318f0a99e74c360e3eb1c76b.jpg?UCloudPublicKey=4E9UU0VhDy6mp5pBg6YhGi5V%2B6Ag2NjoOnUSLDUl6INJ%2FMdW3ZAPWQ%3D%3D&Signature=p53UC4f%2FSnxcrJD9WSxWvQ4FIug%3D&Expires=1620725846&iopcmd=convert&q=50&dst=jpg",
                "Name": "赵敏",
                "Id": "140521199205174823",
                "Operator": "william.qian",
                "OrderNo": "O20210423144956001366",
                "Remark": "网站负责人140521199205174823_赵敏",
                "CompanyId": 34278,
                "Backend": "IdAuth",
                "Action": "CheckPersonalInfo",
                "Source": "ICP",
                "Type": 5
            },
            "Response": {
                "Name": "赵敏",
                "Id": "140521199205174823",
                "RespInfo": "认证一致(通过)",
                "UUID": "1758efdc-4f7a-4c81-a5c5-a78de7620b50",
                "RetCode": 0,
                "Action": "CheckPersonalInfo"
            },
            "ReturnObject": {
                "IsMatch": true,
                "Message": "认证一致(通过)"
            },
            "Id": 68984,
            "UUID": "",
            "CreateTime": 1620725246,
            "UpdateTime": 1620725248,
            "Result": 0,
            "Type": "验证个人信息（三要素）",
            "Multiplex": 0,
            "OrderNo": "O20210423144956001366",
            "Operator": "william.qian",
            "Remark": "网站负责人140521199205174823_赵敏",
            "CompanyId": 34278
        }
    ],
    "RetCode": 0
}

```


---


## 获取图片URL     GetPicture

通过文件名获取文件URL，增加了视频等文件的支持。

信息核验过程中产生的图片与视频都可以在此查询。
线上地址    https://icp-admin.ucloudadmin.com/api/?Action=GetPicture
测试地址    *************:5000/?Action=GetPicture

请求参数

| 字段    | 类型   | 必填 | 描述                                     |
| ------- | ------ | ---- | ---------------------------------------- |
| Action  | String | Yes  | GetPicture                               |
| Picture | String | Yes  | 文件名，原来只存图片，现在支持了各类文件 |


返回参数


| 字段        | 类型   | 描述               |
| ----------- | ------ | ------------------ |
| Action      | String | GetPictureResponse |
| PictureInfo | String | 图片地址           |
| RetCode     | Int    | 状态码             |


response


```json
{
    "RetCode": 0,
    "Action": "GetPictureResponse",
    "PictureInfo": "http://icp.cn-bj.ufileos.com/2c86de0290e7dffb45cb0d83e0a3b304b182ca20.mp4?UCloudPublicKey=4E9UU0VhDy6mp5pBg6YhGi5V%2B6Ag2NjoOnUSLDUl6INJ%2FMdW3ZAPWQ%3D%3D&Signature=qOumQPPB5MdyvLEe211bzZeZbWQ%3D&Expires=1620456495"
}


```



---



## 创建信息核验的批次     CreateCompanyInfoCheckBatch

创建信息核验批次。

通过上传或者指定类型，或者SQL的方式，来执行查询。
线上地址    https://icp-admin.ucloudadmin.com/newicp/?Action=CreateCompanyInfoCheckBatch
测试地址    *************:6161/?Action=CreateCompanyInfoCheckBatch

创建好的批次，需要启动。测试环境下，为防止滥用。数据量会截取前10的数组


请求参数

| 字段       | 类型   | 必填 | 描述                                                            |
| ---------- | ------ | ---- | --------------------------------------------------------------- |
| Action     | String | Yes  | CreateCompanyInfoCheckBatch                                     |
| SourceType | Int    | No   | 批次来源类型,1为当前全部已备案的企业，3为上传文件，2为指定的SQL |
| File       | String | No   | 文件Base64,CSV格式                                              |
| SQLString  | String | No   | 查询的SQL语句                                                   |
| Remark     | String | Yes  | 备注                                                            |


Type为1，不需要其它值，Type为2需要File字段，Type3为SQLString


返回参数


| 字段    | 类型   | 描述                                |
| ------- | ------ | ----------------------------------- |
| Action  | String | CreateCompanyInfoCheckBatchResponse |
| BatchId | Int    | 批次Id                              |
| RetCode | Int    | 状态码                              |


response


```json
{
    "RetCode": 0,
    "BatchId":1,
    "Action": "CreateCompanyInfoCheckBatchResponse"
}


```


---




## 获取信息验证的批次列表     GetCompanyInfoCheckBatchList

最近的接口命名有点土


线上地址    https://icp-admin.ucloudadmin.com/newicp/?Action=GetCompanyInfoCheckBatchList
测试地址    *************:6161/?Action=CreateGetCompanyInfoCheckBatchList



请求参数

| 字段   | 类型   | 必填 | 描述                         |
| ------ | ------ | ---- | ---------------------------- |
| Action | String | Yes  | GetCompanyInfoCheckBatchList |
| Offset | Int    | No   | 偏移                         |
| Limit  | Int    | No   | 大小限制                     |




返回参数


| 字段      | 类型   | 描述                                 |
| --------- | ------ | ------------------------------------ |
| Action    | String | GetCompanyInfoCheckBatchListResponse |
| RetCode   | Int    | 状态码                               |
| BatchList | Array  | 批次详情                             |


response


```json
{
    "BatchList": [
        {
            "Id": 1,
            "Remark": "第1次全量",
            "FileName": "c7dcdb522f8cea30cabdb95b908684d84c87eb54.csv",
            "CreateTime": 1622109664,
            "UpdateTime": 1622110134,
            "SourceType": "上传文件",
            "Status": "初始化",
            "Creator": "william.qian"
        },
        {
            "Id": 2,
            "Remark": "第2次测试",
            "FileName": "",
            "CreateTime": **********,
            "UpdateTime": 1622110287,
            "SourceType": "SQL查询",
            "Status": "查询中",
            "Creator": "william.qian"
        }
    ],
    "RetCode": 0
}

```


---





## 获取单个批次信息详情     DescribeCompanyInfoCheckBatch

查看列表中的查询详情


线上地址    https://icp-admin.ucloudadmin.com/newicp/?Action=DescribeCompanyInfoCheckBatch
测试地址    *************:6161/?Action=DescribeCompanyInfoCheckBatch


详情展示页用API,支持执行状态与执行结果的查询

请求参数

| 字段        | 类型   | 必填 | 描述                                   |
| ----------- | ------ | ---- | -------------------------------------- |
| Action      | String | Yes  | DescribeCompanyInfoCheckBatch          |
| BatchId     | Int    | Yes  | 批次Id                                 |
| Status      | String | No   | 状态 , INIT,DOING,DONE                 |
| CheckResult | String | No   | 执行情况  DOING, SUCESS , FAILD, ERROR |
| Offset      | Int    | No   | 偏移                                   |
| Limit       | Int    | No   | 大小限制                               |


返回参数


| 字段    | 类型   | 描述                                  |
| ------- | ------ | ------------------------------------- |
| Action  | String | DescribeCompanyInfoCheckBatchResponse |
| RetCode | Int    | 状态码                                |
| Record  | Array  | 批次详情                              |


response


```json
{
    "Record": [
        {
            "ReturnObject": [],
            "Id": 58772,
            "BatchId": 2,
            "OrganizerType": 4,
            "OrganizerName": "优刻得科技股份有限公司",
            "OrganizerLicenseArea": "上海市杨浦区隆昌路619号10#B号楼201室",
            "PICMainName": "季昕华",
            "OrganizerLicenseId": "91310110591673062R",
            "PICMainLicenseId": "332502197903295170",
            "CreateTime": **********,
            "UpdateTime": **********,
            "Status": "完成",
            "CheckResult": "检查通过",
            "Message": "{\"OrganizerNameResult\":true,\"OrganizerLicenseAreaResult\":true,\"PICMainNameResult\":true,\"OrganizerLicenseIdResult\":true,\"RegResult\":true}",
            "Remark": "沪ICP备12020087号"
        }
    ],
    "Count": 1,
    "RetCode": 0
}

```



---


## 启动查询批次     StartCompanyInfoCheckBatch

启动查询批次


线上地址    https://icp-admin.ucloudadmin.com/newicp/?Action=StartCompanyInfoCheckBatch
测试地址    *************:6161/?Action=StartCompanyInfoCheckBatch


请求参数

| 字段    | 类型   | 必填 | 描述                       |
| ------- | ------ | ---- | -------------------------- |
| Action  | String | Yes  | StartCompanyInfoCheckBatch |
| BatchId | Int    | No   | 批次Id                     |

返回参数


| 字段    | 类型   | 描述                               |
| ------- | ------ | ---------------------------------- |
| Action  | String | StartCompanyInfoCheckBatchResponse |
| RetCode | Int    | 状态码                             |


response


```json
{
    "RetCode": 0
}

```




---


## 未完成的任务重新推送     RePushCompanyInfoCheckBatch

重新推送执行任务，解决丢消息的问题。
批次中有未完成的记录，且任务队列为空（长期执行无问题的话，可以考虑让定时任务来自动处理）


线上地址    https://icp-admin.ucloudadmin.com/newicp/?Action=RePushCompanyInfoCheckBatch
测试地址    *************:6161/?Action=RePushCompanyInfoCheckBatch


请求参数

| 字段    | 类型   | 必填 | 描述                        |
| ------- | ------ | ---- | --------------------------- |
| Action  | String | Yes  | RePushCompanyInfoCheckBatch |
| BatchId | Int    | No   | 批次Id                      |

返回参数


| 字段    | 类型   | 描述                                |
| ------- | ------ | ----------------------------------- |
| Action  | String | RePushCompanyInfoCheckBatchResponse |
| RetCode | Int    | 状态码                              |


response


```json
{
    "RetCode": 0
}

```



---


## 结束查询批次     StopCompanyInfoCheckBatch

任务执行完成，结束批次,结束状态的批次，除下载外，禁止做任务操作
执行时，不能有未查询的任务


线上地址    https://icp-admin.ucloudadmin.com/newicp/?Action=StopCompanyInfoCheckBatch
测试地址    *************:6161/?Action=StopCompanyInfoCheckBatch


请求参数

| 字段    | 类型   | 必填 | 描述                      |
| ------- | ------ | ---- | ------------------------- |
| Action  | String | Yes  | StopCompanyInfoCheckBatch |
| BatchId | Int    | No   | 批次Id                    |

返回参数


| 字段    | 类型   | 描述                              |
| ------- | ------ | --------------------------------- |
| Action  | String | StopCompanyInfoCheckBatchResponse |
| RetCode | Int    | 状态码                            |


response


```json
{
    "RetCode": 0
}

```



---


## 下载批次内结果详情     DownloadCompanyInfoCheckBatch

下载目前批次内的执行情况，提供下载的URL

线上地址    https://icp-admin.ucloudadmin.com/newicp/?Action=DownloadCompanyInfoCheckBatch
测试地址    *************:6161/?Action=DownloadCompanyInfoCheckBatch


请求参数

| 字段    | 类型   | 必填 | 描述                          |
| ------- | ------ | ---- | ----------------------------- |
| Action  | String | Yes  | DownloadCompanyInfoCheckBatch |
| BatchId | Int    | No   | 批次Id                        |

返回参数


| 字段     | 类型   | 描述                                  |
| -------- | ------ | ------------------------------------- |
| Action   | String | DownloadCompanyInfoCheckBatchResponse |
| RetCode  | Int    | 状态码                                |
| FileName | String | 文件名                                |


response


```json
{
    "FileName":"xxxx.csv",
    "RetCode": 0
}

```

---


## 清空全部任务     ForceStopCompanyInfoCheck

强制结束全部查询队列。防止误操作引发的损失(提示需要谨慎)


线上地址    https://icp-admin.ucloudadmin.com/newicp/?Action=ForceStopCompanyInfoCheck
测试地址    *************:6161/?Action=ForceStopCompanyInfoCheck


请求参数

| 字段   | 类型   | 必填 | 描述                      |
| ------ | ------ | ---- | ------------------------- |
| Action | String | Yes  | ForceStopCompanyInfoCheck |

返回参数


| 字段    | 类型   | 描述                              |
| ------- | ------ | --------------------------------- |
| Action  | String | ForceStopCompanyInfoCheckResponse |
| RetCode | Int    | 状态码                            |


response


```json
{
    "RetCode": 0
}

```

---



## 状态Map



```javascript

/* 
 * 公司检查批次状态
 * 对应的是t_company_info_check_batch的Status
 */

const BatchStatusList = [{
	VALUE: 0,
	STATUS: 'INIT',
	COMMENT: '初始化',
},
{
	VALUE: 1,
	STATUS: 'DOING',
	COMMENT: '查询中',
},
{
	VALUE: 2,
	STATUS: 'DONE',
	COMMENT: '完成',
}]


/* 
 * 公司检查批次状态
 * 对应的是t_company_info_check_batch的Status
 */
const SourceTypeList = [{
	VALUE: 1,
	STATUS: 'ALLICPCOMPANY',
	COMMENT: '全部已备案的企业',
},
{
	VALUE: 3,
	STATUS: 'FILE',
	COMMENT: '上传文件',
},
{
	VALUE: 2,
	STATUS: 'SQL',
	COMMENT: 'SQL查询',
}]

/* 
 * 公司检查批次状态
 * 对应的是t_company_info_check_batch的Status
 */
const CheckResultList = [{
	VALUE: 0,
	STATUS: 'DOING',
	COMMENT: '未检查',
},
{
	VALUE: 1,
	STATUS: 'SUCESS',
	COMMENT: '检查通过',
},
{
	VALUE: -1,
	STATUS: 'FAILD',
	COMMENT: '检查未通过',
},
{
	VALUE: 2,
	STATUS: 'ERROR',
	COMMENT: '检查过程出错',
}]
```