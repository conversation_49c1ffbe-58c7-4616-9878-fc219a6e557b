
# 变更主体

标签（空格分隔）： 变更，主体

---

特殊规则：
1.江苏省和安徽省 不支持变更主体
2.浙江地区，个人&非个人性质，主办单位名称均不支持变更。
3.所有省份，暂不支持变更省份，后续视各省管局要求可能允许，不写死。
4.除上述特殊规则外，企业性质，其他字段均支持变更。

优化：
1.新增主体，所有类型（个人&非个人），黑龙江、河南，主体负责人 & 网站负责人 手机号和应急联系电话可以一致。

订单类型冲突：
1.变更主体和变更主体不能同时进行
2.变更主体和注销主体不能同时进行
3.变更主体和取消接入或注销网站不能同时进行

变更主体的前提是需要已完成备案
因此需要先使用DescribeICPMain取到已备案的数据,删除部分数据后。使用CreateICPOrder接口创建一个订单类型为8的变更订单。

----------

### 获取需要变更的主体内容

请求结构

字段 | 类型 | 必填 | 描述
---- | ---- | ---- | ----
Action | string | Yes | DescribeICPMain
ICPMainNo| string | Yes | 需要变更的主体备案号

返回结构

```javascript

{
    "TotalCount": 1,
    "ICP": [
        {
            "Id": 77878,
            "Status": 7,
            "CMainId": 4617577,
            "CompanyId": 34278,
            "ICPMainNo": "粤ICP备17053391号",
            "AreaId": 441901,
            "OrganizerType": 4,
            "OrganizerName": "东莞市嘉诚化工有限公司",
            "OrganizerAddress": "广东省东莞市大岭山镇大岭山社区西园街33号",
            "OrganizerLicenseType": 1,
            "OrganizerLicenseId": "91441900MA4WDH6J4E",
            "OrganizerLicenseArea": "东莞市大岭山镇大岭山社区西园街33号",
            "PICMainName": "余星",
            "PICMainOfficePhone": "",
            "PICMainOfficePhonePre": "",
            "PICMainMobile": "13728426748",
            "PICMainEmail": "<EMAIL>",
            "PICMainQQ": "",
            "PICMainLicenseType": 2,
            "PICMainLicenseId": "430621198710281822",
            "PICMainLicenseDate": "2022-06-09-长期",
            "OrganizerLicensePicture": [
                "f112bf034fcd51f00d40962c82673c8631e95fd6.jpg"
            ],
            "OrganizerResidencePermitPicture": [],
            "PICMainLicensePicture": [
                "c67799748f9001875f5993e17180dd144e990fb5.jpg"
            ],
            "OtherPicture": [
                "1c8403395be455efc7930c6505e55375d685443c.jpg"
            ],
            "Remark": null,
            "CreateTime": **********,
            "UpdateTime": **********,
            "IsDeleted": 0,
            "UnitSuperior": "东莞市嘉诚化工有限公司",
            "EmergencyPhone": "",
            "UnitCertificateExpDate": null,
            "CanOperated": false, //是否可以 变更备案
            "CanModify": false,  //暂时不使用
            "CanChange":false,  //是否可以 变更主体
            "DeleteMain":false, //是否可以 注销主体
            "Website": [
                {
                    "Id": 120227,
                    "MainId": 77878,
                    "CWebsiteId": 4579637,
                    "ICPWebNo": "粤ICP备17053391号-2",
                    "Status": 7,
                    "Name": "东莞市嘉诚化工有限公司",
                    "Domain": [
                        {
                            "CerificationPicture": [
                                "7e84235bff9330eb778ce641ed95ff193a27442f.jpg"
                            ],
                            "domainAuthenctionPicture": [
                                "8cs79878csd89vs90s8v0s8sv09vs8vs79vsvs88.jpg" // 域名实名信息截图，当域名不在我司注册时，该字段必传
                            ],
                            "Domain": "jiachenghg.com",
                            "IsMain": 1
                        }
                    ],
                    "IP": [
                        "*************"
                    ],
                    "Url": "www.jiachenghg.com",
                    "Language": [
                        "1"
                    ],
                    "ServiceType": [
                        "128"
                    ],
                    "ServiceContent": 1,
                    "PreAppoval": [],
                    "PICName": "刘勇平",
                    "Phone": "13829212987",
                    "PhonePre": "",
                    "Mobile": "13006832688",
                    "Email": "<EMAIL>",
                    "QQ": "",
                    "LicenseType": 2,
                    "LicenseId": "43062119881216183X",
                    "LicenseDate": "2022-06-09-长期",
                    "LicensePicture": [
                        "df319c249a0634fb13ae1b9e8f8dbfab83c30a2b.jpg"
                    ],
                    "CurtainPicture": [
                        "b3ca876fe579629dcce4be8b4e72df69ba231dbb.jpg"
                    ],
                    "RelevantPromiseLetter": [
                        "df319c249a0634fb13ae1b9e8f8dbfab83c30a2b.jpg"
                    ],
                    "AuthVerificationPicture": [
                        "da472a61d24cee0270cdf3adcd8c043a99c518fb.jpg"
                    ],
                    "OtherPicture": [],
                    "Remark": "应急联系人：【方林】，应急联系电话：【13829212987】",
                    "CreateTime": **********,
                    "UpdateTime": **********,
                    "EmergencyPhone": "13829212987",
                    "AppCode": null,
                    "ConnectType": 5,
                    "BaseManageprovince": "5",
                    "InternetServiceType": 1,
                    "IsDeleted": 0,
                    "CanOperated": false, // 是否可以 变更备案
                    "ConnectCanModify": true, // 是否可以 变更接入
                    "WebsiteCanModify": true,
                    "CanChange": true, //是否可以 变更网站
                    "DeleteWeb": true, //是否可以 注销网站
                }
            ]
        }
    ],
    "RetCode": 0,
    "Action": "DescribeICPMainResponse"
}




```

----------

### 开始变更订单

确定主体可变更后，取出返回体，结构中ICP元素，取第一个主体,使用备案号搜索，恒定只会有一个主体，如有多可请向备案组确认。

关注最外层的CanChange字段，如为true侧可操作，如为false,说明不可操作。请确定是否有流程中的订单
关注CMainId字段，如为-1 则说明备案主体未与工信部数据做匹配，请向备案组确认，执行同步后再做变更。

确认无误删除主体中无关的内容开始执行变更。

删除主体部分"CanOperated","CanModify","CanChange","DeleteMain","Id",“CompanyId”,"Status","IsDeleted"，"Website"字段。

删除完后，得到干净的可变更的备案结构体。增加备案类型标记 Type为8 ，状态标记Status为1，增加ICPId 字段（为刚刚删除的Id的值）.后更新其中需要更新的内容后。然后加上Action后发起请求

```javascript
{
    "Action": "CreateICPOrder",
    "Type": "8",
    "ICPId": "77878",
    "OrganizerLicenseId": "91441900MA4WDH6J4E",
    "request_uuid": "debc7641-34b6-48b0-81b7-cae18d0d3106",
    "OrganizerType": "4",
    "OrganizerName": "东莞市嘉诚化工有限公司",
    "OrganizerAddress": "广东省东莞市大岭山镇横镇西路81号201房",
    "OrganizerLicenseArea": "广东省东莞市大岭山镇横镇西路81号201房",
    "PICMainName": "余星",
    "PICMainOfficePhone": "",
    "PICMainOfficePhonePre": "",
    "PICMainMobile": "13537393538",
    "PICMainEmail": "<EMAIL>",
    "PICMainQQ": "",
    "PICMainLicenseType": "2",
    "PICMainLicenseId": "430621198710281822",
    "PICMainLicenseDate": "2022-06-09-长期",
    "EmergencyPhone": "13580979690",
    "AreaId": "441901",
    "CMainId": "4617577",
    "Status": "1",
    "EditStatus": "1",
    "UnitSuperior": "东莞市嘉诚化工有限公司",
    "OrganizerLicenseType": "1",
    "ICPMainNo": "粤ICP备17053391号",
    "Step": "1"
}


```

-----

# 开始修改订单

创建订单成功后，需要调用 DescribeICPOrder 获取该订单的最新信息，

请求结构

```javascript
{
    "Action": "DescribeICPOrder",
    "OrderNo": "O20211022171057007600",
    "FetchPicture": true //是否要获取图片，不获取 可不传
}

```

返回结构

```javascript
{
    "TotalCount": 1,
    "Order": [
        {
            "OrganizerType": 4,
            "OrganizerAddress": "广东省广州市白云区云城街齐富路62号522房",
            "OrganizerLicenseArea": "广州市白云区云城街齐富路62号522房",
            "OrganizerLicensePicture": [
                "9ca03191b7cc86e1ad8cf1501t9u90fa2b0ea5b7.jpg"
            ],
            "OrganizerResidencePermitPicture": [],
            "PICMainLicensePicture": [
                "214ee566a8f2768f11062839s3af138118496f50.jpg",
                "3b9e21853e50daeb2aafb2ara3856dde623f82ec.jpg"
            ],
            "PromiseLetterPicture": [],
            "EmergencyPhone": "135000355531",
            "PICMainMobile": "13826477776",
            "PICMainEmail": "<EMAIL>",
            "PICMainQQ": "",
            "AppCode": null,
            "UnitSuperior": "广州市卫健医疗器械有限公司",
            "UUID": null,
            "OrganizerUUID": null,
            "Id": 4661804,
            "AreaId": 440111,
            "ICPMainNo": "粤ICP备16066100号",
            "ICPWebNo": "",
            "OrderNo": "O20211022171057007600",
            "OrganizerName": "广州市医疗器械有限公司",
            "OrganizerLicenseType": 1,
            "PICMainName": "张三",
            "PICMainLicenseType": 2,
            "PICMainLicenseId": "445122177401204751",
            "PICMainLicenseDate": "2022-06-09-长期",
            "UnitCertificateExpDate": "(null)",
            "Status": 1,
            "CWebsiteId": -1,
            "Type": 8,
            "CreateTime": **********,
            "UpdateTime": **********,
            "OrganizerLicenseId": "91440221068168213G",
            "CMainId": 4676904,
            "SubmitReexamineType": 0,
            "Error": {},
            "OtherPicture": [],
            "IsExtractType": null,
            "EditStatus": "1",
            "CurtainStatus": 0,
            "FrontError": {},
            "Stash": {},
            "FrontPictureError": {},
            "Version": "4.6",
            "UpdateContent": [],
            "Remark": "(null)",
            "Website": [],
            "Picture": {
                "OrganizerLicensePicture": [
                    "9ca03191b7cc86e1adccf150109590fa2b0ea5b7.jpg"
                ],
                "PICMainLicensePicture": [
                    "214ee566a8f2768f1106283k63af838118496f50.jpg",
                    "3b9e21853e50daeb2hafb2a1a3856dde663f82ec.jpg"
                ],
                "OrganizerResidencePermitPicture": [],
                "PromiseLetterPicture": [],
                "Website": {}
            }
        }
    ],
    "Action": "DescribeICPOrderResponse",
    "RetCode": 0
}

```

取出Order中的信息

删除Website字段

将更新的内容覆盖获取到的信息
更改Status 为2。然后加上Action后发起请求

```javascript
{
    "Action": "ModifyICPOrder",
    "Id": 33532,  //订单Id
    "Type": "8",
    "OrderNo": "O20211022171057007600"
    "OrganizerLicenseId": "91441900MA4WDH6J4E",
    "OrganizerType": "4",
    "OrganizerName": "东莞市嘉诚化工有限公司",
    "OrganizerAddress": "广东省东莞市大岭山镇横镇西路81号201房",
    "OrganizerLicenseArea": "广东省东莞市大岭山镇横镇西路81号201房",
    "PICMainName": "余星",
    "PICMainOfficePhone": "",
    "PICMainOfficePhonePre": "",
    "PICMainMobile": "13537393538",
    "PICMainEmail": "<EMAIL>",
    "PICMainQQ": "",
    "PICMainLicenseType": "2",
    "PICMainLicenseId": "430621198710281822",
    "PICMainLicenseDate": "2022-06-09-长期",
    "EmergencyPhone": "13580979690",
    "AreaId": "441901",
    "CMainId": "4617577",
    "Status": "2",
    "EditStatus": "1",
    "UnitSuperior": "东莞市嘉诚化工有限公司",
    "OrganizerLicenseType": "1",
    "ICPMainNo": "粤ICP备17053391号",
    "Picture":"ewogICAgICAgICJQcm9taXNlTGV0dGVyUGljdHVyZSI6WwogICAgICAgICJkYjNjNmM4OTMwOTY5MGUwZTg5MzQ2NDY3ZmU4NTcwNjQwOGExNGM4LmpwZyIKICAgICAgICBdCiAgICB9"
}

```
