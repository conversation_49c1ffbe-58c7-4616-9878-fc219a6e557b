
# 通知功能api文档

标签（空格分隔）： 通知

----------

### 创建通知批次

### CreateNotifyBatch

请求结构

| 字段     | 类型    | 必填 | 描述                                                                  |
| -------- | ------- | ---- | --------------------------------------------------------------------- |
| Action   | string  | Yes  | CreateNotifyBatch                                                     |
| File     | string  | Yes  | 文件base64                                                            |
| FileName | string  | Yes  | 文件名称                                                              |
| Type     | integer | Yes  | 通知类型 0:过期域名通知,1:企业四要素通知，2：域名实名与备案不一致通知 |

返回结构
| 名称    | 类型   | 必返回 | 描述     |
| ------- | ------ | ------ | -------- |
| RetCode | int    | Yes    | 返回码   |
| Action  | string | Yes    | Action   |
| Message | string | No     | 出错原因 |

例如：

```javascript

{
    "RetCode": 0,
    "Message": "出错原因"
    "Action": "CreateNotifyBatchResponse"
}

```

### 获取批次列表

### GetNotifyBatchList

请求参数

| 字段      | 类型    | 必填 | 描述                   |
| --------- | ------- | ---- | ---------------------- |
| Action    | String  | Yes  | GetNotifyBatchList     |
| CompanyId | String  | No   | 公司ID                 |
| Domain    | String  | No   | 域名                   |
| Id        | Integer | No   | 批次Id                 |
| Type      | Integer | No   | 通知类型               |
| BeginTime | Int     | Yes  | 按时间查询，开始的时间 |
| EndTime   | Int     | Yes  | 按时间查询，结束的时间 |
| Offset    | Int     | Yes  | 位置                   |
| Limit     | Int     | Yes  | 条数                   |

返回参数

| 字段       | 类型      | 描述                       |
| ---------- | --------- | -------------------------- |
| RetCode    | Integer   | 返回码                     |
| Action     | String    | GetNotifyBatchListResponse |
| Rows       | Object [] | 通知批次信息               |
| TotalCount | Int       | 总数据条数                 |

```json
{
    "RetCode": 0,
    "Action": "GetNotifyBatchListResponse",
    "TotalCount": 121,
    "Rows": [
        {
            "Id": "批次id",
            "Remark": "批次名称",
            "Type": 1, // 见TypeEnum
            "Status": 1, //见BatchStatusEnum
            "CreateTime": "312423542345", //创建时间
        }
    ]
}



```

```javascript

const TypeEnum = {
    Expired: 0, // '过期域名通知',
    CompanyInfo: 1, // '企业四要素不一致通知', // 准备激活
    BeianInfo: 2, //'域名实名与备案不一致通知', // 已激活
    Unresource: 3, // 无资源有接入通知
}

const BatchStatusEnum = {
    PUll: 0, //数据获取中，
    Parsing: 1, //数据解析中，
    Parsed: 2, //已解析，
    Sending: 3, //发送中，
    SendFinish: 4, //发送完成，
    Finish: 5, //已完成
}

```

---

### 查看通知批次详情

### DescribeNotifyBatch

请求参数

| 字段      | 类型    | 必填 | 描述                |
| --------- | ------- | ---- | ------------------- |
| Action    | String  | Yes  | DescribeNotifyBatch |
| CompanyId | String  | No   | 公司ID              |
| Domain    | String  | No   | 域名                |
| Id        | Integer | No   | 批次Id              |
| Status    | Integer | No   | 记录的发送状态      |
| MainName  | String  | No   | 主体名称            |
| ICPMainNo | String  | No   | 主体备案号          |
| LicenseNo | String  | No   | 主体证件号          |
| ICPWebNo  | String  | No   | 网站备案号          |
| Offset    | Int     | Yes  | 位置                |
| Limit     | Int     | Yes  | 条数                |
| GetAll    | Boolean | No   | 是否获取全量        |

返回参数

| 字段        | 类型      | 描述                        |
| ----------- | --------- | --------------------------- |
| RetCode     | Integer   | 返回码                      |
| Action      | String    | DescribeNotifyBatchResponse |
| BatchStatus | Integer   | 批次状态                    |
| Type        | Integer   | 通知类型                    |
| Rows        | Object [] | 通知批次信息                |
| TotalCount  | Int       | 总数据条数                  |

```json
{
    "RetCode": 0,
    "Action": "DescribeNotifyBatchResponse",
    "TotalCount": 121,
    "Type": 1,
    "BatchStatus": 1,
    "Rows": [
        {
            "Id": "记录Id",
            "CompanyId": "公司Id",
            "CompanyName": "公司名称",
            "Status": 1, //记录状态 0待发送,1发送中,2发送完成,3 发送失败,4超时未响应,5禁止发送
            "ICPInfo": [
                {   // 域名过期通知 返回格式
                    "Domain": "xxxxx",
                    "ICPWebNo": "网站备案号",
                    "ExpiredTime": "2022-06-23",//域名过期时间
                    "Redeem": "xxxxx",//是否赎回
                    "Phone": "xxxxx",//网站负责人手机号
                    "Mail": "xxxxx", //网站负责人邮箱
                },
                {//企业四要素不一致通知 返回格式
                    "Domain": "xxxxx",
                    "ICPMainNo": "主体备案号",
                    "OrganizerName": "备案主体名称",
                    "OrganizerLicenseId": "主体证件号",
                    "OrganizerAddress": "主体证件地址",
                    "PICMainName": "主体负责人名称",
                    "PICMainLicenseId": "主体负责人证件号",
                    "PICMainEmail": "主体负责人邮箱",
                    "PICMainMobile": "主体负责人手机号",
                    "OrganizerNameRes": "主体名称检查结果",
                    "OrganizerLicenseIdRes":"主体证件号检查结果",
                    "OrganizerAddressRes": "主体证件地址检查结果",
                    "PICMainRes": "主体负责人结果",
                    "Result": "总体结果",
                    "Phone": "xxxxx",//主体负责人手机号
                    "Mail": "xxxxx", //主体负责人邮箱
                },
                {//域名实名与备案不一致通知 返回格式
                    "CompanyId": "公司Id",
                    "ICPWebNo": "网站备案号",
                    "OrganizerName": "备案主体名称",
                    "OrganizerLicenseType": "备案主体证件类型",
                    "OrganizerLicenseId": "证件号码",
                    "OrganizerNameRes": "名称对比结果",
                    "OrganizerLicenseTypeRes": "证件类型对比结果",
                    "OrganizerLicenseIdRes": "证件号对比结果",
                    "Registrant": "域名注册商",
                    "Phone": "xxxxx",//主体负责人手机号
                    "Mail": "xxxxx", //主体负责人邮箱
                }
            ],
            "CreateTime": "312423542345", //创建时间
        }
    ]
}
```

```javascript
const RecordStatusEnum = {
    New: 0, //待发送
    Sending: 1, //发送中
    Sendfinish: 2, //发送完成
    Sendfailed: 3, //发送失败
    Timeout: 4, //超时未响应
    Forbidden: 5, //禁止发送
}

```

### 获取通知详情

### GetNotifyDetail

请求参数

| 字段   | 类型    | 必填 | 描述            |
| ------ | ------- | ---- | --------------- |
| Action | String  | Yes  | GetNotifyDetail |
| Id     | Integer | No   | 公司记录Id      |

返回参数

| 字段         | 类型      | 描述                    |
| ------------ | --------- | ----------------------- |
| RetCode      | Integer   | 返回码                  |
| Action       | String    | GetNotifyDetailResponse |
| EmailContent | String    | 邮件内容                |
| SmsContent   | String    | 短信内容                |
| CompanyInfos | Object [] | 通知内容                |
| NotifyInfos  | Object [] | 通知记录                |

```json

{
    "RetCode": 0,
    "Action": "GetNotifyDetailResponse",
    "Type": 1, //通知类型,
    "EmailContent": "fsdfasdfas",
    "SmsContent": "ffsadfasdf",
    "CompanyInfos": [
        {   // 域名过期通知 返回格式
            "Domain": "xxxxx",
            "ICPWebNo": "网站备案号",
            "ExpiredTime": "2022-06-23",//域名过期时间
            "Redeem": "xxxxx",//是否赎回
            "Phone": "xxxxx",//网站负责人手机号
            "Mail": "xxxxx", //网站负责人邮箱
        },
        {//企业四要素不一致通知 返回格式
            "Domain": "xxxxx",
            "ICPMainNo": "主体备案号",
            "OrganizerName": "备案主体名称",
            "OrganizerLicenseId": "主体证件号",
            "OrganizerAddress": "主体证件地址",
            "PICMainName": "主体负责人名称",
            "PICMainLicenseId": "主体负责人证件号",
            "PICMainEmail": "主体负责人邮箱",
            "PICMainMobile": "主体负责人手机号",
            "OrganizerNameRes": "主体名称检查结果",
            "OrganizerLicenseIdRes":"主体证件号检查结果",
            "OrganizerAddressRes": "主体证件地址检查结果",
            "PICMainRes": "主体负责人结果",
            "Result": "总体结果",
            "Phone": "xxxxx",//主体负责人手机号
            "Mail": "xxxxx", //主体负责人邮箱
        },
        {//域名实名与备案不一致通知 返回格式
            "CompanyId": "公司Id",
            "ICPWebNo": "网站备案号",
            "OrganizerName": "备案主体名称",
            "OrganizerLicenseType": "备案主体证件类型",
            "OrganizerLicenseId": "证件号码",
            "OrganizerNameRes": "名称对比结果",
            "OrganizerLicenseTypeRes": "证件类型对比结果",
            "OrganizerLicenseIdRes": "证件号对比结果",
            "Registrant": "域名注册商",
            "Phone": "xxxxx",//主体负责人手机号
            "Mail": "xxxxx", //主体负责人邮箱
        }
    ],
    "NotifyInfos": [
        {
            "Id": 11232,
            "Contact": "xxxx", //手机号/邮箱
            "Type": 1, //0：手机号，1：邮箱
            "SendStatus": 1,//发送状态
            "UpdateTime": "23324234",//更新时间
        }
    ]
}

```

```javascript

const SendStatusEnum = {
    New: 0, //待发送
    Sending: 1, //发送中
    Sendfinish: 2, //已经接收
    Sendfailed: 3, //发送失败
    Recivefailed: 4, //接收失败
    Timeout: 5, //超时未响应
    Trytosending: 6, //尝试发送中
    Forbidden: 7, //禁止发送
}
```

### 通知批次

### NotifyBatch

请求参数

| 字段   | 类型    | 必填 | 描述        |
| ------ | ------- | ---- | ----------- |
| Action | String  | Yes  | NotifyBatch |
| Id     | Integer | Yes  | 批次id      |

返回参数

| 字段    | 类型    | 描述                |
| ------- | ------- | ------------------- |
| RetCode | Integer | 返回码              |
| Action  | String  | NotifyBatchResponse |
| Message | String  | 错误信息            |

### 完成处理

### FinishNotifyBatch

请求结构

| 字段    | 类型   | 必填 | 描述              |
| ------- | ------ | ---- | ----------------- |
| Action  | string | Yes  | FinishNotifyBatch |
| BatchId | int    | Yes  | 批次Id            |

返回结构
| 名称    | 类型   | 必返回 | 描述   |
| ------- | ------ | ------ | ------ |
| RetCode | int    | Yes    | 返回码 |
| Action  | string | Yes    | Action |

例如：

```javascript
{
    "RetCode": 0,
    "Action": "FinishNotifyBatchResponse",
}

```

### 重试发送通知

### RetryNotify

请求参数

| 字段   | 类型    | 必填 | 描述         |
| ------ | ------- | ---- | ------------ |
| Action | String  | Yes  | RetryNotify  |
| Id     | Integer | No   | 该条通知的id |

返回参数

| 字段    | 类型    | 描述                |
| ------- | ------- | ------------------- |
| RetCode | Integer | 返回码              |
| Action  | String  | RetryNotifyResponse |
| Message | String  | 错误信息            |

### 获取通知类型列表

### GetNotifyTypeList

请求参数

| 字段   | 类型    | 必填 | 描述              |
| ------ | ------- | ---- | ----------------- |
| Action | String  | Yes  | GetNotifyTypeList |
| Type   | Integer | No   | 该条通知的Type    |

返回参数

| 字段    | 类型     | 描述                      |
| ------- | -------- | ------------------------- |
| RetCode | Integer  | 返回码                    |
| Action  | String   | GetNotifyTypeListResponse |
| Rows    | Object[] | 类型信息                  |
| Message | String   | 错误信息                  |

```javascript

{
    "RetCode": 0,
    "Action": "GetNotifyTypeListResponse",
    "Rows": [
        {
            _id: "xxxxxxx",
            Type: 1,
            Name: "xxxx通知"
        }
    ]
}


```

### 获取通知模板列表

### GetNotifyTemplate

请求参数

| 字段   | 类型    | 必填 | 描述              |
| ------ | ------- | ---- | ----------------- |
| Action | String  | Yes  | GetNotifyTemplate |
| Type   | Integer | No   | 该条通知的Type    |

返回参数

| 字段    | 类型     | 描述                      |
| ------- | -------- | ------------------------- |
| RetCode | Integer  | 返回码                    |
| Action  | String   | GetNotifyTemplateResponse |
| Rows    | Object[] | 类型信息                  |
| Message | String   | 错误信息                  |

```javascript

{
    "RetCode": 0,
    "Action": "GetNotifyTemplateResponse",
    "Rows": [
        {
            _id: "xxxxxxx",
            Type: 1, //通知类型
            TypeName: "xxxx通知", // 通知类型名称
            NotifyType: 1,//短信or邮件，1：邮件，0：短信
            Name: "通知标题",
            TableTitle: {
                "主体名称": "xxxx",
                "备案号": "ICPMainNo",
                "首页地址或主域名": "AddressOrDomain"
            },
            Content: "xxxxxx", //通知内容，邮件为html,短信为文本
        }
    ]
}


```

### 修改模板

### ModifyTemplate

请求参数

| 字段       | 类型   | 必填 | 描述                       |
| ---------- | ------ | ---- | -------------------------- |
| Action     | String | Yes  | ModifyTemplate             |
| _id        | String | Yes  | 该模板的id                 |
| TableTitle | Object | No   | 通知信息中表格的中英文映射 |
| Content    | String | No   | 邮件/短信通知内容          |
| Name       | String | No   | 邮件通知标题               |

返回参数

| 字段    | 类型    | 描述                   |
| ------- | ------- | ---------------------- |
| RetCode | Integer | 返回码                 |
| Action  | String  | ModifyTemplateResponse |
| Message | String  | 错误信息               |

```javascript

{
    "RetCode": 0,
    "Action": "ModifyTemplateResponse"
}

```

### 修改通知类型的名称

### ModifyType

请求参数

| 字段   | 类型    | 必填 | 描述         |
| ------ | ------- | ---- | ------------ |
| Action | String  | Yes  | ModifyType   |
| Type   | Integer | Yes  | 通知类型     |
| Name   | String  | Yes  | 通知类型名称 |

返回参数

| 字段    | 类型    | 描述               |
| ------- | ------- | ------------------ |
| RetCode | Integer | 返回码             |
| Action  | String  | ModifyTypeResponse |
| Message | String  | 错误信息           |

```javascript

{
    "RetCode": 0,
    "Action": "ModifyTypeResponse"
}

```
