# 授权信息协议确认相关API

标签： 授权协议 确认 API

---

### 确认客户是否确认过授权 CheckAuthorizedInformationGather

确认此客户是否同意我司采集他的数据

#### 请求参数

| 字段      | 类型   | 必填 | 描述                               |
| --------- | ------ | ---- | ---------------------------------- |
| Action    | String | Yes  | CheckAuthorizedInformationGather   |
| CompanyId | Int    | Yes  | 必填，但不需要前端传入，网关会传的 |

#### 返回参数

| 字段         | 类型   | 描述                                |
| ------------ | ------ | ----------------------------------- |
| Action       | String | CheckAuthorizedInformationGathering |
| IsAuthorized | Bool   | 是否授权，True授权，false未授权     |
| RetCode      | Int    | 返回码                              |

---

### 确认客户是否确认过授权 SaveAuthorizedInformationGather

授权我们做信息采集，返回的状态码如果0为正确，如果不为0则异常

#### 请求参数

| 字段      | 类型   | 必填 | 描述                               |
| --------- | ------ | ---- | ---------------------------------- |
| Action    | String | Yes  | SaveAuthorizedInformationGather        |
| CompanyId | Int    | Yes  | 必填，但不需要前端传入，网关会传的 |

#### 返回参数

| 字段    | 类型   | 描述                        |
| ------- | ------ | --------------------------- |
| Action  | String | SaveAuthorizedInformationGather |
| RetCode | Int    | 返回码                      |
