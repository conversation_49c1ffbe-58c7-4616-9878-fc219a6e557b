# 核验相关API（Token部分）

标签（空格分隔）： 核验 授权 Token

---

H5页面的地址由授权Token与基础的客户信息组成


## 获取临时Token GetTemporaryToken 
调用API,得Token Id ，Token默认24小时有效，可刷新使用ResetToken 。当在H5提交完数据后。会自动失效
如果之前已存在Token，会自动取


请求参数
| 字段        | 类型   | 必填 | 描述              |
| ----------- | ------ | ---- | ----------------- |
| Action      | String | Yes  | GetTemporaryToken |
| LicenseId   | String | Yes  | 证件Id            |
| LicenseType | Int    | Yes  | 证件类型          |
| OrderNo     | String | Yes  | 订单号            |
 

返回参数


| 字段    | 类型   | 描述                      |
| ------- | ------ | ------------------------- |
| Action  | String | GetTemporaryTokenResponse |
| Token   | String | TokenId                   |
| RetCode | Int    | 状态码                    |


response
```json
{
    "Token": "d9bccdec-c9c0-4adb-b18b-fc6deabce934",
    "RetCode": 0,
    "Action": "GetTemporaryTokenResponse"
}
```


## 通过Token得到临时授权Sessoin GetTemporarySession 
调用API,通过Token Id 得到授权Session,Session因有效期问题，内容会一直更新


请求参数


| 字段   | 类型   | 必填 | 描述                |
| ------ | ------ | ---- | ------------------- |
| Action | String | Yes  | GetTemporarySession |
| Token  | String | Yes  | Token号             |

返回参数


| 字段    | 类型   | 描述                      |
| ------- | ------ | ------------------------- |
| Action  | String | GetTemporaryTokenResponse |
| Session | String | Session                   |
| RetCode | Int    | 状态码                    |


response
```json
{
    "Session": "VTJGc2RHVmtYMSswamdra1phaVh4M1Y0UWdlckN1MC9vWnFuVTRHUVR5UjZEWWJVZk9RRTJ5TURraHhjcnNmYW9aaTdvbGZteStpNlNJM0VyazF5Z0lyVUw4U0FTYmtYR2ZjTTV2UWpjTXBBY2NRcG5wWk1CNlhEYzFFK01JMS92enl4QVVXR3k0VXAyUHRsMUwzWXdUTzBDb0lxaXJRbXNxcFdPMmNDbnVDaGhmZWdya3NkOVUrVDlDcnJyK3M4aWF3R2MrNWFYQnFtNGNsMzBnNXZIdzdxOFJCWWJYWUtyWExUNmlWL1F4RzI4ZTgrSVFZdTgwRVFRcFl4Q1AwZ1k4bnB0TVc3RWM5Y1p2Y3NSVGxaWk1PcWdRbFpDNkVJL2xYL0h4dUduNzJEWW5QcjFCUUUyM0dnTzF5UlRjTno",
    "RetCode": 0,
    "Action": "GetTemporarySessionResponse"
}
```



## 通过Token确定是否有效 InquiryToken##
调用API,通过Token Id 确定Token是否在有效期

请求参数


| 字段   | 类型   | 必填 | 描述         |
| ------ | ------ | ---- | ------------ |
| Action | String | Yes  | InquiryToken |
| Token  | String | Yes  | Token号      |


返回参数


| 字段    | 类型   | 描述                 |
| ------- | ------ | -------------------- |
| Action  | String | InquiryTokenResponse |
| RetCode | Int    | 状态码               |


response
```json
{
    "RetCode": 0,
    "Action": "InquiryTokenResponse"
}
```


## 刷新Token ResetToken 
调用API,通过Token Id 重新生成授权Token

请求参数


| 字段        | 类型   | 必填 | 描述       |
| ----------- | ------ | ---- | ---------- |
| Action      | String | Yes  | ResetToken |
| LicenseId   | String | Yes  | 证件Id     |
| LicenseType | Int    | Yes  | 证件类型   |
| OrderNo     | String | Yes  | 订单号     |
| Token       | String | Yes  | Token号    |



返回参数


| 字段    | 类型   | 描述                 |
| ------- | ------ | -------------------- |
| Action  | String | InquiryTokenResponse |
| Token   | String | TokenId              |
| RetCode | Int    | 状态码               |


response
```json
{
    "Token": "d9bccdec-c9c0-4adb-b18b-fc6deabce934",
    "RetCode": 0,
    "Action": "ResetTokenResponse"
}
```

