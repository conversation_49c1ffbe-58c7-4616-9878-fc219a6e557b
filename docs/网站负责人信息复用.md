# 网站负责人信息复用功能

标签（空格分隔）： 信息复用 网站负责人

---

## 获取公司名下近20个可用的网站负责人信息 GetRecentWebOwnerInfo

支持搜索，输入名字为精确搜索，否则就展示近20个

请求参数

| 字段   | 类型   | 必填 | 描述           |
| ------ | ------ | ---- | -------------- |
| Action | String | Yes  | GetRecentWebOwnerInfo  |
| PICName | Int    | NO   | 网站负责人名字，默认不填，有就精确查找         |

返回参数

| 字段    | 类型   | 描述          |
| ------- | ------ | ------------- |
| Action  | String | GetRecentWebOwnerInfoResponse |
| PICList    | Array | 配置列表      |
| RetCode | Int    | 状态码        |

PICList

| 字段    | 类型   | 描述          |
| ------- | ------ | ------------- |
| PICName  | String | 网站负责人名称 |
| LicenseType    | String | 网站负责人证件类型      |
| LicenseId | String    | 网站负责人证件号        |
| Mobile | String    | 网站负责人手机        |
| EmergencyPhone | String    | 网站负责人证件号        |
| Email | String    | 网站负责人邮箱        |
| LicensePicture | String    | 网站负责人证件照片       |
| CurtainStatus | String    | 幕布照片        |

<!-- 负责人姓名、负责人证件类型、负责人证件号码、手机号码、应急联系电话、电子邮箱地址、网站负责人证件、最佳成像照 -->

```json
{
    "PICList": [
        {
            "PICName": "邹*君",
            "LicenseId": "522601010101",
            "LicenseType": 2,
            "Mobile": "18501010582",
            "LicensePicture": [
                "fec138243a9f90abaed834c86380f4f5d56eec05.jpg",
                "64680a5939026faf0966d6f3b473b449c9334d57.jpg"
            ],
            "CurtainPicture": [
                "159cdb0c53ce4ab8df9459def4d95dc5b057dce2.png"
            ]
        }
    ],
    "RetCode": 0
}
```
