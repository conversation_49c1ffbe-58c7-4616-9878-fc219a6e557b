# 下拉线上订单相关文档


支持通过订单号和目标公司ID,将线上订单下拉到灰度环境中


标签： 订单 线上 拉取数据到灰度

---





## 下载线上订单到本地    DownLoadOnlineOrder

通过运行环境判断，运行环境不能是production
通过传入的订单号判断本地是否有该订单，若有，则不能拉取
通过传入的订单号判断线上是否有该订单，若无，无法拉取
通过公司ID将该订单导入到灰度的该公司ID下

请求参数

| 字段      | 类型   | 必填 | 描述                |
| --------- | ------ | ---- | ------------------- |
| Action    | String | Yes  | DownLoadOnlineOrder |
| OrderNo   | String | Yes  | 订单号              |
| CompanyId | String | Yes  | 导入目标的公司ID    |
 


返回参数


| 字段    | 类型   | 描述                        |
| ------- | ------ | --------------------------- |
| Action  | String | DownLoadOnlineOrderResponse |
| Message | String | 具体不能下载下来数据的原因  |
| RetCode | Int    | 状态码                      |


response


```json
{
    "RetCode": 0
}

```
---


