# 新版备案后端，结合审核侧与用户侧

标签（空格分隔）： 检验 配置 在线 API

---

## 获取数据库中配置列表 GetConfigList

查询当前 Mongo 中的配置列表
请求参数

| 字段   | 类型   | 必填 | 描述           |
| ------ | ------ | ---- | -------------- |
| Action | String | Yes  | GetConfigList  |
| Offset | Int    | NO   | 偏移量         |
| Limit  | String | NO   | 长度           |
| Id     | String | NO   | 配置 Id,String |
| Name   | String | NO   | 配置名         |

返回参数

| 字段    | 类型   | 描述          |
| ------- | ------ | ------------- |
| Action  | String | GetConfigList |
| List    | Object | 配置列表      |
| Count   | String | 长度          |
| RetCode | Int    | 状态码        |

```json
{
  "List": [
    {
      "Config": {
        "main": {},
        "web": {}
      },
      "Name": "20200525Mmd",
      "UnixTime": 1590339094,
      "Id": "5ecaa61649188f076776af33"
    }
  ],
  "Count": 1,
  "RetCode": 0
}
```

### 查询当前线上 Redis 配置 DescribeOnlineConfig

查询当前线上 Redis 配置
请求参数

| 字段   | 类型   | 必填 | 描述             |
| ------ | ------ | ---- | ---------------- |
| Action | String | Yes  | DescribeOnlineConfig |

返回参数

| 字段    | 类型   | 描述             |
| ------- | ------ | ---------------- |
| Action  | String | DescribeOnlineConfig |
| Config  | Object | 配置列表         |
| RetCode | Int    | 状态码           |

```json
{
  "Config": [
    {
      "Id": "5ecaa61649188f076776af33",
      "Config": {
        "main": {
          "AreaId": [
            {
              "name": "必填",
              "rule": "required",
              "value": true,
              "remark": ""
            }
          ],
          "OrganizerLicenseId": [
            {
              "name": "必填",
              "rule": "required",
              "value": true,
              "remark": ""
            },
            {
              "name": "长度",
              "rule": "length",
              "value": 18,
              "remark": ""
            },
            {
              "name": "身份证号",
              "rule": "fun",
              "value": "idCheck",
              "remark": ""
            },
            {
              "notTypes": [1, 2],
              "province": [23, 21],
              "orgTypes": [5],
              "rule": "fun",
              "value": "idCheck",
              "remark": ""
            }
          ]
        },
        "web": {
          "IP": [
            {
              "name": "必填",
              "rule": "required",
              "value": true,
              "remark": ""
            },
            {
              "name": "类型",
              "rule": "lodash",
              "value": "isArray",
              "remark": ""
            }
          ]
        }
      },
      "Name": "20200525Mmd",
      "UnixTime": 1590339094
    }
  ],
  "RetCode": 0
}
```

### 编辑配置 EditConfig

编辑 Mongo 中保存的配置，如无 Id 系统会新建记录，如有则会在原 Id 上更新。

`Config需要完整传入`

请求参数

| 字段   | 类型   | 必填 | 描述           |
| ------ | ------ | ---- | -------------- |
| Action | String | Yes  | EditConfig     |
| Id     | String | NO   | 配置 Id,String |
| Name   | String | NO   | 配置名         |
| Config | Object | Yes  | 配置           |

返回参数

| 字段    | 类型   | 描述       |
| ------- | ------ | ---------- |
| Action  | String | EditConfig |
| RetCode | Int    | 状态码     |

```json
{
  "RetCode": 0
}
```

### 编辑配置 ApplyConfig

传入 Mongo 中配置的 Id ,更新至 Redis 中。原 Redis 记录保存至 Mongo 成为历史配置，不允许更新

`Config需要完整传入`

请求参数

| 字段   | 类型   | 必填 | 描述           |
| ------ | ------ | ---- | -------------- |
| Action | String | Yes  | EditConfig     |
| Id     | String | NO   | 配置 Id,String |

返回参数

| 字段    | 类型   | 描述       |
| ------- | ------ | ---------- |
| Action  | String | EditConfig |
| RetCode | Int    | 状态码     |

```json
{
  "RetCode": 0
}
```

### 测试配置 TestConfig

传入订单号与配置 Id,测试是否能通过规校验

`Config需要完整传入`

请求参数

| 字段    | 类型   | 必填 | 描述       |
| ------- | ------ | ---- | ---------- |
| Action  | String | Yes  | TestConfig |
| Id      | String | NO   | 配置 Id    |
| OrderNo | String | NO   | 订单号     |

返回参数

| 字段        | 类型   | 描述       |
| ----------- | ------ | ---------- |
| Action      | String | TestConfig |
| RetCode     | Int    | 状态码     |
| CheckResult | Object | 状态码     |

```json
{
  "CheckResult": {
    "Website": [
      {
        "URL": "该字段不能为空！"
      }
    ]
  },
  "RetCode": 0
}
```

### 测试配置 DeleteConfig

删除配置

请求参数

| 字段   | 类型   | 必填 | 描述         |
| ------ | ------ | ---- | ------------ |
| Action | String | Yes  | DeleteConfig |
| Id     | String | NO   | 配置 Id      |

返回参数

| 字段    | 类型   | 描述         |
| ------- | ------ | ------------ |
| Action  | String | DeleteConfig |
| RetCode | Int    | 状态码       |

```json
{
  "RetCode": 0
}
```

---

查询规则项 DescribeRule

请求参数

| 字段   | 类型   | 必填 | 描述                        |
| ------ | ------ | ---- | --------------------------- |
| Action | String | Yes  | DescribeRule                |
| Enable | Bool   | NO   | 是否为线上配置，默认为 true |

返回参数

| 字段    | 类型   | 描述         |
| ------- | ------ | ------------ |
| Action  | String | DescribeRule |
| RetCode | Int    | 状态码       |

Request

```json
{
  "Action": "DescribeRule"
}
```

Response

```json
{
  "RetCode": 0,
  "RuleList": [
    {
      "Id": "xxxxxx",
      "Operator": "william.qian",
      "Enable": true,
      "Rule": {
        "DOMAIN_HOME_URL_IS_SINGLE": {
          "provinces": [
            11,
            12,
            13,
            14,
            15,
            21,
            22,
            23,
            31,
            32,
            33,
            34,
            35,
            36,
            37,
            41,
            42,
            43,
            44,
            45,
            46,
            50,
            52,
            53,
            54,
            62,
            63,
            64,
            65
          ]
        }
      }
    }
  ]
}
```

---

查询规则项 EditRule

请求参数

| 字段   | 类型   | 必填 | 描述                        |
| ------ | ------ | ---- | --------------------------- |
| Action | String | Yes  | EditRule                    |
| Rule   | Object | Yes  | 配置列表                    |
| Id     | String | Yes  | Id,传入为更新，不传入为新建 |
| Name   | String | No   | 配置名，如果新建可以传入    |

返回参数

| 字段    | 类型   | 描述     |
| ------- | ------ | -------- |
| Action  | String | EditRule |
| RetCode | Int    | 状态码   |

Request

```json
{
  "Action": "EditRule",
  "Id": "xxxxxx",
  "Rule": {
    "DOMAIN_HOME_URL_IS_SINGLE": {
      "provinces": [
        11,
        12,
        13,
        14,
        15,
        21,
        22,
        23,
        31,
        32,
        33,
        34,
        35,
        36,
        37,
        41,
        42,
        43,
        44,
        45,
        46,
        50,
        52,
        53,
        54,
        62,
        63,
        64,
        65
      ]
    }
  }
}
```

Response

````json
{
    "RetCode":0
}



-------

### 上线配置 ApplyRule

上线配置

请求参数

| 字段   | 类型   | 必填 | 描述               |
| ------ | ------ | ---- | ------------------ |
| Action | String | Yes  | ApplyRuleApplyRule |
| Id     | String | NO   | 配置Id             |


返回参数

| 字段    | 类型   | 描述      |
| ------- | ------ | --------- |
| Action  | String | ApplyRule |
| RetCode | Int    | 状态码    |



```json
{
    "RetCode": 0
}

````

---

### 获取验证日志 GetOrderVerifyLog

获取验证日志

请求参数

| 字段    | 类型   | 必填 | 描述              |
| ------- | ------ | ---- | ----------------- |
| Action  | String | Yes  | GetOrderVerifyLog |
| OrderNo | String | Yes  | 订单号            |

返回参数

| 字段          | 类型   | 描述      |
| ------------- | ------ | --------- |
| Action        | String | ApplyRule |
| RetCode       | Int    | 状态码    |
| VerifyLogList | Array  | 状态码    |

```json
{
  "RetCode": 0,
  "VerifyLogList": [
    {
      "DataContent": {
        "Type": 7,
        "CompanyName": "优刻得科技股份有限公司",
        "CompanyCode": "91310110591673062R",
        "LegalEntityName": "季昕华",
        "LegalEntityId": "332502197903295170",
        "CompanyId": 3019
      },
      "Response": {
        "Type": 7,
        "CompanyName": "优刻得科技股份有限公司",
        "CompanyCode": "91310110591673062R",
        "LegalEntityName": "季昕华",
        "LegalEntityId": "332502197903295170",
        "CompanyId": 3019
      },
      "UpdateTime": "1594891194",
      "Result": "0",
      "Operator": "User",
      "Id": 1842,
      "CreateTime": "1594891194",
      "Type": 7,
      "Multiplex": 1,
      "CompanyId": 3019
    }
  ]
}
```

---

### 验证企业四要素信息 CheckEpicEntPersonRelation

获取验证日志

请求参数

| 字段            | 类型   | 必填 | 描述                          |
| --------------- | ------ | ---- | ----------------------------- |
| Action          | String | Yes  | GetOrderVerifyLog             |
| Order           | String | No   | 订单号                        |
| CompanyName     | String | Yes  | 公司名                        |
| CompanyCode     | String | Yes  | 公司 Id                       |
| LegalEntityName | String | Yes  | 法人名                        |
| LegalEntityId   | String | Yes  | 法人证件号                    |
| CompanyId       | String | Yes  | 公司 Id                       |
| \_\_ssoUser     | String | Yes  | 不需要手工传，由 SSO 自动增加 |

返回参数

| 字段    | 类型   | 描述                                |
| ------- | ------ | ----------------------------------- |
| Action  | String | CheckEpicEntPersonRelation              |
| RetCode | Int    | 状态码                              |
| IsMatch | Bool   | 是否匹配                            |
| Message | String | 出错描述,如何验证失败，会有内容提示 |

```json
{
  "IsMatch": true,
  "Message": "如何验证失败，会有内容提示",
  "RetCode": 0,
  "Action": "CheckEpicEntPersonRelationResponse"
}
```

---

### 个人证件 OCR GetIdenityOCRInfo

获取验证日志

请求参数

| 字段        | 类型   | 必填 | 描述                          |
| ----------- | ------ | ---- | ----------------------------- |
| Action      | String | Yes  | GetOrderVerifyLog             |
| Order       | String | No   | 订单号                        |
| PictureName | String | Yes  | 图片名                        |
| CompanyId   | String | Yes  | 公司 Id                       |
| \_\_ssoUser | String | Yes  | 不需要手工传，由 SSO 自动增加 |

返回参数

| 字段       | 类型   | 描述                                |
| ---------- | ------ | ----------------------------------- |
| Action     | String | CheckEpicEntPersonRelation              |
| RetCode    | Int    | 状态码                              |
| IsLegality | Bool   | 是否合法（证件可靠性）              |
| Adults     | Bool   | 是否成年                            |
| Info       | Object | 识别内容                            |
| Message    | String | 出错描述,如何验证失败，会有内容提示 |

```json
{
  "IsLegality": true,
  "Legality": {
    "Edited": 0,
    "Photocopy": 0,
    "ID Photo": 1,
    "Screen": 0,
    "Temporary ID Photo": 0
  },
  "Info": {
    "Address": "江苏省张家港市金港镇福民村东片三组7号",
    "Birthday": "1992-02-29",
    "Gender": "男",
    "IdCardNumber": "32058219920229421X",
    "Name": "钱俊烨",
    "Race": "汉",
    "Type": 1,
    "Side": "front"
  },
  "UUID": "767f0811-d20c-4b05-86fd-acf434f5144b",
  "RetCode": 0,
  "Action": "GetIdenityOCRInfoResponse",
  "Adults": true,
  "IsMatch": true
}
```

### 验证视频中是否为同一人，是否光线过暗，把后返回最佳成像照片 CheckVideoAndGetBestPicture

获取验证日志

请求参数

| 字段        | 类型   | 必填 | 描述                                               |
| ----------- | ------ | ---- | -------------------------------------------------- |
| Action      | String | Yes  | CheckVideoAndGetBestPicture                        |
| OrderNo     | String | Yes  | 订单号                                             |
| VideoName   | String | Yes  | 视频名                                             |
| UUID        | String | Yes  | 公司 UUID                                          |
| ForceSelect | String | No   | 是否强制查询，如不加，默认会取同视频最近成功的记录 |

返回参数

| 字段      | 类型   | 描述                        |
| --------- | ------ | --------------------------- |
| Action    | String | CheckVideoAndGetBestPicture |
| RetCode   | Int    | 状态码                      |
| IsMatch   | Bool   | 是否匹配                    |
| Message   | Bool   | 如不匹配会有出错信息        |
| Multiplex | Object | 是否复用了数据              |

```json
{
  "IsMatch": false,
  "Multiplex": 1,
  "Message": "视频中存在相似度过低的脸",
  "result_ref1": {
    "confidence": 94.57,
    "thresholds": {
      "1e-3": 62.169,
      "1e-5": 74.399,
      "1e-4": 69.315,
      "1e-6": 78.038
    }
  },
  "result_ref2": {
    "confidence": 68.838,
    "thresholds": {
      "1e-3": 62.169,
      "1e-5": 74.399,
      "1e-4": 69.315,
      "1e-6": 78.038
    }
  },
  "result_ref3": {
    "confidence": 73.728,
    "thresholds": {
      "1e-3": 62.169,
      "1e-5": 74.399,
      "1e-4": 69.315,
      "1e-6": 78.038
    }
  },
  "request_id": "1597655884,88f77cf1-db0d-4db6-8920-a001659c48ba",
  "time_used": 1957,
  "RetCode": 0
}
```
