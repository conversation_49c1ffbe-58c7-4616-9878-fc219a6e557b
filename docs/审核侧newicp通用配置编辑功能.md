# 审核侧 newicp 通用配置编辑功能

标签（空格分隔）： 审核侧 common ICPConfig

---

替换 3 个项目中，保存在本地的 JSON 文件。数据统一保存在 Redis.可在线完成更新

---

### DescribeICPConfig

| 字段   | 类型   | 必填 | 描述              |
| ------ | ------ | ---- | ----------------- |
| Action | String | Yes  | DescribeICPConfig |

仅需传入 Action,无其他入参。返回通用的配置

---

### DescribeICPConfigList

从 Mongo 中取出历史全部版本的配置记录

请求参数

| 字段   | 类型   | 必填 | 描述                    |
| ------ | ------ | ---- | ----------------------- |
| Action | String | Yes  | DescribeICPConfigList   |
| Offset | Int    | NO   | 偏移量，默认为 0        |
| Limit  | Int    | NO   | 数据返回长度，默认 20， |

返回值

| 字段       | 类型          | 描述                  |
| ---------- | ------------- | --------------------- |
| Action     | String        | DescribeICPConfigList |
| ConfigList | Array[Object] | 配置列表              |
| Count      | String        | 数据总长度            |
| RetCode    | Int           | 状态码                |



```JSON

{
  "Action" : "DescribeICPConfigList",
  "ConfigList" : [
    {
      "Remark" : "",
      "Id" : "xx1difadslkfj23",
      "Config" : {}
    }
  ],
  "Count" : 25,
  "RetCode" : 0
}


```

其中 Config 为具体的配置，Remark，标记本次更新的作用，Id 供应用配置时做为入参

---

### EditICPConfig

当用户点击编辑时，前端取出指定记录供用户操作编辑，也可从当前线上版本取出。前端编辑完成。点击保存，调用此 API

| 字段    | 类型   | 必填   | 描述              |
| ------- | ------ | ------ | ----------------- |
| Action  | String | Yes    | DescribeICPConfig |
| Config  | Object | Yes    | 编辑后的配置      |
| RetCode | Int    | 状态码 |



返回值

| 字段   | 类型   | 描述          |
| ------ | ------ | ------------- |
| Action | String | EditICPConfig |
| Id     | String | 保存后的 Id   |

结构简单，不提供示例的返回 JSON

---

### ApplyICPConfig，

指定 Mongo 中的某条记录，发布到线上

底层操作如下

1.  取出 Id 对应在 Mongo 中的记录
2.  取出线上 Redis 目前的记录
3.  取出的 Mongo� 新记录更新至 Redis
4.  Redis 中的记录保存在 Mongo.Remark 为“YYYY-mm-DD 线上记录”

| 字段   | 类型   | 必填 | 描述              |
| ------ | ------ | ---- | ----------------- |
| Action | String | Yes  | DescribeICPConfig |
| Id     | Object | Yes  | 需要发布的记录    |

返回值

| 字段    | 类型   | 描述          |
| ------- | ------ | ------------- |
| Action  | String | EditICPConfig |
| Id      | String | 保存后的 Id   |
| RetCode | Int    | 状态码        |


### UpdateStatus

支持对订单、已备案主体、已备案网站的状态做更新


| 字段   | 类型   | 必填 | 描述                                                                                           |
| ------ | ------ | ---- | ---------------------------------------------------------------------------------------------- |
| Action | String | Yes  | UpdateStatus                                                                                   |
| Type   | String | Yes  | 'Order'、'ICP'、'Web'                                                                          |
| Key    | Object | Yes  | 如果Type是'Order'，如果是'ICP'则为ICPMainNo，如果是网站则为ICPWebNo                            |
| Status | Object | Yes  | 需要更新的目标状态值，从DescribeICPConfig取如果是Order取OrderStatus，如果是ICP与Web取ICPStatus |

返回值

| 字段    | 类型   | 描述          |
| ------- | ------ | ------------- |
| Action  | String | EditICPConfig |
| Type    | String | 保存后的 Id   |
| RetCode | Int    | 状态码        |

