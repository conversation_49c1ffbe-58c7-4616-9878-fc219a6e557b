# 域名注册信息查看与校验API

标签： 域名信息 查看 对比
URL: <https://api.ucloud.cn/>
签名生成文档： <https://docs.ucloud.cn/api/summary/signature>

---

## 查看域名注册信息    GetDomainRegisterMsg

以域名为条件，通过查看该接口域名注册信息，返回返回域名持有者（中文）、域名持有者（英文）、注册商名称、注册时间、更新时间、过期时间、IANA标记、批复状态、域名注册是否过期等信息。

请求类型 POST

请求参数

| 字段   | 类型   | 必填 | 描述                 |
| ------ | ------ | ---- | -------------------- |
| Action | String | Yes  | GetDomainRegisterMsg |
| UUID   | String | Yes  | UUID                 |
| Domain | String | Yes  | 域名                 |

返回参数

| 字段          | 类型     | 描述                 |
| ------------- | -------- | -------------------- |
| Action        | String   | GetDomainRegisterMsg |
| RetCode       | Int      | 状态码               |
| IsExpired     | Boolean  | 是否过期             |
| DetailResults | Object[] | 详细结果             |
| Message       | String   | 错误信息             |

response

```javascript

{
    "ReportStatus": 0,//1：未报送，0：已报送
    "IsExpired": false, //true:过期， false:未过期
    "DetailResults": [
        {
            //域名持有者（中文）
            "domainHolders": "工业和信息化部信息中心",
            //域名持有者（英文）
            "domainHoldersForEnglish": " Information center of Ministry of industry and information technolog",
            //注册商名称
            "registrar": "中国互联网络信息中心",
            //注册时间
            "registrationTime": "2009-10-24 10:16:11",
            //更新时间
            "updateTime": "2019-10-24 10:16:11",
            //过期时间
            "expirationTime": "2029-10-24 10:16:11",
            // IANA标记
            "ianaTags": "W859S436S6892A",
            //批复状态：已注册
            "approvalStatus ": "已注册",
            // 更新时间,接入商上报更新时间
            "ispUpdateTime": "2019-10-24 10:16:11"
        }
    ],
    "Action": "GetDomainRegisterMsgResponse",
    "RetCode": 0
}

```

---

## 对比域名注册信息是否一致 CheckDomainRegisterMsg

以域名、域名持有者(姓名/企业名称)、证件类型、证件号码为条件，通过该接口与支撑平台域名注册信息比对，返回域名持有者(姓名/企业名称)、证件类型、证件号码比对结果。

请求类型 POST

请求参数

| 字段                    | 类型   | 必填     | 描述                                                                          |
| ----------------------- | ------ | -------- | ----------------------------------------------------------------------------- |
| Action                  | String | Yes      | CheckDomainRegisterMsg                                                        |
| UUID                    | String | Yes      | UUID                                                                          |
| TypeTranslate           | Bool   | Yes      | 是否需要证件类型转义，直接用订单中的类型的话需要打开                          |
| DomainInfo.0.domain     | String | Yes      | 域名信息                                                                      |
| DomainInfo.0.registrant | String | Yes      | 注册者信息                                                                    |
| DomainInfo.0.certType   | Int    | Yes      | 证件类型（审核侧，TypeTranslate为true后从订单中直接取，用户侧参考下方类型表） |
| DomainInfo.0.certNo     | String | 证件号码 |

request

```javascript
// 用户侧调用因签名问题，需要将数字拆开
{
    
    "UUID": "XXXXXXXXXXXXXXXXXXX",
    "DomainInfo.0.domain": "miit.gov.cn",
    "DomainInfo.0.registrant": "工业和信息化部信息中心",
    "DomainInfo.0.certType": 13,
    "DomainInfo.0.certNo": "12100000400001360B",
    "DomainInfo.1.domain": "miit.gov.cn",
    "DomainInfo.1.registrant": "淘宝（中国）软件有限公司",
    "DomainInfo.1.certType": 13,
    "DomainInfo.1.certNo": "330100400015044"
}

// 审核侧直接使用便可
{
    "OrderNo": "O20220621154324008582",
    "CompanyId": 3019,
    "DomainInfo": [
        {
            "domain": "ucloud365.com",
            "registrant": "优刻得科技股份有限公司",
            "certType": 3,
            "certNo": "91310110591673062R"
        },
        {
            "domain": "ucloud.cn",
            "registrant": "优刻得科技股份有限公司",
            "certType": 3,
            "certNo": "91310110591673062R"
        },
        {
            "domain": "facebook.com",
            "registrant": "钱俊烨",
            "certType": 1,
            "certNo": "320582199202294111"
        }
    ]
}
```

返回参数

| 字段    | 类型     | 描述                   |
| ------- | -------- | ---------------------- |
| Action  | String   | CheckDomainRegisterMsg |
| RetCode | Int      | 状态码                 |
| CompRes | Object[] | 检查结果               |

response

```javascript
{
    IsMatch: true,
    CompRes: [
        //对比结果
        {
            Domain: 'miit.gov.cn', //注册域名
            ReportStatus: 1, //0：均已报送 ，1：均未报送，2：注册商已报送，注册局未报送，3：注册商未报送，注册局已报送。
            Results: null, //0:均一致，1：均不一致，2：与注册商一致，注册局不一致，与注册局一致，注册商不一致 3
            RegistrarContrastDetails: null, // 与注册商对比明细
            RegistryContrastDetails: null, // 与注册局对比明细
            Contras: {
                //对比结果，确定域名中有某项对不对，参考此值
                registrant: false, // 注册者是否一致（公司名，人名）
                certType: false, // 注册者证件类型是否一致
                certNo: false, // 注册者证件号是否一致
            },
        },
        {
            Domain: 'taobao.com',
            ReportStatus: 0,
            Results: 1,
            Contras: {
                registrant: true,
                certType: true,
                certNo: false,
            },
            // 与注册商对比明细
            registrarContrastDetails: [
                {
                    //注册者比对结果
                    registrant: '淘宝（中国）软件有限公司',
                    //比对结果：一致 0；不一致 1；
                    results: 0,
                },
                {
                    //证件类型：使用和部里一致的字典枚举；
                    certType: 13,
                    //比对结果：一致 0；不一致 1；
                    results: 0,
                },
                {
                    //证件号码比对结果
                    certNo: '330100400015044',
                    //比对结果：一致 0；不一致 1；
                    results: 0,
                },
            ],
            //与注册局比对明细
            RegistrarContrastDetails: [
                {
                    //注册者比对结果
                    registrant: '阿里巴巴（中国）网络技术有限公司',
                    //比对结果：一致 0；不一致 1；
                    results: 1,
                    reportRegistrant: '淘宝（中国）软件有限公司',
                },
                {
                    //证件类型：使用和部里一致的字典枚举；
                    certType: 13,
                    //比对结果：一致 0；不一致 1；
                    results: 0,
                },
                {
                    //证件号码比对结果
                    certNo: '330100500015044',
                    //比对结果：一致 0；不一致 1；
                    results: 1,
                    reportCertNO: '330100400015044',
                },
            ],
        },
    ],
    Action: 'CheckDomainRegisterMsgResponse',
    RetCode: 0,
}


```

### 证件类型map

```javascript

const certType = {
    1: '居民身份证',
    2: '组织机构代码证',
    3: '营业执照（个人或企业）',
    4: '统一社会信用代码证书',
    5: '护照',
    6: '港澳居民来往内地通行证',
    7: '其他',
    8: '部队代号',
    9: '军队单位对外有偿服务许可证',
    10: '事业单位法人证书',
    11: '外国企业常驻代表机构登记证',
    12: '台湾居民来往大陆通行证',
    13: '外国人永久居留身份证',
    14: '社会团体法人登记证书',
    15: '宗教活动场所登记证',
    16: '民办非企业单位登记证书',
    17: '基金会法人登记证书',
    18: '律师事务所执业许可证',
    19: '外国在华文化中心登记证',
    20: '外国政府旅游部门常驻代表机构批准登记证',
    21: '司法鉴定许可证',
    22: '境外机构证件',
    23: '社会服务机构登记证书',
    24: '民办学校办学许可证',
    25: '医疗机构执业许可证',
    26: '公证机构执业证',
    27: '北京市外国驻华使馆人员子女学校办学许可证',
    28: '港澳居民居住证',
    29: '台湾居民居住证',
    30: '其他',
}

```
