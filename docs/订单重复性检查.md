# 订单重复性检查

标签（空格分隔）：订单，重复检查

---

订单重复性检查，主体信息，网站信息，分开检查


----------



### 主体重复性检查

请求结构

| 字段    | 类型   | 必填 | 描述                             |
| ------- | ------ | ---- | -------------------------------- |
| Action  | string | Yes  | CheckPICMainPersonRepetitionInfo |
| OrderNo | string | Yes  | 需要查询的订单号                 |


返回结构

```javascript

{
    "OrderList": {
        "O20181015182916006534": {
            "PICMainMobile": true,
            "EmergencyPhone": true
        },
        "O20200306150623007494": {
            "PICMainLicenseId": true
        },
        "O20200317232917003182": {
            "PICMainLicenseId": true
        },
        "O20200630100357005531": {
            "PICMainLicenseId": true
        },
        "O20210224134447004362": {
            "PICMainMobile": true
        }
    },
    "ICPList": {
        "苏ICP备15005111号": {
            "PICMainMobile": true,
            "PICMainLicenseId": true,
            "EmergencyPhone": true,
            "PICMainEmail": true
        }
    },
    "RetCode": 0
}




```



----------


### 网站重复性检查

请求结构

| 字段    | 类型   | 必填 | 描述                        |
| ------- | ------ | ---- | --------------------------- |
| Action  | string | Yes  | CheckWebOwnerRepetitionInfo |
| OrderNo | string | Yes  | 需要查询的订单号            |


返回结构

```javascript

{
    "OrderList": {
        "O20181015182916006534": {
            "PICMainMobile": true,
            "EmergencyPhone": true
        },
        "O20200306150623007494": {
            "PICMainLicenseId": true
        },
        "O20200317232917003182": {
            "PICMainLicenseId": true
        },
        "O20200630100357005531": {
            "PICMainLicenseId": true
        },
        "O20210224134447004362": {
            "PICMainMobile": true
        }
    },
    "ICPList": {
        "苏ICP备15005111号": {
            "PICMainMobile": true,
            "PICMainLicenseId": true,
            "EmergencyPhone": true,
            "PICMainEmail": true
        }
    },
    "RetCode": 0
}




```