
备案系统V4.2用户侧API，增加注销主体，注销网站

## API

----


### CreateICPOrder
请求接口后，生成订单


请求参数

| 字段   | 类型 | 必填 | 描述     |
| ------ | ---- | ---- | -------- |
| Type   | Int  | Yes  | 备案类型 |
| Status | int  | Yes  | 状态     |



备案类型枚举值

| 字段 | 描述       |
| ---- | ---------- |
| 4    | 注销主体   |
| 5    | 注销网站   |
| 6    | 取消转接入 |


注销理由枚举值

| 字段 | 描述         |
| ---- | ------------ |
| 1    | 主动申请注销 |
| 2    | 有违规行为   |
| 3    | 停办网站     |
| 4    | 其它         |



状态枚举值

| 字段 | 描述           |
| ---- | -------------- |
| 1    | 编辑中         |
| 2    | 等待初审       |
| 3    | 初审通过       |
| 4    | 初审打回       |
| 7    | 等待复审       |
| 8    | 复审通过       |
| 9    | 复审打回       |
| 11   | 管局审核中     |
| 12   | 管局审核通过   |
| 13   | 管局审核不通过 |
| 15   | 管局退回暂存   |

#### Response
| 字段    | 类型 | 必填 | 描述     |
| ------- | ---- | ---- | -------- |
| RetCode | int  | Yes  | 错误码 0 |


请求结构体

#### `Request Example`

申请注销网站

```json
{
    "Type": 5,
    "Status": 1,
    "ICPWebNo": "测滇ICP备18010343号-1",
    "ReasonId": 1,
    "Remark": "申请注销"
}
```


申请注销主体
   
```json
{
    "Type": 4,
    "Status": 11,
    "ICPMainNo": "测滇ICP备18010343",
	"ICPMainPassword":"123"
    "ReasonId": 1,
    "Remark": "申请注销"
}
```

申请注销接入

```json
{
    "Type": 5,
    "Status": 1,
    "ICPWebNo": "测滇ICP备18010343号-1",
    "ReasonId": 1,
    "Remark": "申请注销"
}
```
----








----





