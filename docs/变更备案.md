# 变更备案

标签（空格分隔）： 变更，备案

---

变更备案的前提是需要已完成备案。
因此需要先使用DescribeICPMain取到已备案的数据,删除部分数据后。使用CreateICPOrder接口创建一个订单类型为7的变更订单。

----------

### 获取需要变更的主体内容

请求结构

| 字段      | 类型   | 必填 | 描述                 |
| --------- | ------ | ---- | -------------------- |
| Action    | string | Yes  | DescribeICPMain      |
| ICPMainNo | string | Yes  | 需要变更的主体备案号 |

返回结构

```javascript

{
    "TotalCount": 1,
    "ICP": [
        {
            "Id": 77878,
            "Status": 7,
            "CMainId": 4617577,
            "CompanyId": 34278,
            "ICPMainNo": "粤ICP备17053391号",
            "AreaId": 441901,
            "OrganizerType": 4,
            "OrganizerName": "东莞市嘉诚化工有限公司",
            "OrganizerAddress": "广东省东莞市大岭山镇大岭山社区西园街33号",
            "OrganizerLicenseType": 1,
            "OrganizerLicenseId": "91441900MA4WDH6J4E",
            "OrganizerLicenseArea": "东莞市大岭山镇大岭山社区西园街33号",
            "PICMainName": "余星",
            "PICMainOfficePhone": "",
            "PICMainOfficePhonePre": "",
            "PICMainMobile": "13728426748",
            "PICMainEmail": "<EMAIL>",
            "PICMainQQ": "",
            "PICMainLicenseType": 2,
            "PICMainLicenseId": "430621198710281822",
            "PICMainLicenseDate": "2022-06-09-长期",
            "OrganizerLicensePicture": [
                "f112bf034fcd51f00d40962c82673c8631e95fd6.jpg"
            ],
            "OrganizerResidencePermitPicture": [],
            "PICMainLicensePicture": [
                "c67799748f9001875f5993e17180dd144e990fb5.jpg"
            ],
            "OtherPicture": [
                "1c8403395be455efc7930c6505e55375d685443c.jpg"
            ],
            "Remark": null,
            "CreateTime": **********,
            "UpdateTime": **********,
            "IsDeleted": 0,
            "UnitSuperior": "东莞市嘉诚化工有限公司",
            "EmergencyPhone": "",
            "UnitCertificateExpDate": null,
            "CanOperated": false,
            "CanModify": false,
            "Website": [
                {
                    "Id": 120227,
                    "MainId": 77878,
                    "CWebsiteId": 4579637,
                    "ICPWebNo": "粤ICP备17053391号-2",
                    "Status": 7,
                    "Name": "东莞市嘉诚化工有限公司",
                    "Domain": [
                        {
                            "CerificationPicture": [
                                "7e84235bff9330eb778ce641ed95ff193a27442f.jpg"
                            ],
                            "domainAuthenctionPicture": [
                                "8cs79878csd89vs90s8v0s8sv09vs8vs79vsvs88.jpg" // 域名实名信息截图，当域名不在我司注册时，该字段必传
                            ],
                            "Domain": "jiachenghg.com",
                            "IsMain": 1
                        }
                    ],
                    "IP": [
                        "*************"
                    ],
                    "Url": "www.jiachenghg.com",
                    "Language": [
                        "1"
                    ],
                    "ServiceType": [
                        "128"
                    ],
                    "ServiceContent": 1,
                    "PreAppoval": [],
                    "PICName": "刘勇平",
                    "Phone": "13829212987",
                    "PhonePre": "",
                    "Mobile": "13006832688",
                    "Email": "<EMAIL>",
                    "QQ": "",
                    "LicenseType": 2,
                    "LicenseId": "43062119881216183X",
                    "LicenseDate": "2022-06-09-长期",
                    "LicensePicture": [
                        "df319c249a0634fb13ae1b9e8f8dbfab83c30a2b.jpg"
                    ],
                    "RelevantPromiseLetter": [
                        "df319c249a0634fb13ae1b9e8f8dbfab83c30a2b.jpg"
                    ],
                    "CurtainPicture": [
                        "b3ca876fe579629dcce4be8b4e72df69ba231dbb.jpg"
                    ],
                    "AuthVerificationPicture": [
                        "da472a61d24cee0270cdf3adcd8c043a99c518fb.jpg"
                    ],
                    "OtherPicture": [],
                    "Remark": "应急联系人：【方林】，应急联系电话：【13829212987】",
                    "CreateTime": **********,
                    "UpdateTime": **********,
                    "EmergencyPhone": "13829212987",
                    "AppCode": null,
                    "ConnectType": 5,
                    "BaseManageprovince": "5",
                    "InternetServiceType": 1,
                    "IsDeleted": 0,
                    "CanOperated": false,
                    "ConnectCanModify": true,
                    "WebsiteCanModify": true
                }
            ]
        }
    ],
    "RetCode": 0,
    "Action": "DescribeICPMainResponse"
}




```

----------

### 开始变更订单

确定主体可变更后，取出返回体，结构中ICP元素，取第一个主体,使用备案号搜索，恒定只会有一个主体，如有多可请向备案组确认。

关注CanOperated字段，如为true侧可操作，如为false,说明不可操作。请确定是否有流程中的订单
关注CMainId字段，如为-1 则说明备案主体未与工信部数据做匹配，请向备案组确认，执行同步后再做变更。
循环读取ICP结构中Website的内容，关注CWebsiteId字段，如为-1则说明网站未与工信部数据做匹配，请向备案组确认，执行同步后再做变更。

确认无误删除主体中无关的内容开始执行变更。

删除主体部分"CanOperated","CanModify",“CompanyId”,"Status","IsDeleted"字段，主体结构中增加ICPId，值为主体Id。
循环读取ICP结构中Website的内容，删除其中"CanOperated", "ConnectCanModify","WebsiteCanModify","MainId","Status"等字段。

删除完后，得到干净的可变更的备案结构体。增加备案类型标记 Type为7 ，状态标记Status为2.后更新其中需要更新的内容后。然后加上Action后发起请求

```javascript

{
    "Action": "CreateICPOrder",
    "PICMainMobile": "13537393538",
    "ICPId": "77878",
    "OrganizerLicenseId": "91441900MA4WDH6J4E",
    "PICMainLicenseId": "430621198710281822",
    "PICMainLicenseDate": "2022-06-09-长期",
    "OrganizerName": "东莞市嘉诚化工有限公司",
    "PICMainEmail": "<EMAIL>",
    "PICMainLicenseType": "2",
    "request_uuid": "debc7641-34b6-48b0-81b7-cae18d0d3106",
    "OrganizerType": "4",
    "OrganizerLicenseArea": "广东省东莞市大岭山镇横镇西路81号201房",
    "EmergencyPhone": "13580979690",
    "PICMainName": "余星",
    "PICMainQQ": "",
    "AreaId": "441901",
    "Type": "7",
    "OrganizerAddress": "广东省东莞市大岭山镇横镇西路81号201房",
    "PICMainOfficePhonePre": "",
    "CMainId": "4617577",
    "PICMainOfficePhone": "",
    "Status": "1",
    "EditStatus": "1",
    "CanOperated": "true",
    "UnitSuperior": "东莞市嘉诚化工有限公司",
    "OrganizerLicenseType": "1",
    "ICPMainNo": "粤ICP备17053391号",
    "Step": "1",
    "Website": [
        {
            "EmergencyPhone": "13829212987",
            "Status": "0",
            "Phone": "13829212987",
            "CanOperated": "true",
            "PICName": "刘勇平",
            "InternetServiceType": "1",
            "LicenseId": "43062119881216183X",
            "LicenseDate": "2022-06-09-长期",
            "IP": [
                null
            ],
            "Email": "<EMAIL>",
            "Remark": "应急联系人：【方林】，应急联系电话：【13829212987】",
            "ICPWebNo": "粤ICP备17053391号-2",
            "MainId": "77878",
            "PreAppoval": "",
            "Mobile": "13006832688",
            "WebsiteCanModify": "true",
            "Url": "www.jiachenghg.com",
            "ServiceType": [
                null
            ],
            "BaseManageprovince": "5",
            "Index": "0",
            "CreateTime": "**********",
            "Domain": [
                null
            ],
            "ConnectCanModify": "true",
            "LicenseType": "2",
            "Name": "东莞市嘉诚化工有限公司",
            "UpdateTime": "**********",
            "QQ": "",
            "CWebsiteId": "4579637",
            "ServiceContent": "1",
            "ConnectType": "5",
            "IsDeleted": "0",
            "Language": [
                null
            ],
            "PhonePre": "",
            "Id": "120227"
        }
    ]
}


```
