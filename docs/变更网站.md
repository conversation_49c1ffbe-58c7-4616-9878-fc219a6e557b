
# 变更网站

标签（空格分隔）： 变更，网站

---

特殊规则：
1.江苏省和安徽省 不支持变更网站

订单类型冲突：
1.变更网站与变更备案类型冲突。
2.变更网站与注销对应网站、取消对应接入、变更对应接入、注销主体冲突。

变更网站的前提是需要已完成备案
因此需要先使用DescribeICPMain取到已备案的数据,删除部分数据后。使用CreateICPOrder接口创建一个订单类型为9的变更订单。

----------

### 获取需要变更的主体内容

请求结构

字段 | 类型 | 必填 | 描述
---- | ---- | ---- | ----
Action | string | Yes | DescribeICPMain
Id | string | Yes | 网站所属的主体的Id

返回结构

```javascript

{
    "TotalCount": 1,
    "ICP": [
        {
            "Id": 77878,
            "Status": 7,
            "CMainId": 4617577,
            "CompanyId": 34278,
            "ICPMainNo": "粤ICP备17053391号",
            "AreaId": 441901,
            "OrganizerType": 4,
            "OrganizerName": "东莞市嘉诚化工有限公司",
            "OrganizerAddress": "广东省东莞市大岭山镇大岭山社区西园街33号",
            "OrganizerLicenseType": 1,
            "OrganizerLicenseId": "91441900MA4WDH6J4E",
            "OrganizerLicenseArea": "东莞市大岭山镇大岭山社区西园街33号",
            "PICMainName": "余星",
            "PICMainOfficePhone": "",
            "PICMainOfficePhonePre": "",
            "PICMainMobile": "13728426748",
            "PICMainEmail": "<EMAIL>",
            "PICMainQQ": "",
            "PICMainLicenseType": 2,
            "PICMainLicenseId": "430621198710281822",
            "PICMainLicenseDate": "2022-06-09-长期",
            "OrganizerLicensePicture": [
                "f112bf034fcd51f00d40962c82673c8631e95fd6.jpg"
            ],
            "OrganizerResidencePermitPicture": [],
            "PICMainLicensePicture": [
                "c67799748f9001875f5993e17180dd144e990fb5.jpg"
            ],
            "OtherPicture": [
                "1c8403395be455efc7930c6505e55375d685443c.jpg"
            ],
            "Remark": null,
            "CreateTime": **********,
            "UpdateTime": **********,
            "IsDeleted": 0,
            "UnitSuperior": "东莞市嘉诚化工有限公司",
            "EmergencyPhone": "",
            "UnitCertificateExpDate": null,
            "CanOperated": false, //是否可以 变更备案
            "CanModify": false,  //暂时不使用
            "CanChange":false,  //是否可以 变更主体
            "DeleteMain":false, //是否可以 注销主体
            "Website": [
                {
                    "Id": 120227,
                    "MainId": 77878,
                    "CWebsiteId": 4579637,
                    "ICPWebNo": "粤ICP备17053391号-2",
                    "Status": 7,
                    "Name": "东莞市嘉诚化工有限公司",
                    "Domain": [
                        {
                            "CerificationPicture": [
                                "7e84235bff9330eb778ce641ed95ff193a27442f.jpg"
                            ],
                            "domainAuthenctionPicture": [
                                "8cs79878csd89vs90s8v0s8sv09vs8vs79vsvs88.jpg" // 域名实名信息截图，当域名不在我司注册时，该字段必传
                            ],
                            "Domain": "jiachenghg.com",
                            "IsMain": 1
                        }
                    ],
                    "IP": [
                        "*************"
                    ],
                    "Url": "www.jiachenghg.com",
                    "Language": [
                        "1"
                    ],
                    "ServiceType": [
                        "128"
                    ],
                    "ServiceContent": 1,
                    "PreAppoval": [],
                    "PICName": "刘勇平",
                    "Phone": "13829212987",
                    "PhonePre": "",
                    "Mobile": "13006832688",
                    "Email": "<EMAIL>",
                    "QQ": "",
                    "LicenseType": 2,
                    "LicenseId": "43062119881216183X",
                    "LicensePicture": [
                        "df319c249a0634fb13ae1b9e8f8dbfab83c30a2b.jpg"
                    ],
                    "CurtainPicture": [
                        "b3ca876fe579629dcce4be8b4e72df69ba231dbb.jpg"
                    ],
                    "RelevantPromiseLetter": [
                        "df319c249a0634fb13ae1b9e8f8dbfab83c30a2b.jpg"
                    ],
                    "AuthVerificationPicture": [
                        "da472a61d24cee0270cdf3adcd8c043a99c518fb.jpg"
                    ],
                    "OtherPicture": [],
                    "Remark": "应急联系人：【方林】，应急联系电话：【13829212987】",
                    "CreateTime": **********,
                    "UpdateTime": **********,
                    "EmergencyPhone": "13829212987",
                    "AppCode": null,
                    "ConnectType": 5,
                    "BaseManageprovince": "5",
                    "InternetServiceType": 1,
                    "IsDeleted": 0,
                    "CanOperated": false, // 是否可以 变更备案
                    "ConnectCanModify": true, // 是否可以 变更接入
                    "WebsiteCanModify": true,  // 暂不使用
                    "CanChange": true, //是否可以 变更网站
                    "DeleteWeb": true, //是否可以 注销网站
                }
            ]
        }
    ],
    "RetCode": 0,
    "Action": "DescribeICPMainResponse"
}


```

----------

### 开始变更订单

确定网站可变更后，取出返回体，结构中的ICP元素。

关注CMainId字段，如为-1 则说明备案主体未与工信部数据做匹配，请向备案组确认，执行同步后再做变更。
读取ICP结构中要"变更的那个网站"的Website的内容，关注CWebsiteId字段，如为-1则说明网站未与工信部数据做匹配，请向备案组确认，执行同步后再做变更。
关注"变更的那个网站"的CanChange字段，如为true则可操作，如为false,说明不可操作。请确定是否有流程中的订单

确认无误删除主体中无关的内容开始执行变更。

删除主体部分"CanOperated","CanModify","Id",“CompanyId”,"Status","IsDeleted"字段。
只读取ICP结构中"变更的那个网站"的Website的内容，删除其中"CanOperated","ConnectCanModify","WebsiteCanModify","CanChange","DeleteWeb","Id","MainId","Status"等字段。

删除完后，得到干净的可变更的备案结构体。增加备案类型标记 Type为9 ，状态标记Status为1。增加ICPId 字段（为刚刚删除的Id的值），增加ICPWebId的字段（为刚刚删除的网站的Id的值）
后更新其中需要更新的内容后。然后加上Action后发起请求

请求结构

```javascript
{
    "Action": "CreateICPOrder",
    "Type": "9",
    "ICPId": "77878",
    "OrganizerLicenseId": "91441900MA4WDH6J4E",
    "request_uuid": "debc7641-34b6-48b0-81b7-cae18d0d3106",
    "OrganizerType": "4",
    "OrganizerName": "东莞市嘉诚化工有限公司",
    "OrganizerAddress": "广东省东莞市大岭山镇横镇西路81号201房",
    "OrganizerLicenseArea": "广东省东莞市大岭山镇横镇西路81号201房",
    "PICMainName": "余星",
    "PICMainOfficePhone": "",
    "PICMainOfficePhonePre": "",
    "PICMainMobile": "13537393538",
    "PICMainEmail": "<EMAIL>",
    "PICMainQQ": "",
    "PICMainLicenseType": "2",
    "PICMainLicenseId": "430621198710281822",
    "PICMainLicenseDate": "2022-06-09-长期",
    "EmergencyPhone": "13580979690",
    "AreaId": "441901",
    "CMainId": "4617577",
    "Status": "1",
    "EditStatus": "1",
    "UnitSuperior": "东莞市嘉诚化工有限公司",
    "OrganizerLicenseType": "1",
    "ICPMainNo": "粤ICP备17053391号",
    "Website": [
        {
            "EmergencyPhone": "13829212987",
            "Status": "2",
            "Phone": "13829212987",
            "PICName": "刘勇平",
            "InternetServiceType": "1",
            "LicenseId": "43062119881216183X",
            "LicenseDate": "2022-06-09-长期",
            "IP": [
                null
            ],
            "Email": "<EMAIL>",
            "Remark": "应急联系人：【方林】，应急联系电话：【13829212987】",
            "ICPWebNo": "粤ICP备17053391号-2",
            "MainId": "77878",
            "PreAppoval": "",
            "Mobile": "13006832688",
            "WebsiteCanModify": "true",
            "Url": "www.jiachenghg.com",
            "ServiceType": [
                null
            ],
            "BaseManageprovince": "5",
            "Index": "0",
            "CreateTime": "**********",
            "Domain": [
                null
            ],
            "LicenseType": "2",
            "Name": "东莞市嘉诚化工有限公司",
            "UpdateTime": "**********",
            "QQ": "",
            "CWebsiteId": "4579637",
            "ServiceContent": "1",
            "ConnectType": "5",
            "IsDeleted": "0",
            "Language": [
                null
            ],
            "PhonePre": ""
        }
    ]
}
```

响应结构

```javascript
{
    "OrderNo": "O20211025110050003634",
    "Action": "CreateICPOrderResponse",
    "RetCode": 0
}

```

-----

# 开始修改订单

创建订单成功后，需要调用 DescribeICPOrder 获取该订单的最新信息，

请求结构

```javascript
{
    "Action": "DescribeICPOrder",
    "OrderNo": "O20211022171057007600",
    "FetchPicture": true //是否要获取图片，不获取 可不传
}

```

返回结构

```javascript
{
    "TotalCount": 1,
    "Order": [
        {
            "OrganizerType": 4,
            "PICMainLicenseType": 2,
            "PromiseLetterPicture": [],
            "EmergencyPhone": "13500037931",
            "PICMainMobile": "13826416496",
            "AppCode": null,
            "OrganizerLicensePicture": [],
            "OrganizerResidencePermitPicture": [],
            "PICMainLicensePicture": [],
            "Id": 4661810,
            "AreaId": 440111,
            "ICPMainNo": "粤ICP备16066100号",
            "ICPWebNo": "粤ICP备16066100号-2",
            "OrderNo": "O20211025110050003634",
            "OrganizerName": "广州市医疗器械有限公司",
            "OrganizerLicenseType": 1,
            "PICMainName": "林权全",
            "PICMainLicenseId": "445122199331204561",
            "PICMainLicenseDate": "2022-06-09-长期",
            "UnitCertificateExpDate": "(null)",
            "Status": 1,
            "CWebsiteId": -1,
            "Type": 9,
            "CreateTime": **********,
            "UpdateTime": **********,
            "OrganizerLicenseId": "91440111066168213G",
            "CMainId": 4676904,
            "SubmitReexamineType": 0,
            "Error": {},
            "OtherPicture": [],
            "IsExtractType": 1,
            "EditStatus": "1",
            "Step": 2,
            "CurtainStatus": 0,
            "FrontError": {},
            "Stash": {},
            "Version": "4.6",
            "UpdateContent": [],
            "Remark": "(null)",
            "Website": [
                {
                    "Domain": [
                        {
                            "CerificationPicture": [
                                "c78776bb814cf01b2496bd7764d069d87e98f423.png"
                            ],
                            "Domain": "kian.com",
                            "IsMain": "1",
                            "key": "main_domain_36"
                        },
                        {
                            "CerificationPicture": [
                                "c78776bb814cf01b2496bd7764d069d87e98f423.png"
                            ],
                            "Domain": "gijian.com",
                            "IsMain": "0",
                            "key": "main_domain_37"
                        }
                    ],
                    "FetchInfo": {},
                    "IP": [
                        "**************"
                    ],
                    "Language": [
                        "1"
                    ],
                    "ServiceType": [
                        "4"
                    ],
                    "PreAppoval": [
                        {
                            "No": "（粤）-非经营性-2016-0255",
                            "Picture": [
                                "c78776bb814cf01b2496bd7764d069d87e98f423.png"
                            ],
                            "Type": "16"
                        }
                    ],
                    "LicensePicture": [],
                    "CurtainPicture": [],
                    "RelevantPromiseLetter": [],
                    "AuthVerificationPicture": [],
                    "OtherPicture": [],
                    "LicensePictureForAuth": [],
                    "CurtainPictureForAuth": [],
                    "AuthVerificationPictureForAuth": [],
                    "AuthenticationCode": [],
                    "AuthoriztionPicture": [],
                    "FrontError": {},
                    "Id": 4624895,
                    "OrderNo": "O20211025110050003634",
                    "ICPWebNo": "粤ICP备16066100号-2",
                    "Name": "广州卫健",
                    "Url": "www.gjian.com",
                    "PICName": "林权全",
                    "Phone": "13500667931",
                    "PhonePre": "",
                    "Mobile": "13826616496",
                    "Email": "<EMAIL>",
                    "QQ": "",
                    "LicenseType": 2,
                    "LicenseId": "445122199477204751",
                    "LicenseDate": "2022-06-09-长期",
                    "Remark": "域名指向同一网站",
                    "CreateTime": **********,
                    "UpdateTime": **********,
                    "EmergencyPhone": "13500777931",
                    "ServiceContent": 1,
                    "AppCode": null,
                    "CWebsiteId": 4627926,
                    "ConnectType": 5,
                    "BaseManageprovince": "5",
                    "UUID": null,
                    "CurtainUUID": null,
                    "ReuseWebsite": null,
                    "InternetServiceType": 1,
                    "IsDeleted": 0,
                    "CurtainStatus": 0
                }
            ]
        }
    ],
    "Action": "DescribeICPOrderResponse",
    "RetCode": 0
}

```

取出Order中的信息

将更新的内容覆盖获取到的信息
更改Status 为2。然后加上Action后发起请求

```javascript
{
    "Action": "ModifyICPOrder",
    "OrganizerType": 4,
    "PICMainLicenseType": 2,
    "PromiseLetterPicture": [],
    "EmergencyPhone": "13500037931",
    "PICMainMobile": "13826416496",
    "AppCode": null,
    "OrganizerLicensePicture": [],
    "OrganizerResidencePermitPicture": [],
    "PICMainLicensePicture": [],
    "Id": 4661810,
    "AreaId": 440111,
    "ICPMainNo": "粤ICP备16066100号",
    "ICPWebNo": "粤ICP备16066100号-2",
    "OrderNo": "O20211025110050003634",
    "OrganizerName": "广州市医疗器械有限公司",
    "OrganizerLicenseType": 1,
    "PICMainName": "林权全",
    "PICMainLicenseId": "445122199331204561",
    "PICMainLicenseDate": "2022-06-09-长期",
    "UnitCertificateExpDate": "(null)",
    "Status": 2,
    "CWebsiteId": -1,
    "Type": 9,
    "CreateTime": **********,
    "UpdateTime": **********,
    "OrganizerLicenseId": "91440111066168213G",
    "CMainId": 4676904,
    "SubmitReexamineType": 0,
    "Error": {},
    "OtherPicture": [],
    "IsExtractType": 1,
    "EditStatus": "1",
    "Step": 2,
    "CurtainStatus": 0,
    "FrontError": {},
    "Stash": {},
    "Version": "4.6",
    "UpdateContent": [],
    "Remark": "(null)",
    "Website": [
        {
            "Domain": [
                {
                    "CerificationPicture": [
                        "c78776bb814cf01b2496bd7764d069d87e98f423.png"
                    ],
                    "Domain": "kian.com",
                    "IsMain": "1",
                    "key": "main_domain_36"
                },
                {
                    "CerificationPicture": [
                        "c78776bb814cf01b2496bd7764d069d87e98f423.png"
                    ],
                    "Domain": "gijian.com",
                    "IsMain": "0",
                    "key": "main_domain_37"
                }
            ],
            "FetchInfo": {},
            "IP": [
                "**************"
            ],
            "Language": [
                "1"
            ],
            "ServiceType": [
                "4"
            ],
            "PreAppoval": [
                {
                    "No": "（粤）-非经营性-2016-0255",
                    "Picture": [
                        "c78776bb814cf01b2496bd7764d069d87e98f423.png"
                    ],
                    "Type": "16"
                }
            ],
            "LicensePicture": [],
            "CurtainPicture": [],
            "RelevantPromiseLetter": [],
            "AuthVerificationPicture": [],
            "OtherPicture": [],
            "LicensePictureForAuth": [],
            "CurtainPictureForAuth": [],
            "AuthVerificationPictureForAuth": [],
            "AuthenticationCode": [],
            "AuthoriztionPicture": [],
            "FrontError": {},
            "Id": 4624895,
            "OrderNo": "O20211025110050003634",
            "ICPWebNo": "粤ICP备16066100号-2",
            "Name": "广州卫健",
            "Url": "www.gjian.com",
            "PICName": "林权全",
            "Phone": "13500667931",
            "PhonePre": "",
            "Mobile": "13826616496",
            "Email": "<EMAIL>",
            "QQ": "",
            "LicenseType": 2,
            "LicenseId": "445122199477204751",
            "LicenseDate": "2022-06-09-长期",
            "Remark": "域名指向同一网站",
            "CreateTime": **********,
            "UpdateTime": **********,
            "EmergencyPhone": "13500777931",
            "ServiceContent": 1,
            "AppCode": null,
            "CWebsiteId": 4627926,
            "ConnectType": 5,
            "BaseManageprovince": "5",
            "UUID": null,
            "CurtainUUID": null,
            "ReuseWebsite": null,
            "InternetServiceType": 1,
            "IsDeleted": 0,
            "CurtainStatus": 0
        }
    ]
}

```
