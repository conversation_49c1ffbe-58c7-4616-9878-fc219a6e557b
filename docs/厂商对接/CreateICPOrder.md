# 创建备案订单-CreateICPOrder

创建备案订单

# Request Parameters
| Parameter name                           | Type   | Description                              | Required |
| ---------------------------------------- | ------ | ---------------------------------------- | -------- |
| Type                                     | int    | 订单类型                                 | **Yes**  |
| Status                                   | int    | 订单状态                                 | **Yes**  |
| OrganizerType                            | int    | 主办单位类型                             | **Yes**  |
| OrganizerName                            | string | 主办单位名称                             | **Yes**  |
| OrganizerLicenseType                     | int    | 主办单位证件类型                         | **Yes**  |
| OrganizerLicenseId                       | string | 主办单位证件号                           | **Yes**  |
| OrganizerLicenseArea                     | string | 主办单位证件地域                         | **Yes**  |
| PICMainName                              | string | 主体负责人姓名                           | **Yes**  |
| Website.n.Domain.n.CerificationPicture.n | string | 域名证书                                 | **Yes**  |
| Website.n.Domain.n.Domain                | string | 网站域名                                 | **Yes**  |
| Website.n.Name                           | string | 网站名称                                 | **Yes**  |
| Website.n.IP.n                           | string | 网站IP                                   | **Yes**  |
| Website.n.Mobile                         | string | 网站负责人手机号                         | **Yes**  |
| AreaId                                   | int    | 地域Id                                   | **Yes**  |
| OrganizerAddress                         | string | 主办单位通信地址                         | **Yes**  |
| PICMainOfficePhone                       | string | 主体负责人办公室电话                     | **Yes**  |
| PICMainOfficePhonePre                    | string | 主体负责人办公室电话地域前缀             | **Yes**  |
| PICMainMobile                            | string | 主体负责人手机号码                       | **Yes**  |
| PICMainEmail                             | string | 主体负责人电子邮箱                       | **Yes**  |
| PICMainLicenseType                       | int    | 主体负责人证件类型                       | **Yes**  |
| PICMainLicenseId                         | string | 主体负责人证件号                         | **Yes**  |
| OrganizerLicensePicture.n                | string | 主办单位证件                             | **Yes**  |
| PICMainLicensePicture.n                  | string | 主体负责人证件照片                       | **Yes**  |
| Website.n.Language.n                     | int    | 网站语言                                 | **Yes**  |
| Website.n.PICName                        | string | 网站负责人名称                           | **Yes**  |
| Website.n.LicenseType                    | int    | 网站负责人证件类型                       | **Yes**  |
| Website.n.LicenseId                      | string | 网站负责人证件号                         | **Yes**  |
| Website.n.LicensePicture.n               | string | 网站负责人证件照片                       | **Yes**  |
| EmergencyPhone                           | string | 应急联系电话                             | **Yes**  |
| Website.n.EmergencyPhone                 | string | 应急联系电话                             | **Yes**  |
| Website.n.ServiceContent                 | string | 应用服务内容                             | **Yes**  |
| PICMainQQ                                | string | 主体负责人QQ                             | No       |
| OrganizerResidencePermitPicture.n        | string | 主办单位暂住证/居住证扫描件              | No       |
| OtherPicture.n                           | string | 其他证件图片                             | No       |
| Remark                                   | string | 备注                                     | No       |
| SubmitReexamineType                      | int    | 复审方案                                 | No       |
| LogoutReason                             | string | 注销原因                                 | No       |
| ICPMainNo                                | string | 主体备案号，新增网站时使用。             | No       |
| ICPMainPassword                          | string | 备案密码，注销主体时使用。转接入网站使用 | No       |
| Website.n.Domain.n.IsMain                | int    | 是否为主域名                             | No       |
| Website.n.Url                            | string | 首页地址                                 | No       |
| Website.n.ServiceType.n                  | int    | 网站服务类型                             | No       |
| Website.n.PreAppoval.n.Type              | int    | 网站前置审批类型                         | No       |
| Website.n.PreAppoval.n.No                | string | 网站前置审批号                           | No       |
| Website.n.PreAppoval.n.Picture.n         | string | 网站前置审批证件                         | No       |
| Website.n.PhonePre                       | string | 网站负责人座机区号                       | No       |
| Website.n.Phone                          | string | 网站负责人座机                           | No       |
| Website.n.Email                          | string | 网站负责人邮箱地址                       | No       |
| Website.n.QQ                             | string | 网站负责人QQ                             | No       |
| Website.n.AuthVerificationPicture.n      | string | 网站真实性核验单                         | No       |
| Website.n.CurtainPicture.n               | string | 网站幕布照片，网站负责人照片             | No       |
| Website.n.OtherPicture.n                 | string | 网站其他图片                             | No       |
| Website.n.ICPWebNo                       | string | 网站备案号                               | No       |
| Website.n.Remark                         | string | 网站备注                                 | No       |
| Website.n.FetchInfo.CommitmentPicture    | string | 承诺书照片，限江苏地区                   | No       |
| Website.n.FetchInfo.CommitmentVedio      | string | 口述承诺书视频，限江苏地区               | No       |
| Website.n.FetchInfo.NotificationPicture  | string | 告知书照片，限江苏地区                   | No       |

# Response Elements
| Parameter name | Type   | Description | Required |
| -------------- | ------ | ----------- | -------- |
| RetCode        | int    | 返回码      | **Yes**  |
| Action         | string | 操作名称    | **Yes**  |
| OrderNo        | string | 订单号      | No       |

# Request Example
```
https://api.ucloud.cn/?Action=CreateICPOrder
&EmergencyPhone=JbHfxJpO
&Website.n.EmergencyPhone=PcUqTDxO
&EmergencyPhone=OqECeCyC
&Website.n.EmergencyPhone=YpSntXVO
&EmergencyPhone=BPkFAXLq
&Website.n.EmergencyPhone=eWNzJHHj
&Website.n.ServiceContent=gNsVWjof
```

# Response Example
```
{
    "Action": "CreateICPOrderResponse", 
    "OrderNo": "HyEoryYA", 
    "RetCode": 0
}
```

