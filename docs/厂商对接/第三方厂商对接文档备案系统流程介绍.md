# 第三方代理商备案系统对接文档
----------

# 1. 简述

    备案的一个全流程是由客户提交一个订单开始，订单的整个生命周期为编辑状态（含核验），到提交审核，UCloud审核至管局审核，全部审核完成，备案状态标记为审核完，同时得到备案号与一个完整的备案主体数据。订单主要的结核由备案主体（备案公司信息、法人信息）与网站内容（网站信息描述，网站负责人信息）组成，网站可以有多个。备案类型的验证，需要先填写完验证信息，若账号实名信息与备案主体不符，需要点击获取授权，个人实名只允许本人备案。验证结果分为三种：新增主体，新增网站，新增接入。新增主体：指备案主体名称、证件号码及域名均未做过备案。新增网站：指备案主体已做过备案，已取得主体备案号，现需在该主体下添加新域名备案。新增接入：指用户的该域名已在其他服务商处做过备案，现因使用UCloud中国内地服务器，需将已备案的域名新增接入至UCloud。具体备案说明参考我司文档https://docs.ucloud.cn/beian1/guidance/guidance1。

更新：
| 更新时间       | 更新内容                             |
| -------------- | ------------------------------------ |
| 2022年03月07日 | 增加变更功能与少数地区附加字段的介绍 |
 
 

# 2. 接口签名加密

    签名方式参考文档：https://docs.ucloud.cn/api/summary/signature
    因签名需要，请将JSON中的数组拆掉结构后加上下标后提交，具体方式如下：

```javacript
    {
    "OrganizerAddress":"xxx街XXX号",
    "Website":[
    {"Email":"<EMAIL>"},
    {"Email":"<EMAIL>"}
    ]
    }
   // 拆成如下结构完成签名与提交
   {
    "OrganizerAddress":"xxx街XXX号",
    "Website.0.Email":"<EMAIL>",
    "Website.1.Email":"<EMAIL>"
   }
```

  系统中存在多种枚举值，其含义请调用API得到。
  
# 3. 获取备案配置信息 DescribeICPConfig

DescribeICPConfig记录了备案的一些配置信息，比如订单类型，订单状态等详细信息。API返回结果详情参考地址：<https://api.ucloud.cn/?Action=DescribeICPConfig。API>返回字段介绍如下，

### Request Parameters

无

### Response Elements

| Parameter name              | Type   | Description                  | Required |
| --------------------------- | ------ | ---------------------------- | -------- |
| RetCode                     | int    | 返回码                       | **Yes**  |
| Action                      | string | 操作名称                     | **Yes**  |
| OrderType                   | array  | 订单类型                     | **Yes**  |
| OrganizerType               | array  | 主办单位性质                 | **Yes**  |
| OrganizerLicenseType        | array  | 主办单位证件类型             | **Yes**  |
| PICLicenseType              | array  | 负责人证件类型               | **Yes**  |
| ServiceType                 | array  | 网站服务内容                 | **Yes**  |
| Language                    | array  | 网站语言                     | **Yes**  |
| OrderStatus                 | array  | 订单状态                     | **Yes**  |
| PreAppovalType              | array  | 网站前置审批类型（专项服务） | **Yes**  |
| Area                        | array  | 备案省市信息                 | **Yes**  |
| OrganizerTypeLicenseMapping | array  | 组织机构类型证书对应         | **Yes**  |
| InternetWebsiteServiceTypes | array  | 互联网服务类型         | **Yes**  |


# 4. 备案流程

    备案第一步，备案类型验证。根据输入的基础信息，推荐出可以使用的备案类型，比如新增主体或者新增网站或者新增接入类型订单等,才能进行相关备案。（参考4.1）

    备案第二步，创建订单。创建备案订单调取接口CreateICPOrder，接口文档请参考https://www.zybuluo.com/f673488649/note/1754640。新增备案，提交成功后得到订单号OrderNo，OrderNo具有唯一性。（参考4.2）

    备案第三步，电子核验。网站负责人信息按核验规则生成H5链接，接入商可使短信或者生成二维码等方式提供需要核验的人员。完成核验后订单中的CurtainStatus值会自动变为3（待提交），然后可调用接口ModifyICPOrder，将Status值变更为2后提交，订单进入UCloud审核状态。（参考4.3）
    
    备案第四步，编辑或修改删除订单。编辑或者修改订单调取接口ModifyICPOrder,ModifyICPOrder与CreateICPOrder其一致。请求入参仅需要加上订单的订单号OrderNo。订单创建为编辑状态时，可反复编辑。如果对生成的网站信息做编辑修改时，调取接口ModifyICPOrder时需要加入编辑修改网站的Id字段信息，新增的网站调取ModifyICPOrder接口不用加Id，如果删除生成的网站则调取接口RemoveICPOrderWebsite，如果删除备案订单调取接口DeleteICPOrder。（参考4.4）

    备案第五步，ucloud循环审核订单信息，直到订单信息修改至符合备案规范。当订单流程进入UCloud审核状态时，审核员会对订单信息进行审核，如果存在不规范的或者错误的信息，审核员会打回订单，订单状态会进入UCloud审核退回状态，这时需要调取接口DescribeICPOrder（接口详情参考5）查看错误信息，并重新调取接口ModifyICPOrder修改打回的信息，最后提交订单至UCloud审核。（参考4.5）
    
    功能流程图如下：
    （图片链接：https://static.ucloud.cn/fbe715a153d95f2b1b992ff227f4b730.png）![备案功能流程图][1]

## 4.1 备案类型验证 ExtractICPType

   新增备案前，通过基础信息，推荐合理的备案类型。验证信息推荐备案类型接口：

### Request Parameters

| Parameter name       | Type   | Description      | Required |
| -------------------- | ------ | ---------------- | -------- |
| Domain               | string | 域名             | **Yes**  |
| OrganizerLicenseType | int    | 主办单位证件类型 | **Yes**  |
| OrganizerLicenseId   | string | 主办单位证件号   | **Yes**  |
| OrganizerName        | string | 主办单位名       | No       |

### Response Elements

| Parameter name | Type   | Description    | Required |
| -------------- | ------ | -------------- | -------- |
| RetCode        | int    | 返回码         | **Yes**  |
| Action         | string | 操作名称       | **Yes**  |
| Type           | array  | 推荐的操作类型 | **Yes**  |
| Info           | string | 推荐使用的信息 | **Yes**  |

### Request Example

```
{
 "OrganizerType": "4",
 "OrganizerLicenseType": "1",
 "Action": "ExtractICPType",
 "Domain": "ssss.com",
 "OrganizerName": "企业多网站test",
 "AreaId": "110101",
 "OrganizerLicenseId": "91110108348335194H"
}
```

### Response Example

```
{
 "Info": {},
 "Determine": [
  0,
  0,
  0,
  0
 ],
 "Action": "ExtractICPTypeResponse",
 "Type": [
  1
 ],
 "RetCode": 0
}
```

根据信息返回结果显示，本次备案类型为Type：1，即新增主体

----------

## 4.2 创建备案订单 CreateICPOrder

CreateICPOrder文档入参请参考 <https://www.zybuluo.com/f673488649/note/2134954>

创建与更新过程，涉及到的各类文件使用CreateICPPicture提交。因存在文件过大的情况，因此上传网关地址更换为“https://icpupload.ucloud.cn/picture_upload/”。具体入参说明请参考：“https://www.zybuluo.com/f673488649/note/2172286”，查看图片使用接口“DescribeICPPicture”，唯一参数为Picture，需要鉴权。

### Request Example

```
{
 "PICMainLicenseType": "2",
 "OrganizerType": "4",
 "OrganizerAddress": "上海市杨浦区隆昌路619号",
 "Type": "1",  //备案类型
 "PICMainEmail": "<EMAIL>",
 "UnitSuperior": "企业多网站test",
 "OrganizerLicenseType": "1",
 "Status": "1", //订单状态：1：编辑，2：Ucloud审核中
 "Domain": "ssss.com",
 "OrganizerLicenseArea": "隆昌路619号",
 "OrganizerLicenseId": "91110108348335194H",
 "PICMainName": "倩女幽魂",
 "PICMainMobile": "18816530374",
 "Action": "CreateICPOrder",
 "EmergencyPhone": "18816530377",
 "OrganizerName": "企业多网站test",
 "AreaId": "110101",
 "PICMainLicenseId": "411528199401011044",
 "Website.0.Domain.0.CerificationPicture.0": "f1a9fdc3565e8c69e75010553cffde3a29cd4834.png",
 "Website.0.Domain.0.domainAuthenctionPicture.0": "f1a9fdc3565e8c69e75010553cffde3a29cd4834.png", // 域名实名信息截图，当域名不在我司注册时该字段必传
 "Website.0.LicensePicture.0": "f1a9fdc3565e8c69e75010553cffde3a29cd4834.png",
 "Website.0.PreAppoval": "",
 "Website.0.ServiceType.0": "128",
 "Website.0.Domain.0.Domain": "ssss.com",
 "Website.0.EmergencyPhone": "18816530375",
 "Website.0.LicenseId": "411528199401011044",
 "Website.0.IP.0": "*******",
 "Website.0.Name": "test",
 "Website.0.Domain.0.IsMain": "1",
 "Website.0.PICName": "倩女幽魂",
 "Website.0.BaseManageprovince": "5",
 "Website.0.LicenseType": "2",
 "Website.0.Mobile": "18816530376",
 "Website.0.ServiceContent": "1",
 "Website.0.Email": "<EMAIL>",
 "Website.0.Index": "0",
 "Website.0.Language.0": "1",
 "Website.0.ConnectType": "5",
 "Website.0.Url": "www.ssss.com",
}
```

### Response Example

```javascript
{
  OrderNo: 'O20220221134159003092',
  Action: 'CreateICPOrderResponse',
  RetCode: 0
}
```

## 4.3 电子核验

H5电子核验功能由UCloud提供，订单在编辑状态时，完成订单全部内容的填写后，接入商需要提取核验人的相关信息，按规则生成H5网页的URL链接。

### 步骤一，提取需要核验的个人信息

当客户信息保存好后，代理商根据证件类型与证件Id,去重取出需要做活体核验的人的信息。举例说明，当前订单中有 3 个网站，其中网站 1 与网站 2 分别为同一网站负责人 A,网站 3 为负责人 B.供应商需要提取出 A 与 B 的信息，请求 2 次接口，生成 2 张二维码提供给客户扫描。

#### 获取授权码  GetTemporaryToken

获取二维码授权Token,API 使用 UCloud 公用的 API 调用方式。主要参数如下

### RequestParams

| 名称        | 类型    | 必填 | 描述                      |
| ----------- | ------- | ---- | ------------------------- |
| Action      | string  | Yes  | 方法名，GetTemporaryToken |
| LicenseType | integer | Yes  | 证件类型                  |
| LicenseId   | string  | Yes  | 证件号                    |
| OrderNo     | string  | Yes  | 订单号                    |

### ResponseParams

| 名称    | 类型   | 必返回 | 描述   |
| ------- | ------ | ------ | ------ |
| RetCode | int    | Yes    | 返回码 |
| Token   | string | Yes    | 授权码 |
请求示例：

```javascript
{
 "OrderNo": "O20220221134159003092",
 "LicenseId": "411528199401011044",
 "Action": "GetTemporaryToken",
 "LicenseType": "2",
}
```

返回示例：

```javascript
{
  Token: '63f8386a-f564-4340-a56a-936fe029bd0e',
  Action: 'GetTemporaryTokenResponse',
  RetCode: 0
}

```

注：Token会在采集完成或者12小时后自动失效，如果失效，需要使用旧Token,证件号，订单号，证件Id,证件类型（Token,LicenseId,LicenseType,OrderNo,），重新刷新Token、
假设，一个主体有3个网站，网站一、网站二是同一个负责人A；网站三是负责人B。本订单需要生成2次Token，由2个负责人分别核验，负责人A的信息验证过后，会自动同同步到网站一、网站二中。

### 步骤二， 生成 H5 URL

代码示例：

```javascript
let url = 'https://m.ucloud.cn/mobile/authentication/icp.html#/pages/'
if ('licId' in params) {
          url += `veriface/start?licId=${encodeURI(params.licId)}&licName=${encodeURI(params.licName)}`
    }
url += `&orderNo=${params.orderNo || ''}&orgType=${params.organizerType || ''}&tokenId=${res.Token}&uuid=${uuid}`

```

请求入参示例：

```
https://m.ucloud.cn/mobile/authentication/icp.html#/pages/veriface/start?licId=411528199401011111&licName=%E5%80%A9%E5%A5%B3%E5%B9%BD%E9%AD%82&orderNo=O20230328170635004596&orgType=2&tokenId=1111-9061-4d7a-b0e7-9bf067293d00&uuid=11111-9061-4d7a-b0e7-9bf067293d00
```

将以上方式生成得到的 URL 提供给客户或者生成二维码，用手机浏览器或者微信打开既进入 H5 验证

### 步骤三， H5 验证完成

在订单与网站中，都存在 CurtainStatus 字段。当订单中的 CurtainStatus 状态为 3，则说明整体的订单已录入完成，可以提交。网站中 CurtainStatus 为 0 说明客户还未做 H5 核验，状态为 1，说明此网站已做核验。

# 4.4 编辑或者修改订单ModifyICPOrder

  ModifyICPOrder入参规范与CreateICPOrder接口的一致，只是入参必须有一个OrderNo。如需要修改订单的主体相关信息，可使用ModifyICPOrder。***如需修改主体下的某个网站信息，需要加上此网站的Id（Id可以通过章节5中提到的查看备案订单DescribeICPOrder接口获取到）,不加Id的话会新增一个网站，***提交示例如下：

### Request Example

```json
{
 "Website.0.EmergencyPhone": "18816530378",
 "Website.0.Name": "test2222211111",
 "Website.0.Mobile": "18816530377",
 "Website.0.Email": "<EMAIL>",
 "Website.0.Id":"4651807", 
 "OrderNo": "O20220221134159003092",
 "Remark": "xxx",
 "PICMainEmail": "<EMAIL>",
 "PICMainMobile": "18916730367",
 "Action": "ModifyICPOrder",
 "EmergencyPhone": "18916730368",
 "Status": "1",
}
```

### Response Example

```json
{
 "OrderNo": "O20220221134159003092",
 "Action": "ModifyICPOrderResponse",
 "RetCode": 0
}
```

## 订单内网站删除操作 RemoveICPOrderWebsite

WebsiteId即上述的网站的Id，可以通过章节5中提到的查看备案订单 DescribeICPOrder接口获取到。

### Request Parameters

| Parameter name | Type   | Description  | Required |
| -------------- | ------ | ------------ | -------- |
| OrderNo        | string | 订单号       | **Yes**  |
| WebsiteId      | int    | 订单内网站Id | **Yes**  |

### Response Elements

| Parameter name | Type   | Description | Required |
| -------------- | ------ | ----------- | -------- |
| RetCode        | int    | 返回码      | **Yes**  |
| Action         | string | 操作名称    | **Yes**  |

如需删除网站信息，请求入参信息如下

```json
{
 "OrderNo": "O20220221134159003092",
 "Action": "RemoveICPOrderWebsite",
 "WebsiteId": 4651807,
}
```

返回结果如下：

```json
{
 "Action": "RemoveICPOrderWebsiteResponse",
 "RetCode": 0
}
```

## 删除备案订单 DeleteICPOrder

### Request Parameters

| Parameter name | Type          | Description                     | Required |
| -------------- | ------------- | ------------------------------- | -------- |
| OrderNo        | array<string> | 订单号,数组形式，支持多订单删除 | **Yes**  |

### Response Elements

| Parameter name | Type   | Description | Required |
| -------------- | ------ | ----------- | -------- |
| RetCode        | int    | 返回码      | **Yes**  |
| Action         | string | 操作名称    | **Yes**  |

请求示例如下：

```json
{
 "OrderNo": "O20220221134159003092",
 "Action": "DeleteICPOrder",
}
```

返回示例：

```json
{
 "Action": "DeleteICPOrderResponse",
 "RetCode": 0
}
```

## 4.5 订单打回，修改订单信息

### 订单提交后常常存在一些不符合备案规范的信息，UCloud审核员审核后可能对不规范的订单信息存在打回操作。查看打回的字段信息，需要调取接口DescribeICPOrder获取订单详情（查看备案订单详情DescribeICPOrder在章节5中介绍），返回的订单状态字段为Status:4（UCloud审核打回），同时订单Order的结构体中Error字段会存在打回的字段名和原因。结构如下

```json
{
    "TotalCount":1,
    "Order":[
        {
            "OrderNo":"O20220221134159003092",
            "Status":4, 
            "Error":{
                "PICMainLicensePicture":"错了",
                "Website":{
                    "4651603":{
                        "LicensePicture":"不行",
                        "CurtainPicture":"重整"
                    }
                }
            },
            ... //省略一些字段内容
        }
    ],
    "Action":"DescribeICPOrderResponse",
    "RetCode":0
}


```

注：打回后，可使用ModifyICPOrder接口对错误的字段内容做更新（参考4.4章节）。需要注意的是，未打回的字段，更新是无效的。打回的字段信息如是涉及到4.3章节中讲述的h5活体核验的相关信息，请重新生成H5链接做核验，然后修改完订单信息后重新提交至ucloud审核。

# 5.查看备案订单 DescribeICPOrder

查看备案订单，可查询当前账号下的订单，可使用订单号精确搜索，也可使用状态或单位名等信息搜索。

详情参考API文档：<https://www.zybuluo.com/f673488649/note/1785915>


# 5.查看已完成备案记录 DescribeICPMain

查看已完成备案记录，供提取出参数做变更与注销类操作

### RequestParams

| 名称          | 类型    | 必填 | 描述                               |
| ------------- | ------- | ---- | ---------------------------------- |
| Action        | string  | Yes  | API名                              |
| Limit         | integer | Yes  | 分页大小数量，最大值1000，默认值20 |
| Offset        | integer | Yes  | 分页偏移数量，默认值0              |
| ICPMainNo     | string  | Yes  | 主体备案号搜索                     |
| OrganizerName | string  | Yes  | 主办单位名搜索                     |
| Id            | integer | Yes  | 已知备案记录Id的情况下，做详情展示 |


# 5.变更已完成备案的信息

变更与新增类似，需增减少部分字段。详情参考API文档：<https://www.zybuluo.com/f673488649/note/1675686>


---
ps：小姐姐我两天呕心沥血写文档，看不懂的语文肯定没及格，多读书多看报少打游戏多睡觉。

  [1]: https://static.ucloud.cn/fbe715a153d95f2b1b992ff227f4b730.png
