<?xml version="1.0" encoding="UTF-8"?>
<wsdl:definitions targetNamespace="http://server.webservice.data.autoBulid.ncs.com" xmlns:soapenc11="http://schemas.xmlsoap.org/soap/encoding/" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:tns="http://server.webservice.data.autoBulid.ncs.com" xmlns:soap12="http://www.w3.org/2003/05/soap-envelope" xmlns:wsdlsoap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:soap11="http://schemas.xmlsoap.org/soap/envelope/" xmlns:soapenc12="http://www.w3.org/2003/05/soap-encoding">
  <wsdl:types>
<xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema" attributeFormDefault="qualified" elementFormDefault="qualified" targetNamespace="http://server.webservice.data.autoBulid.ncs.com">
<xsd:element name="getSubIDCRegUserData">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="in0" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="getSubIDCRegUserDataResponse">
<xsd:complexType/>
</xsd:element>
<xsd:element name="getSubIDCFrameInfoData">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="in0" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="getSubIDCFrameInfoDataResponse">
<xsd:complexType/>
</xsd:element>
<xsd:element name="upload">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="in0" nillable="true" type="xsd:string"/>
<xsd:element maxOccurs="1" minOccurs="1" name="in1" type="xsd:int"/>
<xsd:element maxOccurs="1" minOccurs="1" name="in2" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="uploadResponse">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="out" type="xsd:long"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="dynamic_resource_command">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="in0" nillable="true" type="xsd:string"/>
<xsd:element maxOccurs="1" minOccurs="1" name="in1" nillable="true" type="xsd:string"/>
<xsd:element maxOccurs="1" minOccurs="1" name="in2" nillable="true" type="xsd:string"/>
<xsd:element maxOccurs="1" minOccurs="1" name="in3" nillable="true" type="xsd:string"/>
<xsd:element maxOccurs="1" minOccurs="1" name="in4" nillable="true" type="xsd:string"/>
<xsd:element maxOccurs="1" minOccurs="1" name="in5" type="xsd:int"/>
<xsd:element maxOccurs="1" minOccurs="1" name="in6" nillable="true" type="xsd:long"/>
<xsd:element maxOccurs="1" minOccurs="1" name="in7" type="xsd:int"/>
<xsd:element maxOccurs="1" minOccurs="1" name="in8" type="xsd:int"/>
<xsd:element maxOccurs="1" minOccurs="1" name="in9" type="xsd:int"/>
<xsd:element maxOccurs="1" minOccurs="1" name="in10" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="dynamic_resource_commandResponse">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="out" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="idc_updateRegUser">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="in0" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="idc_updateRegUserResponse">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="out" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="idc_domainDispose">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="in0" nillable="true" type="xsd:string"/>
<xsd:element maxOccurs="1" minOccurs="1" name="in1" nillable="true" type="xsd:string"/>
<xsd:element maxOccurs="1" minOccurs="1" name="in2" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="idc_domainDisposeResponse">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="out" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="getSubIDCGateWayData">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="in0" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="getSubIDCGateWayDataResponse">
<xsd:complexType/>
</xsd:element>
<xsd:element name="getSubIDCDomainListData">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="in0" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="getSubIDCDomainListDataResponse">
<xsd:complexType/>
</xsd:element>
<xsd:element name="batchImpCommandPC">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="in0" nillable="true" type="xsd:string"/>
<xsd:element maxOccurs="1" minOccurs="1" name="in1" nillable="true" type="xsd:string"/>
<xsd:element maxOccurs="1" minOccurs="1" name="in2" type="xsd:int"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="batchImpCommandPCResponse">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="out" type="xsd:boolean"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="download">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="in0" nillable="true" type="xsd:string"/>
<xsd:element maxOccurs="1" minOccurs="1" name="in1" type="xsd:long"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="downloadResponse">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="out" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="idc_updateISMSSystemDateTime">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="in0" nillable="true" type="xsd:long"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="idc_updateISMSSystemDateTimeResponse">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="out" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="getSubIDCServiceInfoData">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="in0" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="getSubIDCServiceInfoDataResponse">
<xsd:complexType/>
</xsd:element>
<xsd:element name="getSubIDCOfficerData">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="in0" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="getSubIDCOfficerDataResponse">
<xsd:complexType/>
</xsd:element>
<xsd:element name="getSyncSensurData">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="in0" nillable="true" type="xsd:string"/>
<xsd:element maxOccurs="1" minOccurs="1" name="in1" type="xsd:int"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="getSyncSensurDataResponse">
<xsd:complexType/>
</xsd:element>
<xsd:element name="getSubIDCIpInfoData">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="in0" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="getSubIDCIpInfoDataResponse">
<xsd:complexType/>
</xsd:element>
<xsd:element name="getSubIDCSensurData">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="in0" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="getSubIDCSensurDataResponse">
<xsd:complexType/>
</xsd:element>
<xsd:element name="getSubIDCVirtualServerData">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="in0" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="getSubIDCVirtualServerDataResponse">
<xsd:complexType/>
</xsd:element>
<xsd:element name="getSubIDCRouterInfoData">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="in0" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="getSubIDCRouterInfoDataResponse">
<xsd:complexType/>
</xsd:element>
<xsd:element name="idc_commandack">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="in0" nillable="true" type="xsd:string"/>
<xsd:element maxOccurs="1" minOccurs="1" name="in1" nillable="true" type="xsd:string"/>
<xsd:element maxOccurs="1" minOccurs="1" name="in2" nillable="true" type="xsd:string"/>
<xsd:element maxOccurs="1" minOccurs="1" name="in3" nillable="true" type="xsd:string"/>
<xsd:element maxOccurs="1" minOccurs="1" name="in4" nillable="true" type="xsd:string"/>
<xsd:element maxOccurs="1" minOccurs="1" name="in5" type="xsd:int"/>
<xsd:element maxOccurs="1" minOccurs="1" name="in6" type="xsd:int"/>
<xsd:element maxOccurs="1" minOccurs="1" name="in7" type="xsd:int"/>
<xsd:element maxOccurs="1" minOccurs="1" name="in8" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="idc_commandackResponse">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="out" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="idc_statFind">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="in0" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="idc_statFindResponse">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="out" type="xsd:boolean"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="getSubIDCApproveSensorData">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="in0" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="getSubIDCApproveSensorDataResponse">
<xsd:complexType/>
</xsd:element>
<xsd:element name="ircs_command">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="in0" nillable="true" type="xsd:string"/>
<xsd:element maxOccurs="1" minOccurs="1" name="in1" nillable="true" type="xsd:string"/>
<xsd:element maxOccurs="1" minOccurs="1" name="in2" nillable="true" type="xsd:string"/>
<xsd:element maxOccurs="1" minOccurs="1" name="in3" nillable="true" type="xsd:string"/>
<xsd:element maxOccurs="1" minOccurs="1" name="in4" nillable="true" type="xsd:string"/>
<xsd:element maxOccurs="1" minOccurs="1" name="in5" type="xsd:int"/>
<xsd:element maxOccurs="1" minOccurs="1" name="in6" nillable="true" type="xsd:long"/>
<xsd:element maxOccurs="1" minOccurs="1" name="in7" type="xsd:int"/>
<xsd:element maxOccurs="1" minOccurs="1" name="in8" type="xsd:int"/>
<xsd:element maxOccurs="1" minOccurs="1" name="in9" type="xsd:int"/>
<xsd:element maxOccurs="1" minOccurs="1" name="in10" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="ircs_commandResponse">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="out" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="idc_flowStat">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="in0" nillable="true" type="xsd:long"/>
<xsd:element maxOccurs="1" minOccurs="1" name="in1" nillable="true" type="xsd:long"/>
<xsd:element maxOccurs="1" minOccurs="1" name="in2" nillable="true" type="xsd:string"/>
<xsd:element maxOccurs="1" minOccurs="1" name="in3" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="idc_flowStatResponse">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="out" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="getSubIDCMachineHouseData">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="in0" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="getSubIDCMachineHouseDataResponse">
<xsd:complexType/>
</xsd:element>
<xsd:element name="getSubIDCIpSegData">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="in0" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="getSubIDCIpSegDataResponse">
<xsd:complexType/>
</xsd:element>
<xsd:element name="idc_deleteRegUser">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="in0" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="idc_deleteRegUserResponse">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="out" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="getSubIDCHousesHoldInfoData">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="in0" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="getSubIDCHousesHoldInfoDataResponse">
<xsd:complexType/>
</xsd:element>
<xsd:element name="getSubIDCIsInfoData">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="in0" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="getSubIDCIsInfoDataResponse">
<xsd:complexType/>
</xsd:element>
</xsd:schema>
  </wsdl:types>
  <wsdl:message name="getSubIDCDomainListDataResponse">
    <wsdl:part name="parameters" element="tns:getSubIDCDomainListDataResponse">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="getSubIDCApproveSensorDataResponse">
    <wsdl:part name="parameters" element="tns:getSubIDCApproveSensorDataResponse">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="idc_statFindResponse">
    <wsdl:part name="parameters" element="tns:idc_statFindResponse">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="downloadRequest">
    <wsdl:part name="parameters" element="tns:download">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="getSubIDCVirtualServerDataRequest">
    <wsdl:part name="parameters" element="tns:getSubIDCVirtualServerData">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="getSubIDCServiceInfoDataResponse">
    <wsdl:part name="parameters" element="tns:getSubIDCServiceInfoDataResponse">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="ircs_commandRequest">
    <wsdl:part name="parameters" element="tns:ircs_command">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="getSubIDCHousesHoldInfoDataResponse">
    <wsdl:part name="parameters" element="tns:getSubIDCHousesHoldInfoDataResponse">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="getSubIDCMachineHouseDataRequest">
    <wsdl:part name="parameters" element="tns:getSubIDCMachineHouseData">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="idc_domainDisposeRequest">
    <wsdl:part name="parameters" element="tns:idc_domainDispose">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="getSubIDCGateWayDataRequest">
    <wsdl:part name="parameters" element="tns:getSubIDCGateWayData">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="uploadRequest">
    <wsdl:part name="parameters" element="tns:upload">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="getSyncSensurDataRequest">
    <wsdl:part name="parameters" element="tns:getSyncSensurData">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="getSubIDCRegUserDataRequest">
    <wsdl:part name="parameters" element="tns:getSubIDCRegUserData">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="dynamic_resource_commandRequest">
    <wsdl:part name="parameters" element="tns:dynamic_resource_command">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="idc_updateISMSSystemDateTimeResponse">
    <wsdl:part name="parameters" element="tns:idc_updateISMSSystemDateTimeResponse">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="idc_domainDisposeResponse">
    <wsdl:part name="parameters" element="tns:idc_domainDisposeResponse">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="getSubIDCIpInfoDataResponse">
    <wsdl:part name="parameters" element="tns:getSubIDCIpInfoDataResponse">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="idc_updateRegUserResponse">
    <wsdl:part name="parameters" element="tns:idc_updateRegUserResponse">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="idc_updateRegUserRequest">
    <wsdl:part name="parameters" element="tns:idc_updateRegUser">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="batchImpCommandPCRequest">
    <wsdl:part name="parameters" element="tns:batchImpCommandPC">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="getSubIDCGateWayDataResponse">
    <wsdl:part name="parameters" element="tns:getSubIDCGateWayDataResponse">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="idc_flowStatRequest">
    <wsdl:part name="parameters" element="tns:idc_flowStat">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="uploadResponse">
    <wsdl:part name="parameters" element="tns:uploadResponse">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="getSubIDCRegUserDataResponse">
    <wsdl:part name="parameters" element="tns:getSubIDCRegUserDataResponse">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="batchImpCommandPCResponse">
    <wsdl:part name="parameters" element="tns:batchImpCommandPCResponse">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="getSubIDCIpSegDataRequest">
    <wsdl:part name="parameters" element="tns:getSubIDCIpSegData">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="getSubIDCServiceInfoDataRequest">
    <wsdl:part name="parameters" element="tns:getSubIDCServiceInfoData">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="getSubIDCMachineHouseDataResponse">
    <wsdl:part name="parameters" element="tns:getSubIDCMachineHouseDataResponse">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="idc_flowStatResponse">
    <wsdl:part name="parameters" element="tns:idc_flowStatResponse">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="getSubIDCVirtualServerDataResponse">
    <wsdl:part name="parameters" element="tns:getSubIDCVirtualServerDataResponse">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="idc_commandackRequest">
    <wsdl:part name="parameters" element="tns:idc_commandack">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="getSubIDCRouterInfoDataResponse">
    <wsdl:part name="parameters" element="tns:getSubIDCRouterInfoDataResponse">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="getSubIDCIpSegDataResponse">
    <wsdl:part name="parameters" element="tns:getSubIDCIpSegDataResponse">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="idc_statFindRequest">
    <wsdl:part name="parameters" element="tns:idc_statFind">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="getSubIDCFrameInfoDataResponse">
    <wsdl:part name="parameters" element="tns:getSubIDCFrameInfoDataResponse">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="getSubIDCIpInfoDataRequest">
    <wsdl:part name="parameters" element="tns:getSubIDCIpInfoData">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="getSubIDCHousesHoldInfoDataRequest">
    <wsdl:part name="parameters" element="tns:getSubIDCHousesHoldInfoData">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="getSubIDCIsInfoDataResponse">
    <wsdl:part name="parameters" element="tns:getSubIDCIsInfoDataResponse">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="getSyncSensurDataResponse">
    <wsdl:part name="parameters" element="tns:getSyncSensurDataResponse">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="getSubIDCOfficerDataRequest">
    <wsdl:part name="parameters" element="tns:getSubIDCOfficerData">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="dynamic_resource_commandResponse">
    <wsdl:part name="parameters" element="tns:dynamic_resource_commandResponse">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="downloadResponse">
    <wsdl:part name="parameters" element="tns:downloadResponse">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="getSubIDCFrameInfoDataRequest">
    <wsdl:part name="parameters" element="tns:getSubIDCFrameInfoData">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="getSubIDCApproveSensorDataRequest">
    <wsdl:part name="parameters" element="tns:getSubIDCApproveSensorData">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="getSubIDCSensurDataRequest">
    <wsdl:part name="parameters" element="tns:getSubIDCSensurData">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="getSubIDCOfficerDataResponse">
    <wsdl:part name="parameters" element="tns:getSubIDCOfficerDataResponse">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="idc_deleteRegUserResponse">
    <wsdl:part name="parameters" element="tns:idc_deleteRegUserResponse">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="idc_updateISMSSystemDateTimeRequest">
    <wsdl:part name="parameters" element="tns:idc_updateISMSSystemDateTime">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="idc_deleteRegUserRequest">
    <wsdl:part name="parameters" element="tns:idc_deleteRegUser">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="getSubIDCIsInfoDataRequest">
    <wsdl:part name="parameters" element="tns:getSubIDCIsInfoData">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="getSubIDCRouterInfoDataRequest">
    <wsdl:part name="parameters" element="tns:getSubIDCRouterInfoData">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="idc_commandackResponse">
    <wsdl:part name="parameters" element="tns:idc_commandackResponse">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="getSubIDCDomainListDataRequest">
    <wsdl:part name="parameters" element="tns:getSubIDCDomainListData">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="getSubIDCSensurDataResponse">
    <wsdl:part name="parameters" element="tns:getSubIDCSensurDataResponse">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="ircs_commandResponse">
    <wsdl:part name="parameters" element="tns:ircs_commandResponse">
    </wsdl:part>
  </wsdl:message>
  <wsdl:portType name="dynamic_resource_commandPortType">
    <wsdl:operation name="getSubIDCRegUserData">
      <wsdl:input name="getSubIDCRegUserDataRequest" message="tns:getSubIDCRegUserDataRequest">
    </wsdl:input>
      <wsdl:output name="getSubIDCRegUserDataResponse" message="tns:getSubIDCRegUserDataResponse">
    </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getSubIDCFrameInfoData">
      <wsdl:input name="getSubIDCFrameInfoDataRequest" message="tns:getSubIDCFrameInfoDataRequest">
    </wsdl:input>
      <wsdl:output name="getSubIDCFrameInfoDataResponse" message="tns:getSubIDCFrameInfoDataResponse">
    </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="upload">
      <wsdl:input name="uploadRequest" message="tns:uploadRequest">
    </wsdl:input>
      <wsdl:output name="uploadResponse" message="tns:uploadResponse">
    </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="dynamic_resource_command">
      <wsdl:input name="dynamic_resource_commandRequest" message="tns:dynamic_resource_commandRequest">
    </wsdl:input>
      <wsdl:output name="dynamic_resource_commandResponse" message="tns:dynamic_resource_commandResponse">
    </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="idc_updateRegUser">
      <wsdl:input name="idc_updateRegUserRequest" message="tns:idc_updateRegUserRequest">
    </wsdl:input>
      <wsdl:output name="idc_updateRegUserResponse" message="tns:idc_updateRegUserResponse">
    </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="idc_domainDispose">
      <wsdl:input name="idc_domainDisposeRequest" message="tns:idc_domainDisposeRequest">
    </wsdl:input>
      <wsdl:output name="idc_domainDisposeResponse" message="tns:idc_domainDisposeResponse">
    </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getSubIDCGateWayData">
      <wsdl:input name="getSubIDCGateWayDataRequest" message="tns:getSubIDCGateWayDataRequest">
    </wsdl:input>
      <wsdl:output name="getSubIDCGateWayDataResponse" message="tns:getSubIDCGateWayDataResponse">
    </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getSubIDCDomainListData">
      <wsdl:input name="getSubIDCDomainListDataRequest" message="tns:getSubIDCDomainListDataRequest">
    </wsdl:input>
      <wsdl:output name="getSubIDCDomainListDataResponse" message="tns:getSubIDCDomainListDataResponse">
    </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="batchImpCommandPC">
      <wsdl:input name="batchImpCommandPCRequest" message="tns:batchImpCommandPCRequest">
    </wsdl:input>
      <wsdl:output name="batchImpCommandPCResponse" message="tns:batchImpCommandPCResponse">
    </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="download">
      <wsdl:input name="downloadRequest" message="tns:downloadRequest">
    </wsdl:input>
      <wsdl:output name="downloadResponse" message="tns:downloadResponse">
    </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="idc_updateISMSSystemDateTime">
      <wsdl:input name="idc_updateISMSSystemDateTimeRequest" message="tns:idc_updateISMSSystemDateTimeRequest">
    </wsdl:input>
      <wsdl:output name="idc_updateISMSSystemDateTimeResponse" message="tns:idc_updateISMSSystemDateTimeResponse">
    </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getSubIDCServiceInfoData">
      <wsdl:input name="getSubIDCServiceInfoDataRequest" message="tns:getSubIDCServiceInfoDataRequest">
    </wsdl:input>
      <wsdl:output name="getSubIDCServiceInfoDataResponse" message="tns:getSubIDCServiceInfoDataResponse">
    </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getSubIDCOfficerData">
      <wsdl:input name="getSubIDCOfficerDataRequest" message="tns:getSubIDCOfficerDataRequest">
    </wsdl:input>
      <wsdl:output name="getSubIDCOfficerDataResponse" message="tns:getSubIDCOfficerDataResponse">
    </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getSyncSensurData">
      <wsdl:input name="getSyncSensurDataRequest" message="tns:getSyncSensurDataRequest">
    </wsdl:input>
      <wsdl:output name="getSyncSensurDataResponse" message="tns:getSyncSensurDataResponse">
    </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getSubIDCIpInfoData">
      <wsdl:input name="getSubIDCIpInfoDataRequest" message="tns:getSubIDCIpInfoDataRequest">
    </wsdl:input>
      <wsdl:output name="getSubIDCIpInfoDataResponse" message="tns:getSubIDCIpInfoDataResponse">
    </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getSubIDCSensurData">
      <wsdl:input name="getSubIDCSensurDataRequest" message="tns:getSubIDCSensurDataRequest">
    </wsdl:input>
      <wsdl:output name="getSubIDCSensurDataResponse" message="tns:getSubIDCSensurDataResponse">
    </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getSubIDCVirtualServerData">
      <wsdl:input name="getSubIDCVirtualServerDataRequest" message="tns:getSubIDCVirtualServerDataRequest">
    </wsdl:input>
      <wsdl:output name="getSubIDCVirtualServerDataResponse" message="tns:getSubIDCVirtualServerDataResponse">
    </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getSubIDCRouterInfoData">
      <wsdl:input name="getSubIDCRouterInfoDataRequest" message="tns:getSubIDCRouterInfoDataRequest">
    </wsdl:input>
      <wsdl:output name="getSubIDCRouterInfoDataResponse" message="tns:getSubIDCRouterInfoDataResponse">
    </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="idc_commandack">
      <wsdl:input name="idc_commandackRequest" message="tns:idc_commandackRequest">
    </wsdl:input>
      <wsdl:output name="idc_commandackResponse" message="tns:idc_commandackResponse">
    </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="idc_statFind">
      <wsdl:input name="idc_statFindRequest" message="tns:idc_statFindRequest">
    </wsdl:input>
      <wsdl:output name="idc_statFindResponse" message="tns:idc_statFindResponse">
    </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getSubIDCApproveSensorData">
      <wsdl:input name="getSubIDCApproveSensorDataRequest" message="tns:getSubIDCApproveSensorDataRequest">
    </wsdl:input>
      <wsdl:output name="getSubIDCApproveSensorDataResponse" message="tns:getSubIDCApproveSensorDataResponse">
    </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ircs_command">
      <wsdl:input name="ircs_commandRequest" message="tns:ircs_commandRequest">
    </wsdl:input>
      <wsdl:output name="ircs_commandResponse" message="tns:ircs_commandResponse">
    </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="idc_flowStat">
      <wsdl:input name="idc_flowStatRequest" message="tns:idc_flowStatRequest">
    </wsdl:input>
      <wsdl:output name="idc_flowStatResponse" message="tns:idc_flowStatResponse">
    </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getSubIDCMachineHouseData">
      <wsdl:input name="getSubIDCMachineHouseDataRequest" message="tns:getSubIDCMachineHouseDataRequest">
    </wsdl:input>
      <wsdl:output name="getSubIDCMachineHouseDataResponse" message="tns:getSubIDCMachineHouseDataResponse">
    </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getSubIDCIpSegData">
      <wsdl:input name="getSubIDCIpSegDataRequest" message="tns:getSubIDCIpSegDataRequest">
    </wsdl:input>
      <wsdl:output name="getSubIDCIpSegDataResponse" message="tns:getSubIDCIpSegDataResponse">
    </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="idc_deleteRegUser">
      <wsdl:input name="idc_deleteRegUserRequest" message="tns:idc_deleteRegUserRequest">
    </wsdl:input>
      <wsdl:output name="idc_deleteRegUserResponse" message="tns:idc_deleteRegUserResponse">
    </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getSubIDCHousesHoldInfoData">
      <wsdl:input name="getSubIDCHousesHoldInfoDataRequest" message="tns:getSubIDCHousesHoldInfoDataRequest">
    </wsdl:input>
      <wsdl:output name="getSubIDCHousesHoldInfoDataResponse" message="tns:getSubIDCHousesHoldInfoDataResponse">
    </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getSubIDCIsInfoData">
      <wsdl:input name="getSubIDCIsInfoDataRequest" message="tns:getSubIDCIsInfoDataRequest">
    </wsdl:input>
      <wsdl:output name="getSubIDCIsInfoDataResponse" message="tns:getSubIDCIsInfoDataResponse">
    </wsdl:output>
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:binding name="dynamic_resource_commandHttpBinding" type="tns:dynamic_resource_commandPortType">
    <wsdlsoap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
    <wsdl:operation name="getSubIDCRegUserData">
      <wsdlsoap:operation soapAction=""/>
      <wsdl:input name="getSubIDCRegUserDataRequest">
        <wsdlsoap:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="getSubIDCRegUserDataResponse">
        <wsdlsoap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getSubIDCFrameInfoData">
      <wsdlsoap:operation soapAction=""/>
      <wsdl:input name="getSubIDCFrameInfoDataRequest">
        <wsdlsoap:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="getSubIDCFrameInfoDataResponse">
        <wsdlsoap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="upload">
      <wsdlsoap:operation soapAction=""/>
      <wsdl:input name="uploadRequest">
        <wsdlsoap:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="uploadResponse">
        <wsdlsoap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="dynamic_resource_command">
      <wsdlsoap:operation soapAction=""/>
      <wsdl:input name="dynamic_resource_commandRequest">
        <wsdlsoap:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="dynamic_resource_commandResponse">
        <wsdlsoap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="idc_updateRegUser">
      <wsdlsoap:operation soapAction=""/>
      <wsdl:input name="idc_updateRegUserRequest">
        <wsdlsoap:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="idc_updateRegUserResponse">
        <wsdlsoap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="idc_domainDispose">
      <wsdlsoap:operation soapAction=""/>
      <wsdl:input name="idc_domainDisposeRequest">
        <wsdlsoap:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="idc_domainDisposeResponse">
        <wsdlsoap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getSubIDCGateWayData">
      <wsdlsoap:operation soapAction=""/>
      <wsdl:input name="getSubIDCGateWayDataRequest">
        <wsdlsoap:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="getSubIDCGateWayDataResponse">
        <wsdlsoap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getSubIDCDomainListData">
      <wsdlsoap:operation soapAction=""/>
      <wsdl:input name="getSubIDCDomainListDataRequest">
        <wsdlsoap:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="getSubIDCDomainListDataResponse">
        <wsdlsoap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="batchImpCommandPC">
      <wsdlsoap:operation soapAction=""/>
      <wsdl:input name="batchImpCommandPCRequest">
        <wsdlsoap:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="batchImpCommandPCResponse">
        <wsdlsoap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="download">
      <wsdlsoap:operation soapAction=""/>
      <wsdl:input name="downloadRequest">
        <wsdlsoap:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="downloadResponse">
        <wsdlsoap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="idc_updateISMSSystemDateTime">
      <wsdlsoap:operation soapAction=""/>
      <wsdl:input name="idc_updateISMSSystemDateTimeRequest">
        <wsdlsoap:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="idc_updateISMSSystemDateTimeResponse">
        <wsdlsoap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getSubIDCServiceInfoData">
      <wsdlsoap:operation soapAction=""/>
      <wsdl:input name="getSubIDCServiceInfoDataRequest">
        <wsdlsoap:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="getSubIDCServiceInfoDataResponse">
        <wsdlsoap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getSubIDCOfficerData">
      <wsdlsoap:operation soapAction=""/>
      <wsdl:input name="getSubIDCOfficerDataRequest">
        <wsdlsoap:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="getSubIDCOfficerDataResponse">
        <wsdlsoap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getSyncSensurData">
      <wsdlsoap:operation soapAction=""/>
      <wsdl:input name="getSyncSensurDataRequest">
        <wsdlsoap:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="getSyncSensurDataResponse">
        <wsdlsoap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getSubIDCIpInfoData">
      <wsdlsoap:operation soapAction=""/>
      <wsdl:input name="getSubIDCIpInfoDataRequest">
        <wsdlsoap:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="getSubIDCIpInfoDataResponse">
        <wsdlsoap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getSubIDCSensurData">
      <wsdlsoap:operation soapAction=""/>
      <wsdl:input name="getSubIDCSensurDataRequest">
        <wsdlsoap:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="getSubIDCSensurDataResponse">
        <wsdlsoap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getSubIDCVirtualServerData">
      <wsdlsoap:operation soapAction=""/>
      <wsdl:input name="getSubIDCVirtualServerDataRequest">
        <wsdlsoap:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="getSubIDCVirtualServerDataResponse">
        <wsdlsoap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getSubIDCRouterInfoData">
      <wsdlsoap:operation soapAction=""/>
      <wsdl:input name="getSubIDCRouterInfoDataRequest">
        <wsdlsoap:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="getSubIDCRouterInfoDataResponse">
        <wsdlsoap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="idc_commandack">
      <wsdlsoap:operation soapAction=""/>
      <wsdl:input name="idc_commandackRequest">
        <wsdlsoap:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="idc_commandackResponse">
        <wsdlsoap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="idc_statFind">
      <wsdlsoap:operation soapAction=""/>
      <wsdl:input name="idc_statFindRequest">
        <wsdlsoap:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="idc_statFindResponse">
        <wsdlsoap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getSubIDCApproveSensorData">
      <wsdlsoap:operation soapAction=""/>
      <wsdl:input name="getSubIDCApproveSensorDataRequest">
        <wsdlsoap:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="getSubIDCApproveSensorDataResponse">
        <wsdlsoap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ircs_command">
      <wsdlsoap:operation soapAction=""/>
      <wsdl:input name="ircs_commandRequest">
        <wsdlsoap:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="ircs_commandResponse">
        <wsdlsoap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="idc_flowStat">
      <wsdlsoap:operation soapAction=""/>
      <wsdl:input name="idc_flowStatRequest">
        <wsdlsoap:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="idc_flowStatResponse">
        <wsdlsoap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getSubIDCMachineHouseData">
      <wsdlsoap:operation soapAction=""/>
      <wsdl:input name="getSubIDCMachineHouseDataRequest">
        <wsdlsoap:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="getSubIDCMachineHouseDataResponse">
        <wsdlsoap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getSubIDCIpSegData">
      <wsdlsoap:operation soapAction=""/>
      <wsdl:input name="getSubIDCIpSegDataRequest">
        <wsdlsoap:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="getSubIDCIpSegDataResponse">
        <wsdlsoap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="idc_deleteRegUser">
      <wsdlsoap:operation soapAction=""/>
      <wsdl:input name="idc_deleteRegUserRequest">
        <wsdlsoap:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="idc_deleteRegUserResponse">
        <wsdlsoap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getSubIDCHousesHoldInfoData">
      <wsdlsoap:operation soapAction=""/>
      <wsdl:input name="getSubIDCHousesHoldInfoDataRequest">
        <wsdlsoap:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="getSubIDCHousesHoldInfoDataResponse">
        <wsdlsoap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getSubIDCIsInfoData">
      <wsdlsoap:operation soapAction=""/>
      <wsdl:input name="getSubIDCIsInfoDataRequest">
        <wsdlsoap:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="getSubIDCIsInfoDataResponse">
        <wsdlsoap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:service name="dynamic_resource_command">
    <wsdl:port name="dynamic_resource_commandHttpPort" binding="tns:dynamic_resource_commandHttpBinding">
      <wsdlsoap:address location="http://**************:8888/IRCSManager/DynamicResourceWebService/dynamic_resource_command"/>
    </wsdl:port>
  </wsdl:service>
</wsdl:definitions>
