#! /bin/bash
# 此脚本与newicp代码放置在同一目录层级中
# 推荐：newicp目录放置在~/production下，既/production/newicp;脚本路径为~/production/pub_new_icp_server_jumper.sh
# 在~/production/中执行·sh pub_new_icp_server_jumper.sh·

GITADRESS="**************************:CRD/icp.git"
REMOTE_1="root@172.18.180.51"
REMOTE_2="root@172.18.176.47"
REMOTE_PATH="/data/"
# OBJETCD_DIR既是项目目录，也是pm2启动的项目名
OBJETCD_DIR="newicp"
NEW_VERSION="newicp.tar"


cd "./newicp"
git checkout master
git pull -f
cd ..
tar cvf $NEW_VERSION "./"$OBJETCD_DIR
scp $NEW_VERSION $REMOTE_1:$REMOTE_PATH
scp $NEW_VERSION $REMOTE_2:$REMOTE_PATH



ssh -t -t $REMOTE_1 << remotessh
cd /data/
sh pub_new_icp_server_online.sh
exit
remotessh

ssh -t -t $REMOTE_2 << remotessh
cd /data/
sh pub_new_icp_server_online.sh
exit
remotessh
rm $NEW_VERSION
