#! /bin/bash
# 发布脚本与newicp代码放置在同一目录中
# 推荐newicp目录放置在~/production下，既/production/newicp;脚本路径为~/production/pub_new_icp_server_jumper.sh
# 目标机需要有旧进程在跑，需提前设置环境变量 export NODE_ENV="production"

GITADRESS="**************************:CRD/icp.git"
REMOTE_1="root@10.182.44.184"
REMOTE_PATH="/data/"
# OBJETCD_DIR既是项目目录，也是pm2启动的项目名
OBJETCD_DIR="newicp"
NEW_VERSION="newicp.tar"


cd "./newicp"
git checkout master
git pull -f
cd ..
tar cvf $NEW_VERSION "./"$OBJETCD_DIR
scp $NEW_VERSION $REMOTE_1:$REMOTE_PATH



ssh -t -t $REMOTE_1 << remotessh
cd /data/
sh pub_newicp_backend_online.sh
exit
remotessh
