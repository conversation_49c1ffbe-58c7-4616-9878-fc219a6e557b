#! /bin/bash

BACKUP_PACKAGE="hegui-backend-"`date +%s`".tar"

mv "./hegui-backend" "./hegui-backend-bak"
tar -zcvf $BACKUP_PACKAGE "./hegui-backend-bak"
mv "./"$BACKUP_PACKAGE "./backup"
tar -zxvf "./hegui-backend.tar.gz"
cd "./hegui-backend" && sed -i "s/development/test/g" configs/env.js && npm --registry=https://registry.npm.taobao.org i
pm2 restart "hegui-backend"
pm2 restart "hegui-backend-cron"
rm -rf "./hegui-backend.tar.gz"
