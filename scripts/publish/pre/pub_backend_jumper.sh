#! /bin/bash
# 发布脚本
# 目标机需要有旧进程在跑，需提前设置环境变量 export NODE_ENV="test"

GITADRESS="**************************:CRD/hegui-backend.git"
REMOTE="root@192.168.151.250"
REMOTE_PATH="/data/pre/"
# OBJETCD_DIR既是项目目录，也是pm2启动的项目名
OBJETCD_DIR="hegui-backend"
NEW_VERSION="hegui-backend.tar.gz"


cd "./hegui-backend"
git pull
cd ..
tar -zcvf $NEW_VERSION "./"$OBJETCD_DIR
scp $NEW_VERSION $REMOTE:$REMOTE_PATH

#ssh $REMOTE << remotessh
#cd $REMOTE_PATH
#sh pub_backend.sh
#exit
#remotessh
