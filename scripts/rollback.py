import etcd
import os
import sys
import os

ENV=os.getenv('NODE_ENV')
NODE_ENV

etcdhost = "*************"
etcdpost = 65001

client = etcd.Client(host=etcdhost, port=etcdpost)
file = open(sys.argv[1])
for line in file.xreadlines():
	unendl = line.strip('\n')
	print unendl
	if(unendl.count(":") == 1):
		keyvalue = unendl.split(":")
		client.write(keyvalue[0], keyvalue[1])
	else:
		print("unendl" + unendl + "keyErr")
