apiVersion: apps/v1
kind: Deployment
metadata:
  name: newicp-console-backend
  namespace: prj-crd
spec:
  replicas: 1
  selector:
    matchLabels:
      app: newicp-console-backend
  template:
    metadata:
      labels:
        app: newicp-console-backend
    spec:
      containers:
      - name: newicp-console-backend
        image: hub.ucloudadmin.com/tpl_crd_icp/newicp-console-backend-gray:v0.0.7
        resources:
          requests:
            memory: 300Mi
            cpu: 200m
          limits:
            memory: 400Mi
            cpu: 200m
        ports:
        - containerPort: 6060
      imagePullSecrets:
      - name: lywregistry
---
apiVersion: v1
kind: Service
metadata:
  name: newicp-console-backend
  namespace: prj-crd
spec:
  ports:
  - port: 6060
    targetPort: 6060
  selector:
    app: newicp-console-backend
  type: ClusterIP
