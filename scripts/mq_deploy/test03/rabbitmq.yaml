apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: rabbitmq-test03
  namespace: prj-crd
spec:
  replicas: 1
  selector:
    matchLabels:
      app: rabbitmq-test03
  template:
    metadata:
      labels:
        app: rabbitmq-test03
    spec:
      containers:
      - name: rabbitmq-test03
        image: hub-test.service.ucloud.cn/crd_rabbitmq/rabbitmq-add-delay:1.1.6
        resources:
          requests:
            memory: 300Mi
            cpu: 300m
          limits:
            memory: 1024Mi
            cpu: 1024m
        ports:
            - name: http
              protocol: TCP
              containerPort: 15672
            - name: amqp
              protocol: TCP
              containerPort: 5672
        env:
          - name: RABBITMQ_ERLANG_COOKIE
            value: "rabbitmq-test03"
          - name: RABBITMQ_NODENAME
            value: "rabbitmq-test03"
          - name: RABBITMQ_DEFAULT_USER
            value: "admin"
          - name: RABBITMQ_DEFAULT_PASS
            value: "Hegui2023.mq"
        imagePullSecrets:
          - name: lywregistry
---
apiVersion: v1
kind: Service
metadata:
  name: rabbitmq-test03
  namespace: prj-crd
spec:
  ports:
    - name: http
      protocol: TCP
      port: 15672
      targetPort: 15672
    - name: amqp
      protocol: TCP
      port: 5672
      targetPort: 5672
  selector:
    app: rabbitmq-test03
  type: ClusterIP

