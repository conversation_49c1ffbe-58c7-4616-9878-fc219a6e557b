apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: rabbitmq-online
  namespace: prj-icp
spec:
  replicas: 1
  selector:
    matchLabels:
      app: rabbitmq-online
  template:
    metadata:
      labels:
        app: rabbitmq-online
    spec:
      containers:
      - name: rabbitmq-online
        image: hub.ucloudadmin.com/tpl_crd_mq/rabbitmq-add-delay:1.1.6
        resources:
          requests:
            memory: 8Gi
            cpu: 1600m
          limits:
            memory: 9Gi
            cpu: '2'
        ports:
            - name: http
              protocol: TCP
              containerPort: 15672
            - name: amqp
              protocol: TCP
              containerPort: 5672
        volumeMounts:
            - name: data
              mountPath: /var/lib/rabbitmq
        env:
          - name: RABBITMQ_ERLANG_COOKIE
            value: "rabbitmq-online"
          - name: RABBITMQ_NODENAME
            value: "rabbitmq-online"
          - name: RABBITMQ_DEFAULT_USER
            value: "admin"
          - name: RABBITMQ_DEFAULT_PASS
            value: "SGVndWkyMD"
      volumes:
      - name: data
        persistentVolumeClaim:
          claimName: rabbitmq-online-pv-ufs
          readOnly: false
      imagePullSecrets:
      - name: lywregistry
---
apiVersion: v1
kind: Service
metadata:
  name: rabbitmq-online
  namespace: prj-crd
spec:
  ports:
    - name: http
      protocol: TCP
      port: 15672
      targetPort: 15672
    - name: amqp
      protocol: TCP
      port: 5672
      targetPort: 5672
  selector:
    app: rabbitmq-online
  type: ClusterIP

