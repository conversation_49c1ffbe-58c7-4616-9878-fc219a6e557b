apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: rabbitmq-gray
  namespace: prj-icp
spec:
  replicas: 1
  selector:
    matchLabels:
      app: rabbitmq-gray
  template:
    metadata:
      labels:
        app: rabbitmq-gray
    spec:
      containers:
      - name: rabbitmq-gray
        image: hub.ucloudadmin.com/tpl_crd_mq/rabbitmq-add-delay:1.1.6
        resources:
          requests:
            memory: 300Mi
            cpu: 300m
          limits:
            memory: 1024Mi
            cpu: 1024m
        ports:
            - name: http
              protocol: TCP
              containerPort: 15672
            - name: amqp
              protocol: TCP
              containerPort: 5672
        volumeMounts:
            - name: data
              mountPath: /var/lib/rabbitmq
        env:
          - name: RABBITMQ_ERLANG_COOKIE
            value: "rabbitmq-gray"
          - name: RABBITMQ_NODENAME
            value: "rabbitmq-gray"
          - name: RABBITMQ_DEFAULT_USER
            value: "admin"
          - name: RABBITMQ_DEFAULT_PASS
            value: "Hegui2023.mq"
      volumes:
      - name: data
        persistentVolumeClaim:
          claimName: rabbitmq-gray-pv-ufs
          readOnly: false
      imagePullSecrets:
      - name: lywregistry
---
apiVersion: v1
kind: Service
metadata:
  name: rabbitmq-gray
  namespace: prj-icp
spec:
  ports:
    - name: http
      protocol: TCP
      port: 15672
      targetPort: 15672
    - name: amqp
      protocol: TCP
      port: 5672
      targetPort: 5672
  selector:
    app: rabbitmq-gray
  type: ClusterIP

