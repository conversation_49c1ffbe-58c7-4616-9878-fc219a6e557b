#!/usr/bin/python2.6
# -*- coding: utf-8 -*-
# 读取文件中的Key,Value.生成URL并请求
import httplib
import sys
import os
import re
import json
import requests
import urllib

urlString="http://hegui.ucloudadmin.com/?Action=CreateRecord&IP="
#urlString = "http://127.0.0.1:4040/?Action=CreateRecord&IP="

file = open(sys.argv[1])
all_Audited_list = []
for i in file.xreadlines():
	i = i.strip('\n')
	if ("/one_key_block_domains/" not in i) and ("/white_ip_list/" not in i) and ("/evil_domains/" not in i):
		every_domain = i.split(":")
		every_domain[0] = every_domain[0].split("/")
		every_domain[0] = every_domain[0][-1]
		for i in every_domain[1].split(","):
			reqString = urlString + every_domain[0] + "&Domain=" + i
			#print reqString
			r = requests.get(reqString)
			print every_domain[0] + i + ":"+r.content
