// 调用接口获取2020年假日信息
// 用来计算工作日： 如7个工作日后天数
//
//
const ENV = require('../configs/env')
global.CONFIG = require('../configs/' + ENV + '/config')

const mysql = require('../libs/mysql')
// const util = require('util')
const moment = require('moment')

const db = mysql.get('weibeian2')

// const ladtDay = moment().endOf('year').format('YYYY-MM-DD').toString()
let holidays = {},
    holidayList = []

var http = require('http')
const year = '2020'
var options = {
    method: 'GET',
    hostname: 'lanfly.vicp.io',
    port: null,
    path: 'timor.tech/api/holiday/info/',
    headers: {
        'cache-control': 'no-cache',
        'postman-token': 'ada98bad-cb3b-9e84-090f-f6ba1472bc50',
    },
}
async function queryHoliday() {
    try {
        holidays = await requestApi()
        console.log('TCL: queryHoliday -> holidays', holidays)
        await operateHolidayList()
        db.insert_ignore('t_holiday_list', holidayList, (err, insertInfo) => {
            console.log(err)
            console.log('TCL: queryHoliday -> insertInfo', insertInfo)
            // if (err) { return reject(new this.Err(err, 30003)) }
            // resolve(insertInfo)
            return
        })
    } catch (e) {
        console.log(e)
    }
}
async function operateHolidayList() {
    let currentDate = moment('2020-01-01')
        .startOf('year')
        .format('YYYY-MM-DD')
        .toString() //当年的第一天
    for (let i = 0; i < 365; i++) {
        let week = i % 7
        if (holidays[currentDate.substring(5)]) {
            const { name, holiday } = holidays[currentDate.substring(5)]
            if (holiday) {
                holidayList.push({
                    name: name,
                    holiday: 1,
                    date: currentDate,
                })
            } else {
                holidayList.push({
                    name: name,
                    holiday: 0,
                    date: currentDate,
                })
            }
        } else {
            holidayList.push({
                name: week < 5 ? '工作日' : '周末',
                holiday: week < 5 ? 0 : 1,
                date: currentDate,
            })
        }
        currentDate = moment(currentDate)
            .add(1, 'day')
            .format('YYYY-MM-DD')
            .toString()
    }
}
function requestApi() {
    return new Promise((resolve) => {
        options.path = `http://timor.tech/api/holiday/year/${year}`
        console.log(111 + options.path)
        var req = http.request(options.path, function (res) {
            console.log(222)
            var chunks = []

            res.on('data', function (chunk) {
                chunks.push(chunk)
            })

            res.on('end', function () {
                var body = Buffer.concat(chunks)
                try {
                    body = JSON.parse(body.toString())
                    if (body.code === 0) {
                        // holidays = body.holiday
                        resolve(body.holiday)
                    }
                } catch (err) {
                    console.log(err)
                }
            })
        })
        req.end()
    })
}

queryHoliday()
