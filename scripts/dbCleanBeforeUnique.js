/*
 * 该脚本的作用是刷新几个数据库
 * 1. `t_batch_org` 增加唯一主键 `UNIQUE KEY(batch_id,org_id)`
 * 2. `t_notify` 增加唯一主键 `UNIQUE KEY(batch_id,batch_org_id,mobile,email)`
 * 3. `t_illegal_batch` 增加唯一主键 `UNIQUE KEY(batch_id,org_id)`
 * 4. `t_illegal_notify` 增加唯一主键 `UNIQUE KEY(batch_id,batch_org_id,mobile,email)`
 *
 * 流程如下
 * 1.备份上述几个数据库
 * 2.删除上述几个数据库内不合规数据。（ps: 数据之间有的有关联）
 * 3.建立唯一索引
 *
 *
 * PS: 目前是把所有数据读取到内存中
 */

const ENV = require('../configs/env')
global.CONFIG = require('../configs/' + ENV + '/config')

const mysql = require('../libs/mysql')
const util = require('util')
const moment = require('moment')

const db = mysql.get('weibeian2')

const TABLES_T_BATCH = ['t_batch_org', 't_notify', 't_record']

const TABLES_T_ILLEGAL_BATCH = [
    't_illegal_batch_org',
    't_illegal_notify',
    't_illegal_record',
]

async function main() {
    await tBatchFlush(TABLES_T_BATCH)

    await tIllegalBatchFlush(TABLES_T_ILLEGAL_BATCH)
}

main().catch(console.error).then(console.log)

async function tBatchFlush(TABLES_T_BATCH) {
    const query = util.promisify(db.query)
    await Promise.all(TABLES_T_BATCH.map((tablename) => dbBackup(tablename)))
    console.log('开始删除t_batch_org中冗余数据及其相关数据')
    await delTBatchData(TABLES_T_BATCH)
    console.log('删除t_batch_org中冗余数据及其相关数据，成功')

    console.log('开始删除t_notify中冗余数据')
    await delNotifyData(TABLES_T_BATCH)
    console.log('删除t_notify中冗余数据,成功')

    await query(
        `ALTER TABLE ${TABLES_T_BATCH[0]} ADD UNIQUE KEY batch(batch_id, org_id)`
    )
    await query(
        `ALTER TABLE ${TABLES_T_BATCH[1]} ADD UNIQUE KEY notify(batch_id, batch_org_id,mobile,email)`
    )
}

async function tIllegalBatchFlush(TABLES_T_ILLEGAL_BATCH) {
    const query = util.promisify(db.query)
    await Promise.all(
        TABLES_T_ILLEGAL_BATCH.map((tablename) => dbBackup(tablename))
    )
    console.log('开始删除t_batch_org中冗余数据及其相关数据')
    await delTBatchData(TABLES_T_ILLEGAL_BATCH)
    console.log('删除t_batch_org中冗余数据及其相关数据，成功')

    console.log('开始删除t_notify中冗余数据')
    await delNotifyData(TABLES_T_ILLEGAL_BATCH)
    console.log('删除t_notify中冗余数据,成功')
    await query(
        `ALTER TABLE ${TABLES_T_ILLEGAL_BATCH[0]} ADD UNIQUE KEY batch(batch_id, org_id)`
    )
    await query(
        `ALTER TABLE ${TABLES_T_ILLEGAL_BATCH[1]} ADD UNIQUE KEY notify(batch_id, batch_org_id,mobile,email)`
    )
}

async function dbBackup(tablename) {
    const query = util.promisify(db.query)
    await query(
        `CREATE TABLE ${tablename}_backup_${moment().format(
            'YYYYMM'
        )} LIKE ${tablename}`
    )
    await query(
        `INSERT INTO ${tablename}_backup_${moment().format(
            'YYYYMM'
        )} SELECT * FROM ${tablename}`
    )
}

async function delTBatchData(tables) {
    const query = util.promisify(db.query)

    const batchData = await query(`SELECT * FROM ${tables[0]}`)

    const replaces = handleBatchData(batchData)

    for (let replace of replaces) {
        console.log(
            '======正在删除batch_org_id 相关数据：' + replace.now.batch_org_id
        )
        let deleteBatch = `delete from ${tables[0]} where id = ${replace.now.batch_org_id}`
        // console.log(deleteBatch)
        await query(deleteBatch)

        let deleteNotifyStr = `delete from ${tables[1]} where batch_id = ${replace.now.batch_id} and batch_org_id = ${replace.now.batch_org_id}`
        // console.log(deleteNotifyStr)
        await query(deleteNotifyStr)

        let replaceRecordStr = `UPDATE ${tables[2]} 
                                SET batch_org_id = ${replace.re.batch_org_id} 
                                WHERE batch_id = ${replace.now.batch_id}
                                AND batch_org_id = ${replace.now.batch_org_id}`
        // console.log(replaceRecordStr)

        await query(replaceRecordStr)
    }
}

/*
 * @return {Array}
 */
function handleBatchData(batchData) {
    const batchMap = new Map()

    const replaces = []
    batchData.forEach((item) => {
        let value = `${item.batch_id}-${item.org_id}`

        if (batchMap.has(value)) {
            let batch_org_id = batchMap.get(value)
            replaces.push({
                now: {
                    batch_id: item.batch_id,
                    batch_org_id: item.id,
                },
                re: {
                    batch_id: item.batch_id,
                    batch_org_id,
                },
            })
        } else {
            batchMap.set(value, item.id)
        }
    })
    return replaces
}

async function delNotifyData(tables) {
    const query = util.promisify(db.query)
    let notifyData = await query(`SELECT * FROM ${tables[1]}`)
    const deleteIds = handleNotifyData(notifyData)
    console.log(`delete id ${deleteIds.join(',')}`)

    const deleteSql = util.promisify(
        db.where({
            id: deleteIds,
        }).delete
    )

    await deleteSql(tables[1])
}

function handleNotifyData(notifyData) {
    let deleteIds = []

    const dict = new Set()

    notifyData.forEach((notify) => {
        let key = `${notify.batch_id}-${notify.batch_org_id}-${notify.mobile}-${notify.email}`

        if (dict.has(key)) {
            deleteIds.push(notify.id)
        } else {
            dict.add(key)
        }
    })

    return deleteIds
}
