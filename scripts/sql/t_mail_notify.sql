CREATE TABLE `t_mail_notify` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `batch_id` int(11) NOT NULL DEFAULT '0' COMMENT '批次Id,对应t_mail_record的id',
  `mobile` varchar(255) NOT NULL DEFAULT '',
  `email` varchar(255) NOT NULL DEFAULT '' COMMENT '邮件地址',
  `email_status` int(4) NOT NULL DEFAULT '0' COMMENT '邮件状态',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT '0' COMMENT '更新时间',
  `email_task_id` varchar(64) NOT NULL DEFAULT '' COMMENT '邮件Id',
  `notify_type` varchar(64) NOT NULL DEFAULT '' COMMENT '通知类型bu\\客户\\',
  `email_retry_times` int(11) NOT NULL DEFAULT '0' COMMENT '重试次数',
  `channel` varchar(255) NOT NULL DEFAULT '1',
  `text` text COMMENT '邮件正文',
  `attachment` varchar(1024) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `notify` (`batch_id`,`email`)
) ENGINE=InnoDB AUTO_INCREMENT=5090 DEFAULT CHARSET=utf8;
