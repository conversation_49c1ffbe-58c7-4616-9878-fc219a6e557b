# ************************************************************
# Sequel Pro SQL dump
# Version 4541
#
# http://www.sequelpro.com/
# https://github.com/sequelpro/sequelpro
#
# Host: 127.0.0.1 (MySQL 5.7.34)
# Database: icp
# Generation Time: 2021-05-27 09:20:22 +0000
# ************************************************************


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;


# Dump of table t_company_info_check_batch
# ------------------------------------------------------------

DROP TABLE IF EXISTS `t_company_info_check_batch`;

CREATE TABLE `t_company_info_check_batch` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `remark` varchar(40) NOT NULL DEFAULT '',
  `create_time` int(11) NOT NULL,
  `update_time` int(11) NOT NULL,
  `source_type` int(11) NOT NULL,
  `status` int(11) NOT NULL,
  `creator` varchar(128) NOT NULL DEFAULT '',
  `file_name` varchar(256) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;



# Dump of table t_company_info_check_record
# ------------------------------------------------------------

DROP TABLE IF EXISTS `t_company_info_check_record`;

CREATE TABLE `t_company_info_check_record` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `batch_id` int(11) NOT NULL,
  `organizer_type` int(11) NOT NULL,
  `organizer_name` varchar(256) DEFAULT NULL,
  `organizer_license_area` varchar(1024) NOT NULL DEFAULT '',
  `pic_main_name` varchar(256) DEFAULT NULL,
  `pic_main_license_id` varchar(256) NOT NULL DEFAULT '',
  `organizer_license_id` varchar(256) NOT NULL,
  `create_time` int(11) NOT NULL,
  `update_time` int(11) NOT NULL,
  `status` int(11) NOT NULL DEFAULT '0',
  `check_result` int(11) NOT NULL DEFAULT '0',
  `message` tinytext,
  `remark` varchar(1024) DEFAULT NULL,
  `return_object` tinytext,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;




/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;
/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
