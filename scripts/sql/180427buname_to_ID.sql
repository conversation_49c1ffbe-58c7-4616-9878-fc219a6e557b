-- 将原先存储的BU中文切换为id 

-- 生成BUlist表
CREATE TABLE `t_bu_list` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `bu_name` varchar(255) NOT NULL DEFAULT '',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=14 DEFAULT CHARSET=utf8;

-- 插入BU信息
INSERT INTO `t_bu_list` (`id`, `bu_name`)
VALUES
	(1, 'IEU'),
	(2, 'SML'),
	(3, 'MMU'),
	(4, 'TEU'),
	(5, 'IBU');


-- 修改表名
 ALTER  TABLE t_bu RENAME TO t_bu_contact
-- 修改t_bu_contact字段名
 ALTER TABLE t_bu_contact CHANGE bu_name bu_id varchar(255)
-- 更新t_bu_contact为ID
UPDATE t_bu_contact SET bu_id=1 WHERE bu_id='互动娱乐事业部-IEU'
UPDATE t_bu_contact SET bu_id=2 WHERE bu_id='中小客户线-SMC'
UPDATE t_bu_contact SET bu_id=3 WHERE bu_id='多媒体事业部-MMU'
UPDATE t_bu_contact SET bu_id=4 WHERE bu_id='企业事业部-TEU'
UPDATE t_bu_contact SET bu_id=5 WHERE bu_id='互联网事业部-IBU'
 alter table t_bu_contact modify column bu_id int(11);

UPDATE t_batch_org SET bu=1 WHERE bu='互动娱乐事业部-IEU';
UPDATE t_batch_org SET bu=2 WHERE bu='中小客户线-SMC';
UPDATE t_batch_org SET bu=3 WHERE bu='多媒体事业部-MMU';
UPDATE t_batch_org SET bu=4 WHERE bu='企业事业部-TEU';
UPDATE t_batch_org SET bu=5 WHERE bu='互联网事业部-IBU';
 alter table t_batch_org modify column bu int(11);


UPDATE t_bu_notify SET bu=1 WHERE bu='互动娱乐事业部-IEU';
UPDATE t_bu_notify SET bu=2 WHERE bu='中小客户线-SMC';
UPDATE t_bu_notify SET bu=3 WHERE bu='多媒体事业部-MMU';
UPDATE t_bu_notify SET bu=4 WHERE bu='企业事业部-TEU';
UPDATE t_bu_notify SET bu=5 WHERE bu='互联网事业部-IBU';
 alter table t_bu_notify modify column bu int(11);