CREATE TABLE `t_mail_record` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `subject` varchar(1024) NOT NULL DEFAULT '' COMMENT '标题',
  `from` varchar(255) NOT NULL DEFAULT '' COMMENT '发件人',
  `text` longtext NOT NULL COMMENT '邮件正文',
  `attachment` varchar(255) DEFAULT NULL COMMENT '附件',
  `source_id` varchar(255) NOT NULL DEFAULT '',
  `create_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
  `status` int(11) NOT NULL DEFAULT '0' COMMENT '状态',
  `type` varchar(255) NOT NULL DEFAULT '0' COMMENT '类型',
  `ip` varchar(255) DEFAULT NULL COMMENT '解析IP',
  `org_id` int(11) DEFAULT NULL COMMENT '项目Id',
  `company_id` int(11) DEFAULT NULL COMMENT '公司Id',
  PRIMARY KEY (`id`),
  UNIQUE KEY `UNIQUE_MESSAGE_ID` (`from`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=4854 DEFAULT CHARSET=utf8;
