# ************************************************************
# Sequel Pro SQL dump
# Version 4541
#
# http://www.sequelpro.com/
# https://github.com/sequelpro/sequelpro
#
# Host: *************** (MySQL 5.5.35)
# Database: hegui
# Generation Time: 2018-03-21 06:16:47 +0000
# ************************************************************


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;


# Dump of table t_log_action
# ------------------------------------------------------------

CREATE TABLE `t_log_action` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL DEFAULT '',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

LOCK TABLES `t_log_action` WRITE;
/*!40000 ALTER TABLE `t_log_action` DISABLE KEYS */;

INSERT INTO `t_log_action` (`id`, `name`)
VALUES
	(1,'UpdateIdentityStatus');

/*!40000 ALTER TABLE `t_log_action` ENABLE KEYS */;
UNLOCK TABLES;


# Dump of table t_verify_log
# ------------------------------------------------------------

CREATE TABLE `t_verify_log` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `company_id` int(11) NOT NULL,
  `action_id` int(11) NOT NULL,
  `create_time` int(11) DEFAULT NULL,
  `remark` varchar(256) DEFAULT NULL,
  `result` int(11) NOT NULL DEFAULT '0',
  `operator` varchar(255) NOT NULL DEFAULT '',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

LOCK TABLES `t_verify_log` WRITE;
/*!40000 ALTER TABLE `t_verify_log` DISABLE KEYS */;

INSERT INTO `t_verify_log` (`id`, `company_id`, `action_id`, `create_time`, `remark`, `result`, `operator`)
VALUES
	(1,*********,1,1516178578,'abcc',1,''),
	(2,34115,1,1516184313,'abcc',2,'ivy.zhu'),
	(3,34115,1,1516291953,'abcc',1,'ivy.zhu'),
	(4,*********,1,1516292857,NULL,1,''),
	(5,*********,1,1516292858,NULL,1,''),
	(6,*********,1,1516352411,'',1,''),
	(7,*********,1,1516356067,'',1,''),
	(8,*********,1,1516958967,'',1,'roy.chen'),
	(9,*********,1,1516959105,'',1,'roy.chen'),
	(10,*********,1,1516959191,'1111',2,'roy.chen'),
	(11,*********,1,1517305551,'test',2,'pre'),
	(12,*********,1,1517306330,'',1,'pre'),
	(13,*********,1,1517306367,'test',2,'pre'),
	(14,10905,1,1517306416,'',1,'pre'),
	(15,18478,1,1519874138,'linsa-test',2,'pre'),
	(16,*********,1,1519897854,'linsa-test',2,'pre'),
	(17,*********,1,1519898873,'test-linsa',2,'pre'),
	(18,*********,1,1519961745,'linsa-test',2,'pre'),
	(19,*********,1,1519962004,'11111111',2,'pre'),
	(20,*********,1,1519962824,'',1,'pre'),
	(21,*********,1,1520220295,'linsa-test',2,'pre'),
	(22,*********,1,1520235506,'linsa-test',2,'pre'),
	(23,*********,1,1520236332,'linsa-test-0305',2,'pre'),
	(24,*********,1,1520320271,'',2,'pre'),
	(25,*********,1,1520321470,'',2,'pre'),
	(26,*********,1,1520323424,'',2,'pre'),
	(27,*********,1,1520324133,'测试驳回。。。。。。。。。。。。。。。。。。。',2,'pre'),
	(28,*********,1,1520325187,'测试测试测试。。。。。。。。。。。。。。。。。。。。',2,'pre'),
	(29,*********,1,1520325280,'test。。。。。。。。。。。。。',2,'pre'),
	(30,*********,1,1520326539,'............',2,'pre'),
	(31,*********,1,1520327764,'。。。。。。。。。。。。。。。。。。测试。。。。。。。。。',2,'pre'),
	(32,34115,1,1520391077,'',1,'pre'),
	(33,34115,1,1520477920,'linsa-test',2,'pre'),
	(34,*********,1,1520818947,'长得太帅',1,'development'),
	(35,*********,1,1520818947,'长得太帅',2,'development'),
	(36,200000316,1,1521594917,'test',2,'pre'),
	(37,200000315,1,1521595016,'',1,'pre'),
	(38,*********,1,1521611657,'长得太帅',1,'development'),
	(39,*********,1,1521611657,'长得太帅',2,'development'),
	(40,*********,1,1521611720,'长得太帅',1,'development'),
	(41,*********,1,1521611720,'长得太帅',2,'development');

/*!40000 ALTER TABLE `t_verify_log` ENABLE KEYS */;
UNLOCK TABLES;



/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;
/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
