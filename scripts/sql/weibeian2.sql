# ************************************************************
# Sequel Pro SQL dump
# Version 4541
#
# http://www.sequelpro.com/
# https://github.com/sequelpro/sequelpro
#
# Host: *************** (MySQL 5.5.35)
# Database: weibeian2
# Generation Time: 2018-03-21 06:31:21 +0000
# ************************************************************


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;


# Dump of table t_batch
# ------------------------------------------------------------

DROP TABLE IF EXISTS `t_batch`;

CREATE TABLE `t_batch` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `batch_no` varchar(255) NOT NULL DEFAULT '',
  `description` varchar(1024) NOT NULL DEFAULT '',
  `create_time` int(16) NOT NULL DEFAULT '0',
  `update_time` int(16) NOT NULL DEFAULT '0',
  `creator` varchar(255) NOT NULL DEFAULT '',
  `status` int(4) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

LOCK TABLES `t_batch` WRITE;
/*!40000 ALTER TABLE `t_batch` DISABLE KEYS */;

INSERT INTO `t_batch` (`id`, `batch_no`, `description`, `create_time`, `update_time`, `creator`, `status`)
VALUES
	(1,'test','test',1520470780,1520861942,'roy.chen',2),
	(6,'79424f0e-42be-4737-ae80-2c94c0381a9c','SuccessFile',1521098524,1521101040,'pre',2),
	(7,'fb390810-1a75-4dad-92f7-2c4bab348e26','success.csv',1521099661,1521101100,'pre',1);

/*!40000 ALTER TABLE `t_batch` ENABLE KEYS */;
UNLOCK TABLES;


# Dump of table t_batch_org
# ------------------------------------------------------------

DROP TABLE IF EXISTS `t_batch_org`;

CREATE TABLE `t_batch_org` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `batch_id` int(11) NOT NULL DEFAULT '0',
  `company_id` int(16) NOT NULL DEFAULT '0',
  `company_name` varchar(255) NOT NULL DEFAULT '',
  `manager` varchar(255) NOT NULL DEFAULT '',
  `vip_level` varchar(255) NOT NULL DEFAULT '',
  `org_id` int(16) NOT NULL DEFAULT '0',
  `create_time` int(16) NOT NULL DEFAULT '0',
  `has_notify_info` int(4) NOT NULL DEFAULT '0',
  `retry_time` int(4) NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

LOCK TABLES `t_batch_org` WRITE;
/*!40000 ALTER TABLE `t_batch_org` DISABLE KEYS */;

INSERT INTO `t_batch_org` (`id`, `batch_id`, `company_id`, `company_name`, `manager`, `vip_level`, `org_id`, `create_time`, `has_notify_info`)
VALUES
	(6,1,50120011,'互动娱乐事业部官方账号','<EMAIL>','',50200010,1520858829,1),
	(7,1,50120011,'互动娱乐事业部官方账号','<EMAIL>','',50200010,1521076094,1),
	(8,1,50120011,'互动娱乐事业部官方账号','<EMAIL>','',50200010,1521076477,1),
	(9,6,50120011,'互动娱乐事业部官方账号','<EMAIL>','',50200010,1521100207,1),
	(10,6,50120011,'互动娱乐事业部官方账号','<EMAIL>','',50200010,1521100510,1),
	(11,7,50120011,'互动娱乐事业部官方账号','<EMAIL>','',50200010,1521100511,1),
	(12,7,50120011,'互动娱乐事业部官方账号','<EMAIL>','',50200010,1521100515,1),
	(13,0,50120011,'互动娱乐事业部官方账号','<EMAIL>','',50200010,1521100995,1);

/*!40000 ALTER TABLE `t_batch_org` ENABLE KEYS */;
UNLOCK TABLES;


# Dump of table t_notify
# ------------------------------------------------------------

DROP TABLE IF EXISTS `t_notify`;

CREATE TABLE `t_notify` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `batch_org_id` int(11) NOT NULL DEFAULT '0',
  `mobile` varchar(255) NOT NULL DEFAULT '',
  `email` varchar(255) NOT NULL DEFAULT '',
  `sms_status` int(4) NOT NULL DEFAULT '0',
  `email_status` int(4) NOT NULL DEFAULT '0',
  `create_time` int(16) NOT NULL DEFAULT '0',
  `update_time` int(16) NOT NULL DEFAULT '0',
  `sms_task_id` varchar(64) NOT NULL DEFAULT '',
  `email_task_id` varchar(64) NOT NULL DEFAULT '',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

LOCK TABLES `t_notify` WRITE;
/*!40000 ALTER TABLE `t_notify` DISABLE KEYS */;

INSERT INTO `t_notify` (`id`, `batch_org_id`, `mobile`, `email`, `sms_status`, `email_status`, `create_time`, `update_time`, `sms_task_id`, `email_task_id`)
VALUES
	(3,6,'15366261200','<EMAIL>',1,1,1520859054,1521023896,31,33),
	(5,7,'15366261200','<EMAIL>',0,0,1521076115,1521076115,0,0),
	(6,8,'15366261200','<EMAIL>',0,0,1521076511,1521076511,0,0),
	(7,9,'15366261200','<EMAIL>',0,0,1521100577,1521100577,0,0),
	(8,10,'15366261200','<EMAIL>',0,0,1521100985,1521100985,0,0),
	(9,11,'15366261200','<EMAIL>',0,0,1521100990,1521100990,0,0),
	(10,12,'15366261200','<EMAIL>',0,0,1521100995,1521100995,0,0),
	(11,13,'15366261200','<EMAIL>',0,0,1521101000,1521101000,0,0);

/*!40000 ALTER TABLE `t_notify` ENABLE KEYS */;
UNLOCK TABLES;


# Dump of table t_record
# ------------------------------------------------------------

DROP TABLE IF EXISTS `t_record`;

CREATE TABLE `t_record` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `batch_id` int(11) NOT NULL DEFAULT '0',
  `batch_org_id` int(11) NOT NULL DEFAULT '0',
  `ip` varchar(255) NOT NULL DEFAULT '',
  `domain` varchar(255) NOT NULL DEFAULT '',
  `create_time` int(16) NOT NULL DEFAULT '0',
  `register_status` int(4) NOT NULL DEFAULT '0',
  `remark` VARCHAR(128) NOT NULL DEFAULT '',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

LOCK TABLES `t_record` WRITE;
/*!40000 ALTER TABLE `t_record` DISABLE KEYS */;
/* alter table t_record add remark VARCHAR(128) NOT NULL DEFAULT '' */

INSERT INTO `t_record` (`id`, `batch_id`, `batch_org_id`, `ip`, `domain`, `create_time`)
VALUES
	(1,1,8,'**************','avi.qianjunye.com',1520470780,0),
	(4,6,9,'*************','avi.qianjunye.com',1521098524,0),
	(5,6,10,'*************','av.qianjunye.com',1521098524,1),
	(7,7,11,'*******','',1521099661,2),
	(8,7,12,'*******','',1521099661,2);

/*!40000 ALTER TABLE `t_record` ENABLE KEYS */;
UNLOCK TABLES;




# Dump of table t_registered_domain
# ------------------------------------------------------------

DROP TABLE IF EXISTS `t_registered_domain`;

CREATE TABLE `t_registered_domain` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `ip` varchar(255) NOT NULL,
  `domain` varchar(255) UNIQUE NOT NULL,
  `create_time` int(16) NOT NULL DEFAULT '0',
  `reopen_status` int(4) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

LOCK TABLES `t_registered_domain` WRITE;
/*!40000 ALTER TABLE `t_registered_domain` DISABLE KEYS */;

INSERT INTO `t_registered_domain` (`id`, `ip`, `domain`, `create_time`, `reopen_status`)
VALUES
	(1,'**************','avi.qianjunye.com',1520470780,0),
	(2,'*************','avi.qianjunye.com',1521098524,0),
	(3,'*************','av.qianjunye.com',1521098524,1),
	(4,'*******','',1521099661,1),
	(5,'*******','',1521099661,0);

/*!40000 ALTER TABLE `t_registered_domain` ENABLE KEYS */;
UNLOCK TABLES;


/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;
/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;


-- 违规信息通知


# Dump of table t_illegal_batch
# ------------------------------------------------------------

DROP TABLE IF EXISTS `t_illegal_batch`;

CREATE TABLE `t_illegal_batch` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `batch_no` varchar(255) NOT NULL DEFAULT '',
  `description` varchar(1024) NOT NULL DEFAULT '',
  `create_time` int(16) NOT NULL DEFAULT '0',
  `update_time` int(16) NOT NULL DEFAULT '0',
  `creator` varchar(255) NOT NULL DEFAULT '',
  `status` int(4) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

LOCK TABLES `t_illegal_batch` WRITE;


LOCK TABLES `t_illegal_batch` WRITE;
/*!40000 ALTER TABLE `t_illegal_batch` DISABLE KEYS */;

INSERT INTO `t_illegal_batch` (`id`, `batch_no`, `description`, `create_time`, `update_time`, `creator`, `status`)
VALUES
	(1,'test','test',1520470780,1520861942,'roy.chen',2),
	(6,'79424f0e-42be-4737-ae80-2c94c0381a9c','SuccessFile',1521098524,1521101040,'pre',2),
	(7,'fb390810-1a75-4dad-92f7-2c4bab348e26','success.csv',1521099661,1521101100,'pre',1);

/*!40000 ALTER TABLE `t_illegal_batch` ENABLE KEYS */;
UNLOCK TABLES;


# Dump of table t_illegal_batch_org
# ------------------------------------------------------------

DROP TABLE IF EXISTS `t_illegal_batch_org`;

CREATE TABLE `t_illegal_batch_org` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `batch_id` int(11) NOT NULL DEFAULT '0',
  `company_id` int(16) NOT NULL DEFAULT '0',
  `company_name` varchar(255) NOT NULL DEFAULT '',
  `manager` varchar(255) NOT NULL DEFAULT '',
  `vip_level` varchar(255) NOT NULL DEFAULT '',
  `org_id` int(16) NOT NULL DEFAULT '0',
  `create_time` int(16) NOT NULL DEFAULT '0',
  `has_notify_info` int(4) NOT NULL DEFAULT '0',
  `retry_time` int(4) NOT NULL DEFAULT 0,
  `bu` int(11),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

LOCK TABLES `t_illegal_batch_org` WRITE;
/*!40000 ALTER TABLE `t_illegal_batch_org` DISABLE KEYS */;

INSERT INTO `t_illegal_batch_org` (`id`, `batch_id`, `company_id`, `company_name`, `manager`, `vip_level`, `org_id`, `create_time`, `has_notify_info`)
VALUES
	(6,1,50120011,'互动娱乐事业部官方账号','<EMAIL>','',50200010,1520858829,1),
	(7,1,50120011,'互动娱乐事业部官方账号','<EMAIL>','',50200010,1521076094,1),
	(8,1,50120011,'互动娱乐事业部官方账号','<EMAIL>','',50200010,1521076477,1),
	(9,6,50120011,'互动娱乐事业部官方账号','<EMAIL>','',50200010,1521100207,1),
	(10,6,50120011,'互动娱乐事业部官方账号','<EMAIL>','',50200010,1521100510,1),
	(11,7,50120011,'互动娱乐事业部官方账号','<EMAIL>','',50200010,1521100511,1),
	(12,7,50120011,'互动娱乐事业部官方账号','<EMAIL>','',50200010,1521100515,1),
	(13,0,50120011,'互动娱乐事业部官方账号','<EMAIL>','',50200010,1521100995,1);

/*!40000 ALTER TABLE `t_illegal_batch_org` ENABLE KEYS */;
UNLOCK TABLES;


# Dump of table t_notify
# ------------------------------------------------------------

DROP TABLE IF EXISTS `t_illegal_notify`;

CREATE TABLE `t_illegal_notify` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `batch_id` int(11) NOT NULL DEFAULT '0',
  `batch_org_id` int(11) NOT NULL DEFAULT '0',
  `mobile` varchar(255) NOT NULL DEFAULT '',
  `email` varchar(255) NOT NULL DEFAULT '',
  `sms_status` int(4) NOT NULL DEFAULT '0',
  `email_status` int(4) NOT NULL DEFAULT '0',
  `create_time` int(16) NOT NULL DEFAULT '0',
  `update_time` int(16) NOT NULL DEFAULT '0',
  `sms_task_id` varchar(64) NOT NULL DEFAULT '',
  `email_task_id` varchar(64) NOT NULL DEFAULT '',
  `sms_retry_times` int(11) NOT NULL DEFAULT '0',
  `email_retry_times` int(11) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

LOCK TABLES `t_illegal_notify` WRITE;
/*!40000 ALTER TABLE `t_illegal_notify` DISABLE KEYS */;

INSERT INTO `t_illegal_notify` (`id`, `batch_org_id`, `mobile`, `email`, `sms_status`, `email_status`, `create_time`, `update_time`, `sms_task_id`, `email_task_id`)
VALUES
	(3,6,'15366261200','<EMAIL>',1,1,1520859054,1521023896,31,33),
	(5,7,'15366261200','<EMAIL>',0,0,1521076115,1521076115,0,0),
	(6,8,'15366261200','<EMAIL>',0,0,1521076511,1521076511,0,0),
	(7,9,'15366261200','<EMAIL>',0,0,1521100577,1521100577,0,0),
	(8,10,'15366261200','<EMAIL>',0,0,1521100985,1521100985,0,0),
	(9,11,'15366261200','<EMAIL>',0,0,1521100990,1521100990,0,0),
	(10,12,'15366261200','<EMAIL>',0,0,1521100995,1521100995,0,0),
	(11,13,'15366261200','<EMAIL>',0,0,1521101000,1521101000,0,0);

/*!40000 ALTER TABLE `t_illegal_notify` ENABLE KEYS */;
UNLOCK TABLES;


# Dump of table t_notify
# ------------------------------------------------------------

DROP TABLE IF EXISTS `t_illegal_bu_notify`;

CREATE TABLE `t_illegal_bu_notify` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `batch_id` int(11) NOT NULL DEFAULT '0',
  `bu` int(11),
  `email` varchar(255) NOT NULL DEFAULT '',
  `create_time` int(16) NOT NULL DEFAULT '0',
  `update_time` int(16) NOT NULL DEFAULT '0',
  `task_id` varchar(64) NOT NULL DEFAULT '',
  `task_status` int(16) NOT NULL DEFAULT '0',
  `channel_id` int(11) DEFAULT 1,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

# Dump of table t_illegal_record
# ------------------------------------------------------------

DROP TABLE IF EXISTS `t_illegal_record`;

CREATE TABLE `t_illegal_record` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `batch_id` int(11) NOT NULL DEFAULT '0',
  `batch_org_id` int(11) NOT NULL DEFAULT '0',
  `ip` varchar(255) NOT NULL DEFAULT '',
  `url` varchar(255) NOT NULL DEFAULT '',
  `illegal_detail` varchar(255) NOT NULL DEFAULT '',
  `create_time` int(16) NOT NULL DEFAULT '0',
  `remark` VARCHAR(128) NOT NULL DEFAULT '',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

/* alter table t_illegal_record add remark VARCHAR(128) NOT NULL DEFAULT '' */

LOCK TABLES `t_illegal_record` WRITE;
/*!40000 ALTER TABLE `t_illegal_record` DISABLE KEYS */;

INSERT INTO `t_illegal_record` (`id`, `batch_id`, `batch_org_id`, `ip`, `url`, `illegal_detail`, `create_time`)
VALUES
	(1,1,8,'**************','avi.qianjunye.com','',1520470780,0),
	(4,6,9,'*************','avi.qianjunye.com','',1521098524,0),
	(5,6,10,'*************','av.qianjunye.com','',1521098524,1),
	(7,7,11,'*******','',1521099661,'',2),
	(8,7,12,'*******','',1521099661,'',2);

/*!40000 ALTER TABLE `t_illegal_record` ENABLE KEYS */;
UNLOCK TABLES;

-----------------------------------------------
-- 公安未备案系统

DROP TABLE IF EXISTS `t_police_batch`;

CREATE TABLE IF NOT EXISTS `t_police_batch` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `description` VARCHAR(255) NOT NULL,
  `create_time` int(16) NOT NULL DEFAULT '0',
  `update_time` int(16) NOT NULL DEFAULT '0',
  `type` int(11) DEFAULT 1,
  `status` int(4)  NOT NULL DEFAULT '0',
  `operator` varchar(255) NOT NULL DEFAULT '',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

DROP TABLE IF EXISTS `t_police_domain`;
CREATE TABLE IF NOT EXISTS `t_police_domain` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `batch_id`  int(11) NOT NULL,
  `company_name` VARCHAR(255) NOT NULL,
  `domain` VARCHAR(255) NOT NULL,
  PRIMARY KEY (`id`,`batch_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

DROP TABLE IF EXISTS `t_police_record`;
CREATE TABLE IF NOT EXISTS `t_police_record` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `batch_id`  int(11) unsigned NOT NULL,
  `company_name` VARCHAR(255) NOT NULL,
  `mobile` VARCHAR(255) NOT NULL,
  `email` VARCHAR(255) NOT NULL,
  `province` VARCHAR(255),
  `sms_status` int(4) NOT NULL DEFAULT '0',
  `email_status` int(4) NOT NULL DEFAULT '0',
  `sms_task_id` varchar(64) NOT NULL DEFAULT '',
  `email_task_id` varchar(64) NOT NULL DEFAULT '',
  `sms_retry_times` int(11) NOT NULL DEFAULT '0',
  `email_retry_times` int(11) NOT NULL DEFAULT '0',
  `create_time` int(16) NOT NULL DEFAULT '0',
  `update_time` int(16) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`,`batch_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-----------------------------------------
-- 邮件通知
DROP TABLE
IF EXISTS `t_email_notice`;

CREATE TABLE
IF NOT EXISTS `t_email_notice` (
	`id` INT (11) UNSIGNED NOT NULL AUTO_INCREMENT,
	`send_time` INT (16) NOT NULL DEFAULT '0',
	`company_id` INT (16) NOT NULL,
	`email_type` VARCHAR (64) NOT NULL DEFAULT '',
	`user_email` VARCHAR (255) NOT NULL DEFAULT '',
  `ip` VARCHAR(50),
	`email_status` INT (4) NOT NULL DEFAULT '0',
	`email_task_id` VARCHAR (64) NOT NULL DEFAULT '',
	`create_time` INT (16) NOT NULL DEFAULT '0',
	`retry_times` INT (1) NOT NULL DEFAULT '0',
	`update_time` INT (16) NOT NULL DEFAULT '0',
	`close_time` INT (16) NOT NULL DEFAULT '0',
	`disabled_time` INT (16) NOT NULL DEFAULT '0',
  `channel_id` VARCHAR(20) DEFAULT '1',
  `is_del` INT(1) NOT NULL DEFAULT 0,
	PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

----------------------------------------------
-----2018假期日历

DROP TABLE
IF EXISTS `t_holiday_list`;
CREATE TABLE `t_holiday_list`(
`name` VARCHAR(20) NOT NULL DEFAULT '',
`date` VARCHAR(16) NOT NULL,
`holiday` INT(1) NOT NULL DEFAULT '0',
PRIMARY KEY (`date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

----------------------------------------------
-----待处理列表

DROP TABLE IF EXISTS `t_checker_notify`;
CREATE TABLE `t_checker_notify` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `operate_time` int(16) NOT NULL DEFAULT '0',
  `company_id` int(16) NOT NULL,
  `operate_type` int(4) NOT NULL DEFAULT '0',
  `create_time` int(16) NOT NULL DEFAULT '0',
  `update_time` int(16) NOT NULL DEFAULT '0',
  `is_del` int(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=37 DEFAULT CHARSET=utf8;

SET FOREIGN_KEY_CHECKS = 1;

------------------------------------------------
-------------合规发送邮件短信的历史记录
DROP TABLE IF EXISTS `t_batch_history`;
CREATE TABLE `t_batch_history` (
  `id` int(20) unsigned NOT NULL AUTO_INCREMENT,
  `company_id` int(16) NOT NULL,
  `org_id` int(16) NOT NULL,
  `batch_id` int(16) NOT NULL,
  `batch_org_id` int(16) NOT NULL,
  `type` int(11) DEFAULT '1',
  `send_person` VARCHAR(10000),
  `create_time` int(16) NOT NULL DEFAULT '0',
  `is_del` int(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=39 DEFAULT CHARSET=utf8;

--------------------------------------------------
------------创建批量封号批次
---issue: 下发, type:封号类型,notice:是否发送通知
DROP TABLE IF EXISTS `t_seal_batch`;

CREATE TABLE IF NOT EXISTS `t_seal_batch` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `type` int(4) NOT NULL,
  `issue` int(4) NOT NULL DEFAULT 0,
  `notice`  int(4) NOT NULL DEFAULT 0,
  `create_time` int(16) NOT NULL DEFAULT '0',
  `update_time` int(16) NOT NULL DEFAULT '0',
  `operator` varchar(255) NOT NULL DEFAULT '',
  `status` int(4),
  `remark` VARCHAR(255) NOT NULL DEFAULT '',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

DROP TABLE IF EXISTS `t_seal_record`;


---has_notify_info: 是否已获得联系方式， 默认1

CREATE TABLE `t_seal_record` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `batch_id` int(11) NOT NULL DEFAULT '0',
  `company_id` int(16) NOT NULL DEFAULT '0',
  `seal_status` int(4) NOT NULL DEFAULT '0',
  `recycle_status` int(4) NOT NULL DEFAULT '0',
  `has_notify_info` int(4) NOT NULL DEFAULT 0,
  `operate_type` int(4) NOT NULL DEFAULT 1,
  `operate_remark` varchar(1000),
  `operator` varchar(255),
  `create_time` int(16) NOT NULL DEFAULT '0',
  `update_time` int(16) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


DROP TABLE IF EXISTS `t_seal_notify`;

CREATE TABLE `t_seal_notify` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `batch_id` int(11) NOT NULL DEFAULT '0',
  `company_id` int(16) NOT NULL DEFAULT '0',
  `mobile` varchar(255) NOT NULL DEFAULT '',
  `email` varchar(255) NOT NULL DEFAULT '',
  `sms_status` int(4) NOT NULL DEFAULT '0',
  `email_status` int(4) NOT NULL DEFAULT '0',
  `sms_task_id` varchar(64) DEFAULT '',
  `email_task_id` varchar(64) DEFAULT '',
  `create_time` int(16) NOT NULL DEFAULT '0',
  `sms_update_time` int(16) NOT NULL DEFAULT '0',
  `email_update_time` int(16) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;