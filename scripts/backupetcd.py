import etcd
import time
import os


etcdhost = "*************"
etcdpost = 65001

client = etcd.Client(host=etcdhost, port=etcdpost)
all_key = client.read('/', recursive=True, sorted=True)
string = ""
for child in all_key.children:
	if child.value:
		string += child.key + ":" + child.value + "\n"

print string
filename = str(int(time.time())) + ".txt"
print filename
file_object = open(filename, 'w')
try:
	file_object.write(string)
finally:
     file_object.close()
