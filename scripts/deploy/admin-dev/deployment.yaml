apiVersion: apps/v1
kind: Deployment
metadata:
  name: newicp-admin-bak
  namespace: prj-icp
spec:
  replicas: 1
  selector:
    matchLabels:
      app: newicp-admin-bak
  template:
    metadata:
      annotations:
        cni.networking.kun/ipv4Enabled: "true"
      labels:
        # 开启域名双栈解析
        kun-dns-enable-double: "true"
        app: newicp-admin-bak
    spec:
      containers:
      - name: newicp-admin-bak
        image: hub.ucloudadmin.com/tpl_crd_icp/newicp-backend
        resources:
          requests:
            memory: 500Mi
            cpu: 400m
          limits:
            memory: 600Mi
            cpu: 500m
        env:
          - name: SERVER_ENV
            value: gray
        ports:
          - containerPort: 6161
      imagePullSecrets:
      - name: lywregistry
