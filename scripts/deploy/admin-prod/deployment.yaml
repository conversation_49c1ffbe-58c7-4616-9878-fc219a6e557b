apiVersion: apps/v1
kind: Deployment
metadata:
  name: newicp-admin-bak-online
  namespace: prj-icp
spec:
  replicas: 3
  selector:
    matchLabels:
      app: newicp-admin-bak-online
  template:
    metadata:
      annotations:
        cni.networking.kun/ipv4Enabled: "true"
      labels:
        # 开启域名双栈解析
        kun-dns-enable-double: "true"
        app: newicp-admin-bak-online
    spec:
      containers:
      - name: newicp-admin-bak-online
        image: hub.ucloudadmin.com/tpl_crd_icp/newicp-backend
        volumeMounts:
        - mountPath: "/data/logs/new"
          name: my-logs-volume
        resources:
          requests:
            memory: 1700Mi
            cpu: 1600m
          limits:
            memory: 2000Mi
            cpu: 2000m
        env:
          - name: SERVER_ENV
            value: production
        ports:
          - containerPort: 6161
      imagePullSecrets:
      - name: lywregistry
      volumes:      
      - name: my-logs-volume
        persistentVolumeClaim:
          claimName: newicp-admin-bak-volume