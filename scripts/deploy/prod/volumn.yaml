# ufs-pod-pvc.yaml
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: newicp-console-bak-volume
spec:
  accessModes:
    # 指定 accessModes 为多读多写，其他模式不支持
    - ReadWriteMany
  resources:
    requests:
      # 请求大小最小为 10Gi
      storage: 20Gi
  # 指定 storageClassName 为 ufs-basic-hk，ufs-basic-hk 是 uae-a4 提供的容量型 storageClass
  # 每个集群有哪些 storageClass 可以用，请参考 [KUN 集群概览]
  storageClassName: ufs-basic-cn-bj2
