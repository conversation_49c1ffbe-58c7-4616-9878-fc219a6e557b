#!/bin/bash
#=====================================================
#2点定时从服务器上获取数据、上传WA、删除
#
#作者：william.qian
#日期：2019年12月24日
#=====================================================

#########初始配置数据#################################

#SFTP配置信息
#用户名
USER=youkede_2
#密码
PASSWORD=Youkede_212#$
#待上传文件根目录
SRCDIR=${HOME}/run/data
#银联FTP目录
DESDIR=test
#银联IP
IP=*************

#脱机消费文件成参数
#文件记录条数
LINE=1000
#文件生成清算日期（需要变更操作）
#DATE=20140507
DATE=`date +%Y%m%d`
#文件目录
DIR=${SRCDIR}/${DATE}

#######生成脱机文件##################################
#产生文件
offline ${DATE} ${LINE}

#######上传脱机文件##################################
#获取文件
cd ${DIR} ; 
FILES=`ls`

for FILE in ${FILES}
do
    echo ${FILE}
#发送文件 (关键部分）
lftp -u ${USER},${PASSWORD} sftp://${IP} <<EOF 
cd ${DESDIR}/
lcd ${DIR}
put ${FILE} 
by
EOF

done