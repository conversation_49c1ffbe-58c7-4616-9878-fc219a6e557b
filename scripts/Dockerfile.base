FROM hub.ucloudadmin.com/tpl_crd_icp/node:20.17.0-bullseye

# MAINTAINER yuewen.li "<EMAIL>"

RUN apt-get -y install gcc

# RUN wget http://ftp.icm.edu.pl/pub/unix/graphics/GraphicsMagick/1.3/GraphicsMagick-1.3.21.tar.gz
COPY GraphicsMagick-1.3.21.tar.gz .

RUN tar zxvf GraphicsMagick-1.3.21.tar.gz
RUN cd GraphicsMagick-1.3.21 \
    && ./configure \
    && make -j8 \
    && make install

RUN sed -i "s/deb.debian.org/mirrors.aliyun.com/g" /etc/apt/sources.list

RUN apt-get update && \
    apt-get -y install python && \
    apt-get install -y chromium && \
    apt-get -y install apt-file && \
    apt-get -y install software-properties-common

RUN add-apt-repository ppa:kirillshkrogalev/ffmpeg-next

RUN apt-get install -y ffmpeg