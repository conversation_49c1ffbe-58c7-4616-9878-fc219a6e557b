{
    "extends": ["eslint:recommended", "plugin:prettier/recommended"],
    "plugins": ["prettier"],
    "env": {
      "browser": true,
      "node": true,
      "es2021": true
    },
    "parserOptions": {
      "ecmaVersion": 12,
      "sourceType": "script"
    },
    "rules": {
      "prettier/prettier": "error",
      // "strict": ["error", "global"],
      "no-undef": ["error"],
      // 不允许使用常量表达式 例如 if(true) /while(true)等
      "no-constant-condition": ["error", { "checkLoops": false }],
      "no-unused-vars": "off",
      "require-atomic-updates": "off",
      "linebreak-style": ["error", "unix"]
    }
  }
